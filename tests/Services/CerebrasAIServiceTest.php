<?php

namespace Tests\Services;

use App\Models\GptChat;
use App\Models\GptChatCompletion;
use App\Services\CerebrasAIService;
use Illuminate\Support\Facades\Config;
use JsonException;
use PHPUnit\Framework\MockObject\MockObject;
use RuntimeException;
use Tests\TestCase;

class CerebrasAIServiceTest extends TestCase
{
    private CerebrasAIService $service;
    private GptChat $gptChat;

    public function setUp(): void
    {
        parent::setUp();

        $this->service = new CerebrasAIService();

        // Create a mock GptChat with a business relationship
        $this->gptChat = $this->createMock(GptChat::class);
        $this->gptChat->method('__get')->with('id')->willReturn(1);
        $this->gptChat->business = (object)['business_pk' => 123];
    }

    /**
     * Test successful chat completion
     *
     * @return void
     * @throws JsonException
     */
    public function testChatCompletionsSuccess(): void
    {
        // Mock the curl functions
        $this->mockCurlFunctions(
            200,
            json_encode([
                'id' => 'chat-123',
                'object' => 'chat.completion',
                'model' => 'llama-4-scout-17b-16e-instruct',
                'choices' => [
                    [
                        'message' => [
                            'content' => 'This is a test response'
                        ]
                    ]
                ],
                'usage' => [
                    'prompt_tokens' => 10,
                    'completion_tokens' => 20,
                    'total_tokens' => 30
                ]
            ])
        );

        // Set API key in config
        Config::set('app.CEREBRAS_API_KEY', 'test-api-key');

        // Create a mock GptChatCompletion
        $mockCompletion = $this->createMock(GptChatCompletion::class);
        GptChatCompletion::shouldReceive('create')
            ->once()
            ->andReturn($mockCompletion);

        // Test the method
        $result = $this->service->chatCompletions(
            $this->gptChat,
            ['prompt' => 'Test prompt', 'temperature' => 0.5]
        );

        // Assert result is the mock completion
        $this->assertSame($mockCompletion, $result);
    }

    /**
     * Test chat completion with context
     *
     * @return void
     * @throws JsonException
     */
    public function testChatCompletionsWithContext(): void
    {
        // Mock the curl functions
        $this->mockCurlFunctions(
            200,
            json_encode([
                'id' => 'chat-123',
                'object' => 'chat.completion',
                'model' => 'llama-4-scout-17b-16e-instruct',
                'choices' => [
                    [
                        'message' => [
                            'content' => 'This is a test response'
                        ]
                    ]
                ],
                'usage' => [
                    'prompt_tokens' => 10,
                    'completion_tokens' => 20,
                    'total_tokens' => 30
                ]
            ])
        );

        // Set API key in config
        Config::set('app.CEREBRAS_API_KEY', 'test-api-key');

        // Create a mock GptChatCompletion
        $mockCompletion = $this->createMock(GptChatCompletion::class);
        GptChatCompletion::shouldReceive('create')
            ->once()
            ->andReturn($mockCompletion);

        // Test the method with context
        $result = $this->service->chatCompletions(
            $this->gptChat,
            [
                'prompt' => 'Test prompt',
                'context' => 'You are a helpful assistant',
                'temperature' => 0.5
            ]
        );

        // Assert result is the mock completion
        $this->assertSame($mockCompletion, $result);
    }

    /**
     * Test chat completion with API error
     *
     * @return void
     * @throws JsonException
     */
    public function testChatCompletionsApiError(): void
    {
        // Mock the curl functions to return an error
        $this->mockCurlFunctions(400, 'Bad Request');

        // Set API key in config
        Config::set('app.CEREBRAS_API_KEY', 'test-api-key');

        // Expect exception
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('The server encountered an unexpected condition which prevented it from fulfilling the request');

        // Test the method
        $this->service->chatCompletions(
            $this->gptChat,
            ['prompt' => 'Test prompt']
        );
    }

    /**
     * Test image generation (not implemented)
     *
     * @return void
     */
    public function testImageGenerationNotImplemented(): void
    {
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Not implemented');

        $this->service->imageGeneration(
            $this->gptChat,
            ['prompt' => 'Test prompt']
        );
    }

    /**
     * Helper method to mock curl functions
     *
     * @param int $httpCode
     * @param string $response
     * @return void
     */
    private function mockCurlFunctions(int $httpCode, string $response): void
    {
        // Define the namespace for the functions
        $namespace = 'App\Services';

        // Mock curl_init
        $this->getFunctionMock($namespace, 'curl_init')
            ->expects($this->once())
            ->willReturn('curl-handle');

        // Mock curl_setopt
        $this->getFunctionMock($namespace, 'curl_setopt')
            ->expects($this->atLeastOnce())
            ->willReturn(true);

        // Mock curl_exec
        $this->getFunctionMock($namespace, 'curl_exec')
            ->expects($this->once())
            ->willReturn($response);

        // Mock curl_getinfo
        $this->getFunctionMock($namespace, 'curl_getinfo')
            ->expects($this->once())
            ->willReturn($httpCode);

        // Mock curl_close
        $this->getFunctionMock($namespace, 'curl_close')
            ->expects($this->once())
            ->willReturn(null);
    }
}
