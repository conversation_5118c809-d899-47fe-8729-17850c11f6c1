# Project-specific
/node_modules
/public/storage
/vendor
/.idea
Homestead.json
Homestead.yaml
.env
public/upload/*
storage/*
storage/oauth-private.key
storage/oauth-public.key
storage/debugbar
storage/logs

# Other ignored files
.idea/
.project
*desktop.ini
*.iml
*.ipr
*.iws
.metadata
bin/**
tmp/**
tmp/**/*
*.tmp
*.bak
*.swp
*~.nib
local.properties
.classpath
.settings/
.loadpath
*.launch

nbproject/
nbactions.xml
nb-configuration.xml

# CDT-specific
.cproject

# PDT-specific
.buildpath

*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
.elc
auto-save-list
tramp
.\#*

.DS_Store

# Thumbnails
._*

/*.sublime-project
/*.sublime-workspace
*Thumbs.db
Session.vim

# Files that might appear on external disk
.Spotlight-V100
.Trashes
/src/Survey/vendor
/src/Survey/composer.lock
/package-lock.json
_ide_helper.php
.phpstorm.meta.php
ubperrors.txt
ubptest.txt
public/photos

# Service Accounts
astral-gateway-349121-e524fd2460b5.json
kangaroo-dev-5223a-37c313353066.json
kangaroo-dev-5223a_auth_credentials.json
kangaroo-rewards-prod_auth_credentials.json
config/*.json
.phpunit.result.cache
/tools
.php-cs-fixer.cache
.vscode/settings.json
.env.testing
qodana.yaml
