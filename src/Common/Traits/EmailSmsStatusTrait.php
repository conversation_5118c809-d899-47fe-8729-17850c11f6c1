<?php

namespace KangarooRewards\Common\Traits;

trait EmailSmsStatusTrait
{
    /*public static function getFinalStatusCodeForNexmo($_status): int
    {
        if ($_status === 'DELIVRD') {
            return EMAIL_SMS_STATUS_DELIVERED;
        }

        if (in_array($_status, ['EXPIRED', 'UNDELIV', 'REJECTD', 'UNKNOWN', 'DELETED'])) {
            return EMAIL_SMS_STATUS_FAILED;
        }

        return EMAIL_SMS_STATUS_QUEUED;
    }*/

    /**
     * Getting the status as integer from a string input
     *
     * @param string $strStatus - the status received from <PERSON>wilio or sending email function
     * @return integer
     */
    public static function getStatusCode($strStatus): int
    {
        if ($strStatus == 'queued' || $strStatus == 'sending' || $strStatus == 'accepted') {
            $status = EMAIL_SMS_STATUS_QUEUED;
        } elseif ($strStatus == 'sent') {
            $status = EMAIL_SMS_STATUS_SENT;
        } elseif ($strStatus == 'delivered') {
            $status = EMAIL_SMS_STATUS_DELIVERED;
        } elseif (in_array($strStatus, ['failed', 'undelivered', 'expired', 'rejected', 'bufferedorunknown'])) {
            $status = EMAIL_SMS_STATUS_FAILED;
        } else {
            $status = EMAIL_SMS_STATUS_QUEUED;
        }

        return $status;
    }

    /**
     * @param string $_status - the status received from Broadnet or sending SMS function
     * @return integer
     */

    public static function getStatusCodeForBroadnet($_status): int
    {
        if ($_status === 'DELIVRD') {
            return EMAIL_SMS_STATUS_DELIVERED;
        }

        if (in_array($_status, ['ATES', 'ACCEPTD', 'CP'])) {
            return EMAIL_SMS_STATUS_QUEUED;
        }

        if (in_array($_status, ['EXPIRED', 'UNDELIV', 'UNDELIVERED', 'REJECTD',
            'REJECTED', 'UNKNOWN', 'DELETED', 'INVALID', 'ERR_RESP'])) {
            return EMAIL_SMS_STATUS_FAILED;
        }

        return EMAIL_SMS_STATUS_QUEUED;
    }

    public static function getErrorMsgForNexmo($_status): string
    {
        $r = [
            0 => 'Delivered',
            1 => 'Unknown',
            2 => 'Absent Subscriber Temporary',
            3 => 'Absent Subscriber Permanent',
            4 => 'Call barred by user',
            5 => 'Portability Error',
            6 => 'Anti-Spam Rejection',
            7 => 'Handset Busy',
            8 => 'Network Error',
            9 => 'Illegal Number',
            10 => 'Invalid Message',
            11 => 'Unroutable',
            12 => 'Destination unreachable',
            13 => 'Subscriber Age Restriction',
            14 => 'Number Blocked by Carrier',
            15 => 'Pre-Paid',
            99 => 'General Error',
        ];

        return $r[$_status] ?? 'Unknown Error';
    }

    /**
     * Getting the status as string from Nexmo result
     *
     * @param string $_status - the status received from Nexmo
     * @return integer
     */
    public static function getStatusTextForNexmo($_status)
    {
        if ($_status > 0) {
            return 'failed';
        }

        return 'delivered';
    }
}
