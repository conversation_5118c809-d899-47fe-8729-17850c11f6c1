<?php

declare(strict_types=1);

namespace KangarooRewards\Workflows;

use Illuminate\Foundation\Support\Providers\EventServiceProvider;
use KangarooRewards\Workflows\Events\TriggerInvalidConditions;
use KangarooRewards\Workflows\Events\WorkflowActivityCompleted;
use KangarooRewards\Workflows\Events\WorkflowActivityFailed;
use KangarooRewards\Workflows\Events\WorkflowActivityStarted;
use KangarooRewards\Workflows\Events\WorkflowCanceled;
use KangarooRewards\Workflows\Events\WorkflowCompleted;
use KangarooRewards\Workflows\Events\WorkflowFailed;
use KangarooRewards\Workflows\Events\WorkflowInvalidConditions;
use KangarooRewards\Workflows\Events\WorkflowStarted;
use Kang<PERSON>Rewards\Workflows\Listeners\HandleTriggerInvalidConditions;
use KangarooRewards\Workflows\Listeners\HandleWorkflowInvalidConditions;
use KangarooRewards\Workflows\Listeners\MonitorWorkflowActivityCompleted;
use Kang<PERSON>Rewards\Workflows\Listeners\MonitorWorkflowActivityFailed;
use Kang<PERSON>Rewards\Workflows\Listeners\MonitorWorkflowActivityStarted;
use KangarooRewards\Workflows\Listeners\MonitorWorkflowCanceled;
use KangarooRewards\Workflows\Listeners\MonitorWorkflowCompleted;
use KangarooRewards\Workflows\Listeners\MonitorWorkflowFailed;
use KangarooRewards\Workflows\Listeners\MonitorWorkflowStarted;
use KangarooRewards\Workflows\Listeners\WorkflowEventObserver;
use KangarooRewards\Workflows\Models\WorkflowEvent;
use KangarooRewards\Workflows\Services\WorkflowConditionsService;

class WorkflowsEventServiceProvider extends EventServiceProvider
{
    protected $listen = [
        // UserRegistered::class => [SendWelcomeEmail::class].
        WorkflowInvalidConditions::class => [HandleWorkflowInvalidConditions::class],
        TriggerInvalidConditions::class => [HandleTriggerInvalidConditions::class],
        WorkflowStarted::class => [MonitorWorkflowStarted::class],
        WorkflowCompleted::class => [MonitorWorkflowCompleted::class],
        WorkflowFailed::class => [MonitorWorkflowFailed::class],
        WorkflowCanceled::class => [MonitorWorkflowCanceled::class],
        WorkflowActivityStarted::class => [MonitorWorkflowActivityStarted::class],
        WorkflowActivityCompleted::class => [MonitorWorkflowActivityCompleted::class],
        WorkflowActivityFailed::class => [MonitorWorkflowActivityFailed::class],
    ];

    /**
     * The model observers for the application.
     *
     * @var array
     */
    protected $observers = [
        WorkflowEvent::class => [WorkflowEventObserver::class],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }
}
