<h3>
	Users signed up at the store
</h3>

<div class="dash-transactions-dates-wrap">
   	<ul>
       	<li>
           	<?php echo CHtml::textField('trx-date1', $firstDate, array('class'=>'form-control', )); ?> 
       	</li>
       	<li>
           	<?php echo CHtml::textField('trx-date2', $lastDate, array('class'=>'form-control', )); ?> 
       	</li>
       	<li>
       		<?php

       			echo CHtml::dropDownList('sign', 'eq', array('=' => '=', '>=' => '>=' , '<=' => '<='), [ 'class'=>'form-control']);
       		?> 
       	</li>
       	<li>
       		<?php

       			echo CHtml::dropDownList('total', '0', array('0' => '0', '1' => '1', '2' => '2', '10' => '10', '50' => '50'), ['class'=>'form-control']);
       		?> 
       	</li>
       	<li>
           	<?php echo CHtml::button(Yii::t('backend','BACKEND_DASHBOARD_TRX_LIST_SHOW_BUTTON'), array('id'=>'show-store-zero-trx-list', 'class'=>'btn btn-primary ', )); ?> 
       	</li>
   	</ul>
</div>

<p>
  <table id="users_at_store" class="table table-striped"></table>
</p>


<script>

$(function() {

    $('#trx-date1').datepicker({'dateFormat':'yy-mm-dd' });
    $('#trx-date2').datepicker({'dateFormat':'yy-mm-dd' });

    $('#show-store-zero-trx-list').on('click', function(){
    	var sign = $('#sign').val();
    	var total = $('#total').val();
    	var params = {date1: $('#trx-date1').val(), date2: $('#trx-date2').val(), listType: 'users_at_store', sign: sign, total: total};
		getJsonData('/systemAdmin/main/AjaxTransactionList', params);
    });
    
	var sign = $('#sign').val();
	var total = $('#total').val();
	var params = {date1: $('#trx-date1').val(), date2: $('#trx-date2').val(), listType: 'users_at_store', sign: sign, total: total};
	getJsonData('/systemAdmin/main/AjaxTransactionList', params);


	function getJsonData(url, params)
	{
		$.ajax({
            url: url,
            type: "get",
            dataType:'json',
            data: params,
            async: true,
            success: function(ajaxData)
            {
            	var ajaxData = ajaxData.trx;
            	buildTable(ajaxData, '#users_at_store');
            },
            error:function(){

            }
        }); //ajax
	}; //function

    function buildTable(myList, tableId)
	{
		$(tableId).html('');
		var headers = [];
        headers.push('ID');
        headers.push('Business Name');
        headers.push('Total');

        var $headerTr = $('<tr/>');

        for (var i = 0 ; i < headers.length ; i++) {
            $headerTr.append($('<th/>').html(headers[i]));
        }
       
        $(tableId).append($headerTr);

		var cellValues = []; var total = 0;
         for (var i = 0 ; i < myList.length ; i++) {
            var $row = $('<tr/>');
            cellValues = [];

            total = Number(myList[i]['total']).toLocaleString('en-US');

            cellValues.push(i+1);
            cellValues.push(myList[i]['business_name'].replace(new RegExp("\\\\", "g"), ""));
            cellValues.push(total);
            for (var j = 0 ; j < cellValues.length ; j++) {
                $row.append($('<td/>').html(cellValues[j]));
            }
          
            $(tableId).append($row);
        }
	}
});//ready

</script>