<?php

/**
 * This is the model class for table "user".
 *
 * The followings are the available columns in table 'user':
 * @property string $id
 * @property string $username
 * @property string $salt
 * @property string $password
 * @property string $passwordreset
 * @property string $email
 * @property string $first
 * @property string $last
 * @property string $website
 * @property string $phone
 * @property string $qrcode
 * @property string $location
 * @property integer $enabled
 * @property string $utc_created
 * @property string $utc_updated
 * @property string $gender
 * @property string $birth_date
 * @property string $twitter_id
 * @property string $twitter_secret
 * @property string $facebook_id
 * @property string $facebook_token
 * @property string $utc_last_login
 * @property string $ip
 * @property string $image
 * @property integer $language
 * @property string $address
 * @property integer $city_fk
 * @property integer $region_fk
 * @property integer $country_fk
 * @property string $pin_permission
 * @property integer $pin_enabled
 * @property integer $hidden
 * @property string $platform_solution_fk
 * @property integer $display_facebook_image
 * @property integer $timezone_mysql_fk
 * @property string $login_type_fk
 * @property string $utc_link_expires
 * @property string $device_token
 * @property string $gcm_device_token
 * @property integer $signedupat_fk
 *
 * The followings are the available model relations:
 * @property BusinessTrokPermissions[] $businessTrokPermissions
 * @property Donations[] $donations
 * @property Friend[] $friends
 * @property Friend[] $friends1
 * @property Trok[] $troks
 * @property Trok[] $troks1
 * @property Countries $countryFk
 * @property Regions $regionFk
 * @property Cities $cityFk
 * @property PlatformSolution $platformSolutionFk
 * @property LoginType $loginTypeFk
 * @property BusinessBranch[] $businessBranches
 * @property UserEntityPermission[] $userEntityPermissions
 * @property UserEntityPoints[] $userEntityPoints
 * @property UserFriendsCategory[] $userFriendsCategories
 * @property UserOffer[] $userOffers
 * @property $business_fk
 */

class User extends CActiveRecord
{

/****************************************************************************************************/
/*********************************** MAIN Yii Methods START HERE ************************************/
/****************************************************************************************************/

    public $password_repeat;
    public $iagree;
    public $image;
    public $tw_connect;
    public $fb_connect;
    public $birthday_field;
    public $verifyCode;
    public $user_token;

    public $city_fk;
    public $region_fk;
    public $country_fk;
    public $business_name;
    public $user_balance; // search
    public $user_notes; // search
    public $last_visit; // search
    public $first_visit; // search
    public $customer_type; // search
    public $user_consent; // search
    public $email_consent; //search
    public $sms_consent; //search
    public $push_consent; //search
    public $customer_tags; // search
    public $type_name; // search
    public $businessName;
    public $excludeBranches; // search
    public $businessBranchIds; // search
    public $excludeTiers; // search
    public $userTierIds; // search
    public $userTagIds; // search
    public $excludeTags; // search
    public $custom_field_1;
    public $custom_field_2;
    public $custom_field_3;
    public $custom_field_4;
    public $custom_field_5;
    public $route;
    public $country;
    public $province;
    public $city;
    public $country_short;
    public $lat;
    public $lng;
    public $referrals; 
    public $tier_progress;
    public $account_number;
    public $name;

    //We will keep custom errors
    public $customErrors = array();

    /**
     *   Adding a custom error in list
     */
    public function addCustomError($attribute, $error)
    {
        $this->customErrors[] = array($attribute, $error);
    }

    /**
     *   Before validation check if there are custom errors
     */
    protected function beforeValidate()
    {
        $r = parent::beforeValidate();
        foreach ($this->customErrors as $param) {
            $this->addError($param[0], $param[1]);
        }
        return $r;
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'user';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        if (Utils::feature('UBP')) {
            $emailRules = array(
                array('email', 'unique',
                    'criteria' => array(
                        'condition' => 'business_fk=:business_fk',
                        'params' => array(
                            ':business_fk' => $this->business_fk
                        )),
                    'message' => Yii::t('frontend', 'EMAIL_EXISTS')),
                array('email', 'unique',
                    'criteria' => array(
                        'condition' => 'business_fk=:business_fk',
                        'params' => array(
                            ':business_fk' => $this->business_fk
                        )),
                    'message' => Yii::t('frontend', 'EMAIL_EXISTS'), 'on' => 'join'),
                array('email', 'unique',
                    'criteria' => array(
                        'condition' => 'business_fk is null',
                    ),
                    'message' => Yii::t('frontend', 'EMAIL_EXISTS'), 'on' => 'employee'),
            );
        } else {
            $emailRules = array(
                array('email', 'unique', 'message' => Yii::t('frontend', 'EMAIL_EXISTS')),
                array('email', 'unique', 'message' => Yii::t('frontend', 'EMAIL_EXISTS'), 'on' => 'join'),
                array('email', 'unique', 'message' => Yii::t('frontend', 'EMAIL_EXISTS'), 'on' => 'employee'),
            );
        }

        $rules = array(
            array('username', 'unique', 'message' => Yii::t('frontend', 'USERNAME_EXISTS')),
            array('username', 'unique', 'message' => Yii::t('frontend', 'USERNAME_EXISTS'), 'on' => 'employee'),
            array('username', 'validateUsername', 'on' => 'employee'),
            array('first,last', 'required', 'message' => Yii::t('frontend', 'REQUIRED_FIELDS'), 'on' => 'employee'),
            array('utc_created', 'default', 'value' => new CDbExpression('NOW()'), 'setOnEmpty' => false, 'on' => 'insert'),
            //array('image', 'file', 'types' => 'jpg, gif, png', 'allowEmpty' => true),

            //admin-profile-update
            array('username, language', 'required', 'message' => Yii::t('frontend', 'CANNOT_BE_BLANK'), 'on' => 'admin-profile-update'),
            array('username', 'unique', 'message' => Yii::t('frontend', 'USERNAME_EXISTS'), 'on' => 'admin-profile-update'),
            array('username', 'validateUsername', 'on' => 'admin-profile-update'),
            //array('image', 'file', 'types' => 'jpg, gif, png', 'allowEmpty' => true, 'on' => 'update'),

            array('email', 'safe', 'on' => 'join-email-phone'),

            array('id, username, email, phone, password, enabled, country_phone_fk, address, language, gender, first, last,  birth_date, tw_connect, fb_connect,
            		pin_permission, pin_enabled, platform_solution_fk, display_facebook_image, ip, user_token, image, facebook_id, passwordreset, utc_last_login, login_type_fk,
            		signedupat_fk, utc_created, utc_updated, app_launched, accounts_merge, utc_link_expires, pin_migration, qrcode, timezone_mysql_fk, business_name, user_balance, phone_number_type_fk,
                    user_notes, customer_type, last_visit, first_visit, user_consent, email_consent, sms_consent, push_consent, user_uid, gcm_device_token, device_token, customer_tags, type_name, excludeTiers, userTierIds, businessName, businessBranchIds, excludeBranches, business_fk, userTagIds, excludeTags
                    custom_field_1, custom_field_2, custom_field_3, custom_field_4, custom_field_5, account_number, name', 'safe', 'on' => 'search'),

            array('id, username, email, phone, password, enabled, language, gender, first, last,  birth_date, tw_connect, fb_connect, device_token,
            		pin_permission, pin_enabled, country_phone_fk, platform_solution_fk, display_facebook_image, ip,user_token, image, facebook_id, passwordreset, utc_last_login, login_type_fk,
            		signedupat_fk, utc_created, utc_updated, app_launched, accounts_merge, utc_link_expires, pin_migration, qrcode, timezone_mysql_fk, verification_code, phone_number_type_fk, user_uid, gcm_device_token, device_token, customer_tags, type_name, excludeTier, userTierIds, businessBranchIds, excludeBranches, userTagIds, excludeTags, business_fk, address', 'safe'),
        );
        return array_merge($emailRules, $rules);
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
//            'businessTrokPermissions' => array(self::HAS_MANY, 'BusinessTrokPermissions', 'admin_fk'),
//            'donations' => array(self::HAS_MANY, 'Donations', 'user_fk'),
//            'friends' => array(self::HAS_MANY, 'Friend', 'user_id'),
//            'friends1' => array(self::HAS_MANY, 'Friend', 'friend_id'),
//            'troks' => array(self::HAS_MANY, 'Trok', 'user_troking_fk'),
//            'troks1' => array(self::HAS_MANY, 'Trok', 'user_receiving_trok_fk'),
            //'country' => array(self::BELONGS_TO, 'Countries', 'country_fk'),
            //'region' => array(self::BELONGS_TO, 'Regions', 'region_fk'),
            //'city' => array(self::BELONGS_TO, 'Cities', 'city_fk'),
            'businessBranches' => array(self::MANY_MANY, 'BusinessBranch', 'user_branch(user_fk, business_branch_fk)'),
            'userEntityPermissions' => array(self::HAS_MANY, 'UserEntityPermission', 'user_fk'),
            'userEntityPoints' => array(self::HAS_MANY, 'UserEntityPoints', 'user_fk'),
            'userFriendsCategories' => array(self::HAS_MANY, 'UserFriendsCategory', 'friend_fk'),
            'userOffers' => array(self::HAS_MANY, 'UserOffer', 'user_fk'),
            'signetdUpAt' => array(self::BELONGS_TO, 'BusinessBranch', 'signedupat_fk'),
            'business' => array(self::BELONGS_TO, 'Business', 'business_fk'),
            'followers' => array(self::HAS_ONE, 'Followers', 'user_fk'),
            'balance' => array(self::STAT, 'UserEntityPoints', 'user_fk', 'select'=> 'SUM(current_active_points)'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'id' => Yii::t('frontend', 'ID'),
            'username' => Yii::t('frontend', 'USERNAME'),
            'salt' => 'Salt',
            'password' => Yii::t('frontend', 'PASSWORD'),
            'password_repeat' => Yii::t('frontend', 'PASSWORD_REPEAT'),
            'email' => Yii::t('frontend', 'EMAIL'),
            'first' => Yii::t('frontend', 'FIRST_NAME'),
            'last' => Yii::t('frontend', 'LAST_NAME'),
            'phone' => Yii::t('frontend', 'PHONE'),
            'enabled' => Yii::t('frontend', 'ENABLED'),
            'utc_created' => Yii::t('frontend', 'CREATED'),
            'utc_updated' => Yii::t('frontend', 'MODIFIED'),
            'gender' => Yii::t('frontend', 'GENDER'),
            'birth_date' => Yii::t('frontend', 'BIRTHDATE'),
            'twitter_id' => Yii::t('frontend', 'TWITTER'),
            'facebook_id' => Yii::t('frontend', 'FACEBOOK'),
            'iagree' => Yii::t('frontend', 'AGREE'),
            'image' => Yii::t('frontend', 'IMAGE'),
            'language' => Yii::t('frontend', 'LANGUAGE'),
            'pin_permission' => Yii::t('frontend', 'PIN'),
            'display_facebook_image' => Yii::t('frontend', 'FACEBOOK_PROFILE_PIC'),
            //'country_fk'=> Yii::t('frontend', 'COUNTRY'),
            //'region_fk'=> Yii::t('frontend', 'PROVINCE'),
            //'city_fk'=> Yii::t('frontend', 'CITY'),
            'address' => Yii::t('frontend', 'ADDRESS'),
            'balance' => Yii::t('frontend', 'BALANCE'),
            'phone_number_type_fk' => 'Phone number type',
            'business_fk' => 'Business ID',
        );
    }

    private $originalAttributes = [];
    protected function afterFind()
    {
        // Store the original attribute values
        $this->originalAttributes = $this->getAttributes();
        parent::afterFind();
    }

    private function getDirtyFields()
    {
        $dirtyFields = [];

        foreach ($this->originalAttributes as $attribute => $oldValue) {
            if ($this->$attribute != $oldValue) {
                $dirtyFields[] = $attribute;
            }
        }

        return $dirtyFields;
    }

    public function afterSave()
    {
        parent::afterSave();

        $attributes = $this->attributes;
        unset($attributes['password']);
        unset($attributes['pin_permission']);
        unset($attributes['passwordreset']);

        Utils::logMessage(CJSON::encode($attributes) . " DEBUG_BACKTRACE: \n" . Utils::getTraces(), __METHOD__);

        if (Utils::feature('WORKFLOW')) {
            $fieldsToCheck = ['first', 'last', 'email', 'phone'];
            $dirtyFields = $this->getDirtyFields();

            if (count($this->originalAttributes) == 0 || array_intersect($dirtyFields, $fieldsToCheck)) {

                $queue = new \application\components\queue\ExternalQueue(CUSTOM_QUEUE_DRIVER, CUSTOM_MEMBERS_API_DEFAULT_QUEUE);
                $queue->enqueue("KangarooRewards\\Workflows\\Jobs\\WorkflowEventHandlerJob", [
                    'main_trigger_id' => 3, //USER_PROFILE_COMPLETE
                    'business_fk' => (int)$this->business_fk,
                    'user_fk' => (int)$this->id,
                    'entity_name' => 'User',
                    'entity_id' => (int)$this->id
                ], [
                    'laravelCompatible' => true,
                    'delay' => 10
                ]);
            }
        }
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria = new CDbCriteria;
        $criteria->join ="LEFT JOIN user_business_profile ubp ON ubp.user_fk = t.id ";
        $criteria->select=array('t.id', 't.username', 't.first', 't.last', 't.phone', 't.email',
            't.phone_number_type_fk',
            't.utc_created',
            't.enabled',
            't.ip',
            't.platform_solution_fk',
            't.signedupat_fk',
            't.business_fk',
            'ubp.account_number as account_number'
        );

        $criteria->compare('id', $this->id);
        $criteria->compare('username', $this->username, true);
        $criteria->compare('t.email', $this->email, true);
        $criteria->compare('first', $this->first, true);
        $criteria->compare('last', $this->last, true);
        $criteria->compare('gcm_device_token', $this->last, true);
        $criteria->compare('device_token', $this->last, true);

        $criteria->compare('t.phone', $this->phone, true);
        $criteria->compare('t.enabled', $this->enabled);
        $criteria->compare('t.utc_created', $this->utc_created, true);
        $criteria->compare('t.utc_updated', $this->utc_updated, true);
        $criteria->compare('gender', $this->gender, true);
        $criteria->compare('birth_date', $this->birth_date, true);
        $criteria->compare('utc_last_login', $this->utc_last_login, true);
        $criteria->compare('ip', $this->ip, true);
        $criteria->compare('image', $this->image, true);
        $criteria->compare('language', $this->language);
        $criteria->compare('signedupat_fk', $this->signedupat_fk);
        $criteria->compare('pin_permission', $this->pin_permission, true);
        $criteria->compare('pin_enabled', $this->pin_enabled);
        $criteria->compare('hidden', $this->hidden);
        $criteria->compare('app_launched', $this->app_launched);
        $criteria->compare('accounts_merge', $this->accounts_merge);
        $criteria->compare('platform_solution_fk', $this->platform_solution_fk, true);
        $criteria->compare('display_facebook_image', $this->display_facebook_image);
        $criteria->compare('login_type_fk', $this->login_type_fk, true);
        $criteria->compare('country_phone_fk', $this->country_phone_fk, true);
        $criteria->compare('utc_link_expires', $this->utc_link_expires, true);
        $criteria->compare('phone_number_type_fk', $this->phone_number_type_fk);
        $criteria->compare('t.business_fk', $this->business_fk);
        $criteria->compare('ubp.account_number', trim((string)$this->account_number??''), true);

        $criteria->with = array('signetdUpAt');
        $criteria->compare('signetdUpAt.name', $this->business_name, true);
        $criteria->together = true;

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
            'pagination' => array(
                'pageSize' => 25,
            ),
            'sort' => array(
                'defaultOrder' => 'id DESC',
            ),
        ));
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * System Admin Portal
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function searchSystem()
    {
        $criteria = new CDbCriteria;
        $criteria->limit = 10;
        $criteria->join ="LEFT JOIN user_business_profile ubp ON ubp.user_fk = t.id ";
        $criteria->select=array('t.id', 't.username', 't.first', 't.last', 't.phone', 't.email',
            't.phone_number_type_fk',
            't.utc_created',
            't.enabled',
            't.ip',
            't.platform_solution_fk',
            't.signedupat_fk',
            't.business_fk',
            'ubp.account_number as account_number'
        );

        $criteria->compare('id', $this->id);
        $criteria->compare('username', $this->username, true);
        $criteria->compare('t.email', $this->email, true);
        $criteria->compare('first', $this->first, true);
        $criteria->compare('last', $this->last, true);
        $criteria->compare('gcm_device_token', $this->last, true);
        $criteria->compare('device_token', $this->last, true);
        $criteria->compare('t.phone', $this->phone, true);
        $criteria->compare('t.enabled', $this->enabled);
        $criteria->compare('t.utc_created', $this->utc_created, true);
        $criteria->compare('t.utc_updated', $this->utc_updated, true);
        $criteria->compare('gender', $this->gender, true);
        $criteria->compare('birth_date', $this->birth_date, true);
        $criteria->compare('utc_last_login', $this->utc_last_login, true);
        $criteria->compare('ip', $this->ip, true);
        $criteria->compare('image', $this->image, true);
        $criteria->compare('language', $this->language);
        $criteria->compare('signedupat_fk', $this->signedupat_fk);
        $criteria->compare('pin_permission', $this->pin_permission, true);
        $criteria->compare('pin_enabled', $this->pin_enabled);
        $criteria->compare('hidden', $this->hidden);
        $criteria->compare('app_launched', $this->app_launched);
        $criteria->compare('accounts_merge', $this->accounts_merge);
        $criteria->compare('platform_solution_fk', $this->platform_solution_fk, true);
        $criteria->compare('display_facebook_image', $this->display_facebook_image);
        $criteria->compare('login_type_fk', $this->login_type_fk, true);
        $criteria->compare('country_phone_fk', $this->country_phone_fk, true);
        $criteria->compare('utc_link_expires', $this->utc_link_expires, true);
        $criteria->compare('phone_number_type_fk', $this->phone_number_type_fk);
        $criteria->compare('t.business_fk', $this->business_fk);
        $criteria->compare('ubp.account_number', trim((string)$this->account_number??''), true);

        $criteria->with = array('signetdUpAt');
        $criteria->compare('signetdUpAt.name', $this->business_name, true);
        $criteria->together = true;

        return new CActiveDataProvider(get_class($this), array(
            'criteria' => $criteria,
            'totalItemCount' => 0,
            'pagination' => false,
            'sort' => array(
                'defaultOrder' => 'id DESC',
            ),
        ));
    }

    public function clearLoginAttemptsCache(): void
    {
        $hashedUsername = hash('sha512', $this->username);
        Yii::app()->cache->delete("login_attempts:{$hashedUsername}");

        $hashedUsername = hash('sha512', $this->email);
        Yii::app()->cache->delete("login_attempts:{$hashedUsername}");
    }

    public function isAccountLocked(): bool
    {
        $hashedUsername = hash('sha512', $this->username);
        $usernameLock = Yii::app()->cache->get("login_attempts:{$hashedUsername}");

        $hashedUsername = hash('sha512', $this->email);
        $emailLock = Yii::app()->cache->get("login_attempts:{$hashedUsername}");

        if ($usernameLock || $emailLock) {
            return true;
        }

        return false;
    }

    /**
     * Register the user and push to Lightspeed
     * Return the user ID so the process will continue with the
     * @param array $post
     * @throws CException
     */
    public static function registerFromUpdateProfile(array $post)
    {
        $branch = BusinessBranch::model()->findByPk($post['business_branch_pk']);

        $countryId = null;
        if(isset($post['countryCodeIso']) && $post['countryCodeIso']) {
            $country = Countries::model()->findByAttributes(['code' => (string) $post['countryCodeIso']]);
            $countryId = $country->country_pk ?? null;
        }

        $userData = new \application\components\UserValueObject([
            'email' => $post['email'] ?? null,
            'phone' => $post['phone'] ?? null,
            'platform_solution_fk' => $post['platformSolutionFk'],
            'signedupat_fk' => $branch->business_branch_pk,
            'business_fk' => $branch->business_fk,
            'country_phone_fk' => $countryId,
        ]);

        $registry = new \application\components\UserRegistry($userData, $branch);

        $transaction = Yii::app()->db->beginTransaction();
        try {
            $registry->findOrCreateUser();
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollback();
            throw $e;
        }

        return $registry->getUser() ? $registry->getUser()->id : null;
    }

    /**
     * Create the user in Lightspeed after registration
     * @param array $post
     * @param mixed $userId
     * @throws CDbException
     * @throws CException
     */
    public static function pushToLightspeed(array $post, $userId)
    {
        $queueItem = PosQueueUser::model()->findByAttributes(['user_fk' => $userId]);

        if ($queueItem) {
            return; // Already in the Queue
        }

        if (isset($post['casl_flag']) && (int) $post['casl_flag'] === 1 ) {
            $branch = BusinessBranch::model()->findByPk($post['business_branch_pk']);

            UserBusinessNotifications::createUpdateUserBusinessNotification([
                'business_fk' => $branch->business_fk,
                'user_fk' => $userId,
                'allow_email' => 1,
                'allow_sms' => 1,
                'timezone_mysql_fk' => $branch->branch_timezone_fk,
            ]);
        }

        Yii::import('application.modules.cim.components.LightspeedCustomerManager');

        $lsCustomerManager = new LightspeedCustomerManager($post, 'createCustomer');
        $lsCustomerManager->init();

        $failed = false;

        try {
            $lsCustomerManager->createCustomerAfterSignup();

        } catch (\GuzzleHttp\Exception\GuzzleException $e) {
            $failed = true;
            Yii::log($e . ' ' . json_encode([
                'userId' => $userId,
                'POST' => $post,
            ]), CLogger::LEVEL_WARNING, __METHOD__);
        } catch (\Exception $e) {
            $failed = true;
            Yii::log($e . ' ' . json_encode([
                'userId' => $userId,
                'POST' => $post,
            ]), CLogger::LEVEL_WARNING, __METHOD__);
        }

        if ($failed) {
            // Save in Queue if it failed
            $queueItem = new PosQueueUser();
            $queueItem->user_fk = $userId;
            $queueItem->business_fk = (int) $post['business_fk'];
            $queueItem->form_data = json_encode($post);
            Utils::saveModel($queueItem);
        }
    }

    public static function normalizeFromCrmProfileForLightspeed($post)
    {
        $post['business_fk'] = (int) $post['businessId'];

        if (isset($post['fist_name'])) {
            $post['first'] = $post['fist_name'];
            unset($post['fist_name']);
        }

        if (isset($post['last_name'])) {
            $post['last'] = $post['last_name'];
            unset($post['last_name']);
        }

        if (isset($post['birthday'])) {
            $post['birthdate'] = $post['birthday'];
            unset($post['birthday']);
        }

        if (isset($post['countryCodeIso'])) {
            $post['country_code'] = $post['countryCodeIso'];
            unset($post['countryCodeIso']);
        }

        if(!empty($post['cityId']) && !empty($post['address'])) {
            $cityModel = Cities::model()->findByPk($post['cityId']);
            $regionModel = Regions::model()->findByPk($cityModel->region_fk);
            $countryModel = Countries::model()->findByPk($regionModel->country_fk);

            $post['address'] = trim((string)$post['address']);
            $post['city'] = $cityModel->name;
            $post['region'] = $regionModel->name;
            $post['country'] = $countryModel->name;
            // $post['postal_code'] already exists as is
        }

        unset($post['businessId'], $post['clerk_token'], $post['customer_tags_ID']);
        unset($post['customer_other_tags_name']);

        return $post;
    }

    public static function normalizeFromWidgetForLightspeed($post)
    {
//        dd($post);

        $post['business_fk'] = (int) $post['businessId'];

        if (isset($post['firstName'])) {
            $post['first'] = $post['firstName'];
            unset($post['firstName']);
        }

        if (isset($post['lastName'])) {
            $post['last'] = $post['lastName'];
            unset($post['lastName']);
        }

        if (isset($post['birthDate'])) {
            $post['birthdate'] = $post['birthDate'];
            unset($post['birthDate']);
        }

        // map custom fields 10-14 to 1-5
        $j=0;
        for ($i=10; $i<=14; $i++) {
            $j++;
            if (isset($post[$i])) {
                $post['custom_field_' . $j] = $post[$i];
                unset($post[$i]);
            }
        }

        if (isset($post['allow_email'])) {
            $post['consentEmail'] = $post['allow_email'] ? 'on': 'off';
        }

        if (isset($post['allow_sms'])) {
            $post['consentPhone'] = $post['allow_sms'] ? 'on': 'off';
        }

        if(isset($post['city'], $post['route'])) {
            $streetNb = $post['street_number'] ?? '';
            $route = $post['route'] ?? '';
            $post['address'] = trim((string)$streetNb . ' '. $route);
            $post['city'] = trim((string)$post['city'] ?? '');
            $post['region'] = $post['province'] ?? '';
            $post['country'] = trim((string)$post['country'] ?? '');
            $post['postal_code'] = $post['postalcode'] ?? '';
        }

//        [street_number] =>
//        [city] =>
//        [province] =>
//        [route] =>
//        [postal_code] =>
//        [country] =>
//        [country_short] =>
//        [postalcode] =>
//        [lang] => en
//        allow_email: 1
//        allow_sms: 1

//        "cityId": "",
//        "address": "",
//        "lang_id": "en",
//        "genderId": "",
//        "tmzStore": "America/Toronto",
//        "birthdate": "1983-05-20",
//        "device_id": "167",
//        "appVersion": "4.8.22",
//        "business_fk": 15,
//        "postal_code": "",
//        "countryCodeIso": "",
//        "custom_field_1": "",
//        "custom_field_2": "",
//        "custom_field_3": "",
//        "custom_field_4": "",
//        "custom_field_5": "",
//        "postal_code_fk": "",
//        "profile_status": "1",
//        "promotions_mail": "",
//        "business_branch_pk": "27",
//        "platformSolutionFk": "6"

        unset($post['businessId'], $post['token'], $post['url']);
        unset($post['ref'], $post['lat'], $post['lng'], $post['b']);

        return $post;
    }

    public function changeEmailRequest($business, $post)
    {
        $timeZoneId = $business[0]['branch_timezone_fk'];
        $transLang = $post['lang_id'];
        $countryId = null;
        $email = $post['email'] ?? null;
        $crmProfile = $post['crm_profile'] ?? null;
        $platformSolution = $post['platformSolutionFk'];
        $platformName = PlatformSolution::model()->findByPk($post['platformSolutionFk']);
        $userId = $this->id;
        $notificationSent = false;
        $rules = BusinessRules::model()->findByAttributes(['business_fk' => $business[0]['business_pk']]);

        $userModel = $this;
        //changing email
        if ($userModel->email === $email) {
            if($crmProfile) {
                return false;
            }
            Utils::sendResponse(200, $this->_getJsonResult('NOT_OK', Yii::t('frontend', 'FRONTEND_CHANGEEMAIL_POPUP_VALIDATION', array(), null, $transLang), 200, array()));
            Yii::app()->end();
        }

        $userExists = User::model()->findByAttributes([
            'email' => $email,
            'business_fk' => $userModel->business_fk,
        ]);

        if ($userExists) {
            Utils::sendResponse(200, $this->_getJsonResult('NOT_OK', Yii::t('frontend', 'FRONTEND_CHANGEEMAIL_EMAIL_BELONGSTO_MERCHANT', array(), null, $transLang), 200, array()));
            Yii::app()->end();
        }

        $formModel = new ChangeEmailPhone('tablet-email-scenario');
        $formModel->email = $email;
        $formModel->emailPhone = 'email';

        if (!$formModel->validate()) {
            $errors = $formModel->getErrors();
            $theError = '';
            foreach ($errors as $error) {
                $theError = $error[0];
                break;
            }
            Yii::log('Error in the Email - $formModel->validate(). It returned FALSE the error is = ' . $theError, CLogger::LEVEL_WARNING, $platformName->name . '_' . __FUNCTION__);
            Utils::sendResponse(200, $this->_getJsonResult('NOT_OK', $theError, 200, array()));
            Yii::app()->end();
        }

        $activationToken = ActivationToken::createActivationTokenForChangeEmailPhone($userModel->id, $email, 'email');

        $businessProfile = BusinessProfile::model()->findByAttributes([
            'business_fk' => $business[0]['business_pk'],
        ]);

        if ($businessProfile && $businessProfile->web_app_uri) {
            # http://webapp.local/site/verify?token=44&email=<EMAIL>
            $emailLink = rtrim((string)$businessProfile->web_app_uri, '/') . '/site/verify?' . http_build_query([
                'token' => $activationToken->id, 'email' => $email, 'action' => 'change',
            ]);
        } else {
            $emailLink = rtrim((string)Yii::app()->params['membersUrl'], '/') . '/site/verify?' . http_build_query([
                'token' => $activationToken->id, 'email' => $email, 'action' => 'change',
            ]);
        }

        Yii::log(json_encode([
            'user_id' => $userModel->id,
            'business_pk' => $business[0]['business_pk'],
            'email' => $userModel->email,
            'new_email' => $email,
            'activation_token' => $activationToken->id,
        ]), CLogger::LEVEL_INFO, __METHOD__);

        $businessModel = Business::model()->findByPk($business[0]['business_pk']);
        $mainBranch = BusinessBranch::getDefaultBranch($business[0]['business_pk']);
        $emailData = [
            'username' => $userModel->first,
            'businessLogo' => $mainBranch['logo_image_medium'],
            'link' => $emailLink,
            'businessName' => stripslashes((string)$mainBranch['name']??''),
            'whiteLabel' => $businessModel->white_label_flag,
            'businessEmail' => $mainBranch['email']
        ];

        if(isset($userModel->language)) {
            $strLang = Language::loadLanguageById($userModel->language)['abreviation'];
        } else {
            $strLang = 'en';
        }
        $emailSubject = Yii::t('frontend', 'CHANGE_EMAIL_VERIFICATION_SUBJECT', array(), null, $strLang);

        if ($rules->notify_change_credential) {
            $emailSent = Utils::sendEmailWithTemplate('userChangeEmail', 'bilingual', $emailSubject, $formModel->email, $emailData, 'application.views.mail.'.$strLang,
                null, null, $business[0]['business_pk'], $userId, null, true);
        } else {
            $emailSent = ['status' => 'OK'];
        }

        if ($emailSent['status'] !== 'OK') {
            Yii::log('Error in the Utils::sendEmailWithTemplate. It returned NOT_OK' . ' ' . $userModel->username . ' ' . $formModel->email . ' Result: ' . json_encode($emailSent), CLogger::LEVEL_ERROR, $platformName->name . '_' . __FUNCTION__);
            Utils::sendResponse(200, $this->_getJsonResult('NOT_OK', Yii::t('frontend', 'UNKNOWN_ERROR', array(), null, $transLang), 200, array()));
            Yii::app()->end();
        }

        $transaction = Yii::app()->db->beginTransaction();
        try {
            if ($userModel->email == null || $userModel->email == '') {
                //add email

                UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null,
                    $status_manager_fk = 40, '', $formModel->email, $platformSolution, $timeZoneId);
            } //ADD_EMAIL_REQUEST
            else { //change
                UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null,
                    $status_manager_fk = 48, $userModel->email, $formModel->email, $platformSolution,
                    $timeZoneId); //CHANGE_EMAIL_REQUEST
            }

            $userModel->email = $email;
            $userModel->save();

            $notificationSent = true;

            $transaction->commit();
        } catch(\Exception $e) {
            $transaction->rollback();
            Yii::log($e, CLogger::LEVEL_ERROR, $platformName->name . '_' . __FUNCTION__);
            Utils::sendResponse(200, $this->_getJsonResult('NOT_OK', Yii::t('backend', 'UNKNOWN_ERROR'), 200, array()));
            Yii::app()->end();
        }

        return $notificationSent;
    }

    public function changePhoneRequest($business, $post)
    {
        $timeZoneId = $business[0]['branch_timezone_fk'];

        $transLang = $post['lang_id'];
        $countryId = null;
        $phone = $post['phone'] ?? null;
        $crmProfile = $post['crm_profile'] ?? null;
        $platformSolution = $post['platformSolutionFk'];
        $platformName = PlatformSolution::model()->findByPk($post['platformSolutionFk']);
        $userId = $this->id;
        $notificationSent = false;
        $rules = BusinessRules::model()->findByAttributes(['business_fk' => $business[0]['business_pk']]);

        $userModel = $this;

        $countryCodeIso = isset($post['countryCodeIso']) ? $post['countryCodeIso'] : null;
        if ($countryCodeIso != null && $countryCodeIso != '') {
            $countryId = Countries::model()->findByAttributes(['code' => $countryCodeIso])->country_pk;
            $phone = Utils::getFormattedPhoneNumber($phone, $countryId);
            $phone = preg_replace('/[^0-9]/', '', $phone); // Replaces all spaces with hyphens.
        }

        if ($userModel->phone === $phone) {
            if($crmProfile) {
                return false;
            }
            Utils::sendResponse(200, $this->_getJsonResult('NOT_OK', Yii::t('frontend', 'FRONTEND_CHANGEPHONE_POPUP_VALIDATION', array(), null, $transLang), 200, array()));
            Yii::app()->end();
        }

        $userExists = User::model()->findByAttributes([
            'phone' => $phone,
            'business_fk' => $userModel->business_fk,
        ]);

        if ($userExists) {
            Utils::sendResponse(200, $this->_getJsonResult('NOT_OK', Yii::t('frontend', 'FRONTEND_CHANGEEMAIL_PHONE_BELONGSTO_MERCHANT', array(), null, $transLang), 200, array()));
            Yii::app()->end();
        }

        $formModel = new ChangeEmailPhone('table-phone-scenario');
        $formModel->phone = $phone;
        $formModel->emailPhone = 'phone';

        if (!$formModel->validate()) {
            $errors = $formModel->getErrors();
            $theError = '';
            foreach ($errors as $error) {
                $theError = $error[0];
                break;
            }
            Yii::log('Error in the Phone - $formModel->validate(). It returned FALSE, The error is = ' . $theError, CLogger::LEVEL_WARNING, $platformName->name . '_' . __FUNCTION__);
            Utils::sendResponse(200, $this->_getJsonResult('NOT_OK', $theError, 200, array()));
            Yii::app()->end();
        }

        $activationToken = ActivationToken::createActivationTokenForChangeEmailPhone(
            $userModel->id, $phone, 'phone', $countryCodeIso
        );
        $businessProfile = BusinessProfile::model()->findByAttributes([
            'business_fk' => $business[0]['business_pk'],
        ]);

        if ($businessProfile && $businessProfile->web_app_uri) {
            # http://webapp.local/site/verify?token=44&email=<EMAIL>
            $phoneLink = rtrim((string)$businessProfile->web_app_uri, '/') . '/site/verify?'. http_build_query([
                    'token' => $activationToken->id, 'phone' => $phone, 'action' => 'change',
                ]);
        } else {
            $phoneLink = rtrim((string)Yii::app()->params['membersUrl'], '/') . '/site/verify?'. http_build_query([
                    'token' => $activationToken->id, 'phone' => $phone, 'action' => 'change',
                ]);
        }

        Yii::log(json_encode([
            'user_id' => $userModel->id,
            'business_pk' => $business[0]['business_pk'],
            'phone' => $userModel->phone,
            'new_phone' => $phone,
            'activation_token' => $activationToken->id,
        ]), CLogger::LEVEL_INFO, __METHOD__);

        //$phoneLink = User::shortUrl($phoneLink);
        $phoneLink = Utils::shortUrlKg($phoneLink, $platformName->name);
        $phoneLink = $phoneLink['shortUrl'];

        if(isset($userModel->language)) {
            $strLang = Language::loadLanguageById($userModel->language)['abreviation'];
        }else {
            $strLang = 'en';
        }
        $businessModel = Business::model()->findByPk($business[0]['business_pk']);
        if(isset($businessModel) && $businessModel->white_label_flag) {
            //$mainBranch = BusinessBranch::getDefaultBranch($business[0]['business_pk']);
            $businessName = stripslashes((string)$businessModel->name??''); //stripslashes((string)$mainBranch['name']);
            $smsBody = Yii::t('frontend', 'FRONTEND_CHANGEPHONE_VERIF_SMS_WHITE_LABEL',
                array('{link}' => $phoneLink, '{business_name}' => $businessName), null, $strLang);
        }else {
            $smsBody = Yii::t('frontend', 'FRONTEND_CHANGEPHONE_VERIF_SMS', array('{link}' => $phoneLink), null, $strLang);
        }

        if ($rules->notify_change_credential) {
            $sentSMS = Utils::validateAndSendSMS($smsBody, $phone, $countryId, $platformName->name, [], $business[0]['business_pk'],
                $userId, null, true);
        } else {
            $sentSMS = ['status' => 'OK'];
        }

        if ($sentSMS['status'] !== 'OK') {
            Yii::log('Error. It returned NOT_OK ' . $userModel->username . ' ' . $formModel->phone . ' Result: ' . json_encode($sentSMS), CLogger::LEVEL_ERROR, $platformName->name . '_' . __FUNCTION__);

            //pc - Handling the Twilio error code 21610: Message cannot be sent to the 'To' number because the customer has replied with STOP
            if (isset($sentSMS['errorCode']) && $sentSMS['errorCode'] == 21610) {
                //$result = array('status'=>'NOT_OK', 'error'=>(array) $e,  'message'=>Yii::t('commonlabels','COMMON_VALIDATESEND_SMS_ERROR_CODE21610', array('{twilio_phone}'=>$from)));
                Utils::sendResponse(200, $this->_getJsonResult('NOT_OK', Yii::t('commonlabels', 'COMMON_VALIDATESEND_SMS_ERROR_CODE21610', array('{twilio_phone}' => $sentSMS['phoneFrom'])), 200, array()));
                Yii::app()->end();
            } elseif (isset($sentSMS['message'])) {
                Utils::sendResponse(200, $this->_getJsonResult('NOT_OK', $sentSMS['message'], 200, array()));
                Yii::app()->end();
            } else {
                Utils::sendResponse(200, $this->_getJsonResult('NOT_OK', Yii::t('frontend', 'UNKNOWN_ERROR', array(), null, $transLang), 200, array()));
                Yii::app()->end();
            }
        }
        $transaction = Yii::app()->db->beginTransaction();
        try {
            if ($userModel->phone == null || $userModel->phone == '') {
                //add phone

                UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null,
                    $status_manager_fk = 39, '', $formModel->phone, $platformSolution, $timeZoneId,
                    $countryId);
            } //ADD_EMAIL_REQUEST
            else { //change
                UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null,
                    $status_manager_fk = 47, $userModel->phone, $formModel->phone, $platformSolution,
                    $timeZoneId, $countryId); //CHANGE_EMAIL_REQUEST
            }

            $userModel->phone = $phone;
            $userModel->country_phone_fk = $countryId;
            $userModel->save();

            $notificationSent = true;

            //Yii::app()->session->add('newPhone', $newPhone);
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollback();
            Yii::log($e, CLogger::LEVEL_ERROR, $platformName->name . '_' . __FUNCTION__);
            Utils::sendResponse(200, $this->_getJsonResult('NOT_OK', Yii::t('backend', 'UNKNOWN_ERROR'), 200, array()));
            Yii::app()->end();
        }
        return $notificationSent;
    }

    private function _getJsonResult($status, $message, $code, $result) {
        $theResponse = array('status' => $status, 'message' => $message, 'code' => $code, 'results' => $result);
        return CJSON::encode($theResponse);
    }

    /**
     * @param $businessPk
     * @param $credentialValue
     * @param $businessBranchPk
     * @return User|null
     * @throws Exception
     */
    public static function findAndCloneForConglomerateMember($businessPk, $credentialValue, $businessBranchPk)
    {
        $business = Business::model()->findByPk($businessPk);
        $branch = BusinessBranch::model()->findByPk($businessBranchPk);
        // current business is conglomerate member
        if(!$business->conglomerate_id) {
            return null;
        }

        // Standard conglomeration doesn't have a user_entity_permission record for
        // businessgroupmanager role
        $userPermissionCong = Yii::app()->db->createCommand()
            ->select(['up.userid',])
            ->from('user_permission up')
            ->join('user_entity_permission uep', 'up.userid=uep.user_fk')
            ->where('up.itemname = "businessgroupmanager"')
            ->andWhere('uep.entity_fk = :conglomerateId', [
                ':conglomerateId' => $business->conglomerate_id
            ])
            ->queryRow();

        // For Standard Conglomerate (Agglomeration) like Detail Garage: do not clone for member
        // Only for Conglomeration like CABJM
        if(!$userPermissionCong) {
            return null;
        }

        $user = null;

        //find the conglomerate admin
        $congBusiness = Business::model()->findByPk($business->conglomerate_id);
        // Find the user in conglomerate admin
        if(strpos((string)$credentialValue??'', '@') !== false){
            $userVirtual = User::model()->findByAttributes([
                'business_fk' => $congBusiness->business_pk,
                'email' => $credentialValue,
            ]);
        } else {
            $userVirtual = User::model()->findByAttributes([
                'business_fk' => $congBusiness->business_pk,
                'phone' => $credentialValue,
            ]);
        }

        Yii::log(json_encode([
            'message' => 'Before cloning user',
            'business_pk' => $business->business_pk,
            'business_name' => $business->name,
            'conglomerate_id' => $congBusiness->business_pk,
            'conglomerate_name' => $congBusiness->name,
            'credentialValue' => $credentialValue,
            'original_user' => $userVirtual ? [
                'id' => $userVirtual->id,
                'email' => $userVirtual->email,
                'phone' => $userVirtual->phone,
                'business_fk' => $userVirtual->business_fk,
            ] : null,
        ]), CLogger::LEVEL_INFO, __METHOD__);

        if($userVirtual) {
            $user = User::cloneForBusiness($userVirtual, $business, $branch);
        }

        return $user;
    }

    /**
     * @param $userVirtual
     * @param $business
     * @param $branch
     * @throws Exception
     * @return User|null
     */
    public static function cloneForBusiness($userVirtual, $business, $branch)
    {
        $newUser = null;

        if(!$userVirtual->phone && !$userVirtual->email) {
            return null;
        }

        try {
            $userInfo = $userVirtual->attributes;
            unset($userInfo['id'], $userInfo['signedupat_fk'], $userInfo['user_uid'], $userInfo['passwordreset']);
            $userInfo['business_fk'] = $business->business_pk;
            $userInfo['signedupat_fk'] = $branch->business_branch_pk;
            $userInfo['utc_created'] = date('Y-m-d H:i:s');
            $userInfo['username'] = Utils::generateUniqueUsername();

            $newUser = User::insertUser($userInfo);

            $userId = $newUser->getPrimaryKey();

            Followers::createFollowerRecordV330($userId, $business->business_pk, 1, $newUser->timezone_mysql_fk);

            UserEntityPermission::assignEntityPermissionV330($userId, $permission = 'user', $userId, 4, $newUser->timezone_mysql_fk);

            UserStatusManager::createUserStatusManagerRecordV330($userId, null, $status_manager_fk = 35, '', '', $newUser->platform_solution_fk, $newUser->timezone_mysql_fk);

            if ($newUser->phone && $newUser->email) {
                UserStatusManager::createUserStatusManagerRecordV330($newUser->id, null, $status_manager_fk = 30, '', $newUser->phone, $newUser->platform_solution_fk, $newUser->timezone_mysql_fk, $newUser->country_phone_fk);
                UserStatusManager::createUserStatusManagerRecordV330($newUser->id, null, $status_manager_fk = 32, '', $newUser->email, $newUser->platform_solution_fk, $newUser->timezone_mysql_fk);
            } elseif($newUser->phone) {
                UserStatusManager::createUserStatusManagerRecordV330($newUser->id, null, $status_manager_fk = 30, '', $newUser->phone, $newUser->platform_solution_fk, $newUser->timezone_mysql_fk, $newUser->country_phone_fk);
            } elseif($newUser->email) {
                UserStatusManager::createUserStatusManagerRecordV330($newUser->id, null, $status_manager_fk = 32, '', $newUser->email, $newUser->platform_solution_fk, $newUser->timezone_mysql_fk, $newUser->country_phone_fk);
            }
            Yii::log(json_encode([
                'message' => 'User cloned for conglomerate member',
                'original_user' => [
                    'id' => $userVirtual->id,
                    'email' => $userVirtual->email,
                    'phone' => $userVirtual->phone,
                    'business_fk' => $userVirtual->business_fk,
                ],
                'new_user' => [
                    'id' => $newUser->id,
                    'email' => $newUser->email,
                    'phone' => $newUser->phone,
                    'business_fk' => $newUser->business_fk,
                ],
                'business_pk' => $business->business_pk,
            ]), CLogger::LEVEL_INFO, __METHOD__);
        } catch (Exception $e) {
            Yii::log($e . ' ' . json_encode([
                'original_user' => [
                    'id' => $userVirtual->id,
                    'email' => $userVirtual->email,
                    'phone' => $userVirtual->phone,
                    'business_fk' => $userVirtual->business_fk,
                ],
                'new_user' => $newUser ? [
                    'id' => $newUser->id,
                    'email' => $newUser->email,
                    'phone' => $newUser->phone,
                    'business_fk' => $newUser->business_fk,
                ] : null,
                'business_pk' => $business->business_pk,
            ]), CLogger::LEVEL_ERROR, __METHOD__);
        }
        return $newUser;
    }

    public static function findByCredentials($attr = [])
    {
        $user = null;
        if (isset($attr['business_fk'])) {
            # code...
        } else {
            if (isset($attr['email'], $attr['phone'])) {
                $user = User::model()->findByAttributes([
                    'email' => $attr['email'],
                    'phone' => $attr['phone'],
                    'enabled' => 1,
                ]);
            }

            // If not found by email and phone - find by email only
            if (is_null($user) && !empty($attr['email'])) {
                $user = User::model()->findByAttributes([
                    'email' => $attr['email'],
                    'enabled' => 1,
                ]);
            }

            // If not found by email and phone - find by phone only
            if (is_null($user) && !empty($attr['phone'])) {
                $user = User::model()->findByAttributes([
                    'phone' => $attr['phone'],
                    'enabled' => 1,
                ]);
            }
        }

        return $user;
    }

    /**
     * Deletes user account
     *
     * @throws Exception
     */
    public function deleteAccount()
    {
        if (!Utils::feature('UBP')){
            return;
        }

        Yii::log(CJSON::encode($this->attributes), CLogger::LEVEL_INFO, __METHOD__);

        $this->enabled = 0;
//        $this->email = null;
//        $this->phone = null;
//        $this->qrcode = null;
        $this->passwordreset = null;
        $this->ip = null;
        $this->pin_enabled = 0;
        $this->pin_permission = null;
        $this->device_token = null;
        $this->gcm_device_token = null;
//        $this->signedupat_fk = null; //?
//        $this->business_fk = null; //?
//        $this->birth_date = null;
//        $this->gender = null;
//        $this->first = 'Delete';
//        $this->last = 'Profile';
        Utils::saveModel($this);
    }

    /**
     * Give x points to customers
     *
     * Used in : Website()
     *
     * @version V4.12.0
     * <AUTHOR> David
     * @access public
     * @param Business $business
     * @param array $data = Array
     * @return void
     */
    public static function givePointsCustomers($params)
    {
        $businessId = $params['businessId'];
        $business = Business::model()->findByPk($businessId);
        $entityTypeFk = $business->entity_type_fk;
        $givePoints = $params['points'];
        $comments = $params['comments'];
        $platformName = $params['platformName'];
        $queueId = $params['queueId'];
        $branchModel = BusinessBranch::model()->findbyAttributes(['business_fk'=>$businessId, 'default_branch'=>1]);

        $transaction = Yii::app()->db->beginTransaction();
        try {
            $customers = Followers::userFollowingBusiness($businessId);

            foreach ($customers as $customer) {
                $userBalanceId = $customer['id'];

                $userEntityPoints = UserEntityPoints::model()->findByAttributes(['user_fk' => $userBalanceId, 'entity_fk' => $businessId]);
                if (!$userEntityPoints) { //In case the user does not record in user entity points
                    $userEntityPointsModel = new UserEntityPoints();
                    $userEntityPointsModel->user_fk = $userBalanceId;
                    $userEntityPointsModel->entity_fk = $businessId;
                    $userEntityPointsModel->entity_type_fk = $entityTypeFk;
                    $userEntityPointsModel->date_created = date("Y-m-d H:i:s");
                    $userEntityPointsModel->date_modified = date("Y-m-d H:i:s");
                    $userEntityPointsModel->timezone_mysql_fk = $branchModel->timezone_mysql_fk;
                    $userEntityPointsModel->enabled = 1;
                    $userEntityPointsModel->save();

                    $userEntityPoints = UserEntityPoints::model()->findByAttributes(['user_fk' => $userBalanceId, 'entity_fk' => $businessId]);
                }

                if ($userEntityPoints) {
                    if ($entityTypeFk == 1) {
                        $UserBalanceValue = $userEntityPoints->current_active_points;
                    } else {
                        $UserBalanceValue = $userEntityPoints->current_punches;
                    }

                    $valueToAdjust = $UserBalanceValue + $givePoints;

                    $userEntityPoints->utc_updated = date('Y-m-d H:i:s');
                    if ($entityTypeFk == 1) {
                        $subTypeAdjustBalance = TRX_SUBTYPE_ADJUST_BALANCE_POINTS_INCREASE;

                        $userEntityPoints->current_active_points = $valueToAdjust;
                        $userEntityPoints->save();
                    } else {
                        $subTypeAdjustBalance = TRX_SUBTYPE_ADJUST_BALANCE_PUNCH_INCREASE;

                        $userEntityPoints->current_punches = $valueToAdjust;
                        $userEntityPoints->save();
                    }

                    //Create transaction to adjust the customer balance
                    $paramsTransaction = ['user_fk' => $userBalanceId,
                        'transaction_type_fk' => RESET_BALANCE,
                        'transaction_sub_type_fk' => $subTypeAdjustBalance,
                        'entity_fk' => $branchModel->business_branch_pk,
                        'entity_type_fk' => $entityTypeFk + 1,
                        'timezone_mysql_fk' => $branchModel->timezone_mysql_fk,
                        'comments' => $comments
                    ];
                    $transactionId = Transaction::model()->createTransaction($paramsTransaction);

                    // Create the activity for adjust the customer balance
                    $activity_description = 'ACTIVITY_ADJUST_BALANCE';
                    Activity::createActivityRecordV330($branchModel->timezone_mysql_fk,
                        $transactionId,
                        $activity_description,
                        $givePoints,
                        null,
                        date("Y-m-d H:i:s"), 1);

                    $paramLogs = ['queueId' => $queueId, 'userId' => $userBalanceId, 'businessId' => $businessId, 'transactionId' => $transactionId,
                    'subType' => $subTypeAdjustBalance, 'oldBalance' => $UserBalanceValue, 'givePoins' => $givePoints, 'newBalance' => $valueToAdjust];
                    Utils::logMessage('Give Pts Customers' . CJSON::encode($paramLogs), __METHOD__);
                }
            }

            $transaction->commit();
        } catch (CDbException $e) {
            $transaction->rollback();
            Utils::logMessage('POST: '.CJSON::encode($params). 'Error: ' . $e, __METHOD__, CLogger::LEVEL_ERROR);
        } catch (Exception $e) {
            $transaction->rollback();
            Utils::logMessage('POST: '.CJSON::encode($params). 'Error: ' . $e,__METHOD__, CLogger::LEVEL_ERROR);
        }
    }

    /**
     * Update User profile
     * The $data contains POST params:
     *
     * Used in : Website()
     *
     * @version February 2018 release V4.12.0
     * @since 4.12.0
     * <AUTHOR>
     * @access public
     * @param Business $business
     * @param array $data = Array
     *   (
     *       [clerkId] => 1234567
     *       [clerkPin] => 1234
     *       [firstName] => John
     *       [lastName] => Doe
     *       [birthDate] => 12 Feb | February 12
     *       [consentEmail] => 1 | 0
     *       [consentPhone] => 1 | 0
     *       [notes] => Some user notes
     *       [language] => 1
     *       [platformPk] => 1
     *   )
     * @return void
     */
    public function updateFullProfile(Business $business, $data, $employeeId)
    {
        $branchModel = $business->mainBranch;
        $businessId = $business->business_pk;
        $entityTypeFk = $business->entity_type_fk;
        $mainBranchId = $branchModel->business_branch_pk;
        $rules = BusinessRules::model()->findByAttributes(['business_fk' => $businessId]);

        $clerkId = $data['clerkId'];
        $firstName = isset($data['firstName']) ? $data['firstName'] : $this->first;
        $lastName = isset($data['lastName']) ? $data['lastName'] : $this->last;
        $birthDate = isset($data['birthDate']) ? $data['birthDate'] : $this->birth_date;
        $notes = isset($data['notes']) ? htmlspecialchars((string)$data['notes']) : '';
        $language = isset($data['language']) ? (int) $data['language'] : $this->language;
        $profileUserTierId = isset($data['profileUserTierId']) ? $data['profileUserTierId'] : null;
        $platformPk = isset($data['platformPk']) ? (int) $data['platformPk'] : 1;
        $platformName = PlatformSolution::getNameById($platformPk);
        $override_tier_status = (isset($data["overrideTierStatus"]) && $data["overrideTierStatus"] == "on")?1:0;
        $userTagIds = isset($data['userTags']) ? json_decode($data['userTags']) : null;
        
        // update address from customer profile
        $updateUserAddress = isset($data['updateUserAddress']) ? $data['updateUserAddress'] : 0;
        $displayAddress = $data["address"] ?? '';

        $route = isset($data["route"]) ? $data["route"] : '';
        $country = isset($data["country"]) ? $data["country"] : '';
        $countryShort = isset($data["country_short"]) ? $data["country_short"] : '';
        $province = isset($data["province"]) ? $data["province"] : '';
        $postalCode = isset($data["postal_code"]) ? $data["postal_code"] : '';
        $postalCodeId = isset($data["postal_code_fk"]) ? $data["postal_code_fk"] : '';
        $geoLat = isset($data["geo_lat"]) ? $data["geo_lat"] : '';
        $geoLong = isset($data["geo_long"]) ? $data["geo_long"] : '';
        $city = isset($data["city"]) ? $data["city"] : null;

        $userConsent = UserBusinessNotifications::model()->findByAttributes([
            'business_fk'=> $businessId, 'user_fk'=> $this->id
        ]);

        if ($profileUserTierId != null && strlen((string)$profileUserTierId) == 0) {
            $profileUserTierId = null;
        }

        $consentEmail = 0;
        if (isset($data['consentEmail']) && is_numeric($data['consentEmail'])) {
            $consentEmail = (int) $data['consentEmail'];
        } elseif (isset($data['consentEmail']) && $data['consentEmail'] == 'on') {
            $consentEmail = 1;
        }

        $consentPhone = 0;
        $shouldSendDoubleOptInSms = false;
        if (isset($data['consentPhone']) && is_numeric($data['consentPhone'])) {
            $consentPhone = (int) $data['consentPhone'];
        } elseif (isset($data['consentPhone']) && $data['consentPhone'] == 'on') {
            if ((int) $rules->double_opt_in_flag === 1) {
                // The consent must remain as is if double opt-in is ON
                $consentPhone = $userConsent ? $userConsent->allow_sms : 0;
                $shouldSendDoubleOptInSms = true;
            } else {
                $consentPhone = 1;
            }
        }

        if ($birthDate) {
            $birthDate = $birthDate . ' 1900';
            $date = new DateTime($birthDate);
            $birthDate = $date->format('Y-m-d');
        }

        $this->attributes = [
            'first' => $firstName,
            'last' => $lastName,
            'address' => $displayAddress,
            'birth_date' => $birthDate,
            'language' => $language,
            'platform_solution_fk' => $platformPk,
            'timezone_mysql_fk' => $branchModel->branch_timezone_fk,
        ];

        Utils::saveModel($this);

        // start of saving user note for business
        $timezone = TimezoneMysql::model()->findByPk($branchModel->branch_timezone_fk)->name;
        $storeTimezone = new DateTimeZone($timezone);
        $save_date = new DateTime('now', $storeTimezone);
        $noteHistory = [
            'note' => $notes,
            'user_fk' => $this->id,
            'business_fk' => $businessId,
            'clerk_id' => $clerkId,
            'utc_created' => $save_date->format('Y-m-d H:i:s')
        ];

        $lastNote = UserBusinessNote::getLastUserNoteForBusiness($noteHistory['user_fk'], $noteHistory['business_fk']);

        if(sizeof($lastNote) === 0 || $lastNote[0]['note'] !== $noteHistory['note']){
            UserBusinessNote::createUserBusinessNote($noteHistory);
        }
        // end of saving user note for business

        //Update user business profile
        $userBusinessProfileAttributes = [
            'user_fk' => $this->id,
            'business_fk' => $businessId,
            'notes' => $notes,
            'override_tier_status'=>$override_tier_status
        ];

        if(isset($data['profileUserTierId'])){
            $userBusinessProfileAttributes['customer_type'] = $profileUserTierId;
        }

        if(isset($data["account_number"]) && ((int)$rules->allow_account_number_for_login_and_rewarding === 1)) {//only update when account_number is in the $_POST
            $accountNumber = trim((string)$data["account_number"] ?? '');
            $accountNumber = empty($accountNumber) ? null : $accountNumber;
            $userBusinessProfileAttributes['account_number'] = $accountNumber;
        }

        UserBusinessProfile::createUpdateV417($userBusinessProfileAttributes);

        //Save user tags (Create new tags and remove the non-existing ones)
        if (!is_null($userTagIds) && is_array($userTagIds)) {
            UserTag::createDeleteUserTags($this->id, $userTagIds);
        } else {
            Yii::log(json_encode([
                'message' => 'User tags are empty',
                'user_id' => $this->id,
                'businessId' => $businessId,
                'userTagIds' => $userTagIds
            ]), CLogger::LEVEL_INFO, __METHOD__);
        }

        if($updateUserAddress){
            $businessModel = Business::model()->findByPk($businessId);
            $timeZoneId = $businessModel->timezone_mysql_fk;

            $postalCodeId = PostalCode::insertGetPostalCodeV330($postalCode, $timeZoneId);
            $countryId = Countries::insertGetCountryV330($country, $countryShort, $timeZoneId);
            $regionId = Regions::insertGetRegionV330($province, $countryId, $timeZoneId);
            $cityId = Cities::insertGetCityV330($city, $regionId, $timeZoneId);

            $addressParams = [
                'entityId' => $this->id,
                'entityTypeId' => EntityType::USER_ENTITY,
                'postalCodeId' => $postalCodeId,
                'cityId' => $cityId,
                'street' => $route,
                'postalCode' => $postalCode,
                'geo_lat' => $geoLat,
                'geo_long' => $geoLong
            ];

            $addressModel = Address::insertUpdateAddressForEntityV330($timeZoneId, $addressParams, $platformName);
        }

        $updateConsent = true;
        if ($userConsent) {
            if ($userConsent->allow_email == $consentEmail && $userConsent->allow_sms == $consentPhone){
                $updateConsent = false;
            }
        }

        if ($updateConsent) {
            if(BusinessRole::checkAccessForAction(PERMISSION_CONSENT_UPDATE, $employeeId)) {
                $ubnModel = UserBusinessNotifications::createUpdateUserBusinessNotification([
                    'business_fk' => $businessId,
                    'user_fk' => $this->id,
                    'allow_email' => $consentEmail,
                    'allow_sms' => $consentPhone,
                    'timezone_mysql_fk' => $branchModel->timezone_mysql_fk
                ]);

                UserActionLog::create([
                    'source_tbl' => $ubnModel->tableName(),
                    'source_id' => $ubnModel->user_business_notifications_pk,
                    'user_fk' => $ubnModel->user_fk,
                    'business_fk' => $ubnModel->business_fk,
                    'platform_solution_fk' => $platformPk,
                    'employee_user_fk' => $employeeId,
                    'data' => json_encode([
                        'ip' => Yii::app()->request->userHostAddress,
                        'ua' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                        'originator' => 'CustomerProfile.consent.update',
                        'cem_flag' => $ubnModel->cem_flag,
                        'allow_email' => $ubnModel->allow_email,
                        'allow_sms' => $ubnModel->allow_sms,
                        'allow_push' => $ubnModel->allow_push,
                    ]),
                ]);

                $logger = new AuditLogger(AuditLog::EVENT_CONSENT_UPDATED, $ubnModel);
                $logger->log();
            }
        }

        $paramsAccountActivityDetail = [
            'first' => $firstName,
            'last' => $lastName,
            'birth_date' => $birthDate,
            'language' => $language,
            'platform_solution_fk' => $platformPk,
            'allow_email' => $consentEmail,
            'allow_sms' => $consentPhone,
            'notes' => $notes
        ];

        $paramsAccountActivity = [
            'user_fk' => $clerkId,
            'customer_fk' => $this->id,
            'details' => CJSON::encode($paramsAccountActivityDetail),
            'type' => AccountActivity::PROFILE_SAVE,
            'platform_fk' => $platformPk,
            'business_branch_fk' => $mainBranchId,
        ];
        AccountActivity::createAccountActivity($paramsAccountActivity);

        $logger = new AuditLogger(AuditLog::EVENT_CUSTOMER_UPDATED, $this);
        $logger->log();

        if ($shouldSendDoubleOptInSms && !UserProfile::isDoubleOptInSmsSent($this->id)) {
            Utils::sendDoubleOptInSms($this, $business);
        }
    }

    /**
     * Find a user by UUID
     *
     * Used in : Website()
     *
     * @version June 2017 release V4.8.0
     * @since 4.8.0
     * <AUTHOR>
     * @access public
     * @param string $uuid - hex representation
     * @return User
     */
    public static function findByUuid($uuid)
    {
        $onlyFields = ['id', 'user_uid', 'first', 'last', 'email', 'phone', 'username', 'qrcode',
            'enabled', 'gender', 'language', 'birth_date', 'utc_created', 'country_phone_fk',
            'platform_solution_fk', 'image','business_fk', 'signedupat_fk', 'address'];
        $user = User::model()->findByAttributes([], [
            'condition' => 'user_uid = :uuid',
            'params' => [':uuid' => hex2bin($uuid)]
        ]);

        if($user) {
            foreach ($user as $key => $value) {
                if(!in_array($key, $onlyFields, true)) {
                    unset($user->{$key});
                }
            }
        }

        return $user;
    }
    
	/**
	 * Reverse User address from address model
	 *
	 * <AUTHOR>
	 * @access public
	 * @return object|array $model
	 */
	public static function reverseUserAddress($model, $addressModel)
	{
		if ($addressModel && $addressModel->city_fk != null) {
            $cityModel = Cities::model()->findByPk($addressModel->city_fk);
            $regionModel = Regions::model()->findByPk($cityModel->region_fk);
            $countryModel = Countries::model()->findByPk($regionModel->country_fk);

            if(is_array($model)){
                //construct the address
                $model['route'] = $addressModel->street;
                $model['country'] = $countryModel->name;
                $model['country_short'] = $countryModel->code;
                $model['province'] = $regionModel->name;
                $model['city'] = $cityModel->name;

                // Use the address field in the user table if it is not empty; this field is for display purposes only
                if (empty($model['address'])) {
                    $model['address'] = $model['route'] ? $addressModel->street . ', ' : '';
                    if(!empty($model['city'])) $model['address'] .= $model['city'] . ', ';
                    $model['address'] .= $model['province'] . ', ' .$model['country'];
                }

                $model['lat'] = $addressModel->geo_lat;
                $model['lng'] = $addressModel->geo_long;
            }else {
                //construct the address
                $model->route = $addressModel->street;
                $model->country = $countryModel->name;
                $model->country_short = $countryModel->code;
                $model->province = $regionModel->name;
                $model->city = $cityModel->name;

                // Use the address field in the user table if it is not empty; this field is for display purposes only
                if (empty($model->address)) {
                    $model->address = $model->route ? $addressModel->street . ', ' : '';
                    if(!empty($model->city)) $model->address .= $model->city . ', ';
                    $model->address .= $model->province . ', ' .$model->country;
                }

                $model->lat = $addressModel->geo_lat;
                $model->lng = $addressModel->geo_long;
            }
        } else{
            if(is_array($model)){
                $model['address'] = '';
                $model['lat'] = null;
                $model['lng'] = null;
            }else{
                $model->address = '';
                $model->lat = null;
                $model->lng = null;
            }
        }

        return $model;
	}

    /**
     * Search customer
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.1.0
     * @since 4.1.0
     * <AUTHOR>
     * @access public
     * @return CActiveDataProvider
     */
    public function searchCustomers($businessId, $entityTypeFk, $page = 1)
    {
        $branchIds = BusinessBranch::getAllowedBranchesByBusiness($businessId);

        $criteria = new CDbCriteria;
        $criteria->limit = 10;
        $criteria->offset = ($page - 1) * 10;

        /*$criteria->join="LEFT JOIN user_business_profile ubp ON (t.id=ubp.user_fk AND ubp.business_fk=$businessId) LEFT JOIN user_entity_points uep ON (t.id=uep.user_fk AND uep.entity_fk=$businessId AND uep.entity_type_fk=$entityTypeFk) ";
        $criteria->select=array('t.id','t.username', 't.first', 't.last', 't.phone', 't.email', 'uep.current_active_points+current_punches AS user_balance', 'ubp.notes AS user_notes');*/

        $criteria->join="LEFT JOIN user_entity_points uep ON (t.id=uep.user_fk AND uep.entity_fk=$businessId AND uep.entity_type_fk=$entityTypeFk) ";
        $criteria->join .="LEFT JOIN transaction trx ON trx.user_fk=t.id AND trx.transaction_sub_type_fk IN (4,5,17,18,43,57,62,63,65,67,68) AND
            trx.entity_fk IN (".implode(',', $branchIds).") ";
        $criteria->join .="LEFT JOIN user_business_profile ubp ON ubp.business_fk=$businessId AND ubp.user_fk = t.id ";
        $criteria->join .="LEFT JOIN business_tiers bt ON bt.business_fk=$businessId AND bt.enabled = 1 AND bt.business_tiers_pk = ubp.customer_type ";
        $criteria->join .="LEFT JOIN user_business_notifications ubn ON ubn.business_fk=$businessId AND ubn.user_fk = t.id ";
        $criteria->join .="LEFT JOIN user_tags ut ON ut.user_fk = t.id ";

        /*if ($this->customer_tags && isset($this->customer_tags)) {
            if (!in_array("0", $this->customer_tags)) {
                //$criteria->join .="LEFT JOIN user_tags ut ON ut.user_fk != t.id ";
            //} else {
                $criteria->join .="LEFT JOIN user_tags ut ON ut.user_fk = t.id ";
            }
        }*/

        $userId = Yii::app()->user->id;

        $isBusiness = Yii::app()->authManager->checkAccess('business', $userId);
        $isSuperAdmin = Yii::app()->authManager->checkAccess('superadmin', $userId);
        $isMerchantSetup = Yii::app()->authManager->checkAccess('merchantsetup', $userId);
        $isGroupManager = Yii::app()->authManager->checkAccess('businessgroupmanager', $userId);

        // Note: Yii::app()->user->checkAccess does NOT work as expected when calling from the new Business Portal proxy
        /*if(Yii::app()->user->checkAccess('business')
            || Yii::app()->user->checkAccess('superadmin')
            || Yii::app()->user->checkAccess('merchantsetup')
            || Yii::app()->user->checkAccess('businessgroupmanager')
        ){*/
        if($isBusiness
            || $isSuperAdmin
            || $isMerchantSetup
            || $isGroupManager
        ){
            // When logged in as Business Admin or ..., return all customers all branches
            $criteria->join .="JOIN followers f ON f.user_fk = t.id AND f.entity_fk=$businessId AND f.entity_type_fk=$entityTypeFk ";
            $groupByCond = 'f.user_fk';
        } else {
            // When logged in as Branch Admin, return only customers for one branch
            $userId = Yii::app()->user->id;
            //get businessId, branchId, entityType for logged in user
            $userEntities = UserEntityPermission::getUserEntitiesByRole($userId, __METHOD__, PlatformSolution::PLATFORM_NAME_WEBSITE);
            $businessId = $userEntities['businessId'];
            $mainBranchId = $userEntities['mainBranchId'];
            $branchId = $userEntities['branchId'];

            $criteria->compare('t.signedupat_fk', $branchId);//exact match
            $groupByCond = 't.id';
        }

        $criteria->select=array('t.id', 't.user_uid', 't.username', 't.first', 't.last', 't.phone', 't.email', 't.address',
            'uep.current_active_points+current_punches AS user_balance',
            'max(trx.utc_created) as last_visit',
            'min(trx.utc_created) as first_visit',
            'case
             when min(trx.utc_created) then (select bb.name from business_branch bb where bb.business_branch_pk = trx.entity_fk)
             else (select bb.name from business_branch bb where bb.business_fk = t.business_fk and bb.business_branch_pk = t.signedupat_fk)
             end as businessName',
            'ubn.cem_flag as user_consent',
            'ubn.allow_email as email_consent',
            'ubn.allow_sms as sms_consent',
            'ubn.allow_push as push_consent',
            'bt.type_name as type_name',
            't.signedupat_fk',
            'ubp.account_number',
            'ubp.customer_type',
            'ubp.custom_field_1',
            'ubp.custom_field_2',
            'ubp.custom_field_3',
            'ubp.custom_field_4',
            'ubp.custom_field_5',
        );

        $criteria->compare('t.id', $this->id);
        $criteria->compare('t.username', trim((string)(string)$this->username), true); // LIKE
        $criteria->compare('t.first', trim((string)(string)$this->first), true); // LIKE
        $criteria->compare('t.last', trim((string)(string)$this->last), true); // LIKE
        $criteria->compare('t.phone', trim((string)(string)$this->phone), true); // LIKE
        $criteria->compare('t.email', trim((string)(string)$this->email), true); // LIKE
        $criteria->compare('t.enabled', $this->enabled); // exact match
        $criteria->compare('bt.type_name', trim((string)(string)$this->type_name), true);
        $criteria->compare('ubp.account_number', trim((string)(string)$this->account_number), true);
        
        Yii::log('userTierIds - ' . $this->userTierIds, CLogger::LEVEL_INFO, __METHOD__);
        if($this->userTierIds){
            $userTierIdCriteria = json_decode($this->userTierIds);
            if(!empty($userTierIdCriteria)){
                Yii::log('excludeTiers - ' . $this->excludeTiers, CLogger::LEVEL_INFO, __METHOD__);
                if($this->excludeTiers == "true") {
                    $criteria->addCondition('ubp.customer_type IS NULL');
                    $criteria->addNotInCondition('ubp.customer_type', $userTierIdCriteria, 'OR'); 
                }
                else $criteria->addInCondition('ubp.customer_type', $userTierIdCriteria);
            }
        }
        
        Yii::log('businessBranchIds - ' . $this->businessBranchIds, CLogger::LEVEL_INFO, __METHOD__);
        if($this->businessBranchIds){
            $businessBranchCriteria = json_decode($this->businessBranchIds);
            if(!empty($businessBranchCriteria)){
                Yii::log('excludeBranches - ' . $this->excludeBranches, CLogger::LEVEL_INFO, __METHOD__);
                if($this->excludeBranches == "true") {
                    $criteria->addCondition('t.signedupat_fk IS NULL');
                    $criteria->addNotInCondition('t.signedupat_fk', $businessBranchCriteria, 'OR');
                }
                else $criteria->addInCondition('t.signedupat_fk', $businessBranchCriteria);
            }
        }

        Yii::log('userTagIds - ' . $this->userTagIds, CLogger::LEVEL_INFO, __METHOD__);
        if($this->userTagIds){
            $userTagIdCriteria = json_decode($this->userTagIds);
            if(!empty($userTagIdCriteria)){
                Yii::log('excludeTags - ' . $this->excludeTags, CLogger::LEVEL_INFO, __METHOD__);
                if($this->excludeTags == "true") {
                    $criteria->addCondition('(ut.tag_fk IS NULL)');

                    $command = Yii::app()->db->createCommand();
                    $command->select('ut.user_fk')
                        ->from('user_tags ut')
                        ->leftJoin('tags t', 'ut.tag_fk = t.tag_pk')
                        ->where(['in', 't.tag_pk', $this->excludeTags]);
                    
                    $result = $command->queryColumn();

                    Yii::log('User IDs: ' . json_encode($result), CLogger::LEVEL_INFO, __METHOD__);
                    
                    
                    $criteria->addNotInCondition('ut.user_fk', array_unique($result));
                    //Yii::log('excludeTagsidss - ' . print_r('$userIds'), CLogger::LEVEL_INFO, __METHOD__);
                    //$criteria->addNotInCondition('ut.tag_fk', $userTagIdCriteria); 
                }
                else $criteria->addInCondition('ut.tag_fk', $userTagIdCriteria);
            }
        }

        $criteria->compare('ubp.custom_field_1', $this->custom_field_1, true);
        $criteria->compare('ubp.custom_field_2', $this->custom_field_2, true);
        $criteria->compare('ubp.custom_field_3', $this->custom_field_3, true);
        $criteria->compare('ubp.custom_field_4', $this->custom_field_4, true);
        $criteria->compare('ubp.custom_field_5', $this->custom_field_5, true);

        if ($this->customer_type && isset($this->customer_type)) {
            $criteria->compare('ubp.customer_type', $this->customer_type); // exact match
        }
        
        if ($this->customer_tags && isset($this->customer_tags)) {
            if (in_array("0", $this->customer_tags)) {
                $criteria->addCondition('(ut.tag_fk IS NULL)');
            } else {
                $criteria->addInCondition('ut.tag_fk', $this->customer_tags); // exact match
            }
        }

        if ($this->user_consent && isset($this->user_consent) && strlen((string)trim((string)$this->user_consent)) > 0) {
            $consentYes = 'yes oui si sim';
            $consentNo = 'no non si não';
            $consentNA = 'n/a';
            $consentSearch = strtolower((string)$this->user_consent);
            if (strrpos((string)$consentYes, $consentSearch) !== false) {
                $criteria->compare('ubn.cem_flag', 1);
            } elseif (strrpos((string)$consentNo, $consentSearch) !== false) {
                $criteria->addCondition('(ubn.cem_flag = 0)');
            } elseif (strrpos((string)$consentNA, $consentSearch) !== false) {
                $criteria->addCondition('(ubn.cem_flag IS NULL)');
            }
        }
        
        if ($this->email_consent && isset($this->email_consent) && strlen((string)trim((string)$this->email_consent)) > 0) {
            $emailConsentYes = 'yes oui si sim';
            $emailConsentNo = 'no non si não';
            $emailConsentNA = 'n/a';
            $emailConsentSearch = strtolower((string)$this->email_consent);
            if (strrpos((string)$emailConsentYes, $emailConsentSearch) !== false) {
                $criteria->compare('ubn.allow_email', 1);
            } elseif (strrpos((string)$emailConsentNo, $emailConsentSearch) !== false) {
                $criteria->addCondition('(ubn.allow_email = 0)');
            } elseif (strrpos((string)$emailConsentNA, $emailConsentSearch) !== false) {
                $criteria->addCondition('(ubn.allow_email IS NULL)');
            }
        }
        
        if ($this->sms_consent && isset($this->sms_consent) && strlen((string)trim((string)$this->sms_consent)) > 0) {
            $smsConsentYes = 'yes oui si sim';
            $smsConsentNo = 'no non si não';
            $smsConsentNA = 'n/a';
            $smsConsentSearch = strtolower((string)$this->sms_consent);
            if (strrpos((string)$smsConsentYes, $smsConsentSearch) !== false) {
                $criteria->compare('ubn.allow_sms', 1);
            } elseif (strrpos((string)$smsConsentNo, $smsConsentSearch) !== false) {
                $criteria->addCondition('(ubn.allow_sms = 0)');
            } elseif (strrpos((string)$smsConsentNA, $smsConsentSearch) !== false) {
                $criteria->addCondition('(ubn.allow_sms IS NULL)');
            }
        }
        
        if ($this->push_consent && isset($this->push_consent) && strlen((string)trim((string)$this->push_consent)) > 0) {
            $pushConsentYes = 'yes oui si sim';
            $pushConsentNo = 'no non si não';
            $pushConsentNA = 'n/a';
            $pushConsentSearch = strtolower((string)$this->push_consent);
            if (strrpos((string)$pushConsentYes, $pushConsentSearch) !== false) {
                $criteria->compare('ubn.allow_push', 1);
            } elseif (strrpos((string)$pushConsentNo, $pushConsentSearch) !== false) {
                $criteria->addCondition('(ubn.allow_push = 0)');
            } elseif (strrpos((string)$pushConsentNA, $pushConsentSearch) !== false) {
                $criteria->addCondition('(ubn.allow_push IS NULL)');
            }
        }

        if($this->user_balance && is_numeric($this->user_balance))
            $criteria->having = "user_balance = ".$this->user_balance;
        elseif($this->user_balance && $this->user_balance[0] == '<')
            $criteria->having = "user_balance < ". substr((string)$this->user_balance, 1);
        elseif($this->user_balance && $this->user_balance[0] == '>')
            $criteria->having = "user_balance > ". substr((string)$this->user_balance, 1);

        if ($this->last_visit) {
            $criteria->having = "DATE(last_visit) = DATE('$this->last_visit')";
        }

        if ($this->first_visit) {
            $criteria->having = "DATE(first_visit) = DATE('$this->first_visit')";
        }

        if ($this->businessName) {
            $criteria->having = "businessName LIKE '%$this->businessName%'";
        }

        if(!empty($this->name)) {
            $subCriteria = new CDbCriteria();
            $subCriteria->addCondition('t.first LIKE "%' . trim((string)$this->name) . '%"', 'OR'); // LIKE
            $subCriteria->addCondition('t.last LIKE "%' . trim((string)$this->name) . '%"', 'OR'); // LIKE
            $subCriteria->addCondition('t.email LIKE "%' . trim((string)$this->name) . '%"', 'OR'); // LIKE

            $criteria->mergeWith($subCriteria, 'AND');
        }

        $criteria->group = $groupByCond;

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
            'totalItemCount' => 0,
            'pagination' => false,
//            'pagination' => array(
//                'pageSize' => 10,
//            ),
            'sort' => array(
                'defaultOrder' => $groupByCond .' DESC',
                'attributes'=>array(
                    'user_balance'=>[
                        'asc'=>'user_balance ASC','desc'=>'user_balance DESC'
                    ],
                    'username' => [
                        'asc'=>'username ASC','desc'=>'username DESC'
                    ],
                    'first' => [
                        'asc'=>'first ASC','desc'=>'first DESC'
                    ],
                    'last' => [
                        'asc'=>'last ASC','desc'=>'last DESC'
                    ],
                    'email' => [
                        'asc'=>'email ASC','desc'=>'email DESC'
                    ],
                    'phone' => [
                        'asc'=>'phone ASC','desc'=>'phone DESC'
                    ],
                    'first_visit' => [
                        'asc'=>'first_visit ASC','desc'=>'first_visit DESC'
                    ],
                    'last_visit' => [
                        'asc'=>'last_visit ASC','desc'=>'last_visit DESC'
                    ],
                    'user_consent' => [
                        'asc'=>'user_consent DESC','desc'=>'user_consent ASC'
                    ],
                    'email_consent' => [
                        'asc'=>'email_consent DESC','desc'=>'email_consent ASC'
                    ],
                    'sms_consent' => [
                        'asc'=>'sms_consent DESC','desc'=>'sms_consent ASC'
                    ],
                    'push_consent' => [
                        'asc'=>'push_consent DESC','desc'=>'push_consent ASC'
                    ],
                    'type_name' => [
                        'asc'=>'type_name DESC','desc'=>'type_name ASC'
                    ],
                    'businessName' => [
                        'asc'=>'businessName DESC','desc'=>'businessName ASC'
                    ],
                    'businessBranchIds' => [
                        'asc'=>'t.signedupat_fk DESC','desc'=>'t.signedupat_fk ASC'
                    ],
                    'userTierIds' => [
                        'asc'=>'ubp.customer_type DESC','desc'=>'ubp.customer_type ASC'
                    ],
                    'userTagIds' => [
                        'asc'=>'ut.tag_fk DESC','desc'=>'ut.tag_fk ASC'
                    ],
                    'account_number' => [
                        'asc'=>'account_number DESC','desc'=>'account_number ASC'
                    ],
                    'custom_field_1' => [
                        'asc'=>'custom_field_1 DESC','desc'=>'custom_field_1 ASC'
                    ],
                    'custom_field_2' => [
                        'asc'=>'custom_field_2 DESC','desc'=>'custom_field_2 ASC'
                    ],
                    'custom_field_3' => [
                        'asc'=>'custom_field_3 DESC','desc'=>'custom_field_3 ASC'
                    ],
                    'custom_field_4' => [
                        'asc'=>'custom_field_4 DESC','desc'=>'custom_field_4 ASC'
                    ],
                    'custom_field_5' => [
                        'asc'=>'custom_field_5 DESC','desc'=>'custom_field_5 ASC'
                    ],
                )
            ),
        ));
    }

    /**
     * Search an user by query string for a business
     *
     * Used in : Website()
     *
     * @version August 2017 release V4.8.3
     * @since 4.8.3
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function searchByString($businessId, $q)
    {
        $q = strip_tags($q??'');
        $q = preg_replace('/[^a-zA-Z0-9]/', '', $q);

        return Yii::app()->db->createCommand()
            ->select('id, username, first, last, email, phone, IFNULL(concat(first," ",last), "") AS name')
            ->from('user u')
            ->join('followers f', 'f.user_fk=u.id')
            ->where('u.enabled=1 AND f.entity_fk=:entity_fk', [
                ':entity_fk' => $businessId,
            ])
            ->andWhere('username LIKE :q OR email LIKE :q OR phone LIKE :q OR first LIKE :q OR last LIKE :q
                OR concat(first," ",last) LIKE :q
                OR concat(last," ",first) LIKE :q', [
                ':q' => '%' . $q . '%'
            ])
            ->limit(5)
            ->queryAll();
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return User the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * This event is raised before the record is saved.
     * Globalization Support
     * <AUTHOR> C.
     * @version : May - Jun 2015 release V3.3.0
     */
    public function beforeSave()
    {
        if ($this->isNewRecord) {
            $this->utc_created = date('Y-m-d H:i:s');
            $this->utc_updated = date('Y-m-d H:i:s');
        } else {
            $this->utc_updated = date('Y-m-d H:i:s');
        }

        return parent::beforeSave();
    }


    /**
     * Get user roles
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.1.0
     * @since 4.1.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public function getRoles($userId = null)
    {
        if (!$userId) {
            $userId = Yii::app()->user->getId();
        }

        $r = Yii::app()->authManager->getAuthItems(2, $userId);
        return array_keys($r);
    }

/****************************************************************************************************/
/************************************ MAIN Yii Methods END HERE *************************************/
/****************************************************************************************************/

//*******************************************************************************************//
//******************************** START * Legacy - TEMPORAL ********************************//
//*******************************************************************************************//

    /**
     * Creating Customer - Sign Up Process
     * Used in : Website (BusinessAdmin - Marketing Campaign)
     *
     * @version V4.10
     * <AUTHOR> David
     * @access public
     * @param model $user
     * @return array
     */
    public static function createSignupCustomer($userModel)
    {
        $userId = $userModel->id;
        $timeZoneId = $userModel->timezone_mysql_fk;
        $permission = 'user';
        $entity_fk = $userId;
        $entity_type_fk = 4; //user
        //make the new user follow Kangaroo business
//        Followers::createFollowerRecordV330($userId, 125, 1, $timeZoneId);
        //set ON all the notifications for kangaroo business
//        $userSettings = [['allow_sms' => 1,
//            'allow_push' => 1,
//            'allow_email' => 1,
//            'business_fk' => 125]];
//        UserBusinessNotifications::updateUserBusinessNotificationsV340($userId, $userSettings, 3, $timeZoneId);
        UserEntityPermission::assignEntityPermissionV330($userId, $permission, $entity_fk, $entity_type_fk, $timeZoneId);

        //$sentEmailSMS = User::sendSignupVerificationEmailAndSMS($userId, $businessId, $hasEmail, $hasPhone, $isUserDisabled_Email, $isUserDisabled_Phone, $platformName, $lang);
    }

    /**
     * Clone user for conglomerate admin business
     * Doe not send welcome email because the original user just signed up
     *
     * @param User|object $user
     * @param Business $business
     * @throws Exception
     */
    public static function cloneForConglomerate(User $user, Business $business)
    {
        if(!$business->conglomerate_id) {
            Yii::log(json_encode([
                'message' => 'User wont be cloned. Business is not conglomerate member',
                'user_id' => $user->id,
                'business_pk' => $business->business_pk,
            ]), CLogger::LEVEL_INFO, __METHOD__);
            return;
        }

        $conglomerate = Business::model()->findByPk($business->conglomerate_id);

        // Only for dependent conglomerates
        if(!$conglomerate || $conglomerate->independent == '1'){
            Yii::log(json_encode([
                'message' => 'User wont be cloned. This is iConglomerate',
                'user_id' => $user->id,
                'business_pk' => $business->business_pk,
            ]), CLogger::LEVEL_INFO, __METHOD__);
            return;
        }

        $userModel = null;
        // Check if the user already exists for at least one of the three credentials
        if(!$userModel && $user->phone) {
            $userModel = User::model()->findByAttributes([
                'phone' => $user->phone,
                'business_fk' => $conglomerate->business_pk,
            ]);
        }

        if(!$userModel && $user->email) {
            $userModel = User::model()->findByAttributes([
                'email' => $user->email,
                'business_fk' => $conglomerate->business_pk,
            ]);
        }

        if(!$userModel && $user->qrcode) {
            $userModel = User::model()->findByAttributes([
                'qrcode' => $user->qrcode,
                'business_fk' => $conglomerate->business_pk,
            ]);
        }

        if ($userModel) {
            Yii::log(json_encode([
                'message' => 'User wont be cloned. Already exists',
                'user_id' => $user->id,
                'user_phone' => $user->phone,
                'user_email' => $user->email,
                'user_qrcode' => $user->qrcode,
                'business_pk' => $business->business_pk,
                'conglomerate_id' => $conglomerate->business_pk,
            ]), CLogger::LEVEL_INFO, __METHOD__);
            return;
        }

        $user->refresh();

        $newUserArr = $user->attributes;
        unset($newUserArr['id'], $newUserArr['passwordreset'], $newUserArr['user_uid']);
        // Override attributes
        $newUserArr['business_fk'] = $conglomerate->business_pk;
        $newUserArr['signedupat_fk'] = $conglomerate->mainBranch->business_branch_pk;
        $newUserArr['username'] = Utils::generateUniqueUsername();
        $newUserArr['enabled'] = 1;
        $newUserArr['pin_enabled'] = 1;
        $newUserArr['pin_permission'] = $user->pin_permission;
        $userModel = User::createUpdateUserV350($newUserArr);

        User::createSignupCustomer($userModel);

        Followers::createFollowerRecordV330(
            $userModel->id,
            $conglomerate->business_pk,
            $conglomerate->entity_type_fk,
            $conglomerate->mainBranch->branch_timezone_fk
        );

        $timezoneId = $conglomerate->mainBranch->branch_timezone_fk;
        UserStatusManager::createUserStatusManagerRecordV330(
            $userModel->id, null, $status_manager_fk = 35, '', '', $userModel->platform_solution_fk,
            $timezoneId
        );

        if ($userModel->phone && $userModel->email) {
            UserStatusManager::createUserStatusManagerRecordV330(
                $userModel->id, null, $status_manager_fk = 30, '', $userModel->phone, $userModel->platform_solution_fk,
                $timezoneId, $userModel->country_phone_fk
            );
            UserStatusManager::createUserStatusManagerRecordV330(
                $userModel->id, null, $status_manager_fk = 32, '', $userModel->email, $userModel->platform_solution_fk,
                $timezoneId
            );
        } elseif($userModel->phone) {
            UserStatusManager::createUserStatusManagerRecordV330(
                $userModel->id, null, $status_manager_fk = 30, '', $userModel->phone, $userModel->platform_solution_fk,
                $timezoneId, $userModel->country_phone_fk
            );
        } elseif($userModel->email) {
            UserStatusManager::createUserStatusManagerRecordV330(
                $userModel->id, null, $status_manager_fk = 32, '', $userModel->email, $userModel->platform_solution_fk,
                $timezoneId
            );
        }

        Yii::log(json_encode([
            'message' => 'User cloned successfully',
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_phone' => $user->phone,
            'new_user_id' => $userModel->id,
            'new_user_email' => $userModel->email,
            'new_user_phone' => $userModel->phone,
            'business_pk' => $business->business_pk,
            'conglomerate_id' => $conglomerate->business_pk,
            'platform_solution_fk' => $user->platform_solution_fk,
        ]), CLogger::LEVEL_INFO, __METHOD__);

        $userSettings = [[
            'allow_sms' => 0,
            'allow_email' => 0,
            'allow_push' => 1,
            'business_fk' => $conglomerate->business_pk,
        ]];

        // Allow PUSH notifications automatically
        UserBusinessNotifications::updateUserBusinessNotificationsV340(
            $userModel->id,
            $userSettings,
            1,
            $conglomerate->mainBranch->branch_timezone_fk,
            [
                'originator' => 'MerchantApp.CloneConglomerate',
                'platform_solution_fk' => $userModel->platform_solution_fk,
                'employee_user_fk' => null,
            ]
        );

        // Do not send another welcome email because user just signed up
    }

    /**
     * Getting the user ids who have record in user business notifications
     * Used in : Website (BusinessAdmin - Marketing Campaign)
     *
     * @version V4.8.13
     * <AUTHOR> David
     * @access public
     * @param array $customersId
     * @param string $businessId
     * @return array
     */
    public static function campaignCustomersWithNotifications($customers, $businessId)
    {
        $result = Yii::app()->db->createCommand()
            ->select(array(
                'u.id',
            ))
            ->from('user u')
            ->join('user_business_notifications ubn', 'ubn.user_fk=u.id AND ubn.business_fk='.$businessId)
            ->where('u.enabled=1')
            ->andWhere(array('in', 'u.id', $customers))
            ->queryAll();

        return array_keys(CHtml::listData($result, 'id', 'last_trx'));
    }

    /**
     * Getting the user ids who have made between balance
     * Used in : Website (BusinessAdmin - Marketing Campaign)
     *
     * @version V4.8.4
     * <AUTHOR> David
     * @access public
     * @param int $entityTypeFk
     * @param string $startPts, $endPts
     * @return array
     */
    public static function campaignCustomersBetweenBalance($entityTypeFk, $startPts, $endPts)
    {
        return Yii::app()->db->createCommand()
            ->select(array(
                'uep.user_fk',
                'uep.current_active_points',
            ))
            ->from('user_entity_points uep')
            ->join('user u', 'u.id = uep.user_fk')
            ->where('u.enabled=1 AND uep.entity_fk=:entityTypeFk', array('entityTypeFk' =>$entityTypeFk))
            ->having("current_active_points BETWEEN '$startPts' AND '$endPts'")
            ->queryAll();
    }

    /**
     * Getting the user ids who have gifcard
     * Used in : Website (BusinessAdmin - Marketing Campaign)
     *
     * <AUTHOR>
     * @access public
     * @param $businessId
     * @param $haveGiftcardOperator
     * @param $start
     * @param $end
     * @return mixed
     */
    public static function campaignCustomersGiftcard($businessId, $haveGiftcardOperator, $start, $end)
    {
        $query = Yii::app()->db->createCommand()
            ->select(array(
                'uep.user_fk',
                'uep.giftcard',
            ))
            ->from('user_entity_points uep')
            ->rightJoin('user u', 'u.id = uep.user_fk')
            ->where('u.enabled=1 AND u.business_fk=:businessId', array('businessId' => $businessId));
        if ($haveGiftcardOperator == 2) {
            $query->andWhere("giftcard <= $start or giftcard is null");
        } elseif ($haveGiftcardOperator == 3) {
            $query->andWhere("giftcard BETWEEN $start AND $end");
        } else {
            $query->andWhere("giftcard >= $start");
        }
        return $query->queryAll();
    }

    /**
     * Getting the user ids who have made more/less lifetime balance
     * Used in : Website (BusinessAdmin - Marketing Campaign)
     *
     * @version V4.8.4
     * <AUTHOR> David
     * @access public
     * @param array $entities - branch ids
     * @param int $entityTypeFk
     * @param string $trxCon
     * @return array
     */
    public static function campaignCustomersLifetimeBalance($entityTypeFk, $trxCon = '>=1')
    {
        return Yii::app()->db->createCommand()
            ->select(array(
                'uep.user_fk',
                'uep.lifetime_balance',
            ))
            ->from('user_entity_points uep')
            ->join('user u', 'u.id = uep.user_fk')
            ->where('u.enabled=1 AND uep.entity_fk=:entityTypeFk', array('entityTypeFk' =>$entityTypeFk))
            ->having('lifetime_balance' . $trxCon)
            ->queryAll();
    }

    /**
     * Assign the branch coupon to the user
     *
     * Used in : Merchant App
     * @version V4.8
     * <AUTHOR> David.
     * @return Array
     */
    public static function assignCouponUser($params)
    {
        $userOffer = UserOffer::model()->findByAttributes(['user_fk' => $params['userId'], 'offer_fk' => $params['couponId']]);
        if (!$userOffer) {
            $userOffer = new UserOffer();
            $userOffer->user_fk = $params['userId'];
            $userOffer->offer_fk = $params['couponId'];
        }

        $transactionmodel = new Transaction();
        $transactionmodel->user_fk = $params['userId'];
        $transactionmodel->transaction_type_fk = TRX_TYPE_POINTS_PURCHASE;
        $transactionmodel->transaction_sub_type_fk = TRX_SUBTYPE_COUPON_GET_COUPON; // For tagging
        $transactionmodel->utc_created = date("Y-m-d H:i:s");
        $transactionmodel->entity_fk = $params['businessBranchId'];
        $transactionmodel->timezone_mysql_fk = $params['timeZoneId'];
        $transactionmodel->entity_type_fk = $params['entityTypeId'];

        $userOffer->current_offer_status = TRX_SUBTYPE_COUPON_GET_COUPON;
        $userOffer->timezone_mysql_fk = $params['timeZoneId'];
        Utils::saveModel($userOffer);
        Utils::saveModel($transactionmodel);
        $transId = $transactionmodel->primaryKey;

        $userOfferStory = new UserOfferStory();
        $userOfferStory->user_offer_fk = $userOffer->user_offer_pk;
        $userOfferStory->transaction_fk = $transId;
        $userOfferStory->timezone_mysql_fk = $params['timeZoneId'];
        $userOfferStory->qrcode = UserOffer::generateQRcode();
        Utils::saveModel($userOfferStory);

        Yii::log("User get coupon ". json_encode($params), CLogger::LEVEL_INFO, __METHOD__);
    }

    /**
     * Get The Claim Offers by User and Branch
     * Updated By Mohammad 22/06/2022: Include the targeted tiers (flag equals to 2) to get the offers with tiers and create coupons for customers automatically when customer logs in into the merchant app
     *
     * Used in : Merchant App
     *
     * @version V4.5.0
     * <AUTHOR> David
     * @access public
     * @return array
    */
    public static function getCouponsToAssignUserbyBranch($userId, $branchId)
    {
        $businessBranch = BusinessBranch::model()->findByPk($branchId);
        $businessBranches = BusinessBranch::getAllBranchesByBusinessId($businessBranch->business_fk);
        $businessBranchIds = array_keys(CHtml::listData($businessBranches, 'id', 'name'));
        $userBusinessProfile = UserBusinessProfile::model()->findByAttributes(['user_fk' => $userId]);
        $customerTypeId = empty ($userBusinessProfile) ? 0 : $userBusinessProfile->customer_type;
        $mainBranchModel = BusinessBranch::model()->findByAttributes(['business_fk' => $businessBranch->business_fk, 'default_branch' => 1]);
        $timezoneName = TimezoneMysql::model()->findByPk($mainBranchModel->branch_timezone_fk)->name;

        $userClaimedCoupons = Yii::app()->db->createCommand()
            ->select(array(
                'o.offer_pk',
                'ol.offer_title',
                't.entity_fk',
            ))
            ->from('transaction t')
            ->join('user_offer_story uos', 'uos.transaction_fk=t.transaction_pk')
            ->join('user_offer uo', 'uo.user_offer_pk=uos.user_offer_fk')
            ->join('branch_offer bo', 'bo.offer_punch_fk=uo.offer_fk')
            ->join('offer o', 'o.offer_pk=bo.offer_punch_fk')
            ->join('offer_language ol', 'ol.offer_fk=o.offer_pk')
            ->where('o.enabled=1 AND bo.offer_type = 1 AND bo.business_branch_fk=:branchLogin AND bo.enabled=1')
            ->andwhere(array('IN', 't.transaction_sub_type_fk', [TRX_SUBTYPE_COUPON_GET_COUPON, TRX_SUBTYPE_COUPON_CLAIMED]))
            ->andWhere('t.user_fk=:userId AND o.utc_expires >= NOW()',
                array(':userId' => $userId, ':branchLogin' => $branchId))
            ->andwhere(array('IN', 't.entity_fk', $businessBranchIds))
            ->andWhere('(
                    (o.offer_frequency_fk = 1) OR
                    (o.offer_frequency_fk = 2) OR
                    (o.offer_frequency_fk = 3 and LOCATE(WEEKDAY(CONVERT_TZ(NOW(),@@global.time_zone, :tmzStore)) + 1, o.freq_details) > 0 AND
                        TIME(CONVERT_TZ(NOW(),@@global.time_zone, :tmzStore)) BETWEEN peak_from AND peak_to) OR
                    (o.offer_frequency_fk = 4)
                )', [':tmzStore' => $timezoneName])
            ->group('o.offer_pk')
            ->queryAll();

        $listUserClaimedCoupons = array_keys(CHtml::listData($userClaimedCoupons, 'offer_pk', 'total'));

        $targetedTiersCustomersQuery = Yii::app()->db->createCommand()
            ->select(array(
                'o.offer_pk'
            ))
            ->from('offer o')
            ->join('branch_offer bo', 'bo.offer_punch_fk=o.offer_pk')
            ->where('o.enabled=1 AND o.targeted_offer_flag = 2 AND bo.enabled=1 AND bo.offer_type = 1 
                AND FIND_IN_SET(:customerTypeId, (o.offer_user_tiers))',
                array(':customerTypeId' => $customerTypeId))
            ->andwhere(array('NOT IN', 'o.offer_pk', $listUserClaimedCoupons))
            ->andWhere('o.utc_publish <= NOW() AND o.utc_expires >= NOW() AND o.offer_type_fk IN (8, 9, 10) AND o.coupon_auto_get=1')
            ->andwhere(array('IN', 'bo.business_branch_fk', $businessBranchIds))
            ->andWhere('(
                    (o.offer_frequency_fk = 1) OR
                    (o.offer_frequency_fk = 2) OR
                    (o.offer_frequency_fk = 3 and LOCATE(WEEKDAY(CONVERT_TZ(NOW(),@@global.time_zone, :tmzStore)) + 1, o.freq_details) > 0 AND
                        TIME(CONVERT_TZ(NOW(),@@global.time_zone, :tmzStore)) BETWEEN peak_from AND peak_to) OR
                    (o.offer_frequency_fk = 4)
                )')
            ->group('o.offer_pk')
            ->getText();


        $targetedAllCustomersQuery = Yii::app()->db->createCommand()
            ->select(array(
                'o.offer_pk'
            ))
            ->from('offer o')
            ->join('branch_offer bo', 'bo.offer_punch_fk=o.offer_pk')
            ->where('o.enabled=1 AND o.targeted_offer_flag = 0 AND bo.enabled=1 AND bo.offer_type = 1')
            ->andwhere(array('NOT IN', 'o.offer_pk', $listUserClaimedCoupons))
            ->andWhere('o.utc_publish <= NOW() AND o.utc_expires >= NOW() AND o.offer_type_fk IN (8, 9, 10) AND o.coupon_auto_get=1')
            ->andwhere(array('IN', 'bo.business_branch_fk', $businessBranchIds))
            ->andWhere('(
                    (o.offer_frequency_fk = 1) OR
                    (o.offer_frequency_fk = 2) OR
                    (o.offer_frequency_fk = 3 and LOCATE(WEEKDAY(CONVERT_TZ(NOW(),@@global.time_zone, :tmzStore)) + 1, o.freq_details) > 0 AND
                        TIME(CONVERT_TZ(NOW(),@@global.time_zone, :tmzStore)) BETWEEN peak_from AND peak_to) OR
                    (o.offer_frequency_fk = 4)
                )')
            ->group('o.offer_pk')
            ->getText();

        $finalQuery = " ($targetedTiersCustomersQuery) union ($targetedAllCustomersQuery)";

        $result = Yii::app()->db->createCommand($finalQuery)
            ->bindParam(":customerTypeId", $customerTypeId, PDO::PARAM_STR)
            ->bindParam(":tmzStore", $timezoneName)
            ->queryAll();

        return $result;
    }

    /**
     * Get user full name or email/phone/username
     * For current user model
     * IMPORTANT: must not be called statically. Ex: $user->getFullName();
     *
     * Used in : Website()
     *
     * @version January 2017 release V4.7.1
     * @since 4.7.1
     * <AUTHOR>
     * @access public
     * @return string
     */
    public function getFullName()
    {
        if ($this == null) {
            return '';
        }

        $name = trim((string)$this->first . ' ' . $this->last);

        if ($name) {
            return $name;
        } elseif ($this->email) {
            return $this->email;
        } elseif ($this->phone) {
            return $this->phone;
        } elseif ($this->username) {
            return $this->username;
        } else {
            return '';
        }
    }

    /**
     * Getting the username for a specific user, checking for crm flag
     *
     * Used in : Website, Iphone, Tablet
     *
     * @param userId
     * @access public
     * @return string username
     */

    public static function getUserDisplayNameCrm($userID, $businessId, $showEmailPhone = false)
    {
        $userModel = User::model()->findByPk($userID);

        if ($userModel == null) {
            return null;
        }

        if ($userModel->first != null || $userModel->last != null || $userModel->last != '') {
            $userName = '';
            if ($userModel->first != null) $userName = $userModel->first . ' ';

            if ($userModel->last != null) $userName = $userName . $userModel->last;

            return $userName;
        } else {
            $businessRulesRecord = BusinessRules::model()->findByAttributes(array('business_fk' => $businessId));
            if ($businessRulesRecord->crm_flag == 0) {
                $displayName = 'Hidden';
            } else {
                if ($userModel->username == $userModel->email) {
                    $pos = strrpos((string)$userModel->username??'', "@");
                    if ($pos === false) {
                        $displayName = $userModel->username;
                    } else {
                        $displayName = substr((string)$userModel->username??'', 0, $pos);
                    }
                } else {
                    if ($showEmailPhone) {
                        if ($userModel->email)
                            $displayName = $userModel->email;
                        else if ($userModel->phone)
                            $displayName = $userModel->phone;
                        else
                            $displayName = $userModel->username;
                    } else
                        $displayName = $userModel->username;
                }
            }
            return $displayName;
        }
    }

    /**
     * Getting the user ids who have made more/less balance
     * CASL Flag per business implementation (3.4.0)
     * Used in : Website (BusinessAdmin - Marketing Campaign)
     *
     * @version V3.7.0
     * <AUTHOR> David
     * @access public
     * @param array $entities - branch ids
     * @param int $entityTypeFk
     * @param string $trxCon
     * @return array
     */
    public static function campaignCustomersBalance($entityTypeFk, $trxCon = '>=1')
    {
        return Yii::app()->db->createCommand()
            ->select(array(
                'uep.user_fk',
                'uep.current_active_points',
            ))
            ->from('user_entity_points uep')
            ->join('user u', 'u.id = uep.user_fk')
            ->where('u.enabled=1 AND uep.entity_fk=:entityTypeFk', array('entityTypeFk' =>$entityTypeFk))
            ->having('current_active_points' . $trxCon)
            ->queryAll();
    }


    /**
     * Transfer business user balance to another user
     *
     * Used in : Merchant App
     *
     * @version V4.7.0
     * <AUTHOR> David
     * @access public
     * @return array
     */
    public static function adjustUsersBusinessBalance(...$params)
    {
        $userIdFrom = $params[0];
        $userIdTo = $params[1];
        $businessId = $params[2];
        $userBusinessBalance = $params[3];
        $entityType = $params[4];
        $timeZoneId = $params[5];
        $platformId = $params[6];

        $userBalanceFrom = UserEntityPoints::model()->findByAttributes(['user_fk' => $userIdFrom, 'entity_fk' => $businessId]);
        if ($userBalanceFrom) {
            $userBalanceFrom->utc_updated = date('Y-m-d H:i:s');
            if ($entityType == '1') {
                $userBalanceFrom->current_active_points = 0;
                $userBalanceFrom->update(['utc_updated','current_active_points']);
            } else {
                $userBalanceFrom->current_punches = 0;
                $userBalanceFrom->update(['utc_updated','current_punches']);
            }

            $userBalanceTo = UserEntityPoints::model()->findByAttributes(['user_fk' => $userIdTo, 'entity_fk' => $businessId]);

            if (!$userBalanceTo) {
                UserEntityPoints::createUserEntityPointsV330($timeZoneId, $userIdTo, $businessId, $entityType, $userBusinessBalance);
            } else {
                $userBalanceTo->utc_updated = date('Y-m-d H:i:s');
                if ($entityType == '1') {
                    $userBalanceTo->current_active_points = $userBalanceTo->current_active_points + $userBusinessBalance;
                    $userBalanceTo->update(['utc_updated','current_active_points']);
                } else {
                    $userBalanceTo->current_punches = $userBalanceTo->current_punches + $userBusinessBalance;
                    $userBalanceTo->update(['utc_updated','current_punches']);
                }
            }
        }
    }

    /**
     * Get The Claim Offers by User and Branch
     *
     * Used in : Merchant App
     *
     * @version V4.5.0
     * <AUTHOR> David
     * @access public
     * @return array
     */
    public static function claimUserCouponBranch(...$params)
    {
        $userId = $params[0];
        $branchId = $params[1];
        $offerPk = $params[2];
        $platformId = $params[3];
        $amount = $params[4];

        $branchModel = BusinessBranch::model()->findByPk($branchId);
        $businessId = $branchModel->business_fk;
        $businessModel = Business::model()->findByPk($businessId);
        $businessTypeFk = $businessModel->entity_type_fk;
        $branchType = $businessModel->entity_type_fk+1;
        $platformName = PlatformSolution::model()->findByPk($platformId)->name;
        $mainBranch = BusinessBranch::model()->findByAttributes(['business_fk' => $businessId, 'default_branch' => 1]);

        $businessRules = BusinessRules::model()->findByAttributes(['business_fk' => $businessId]);
        $siloBusiness = ($businessRules != null && $businessRules->silo_flag == 1) ? 1 : 0;
        $offer = Offer::model()->findByPk($offerPk);

        $userModel = User::model()->findByPk($userId);

        $trxCouponClaimed = new Transaction;
        $trxCouponClaimed->utc_created = date('Y-m-d H:i:s');
        $trxCouponClaimed->transaction_type_fk = TRX_TYPE_POINTS_PURCHASE;
        $trxCouponClaimed->transaction_sub_type_fk = TRX_SUBTYPE_COUPON_CLAIMED;
        $trxCouponClaimed->user_fk = $userId;
        $trxCouponClaimed->entity_fk = $branchId;
        $trxCouponClaimed->entity_type_fk = $branchType;
        $trxCouponClaimed->timezone_mysql_fk = $mainBranch->branch_timezone_fk;
        Utils::saveModel($trxCouponClaimed);

        $couponClaimedTrxId = $trxCouponClaimed->getPrimaryKey();

        if ($amount > 0) {
            $purchaseModel = new Purchases();
            $purchaseModel->transaction_fk = $couponClaimedTrxId;
            $purchaseModel->user_clerk_fk = $userId;
            $purchaseModel->amount = $amount;
            $purchaseModel->punch_value = 0;
            $purchaseModel->timezone_mysql_fk = $mainBranch->branch_timezone_fk;
            $purchaseModel = Utils::saveModel($purchaseModel);
            $purchaseId = $purchaseModel->primaryKey;
        }

        //activity for Referrer
        Activity::createActivityRecordV330(
            $mainBranch->branch_timezone_fk,
            $trxCouponClaimed->getPrimaryKey(),
            'ACTIVITY_COUPON_CLAIM',
            0,
            null,
            date('Y-m-d H:i:s'),
            $siloBusiness
        );

        Notifications::createNotificationRecordV341([
            'notificationRequester' => 41,
            'notification_text' => 'NOTIF_TEXT_COUPON_CLAIM',
            'notificationType' => 2,
            'user_receiver_fk' => $userId,
            'user_sender_fk' => $userId,
            'business_fk' => $businessId,
            'business_branch_fk' => $branchId,
            'timezone_mysql_fk' => $mainBranch->branch_timezone_fk,
            'offer_fk' => $offerPk,
            'units_exchange' => 0,
        ]);

        $userOffer = UserOffer::model()->findByAttributes(['user_fk' => $userId, 'offer_fk' => $offerPk]);

        $origCoupon = UserOfferStory::model()->findByAttributes([
            'user_offer_fk' => $userOffer->user_offer_pk,
        ],[
            'condition' => 'qrcode IS NOT NULL'
        ]);

        if ($userOffer) {
            if ($offer->coupon_perm_available == 0) {
                $userOffer->utc_updated = date('Y-m-d H:i:s');
                $userOffer->coupon_applied = 1;
                $userOffer->current_offer_status = TRX_SUBTYPE_COUPON_CLAIMED;
                $userOffer->update(['utc_updated','coupon_applied','current_offer_status']);

                UserOfferStory::model()->updateAll([
                    'coupon_redeemed' => 1, // Mark as redeemed
                    ],
                    'user_offer_fk = :user_offer_fk AND qrcode = :qrcode', [
                    ':user_offer_fk' => $userOffer->user_offer_pk,
                    ':qrcode' => $origCoupon ? $origCoupon->qrcode : null,
                ]);
            }
        }

        $couponUserOfferStory = new UserOfferStory;
        $couponUserOfferStory->utc_created = date('Y-m-d H:i:s');
        $couponUserOfferStory->user_offer_fk = $userOffer->user_offer_pk;
        $couponUserOfferStory->transaction_fk = $couponClaimedTrxId;
        $couponUserOfferStory->timezone_mysql_fk = $mainBranch->branch_timezone_fk;
        $couponUserOfferStory->qrcode = $origCoupon ? $origCoupon->qrcode : null; // Keep the same coupon qrcode
        $couponUserOfferStory->coupon_redeemed = 1;
        $couponUserOfferStory->coupon_locked = 1;
        Utils::saveModel($couponUserOfferStory);
    }

    /**
     * Get The Claim Offers by User and Branch
     *
     * Used in : Merchant App
     *
     * @version V4.5.0
     * <AUTHOR> David
     * @access public
     * @return array
     */
    public static function getUserClaimOffersbyBranch($userId, $branchId)
    {
        $businessBranch = BusinessBranch::model()->findByPk($branchId);
        $businessBranches = BusinessBranch::getAllBranchesByBusinessId($businessBranch->business_fk);
        $businessBranchIds = array_keys(CHtml::listData($businessBranches, 'id', 'name'));

        $mainBranchModel = BusinessBranch::model()->findByAttributes(['business_fk' => $businessBranch->business_fk, 'default_branch' => 1]);
        $timezoneName = TimezoneMysql::model()->findByPk($mainBranchModel->branch_timezone_fk)->name;

        $query = Yii::app()->db->createCommand()
            ->select(array(
                'o.offer_pk',
                'ol.offer_title',
                'ol.offer_description',
                'ol.offer_terms_conditions',
                'o.offer_image1_thumbnail',
                'o.offer_image1_large',
                'o.regular_price',
                'o.discounted_price',
                'o.discount_value',
                'o.offer_type_fk',
                'o.coupon_expires_in',
                'uos.coupon_locked',
                'uos.qrcode',
                'DATEDIFF(NOW(), uos.utc_created) as elapsed_days',
                'IF(o.coupon_expires_in > 0, o.coupon_expires_in, 9999) as coupon_expires_virt',
                'o.never_expires_flag',
                'uos.user_offer_story_pk',
                'CASE
                       WHEN o.coupon_expires_in>0 THEN DATE_ADD(uos.coupon_claimed_at, INTERVAL o.coupon_expires_in DAY)
                       WHEN o.coupon_expires_at IS NOT NULL THEN o.coupon_expires_at
                       ELSE "'.DEFAULT_REWARD_EXPIRY_DATE .'"
                 END as utc_coupon_expiry',
                'o.offer_type_fk as offer_type_id',
                'CASE 
                       WHEN o.coupon_perm_available = 1 THEN o.utc_expires
                       WHEN o.coupon_expires_in>0 THEN DATE_ADD(uos.coupon_claimed_at, INTERVAL o.coupon_expires_in DAY)
                       WHEN o.coupon_expires_at IS NOT NULL THEN o.coupon_expires_at      
                       ELSE IF(o.never_expires_flag = 1, null, o.utc_expires)    
                     END as utc_expires',
                "IF(o.never_expires_flag = 1, null, DATE(CONVERT_TZ(utc_expires, @@global.time_zone, '" . $timezoneName . "'))) as date_expires"
            ))
            ->from('transaction t')
            ->join('user_offer_story uos', 'uos.transaction_fk=t.transaction_pk')
            ->join('user_offer uo', 'uo.user_offer_pk=uos.user_offer_fk')
            ->join('branch_offer bo', 'bo.offer_punch_fk=uo.offer_fk AND bo.business_branch_fk='.$branchId)
            ->join('offer o', 'o.offer_pk=bo.offer_punch_fk')
            ->join('offer_language ol', 'ol.offer_fk=o.offer_pk')
            ->where('o.enabled = 1 AND bo.offer_type = 1 AND bo.enabled = 1 AND ol.language_fk = 1')
            ->andwhere(array('in', 't.transaction_sub_type_fk', [TRX_SUBTYPE_COUPON_GET_COUPON]))
            ->andWhere('uos.coupon_redeemed = 0 AND uos.coupon_locked = 0')// Do not show locked and redeemed
            ->andWhere('uo.coupon_applied = 0 AND t.user_fk=:userId AND uo.user_fk = t.user_fk AND o.utc_expires >= NOW()')
            ->andwhere(array('IN', 't.entity_fk', $businessBranchIds))
            ->andWhere('DATEDIFF(NOW(), uos.utc_created) <= IF(o.coupon_expires_in > 0, o.coupon_expires_in, 9999)')
            ->andWhere('o.utc_publish <= NOW()')
            ->andWhere('(
                    (o.offer_frequency_fk = 1) OR
                    (o.offer_frequency_fk = 2) OR
                    (o.offer_frequency_fk = 3 and LOCATE(WEEKDAY(CONVERT_TZ(NOW(),@@global.time_zone, :tmzStore)) + 1, o.freq_details) > 0 AND
                        TIME(CONVERT_TZ(NOW(),@@global.time_zone, :tmzStore)) BETWEEN peak_from AND peak_to) OR
                    (o.offer_frequency_fk = 4)
                )')
            ->getText();

        $lastCouponQueryForOffer= Yii::app()->db->createCommand()
            ->select(array(
                'max(uos.user_offer_story_pk)'
            ))
            ->from('transaction t')
            ->join('user_offer_story uos', 'uos.transaction_fk=t.transaction_pk')
            ->join('user_offer uo', 'uo.user_offer_pk=uos.user_offer_fk')
            ->join('branch_offer bo', 'bo.offer_punch_fk=uo.offer_fk AND bo.business_branch_fk='.$branchId)
            ->join('offer o', 'o.offer_pk=bo.offer_punch_fk')
            ->join('offer_language ol', 'ol.offer_fk=o.offer_pk')
            ->where('o.enabled = 1 AND bo.offer_type = 1 AND bo.enabled = 1 AND ol.language_fk = 1')
            ->andwhere(array('in', 't.transaction_sub_type_fk', [TRX_SUBTYPE_COUPON_GET_COUPON]))
            ->andWhere('t.user_fk=:userId AND uo.user_fk = t.user_fk AND o.utc_expires >= NOW()')
            ->andwhere(array('IN', 't.entity_fk', $businessBranchIds))
            ->group('o.offer_pk')
            ->getText();

        $result = Yii::app()->db->createCommand('select * from (' . $query . ') as t1 where t1.user_offer_story_pk in ('.$lastCouponQueryForOffer.')');
        $result->bindParam(":userId", $userId);
        $result->bindParam(':tmzStore', $timezoneName);
        return $result->queryAll();
    }

    /**
     * used in smartphone (iphone/android)
     * @param unknown $userID
     * @param number $width
     * @param number $height
     * @param string $debug
     * @return NULL|multitype:unknown Ambigous <number> NULL |multitype:string unknown Ambigous <number>
     */
    public static function getUserImage($userID, $width = 66, $height = 66, $debug = false)
    {
        $user = User::model()->findByPk($userID);
        if ($user == null) {
            return null;
        }

        // TODO: the db entri has an extra slashin the begining.
        $imagePath = $user->image;

        if (User::doesImageExist($imagePath) && $imagePath != null) {

            $resizedDim = User::getResizedImageDiminesions($imagePath, $width, $height);
            return array('height' => $resizedDim['height'], 'width' => $resizedDim['width'], 'fullPath' => $user->image);
        } else {
            $userModel = User::model()->findByPk($userID);

            if ($debug == true) {

            }

            if ($userModel->gender == 2) {
                $resizedDim = User::getResizedImageDiminesions('images/user/MaleDipPic.png', $width, $height);
                return array('height' => $resizedDim['height'], 'width' => $resizedDim['width'], 'fullPath' => 'images/user/MaleDipPic.png');
            } else if ($userModel->gender == 1) {
                $resizedDim = User::getResizedImageDiminesions('images/user/FemaleDipPic.png', $width, $height);
                return array('height' => $resizedDim['height'], 'width' => $resizedDim['width'], 'fullPath' => 'images/user/FemaleDipPic.png');
            } else {
                $resizedDim = User::getResizedImageDiminesions('images/user/user.png', $width, $height);
                return array('height' => $resizedDim['height'], 'width' => $resizedDim['width'], 'fullPath' => 'images/user/user.png');
            }
        }
    }

    public static function getResizedImageDiminesions($image, $newWidth = 66, $newHeight = 66)
    {
        // return height and width for the image to fit into a 66 by 66 px box.
        // the return value would be an array with keys width and height.
        // image is the path to the picture e.g. photos/business/1.jpg
        $dim = getimagesize($image);

        $height = $dim[1];
        $width = $dim[0];

        if ((int)$height < (int)$width) {
            if ($width > 0) {
                $newHeight = (int)($newWidth * $height / $width);
            } else {
                $newHeight = (int)$height;
            }
        } else {
            if ($height > 0) {
                $newWidth = (int)($newHeight * $width / $height);
            } else {
                $newWidth = (int)$width;
            }
        }

        return array('height' => $newHeight, 'width' => $newWidth);
    }

    public static function doesImageExist($fullPath)
    {
        if ($fullPath == null) {
            return false;
        }

        try {
            $logoFile = Yii::app()->file->set($fullPath, true);

            if ($logoFile->exists) {
                return true;
            } else {
                return false;
            }

        } catch (Exception $e) {
            var_dump($e->getTrace());
            die();
        }
    }

    public function getUsersAllNotifications($notificationType, $userId = null)
    {
        if ($userId == null) {
            $userId = Yii::app()->user->id;
        }

        $lang = Yii::app()->language;
        $sql = 'select * from notifications where userId = :userId and notificationType = :notificationType and isHidden = 0 order by notificationDate desc';
        if ($notificationType == 2) {
            $sqlUpdate = 'update notifications set isRead = 1 where userId = :userId and notificationType = 2';
            $cmd = Yii::app()->db->createCommand($sqlUpdate);
            $cmd->bindParam(":userId", $userId, PDO::PARAM_STR);
            $cmd->execute();
        }
        return new CSqlDataProvider($sql, array('keyField' => 'id', 'pagination' => array(
            'pageSize' => 4,
        ), 'params' => array(':userId' => $userId, ':notificationType' => $notificationType)));

    }

    /**
     * Search for User's Friends and NO Friends (friend request pending or not friend yet).
     * In general it checks all User's that exists on TrakTrok System
     *
     * Used in : Website (Friend Search on Friends index option (autocomplete search) / Friends Search Result)
     * @access public
     * @return Array()
     */
    public function searchUserFriendsByNameAndFName($searchKeyWord, $userId = null)
    {
        if ($userId == null) {
            $userId = Yii::app()->user->id;
        }

        $sql = "SELECT *,(if(first is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),if(last is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),concat(first,' ',last)))) as dispname,
					'1' as status,(select name from countries c where c.country_pk = country_fk) as country, (select ci.name from cities ci where ci.city_pk = city_fk) as city
				FROM user
				WHERE id IN(select friend_id from friend where user_id = :userId and status = 1) and if(first is NULL,username like :searchKeyword,if(last is NULL,username like :searchKeyword, concat_ws(' ', `first`,`last`) like :searchKeyword or concat_ws(' ', `last`,`first`) like :searchKeyword))
				UNION ALL
				SELECT *,(if(first is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),if(last is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),concat(first,' ',last)))) as dispname,
					'2' as status,(select name from countries c where c.country_pk = country_fk) as country, (select ci.name from cities ci where ci.city_pk = city_fk) as city
				FROM user
				WHERE id IN(select friend_id from friend where user_id = :userId and status = 2) and if(first is NULL,username like :searchKeyword,if(last is NULL,username like :searchKeyword, concat_ws(' ', `first`,`last`) like :searchKeyword or concat_ws(' ', `last`,`first`) like :searchKeyword))
				UNION ALL
				SELECT *,(if(first is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),if(last is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),concat(first,' ',last)))) as dispname,
					'0' as status,(select name from countries c where c.country_pk = country_fk) as country, (select ci.name from cities ci where ci.city_pk = city_fk) as city
				FROM user
				WHERE id != :userId and id not in(select friend_id from friend where user_id = :userId and status in(1,2,3)) and if(first is NULL,username like :searchKeyword,if(last is NULL,username like :searchKeyword, concat_ws(' ', `first`,`last`) like :searchKeyword or concat_ws(' ', `last`,`first`) like :searchKeyword))";
        return new CSqlDataProvider($sql, array('keyField' => 'id', 'pagination' => false, 'params' => array(':userId' => $userId, ':searchKeyword' => '%' . $searchKeyWord . '%')));
    }

    /**
     * Search for User's Friends. If $friendIds is not null, these $friendIds are not included on the search
     *
     * Used in : Website (Friend Search on Sharing Offer option)
     * @access public
     * @return Array()
     */
    public function searchFriendsByNameAndFName($searchKeyWord, $friendIds)
    {
        $userId = Yii::app()->user->id;
        if ($friendIds != '' && $friendIds != null) {
            $sql = "select *,(if(first is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),if(last is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),concat(first,' ',last)))) as dispname  from user where id in(select friend_id from friend where user_id = :userId and status = 1 ) and if(first is NULL,username like :searchKeyword,if (last is NULL,username like  :searchKeyword,concat_ws(' ', `first`,`last`) like :searchKeyword or concat_ws(' ', `last`,`first`) like :searchKeyword)) and id not in (" . $friendIds . ")";
        } else {
            $sql = "select *,(if(first is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),if(last is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),concat(first,' ',last)))) as dispname from user where id in(select friend_id from friend where user_id = :userId and status = 1 ) and if(first is NULL,username like :searchKeyword,if (last is NULL,username like  :searchKeyword,concat_ws(' ', `first`,`last`) like :searchKeyword or concat_ws(' ', `last`,`first`) like :searchKeyword))";
        }

        return new CSqlDataProvider($sql, array('keyField' => 'id', 'pagination' => false, 'params' => array(':userId' => $userId, ':searchKeyword' => '%' . $searchKeyWord . '%')));
    }

    public function searchFriendsToRefer($searchKeyWord, $friendIds, $busId)
    {
        $userId = Yii::app()->user->id;
        if ($friendIds != '' && $friendIds != null) {
            $sql = "select *,(if(first is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),if(last is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),concat(first,' ',last)))) as dispname  from user where user.id NOT IN (select user_fk from followers f where user.id = f.user_fk AND entity_fk = :busId AND entity_type='BUSINESS') AND id in(select friend_id from friend where user_id = :userId and status = 1 ) and if(first is NULL,username like :searchKeyword,if (last is NULL,username like  :searchKeyword,concat_ws(' ', `first`,`last`) like :searchKeyword or concat_ws(' ', `last`,`first`) like :searchKeyword)) and id not in (" . $friendIds . ")";
        } else {
            $sql = "select *,(if(first is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),if(last is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),concat(first,' ',last)))) as dispname from user where user.id NOT IN (select user_fk from followers f where user.id = f.user_fk AND entity_fk = :busId AND entity_type='BUSINESS') AND id in(select friend_id from friend where user_id = :userId and status = 1 ) and if(first is NULL,username like :searchKeyword,if (last is NULL,username like  :searchKeyword,concat_ws(' ', `first`,`last`) like :searchKeyword or concat_ws(' ', `last`,`first`) like :searchKeyword))";
        }

        return new CSqlDataProvider($sql, array('keyField' => 'id', 'pagination' => false, 'params' => array(':userId' => $userId, 'busId' => $busId, ':searchKeyword' => '%' . $searchKeyWord . '%')));
    }

    /**
     * Getting the username for a specific user
     *
     * Used in : Website, Iphone, Tablet
     *
     * @param userId
     * @access public
     * @return string username
     */
    public static function getUserDisplayName($userID, $showEmailPhone = false)
    {
        $userModel = User::model()->findByPk($userID);

        if ($userModel == null) {
            return null;
        }

        if ($userModel->first != null && $userModel->last != null && $userModel->last != '') {
            $displayName = $userModel->first . ' ' . $userModel->last;
//             if(strlen((string)$displayName) > 15)
            //              {
            //                   $displayName = substr((string)$displayName,0,15).'..';
            //              }
            return $displayName;
        } else {
            if ($userModel->username == $userModel->email) {
                $pos = strrpos((string)$userModel->username, "@");
                if ($pos === false) {
                    // note: three equal signs
                    // not found...
                    $displayName = $userModel->username;
                } else {
                    $displayName = substr((string)$userModel->username??'', 0, $pos);
                    //$displayName = substr((string)$userModel->username,0, $pos + 1).'...';
                }
            } else {
                if ($showEmailPhone) {
                    if ($userModel->email)
                        $displayName = $userModel->email;
                    else if ($userModel->phone)
                        $displayName = $userModel->phone;
                    else
                        $displayName = $userModel->username;
                } else
                    $displayName = $userModel->username;
            }

//             if(strlen((string)$displayName) > 15)
            //             {
            //                 $displayName = substr((string)$displayName,0,15).'..';
            //             }
            return $displayName;
        }
    }

    /**
     * Get user real name if name is not empty
     * @param $userID
     * @return string
     */
    public static function getUserPersonName($userID)
    {
        $userModel = User::model()->findByPk($userID);
        $displayName = '';
        if (isset($userModel)) {
            if (!empty($userModel->first) && empty($userModel->last)) {
                $displayName = $userModel->first;
            } elseif (!empty($userModel->first) && !empty($userModel->last)) {
                $displayName = $userModel->first . ' ' . $userModel->last;
            } elseif (empty($userModel->first) && !empty($userModel->last)) {
                $displayName = $userModel->last;
            }
        }
        return $displayName;
    }

    //TODO: DELETE - Val Feb 2018
//    public function validatePassword($password)
//    {
//        return ($this->hashPassword($password, $this->email) === $this->password);
//    }

    /**
     * to validate if the password is correct for a specific username
     * @param string $password
     * @param string $email
     * @param string $userPassword
     * @return boolean
     * Used in smartphone (iphone/android)
     */
    //TODO: DELETE - Val Feb 2018
//    public function validateUserPassword($password, $email, $userPassword)
//    {
//        return ($this->hashPassword($password, $email) === $userPassword);
//    }

    //TODO: DELETE and use cryptPassword instead - Val Feb 2018
//    public function hashPasswordOld($password, $salt)
//    {
//        //return md5($this->salt.$password);
//        return md5($password . $salt);
//    }

    // Simple yet efficient way for password hashing
    //TODO: DELETE and use cryptPassword instead - Val Feb 2018
//    public function hashPassword($password, $salt)
//    {
//        return sha1(md5($salt) . $password);
//    }

    /**
     * New function for hashing passwords using crypt
     *
     * Used on : Website (signup, login)
     *
     * @version Website May-June 2014 Release
     * @param string $input - unhashed password
     * @access public
     * @return string hash
     */
    public static function cryptPassword($input)
    {
        $rounds = 10;
        $salt = "";
        $salt_chars = array_merge(range('A', 'Z'), range('a', 'z'), range(0, 9));
        for ($i = 0; $i < 22; $i++) {
            $salt .= $salt_chars[array_rand($salt_chars)];
        }
        return crypt($input ?? '', sprintf('$2a$%02d$', $rounds) . $salt);
    }

    /**
     * Comparing the password from input with hash
     *
     * Used on : Website(login)
     *
     * @version Website May-June 2014 Release
     * @param string $input - unhashed password
     * @param string $hash - hashed password
     * @access public
     * @return true/false
     */
    public static function passwordVerify($input, $hash)
    {
        if ($hash && crypt($input??'', $hash) === $hash) {
            return true;
        }

        return false;
    }

    public function encrypt($value)
    {
        return md5($value??'');
    }

    public function validateBirthday($attribute, $params = array())
    {
        $birthday = $this->birthday_field;
        //CVarDumper::dump($birthday);
        if ($birthday['month'] == 0 && $birthday['day'] == 0 && $birthday['year'] == 0) {
            return true;
        }

        if (isset($birthday['month'], $birthday['day'], $birthday['year'])) {
            if (!checkdate($birthday['month'], $birthday['day'], $birthday['year'])) {
                $this->addError('birth_date', 'Please select a valid birthday');
            }

        }
    }

    // Generate a random readable password
    public static function generatePassword($minLength = 5, $maxLength = 10)
    {
        $length = random_int($minLength, $maxLength);

        $letters = 'bcdfghjklmnpqrstvwxyz';
        $vowels = 'aeiou';
        $code = '';
        for ($i = 0; $i < $length; ++$i) {
            if ($i % 2 && random_int(0, 10) > 2 || !($i % 2) && random_int(0, 10) > 9) {
                $code .= $vowels[random_int(0, 4)];
            } else {
                $code .= $letters[random_int(0, 20)];
            }

        }
        return $code;
    }

    public static function getFriends($id)
    {
        $cmd = Yii::app()->db->createCommand(
            'SELECT friend_pk FROM friend WHERE accepted is not null and user_fk = ' . $id
            . ' union ' .
            'SELECT user_fk from friend where accepted is not null and friend_pk = ' . $id
        );
        $result = $cmd->queryColumn();
        return $result;
    }

    //Returns all the businesses sorted by name ascendently
    public static function loadUsers()
    {
        $result = array();
        $sql = 'SELECT `id`, `first`, `last`, `username` FROM `user` ORDER BY `name` ASC';
        $cmd = Yii::app()->db->createCommand($sql);
        $users = $cmd->queryAll(true);

        foreach ($users as $user) {
            $result[$user['id']] = $user['first'] . $user['last'] . "(" . $user['username'] . ")";
        }
        return $result;
    }

    /**
     * checking if the application has already lunched or it's the first time
     *
     * Used in : Smartphone iOS/Android  => API call actionDidApplicationLunched()
     * Version : April release / iOS V 1.3 / Android  V 1.2
     * @access public
     * @return 1 = yes , 0 = no
     */
    public static function didAppAlreadyLunched($userId)
    {
        return Yii::app()->db->createCommand()
            ->select('app_launched')
            ->from('user')
            ->where('id = :userId', array(':userId' => $userId))
            ->queryScalar();
    }

    /**
     * when the user opens the application for the first time we update app_launched flag
     * for the tutorial not to open for the second time
     *
     * Used in : Smartphone iOS/Android  => API call actionUpdateApplicationHasLunched()
     * Version : April release / iOS V 1.3 / Android  V 1.2
     * @access public
     * @return bool
     */
    public static function updateApplicationHasLunched($userId)
    {
        $user = User::model()->findByPk($userId);
        $user->app_launched = 1;
        if ($user->save(false)) {
            return true;
        } else {
            return false;
        }

    }

    public static function loadUsersDetail($id)
    {
        $cityname = Yii::app()->db->createCommand()
            ->select('c.name')
            ->from('cities c')
            ->join('user p', 'c.city_pk=p.city_fk')
            ->where('p.id=:id', array(':id' => $id))
            ->query();

        $rs1 = $cityname->readAll();
        $result_city = $rs1[0]['name'];
        //region
        $regionname = Yii::app()->db->createCommand()
            ->select('c.name')
            ->from('regions c')
            ->join('user p', 'c.region_pk=p.region_fk')
            ->where('p.id=:id', array(':id' => $id))
            ->query();

        $rs2 = $regionname->readAll();
        $result_region = $rs2[0]['name'];

        //country
        $countryname = Yii::app()->db->createCommand()
            ->select('c.name')
            ->from('countries c')
            ->join('user p', 'c.country_pk=p.country_fk')
            ->where('p.id=:id', array(':id' => $id))
            ->query();

        $rs3 = $countryname->readAll();
        $result_country = $rs3[0]['name'];

        $result = array($result_city, $result_region, $result_country);

        //var_dump($result);
        //die();

        return $result;
    }

    public static function loadUsersModels()
    {
        $sql = 'SELECT * FROM user ORDER BY `id` ASC';
        $cmd = Yii::app()->db->createCommand($sql);
        $users = $cmd->queryAll(true);
        return $users;
    }

    public static function fixUserImagePath($imagePath)
    {
        if ($imagePath != null) {
            $imageBasePath = '/photos/user';
            $bsPosition = strrpos((string)$imagePath, "/");
            $imageName = substr((string)$imagePath, $bsPosition);
            return $imageBasePath . $imageName;
        } else {
            return '';
        }

    }

    public function searchAdminsByNameAndFName($searchKeyWord, $entity_id, $entity_type)
    {
        $userId = Yii::app()->user->id;
        $adminList = $userId;
        //pull all admins already added for this entity
        $sqlAdmin = 'SELECT user_fk FROM user_entity_permission
                WHERE entity_fk=? AND entity_type=?';
        $cmd = Yii::app()->db->createCommand($sqlAdmin);
        $admins = $cmd->queryAll(true, array($entity_id, $entity_type));

        if (isset($admins) && (Count($admins > 0))) {
            foreach ($admins as $admin) {
                $adminList = $adminList . ',' . $admin['user_fk'];
            }
            $adminList = $adminList . ',0';
        }

        $sql = "select *,(if(first is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),if(last is NULL,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),if(first IS Null,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),if(last IS Null,if(INSTR(username,'@')>0,SUBSTRING_INDEX(username, '@',1),username),concat(first,' ',last)))))) as dispname from user
              where enabled = 1 AND id not in (" . $adminList . ") and if(first is NULL,username like :searchKeyword,if (last is NULL,username like  :searchKeyword,if (first IS Null,username like  :searchKeyword,if (last IS Null,username like  :searchKeyword,concat_ws(' ', `first`,`last`) like :searchKeyword or concat_ws(' ', `last`,`first`) like :searchKeyword))))";

        return new CSqlDataProvider($sql, array('keyField' => 'id', 'pagination' => false, 'params' => array(':searchKeyword' => '%' . $searchKeyWord . '%')));
    }

    public static function changeLanguage($lang)
    {
        $userId = Yii::app()->user->id;
        $sqlUpdate = 'update `user` set `language` = ? where id = ?';
        $cmd = Yii::app()->db->createCommand($sqlUpdate);
        $cmd->execute(array($lang, $userId));
    }

    public static function getUserLanguage()
    {
        $userId = Yii::app()->user->id;
        $userModel = User::model()->findByPk($userId);
        return $userModel['language'] ?? null;
    }

    public static function getUserLanguageById($userId)
    {
        $userModel = User::model()->findByPk($userId);
        return $userModel['language'] ?? null;
    }

    # Return if there exists similars username on the DB
    public static function searchUserByUsername($searchKeyWord)
    {
        $sql = "select username from user where username LIKE :searchKeyword ORDER BY TRIM(`username`) ASC";
        return new CSqlDataProvider($sql, array('keyField' => 'id', 'pagination' => false, 'params' => array(':searchKeyword' => '%' . $searchKeyWord . '%')));
    }

    /**
     * Check if the phone number exists for in specific user in case of update and in general in case of
     * adding new record
     * should be used all accross the bord to check the uniqueness of the phone number.
     * Used in smart phone (android/iphone) Website(userActivate)
     * @param int $phone
     * @param int $userId
     * @return bool true or false
     */
    public static function checkPhoneExistence($phone, $userId = null)
    {
        $exists = false;

        if ($userId == null) {
            $result = Yii::app()->db->createCommand()
                ->select("count(1)")
                ->from('user u')
                ->where('u.phone = :phone', array(':phone' => $phone))
                ->queryScalar();
            $exists = $result > 0 ? true : false;
        } else {
            $result = Yii::app()->db->createCommand()
                ->select("count(1)")
                ->from('user u')
                ->where('u.phone = :phone and id <> :userId', array(':phone' => $phone, ':userId' => $userId))
                ->queryScalar();
            $exists = $result > 0 ? true : false;
        }
        return $exists;
    }

    /**
     * Check if the username exists for in specific user in case of update and insert
     * Used Website(UserActivate)
     * @param string $username
     * @param int $userId
     * @return bool true or false
     */
    public static function checkUsernameExistence($username, $userId = null)
    {
        $exists = false;

        if ($userId == null) {
            $result = Yii::app()->db->createCommand()
                ->select("count(1)")
                ->from('user u')
                ->where('u.username = :username', array(':username' => $username))
                ->queryScalar();
            $exists = $result > 0 ? true : false;
        } else {
            $result = Yii::app()->db->createCommand()
                ->select("count(1)")
                ->from('user u')
                ->where('u.username = :username and u.id <> :userId', array(':username' => $username, ':userId' => $userId))
                ->queryScalar();
            $exists = $result > 0 ? true : false;
        }
        return $exists;
    }

    /**
     * Check if the PIN code(Employee ID) entered by the clerk is Valid or not
     * It compares this Clerk PIN code against ALL the Clerks PIN code under this Business
     * A Clerk from the same Business can NOT have the same PIN code (Employee ID)
     * Used in : Android Merchant Tablet
     * Version : Aug - Dec 2014 Release V3.1.0
     * @param businessId $businessId
     * @param string $clerkPin
     * @return boolean YES/ NO
     */
    public static function checkClerkPinExistence($businessId, $clerkPin)
    {
        $result = false;
        $branchIds = BusinessBranch::getAllowedBranchesByBusiness($businessId);
        $clerkInfo = UserPermission::getUserListByRoleByBusiness('clerk', $businessId, $branchIds, false);
        for ($i = 0; $i < count($clerkInfo); $i++) {
            if ($clerkInfo[$i]['pin_enabled'] == 1 && $clerkInfo[$i]['enabled'] == 1) {
                $result = User::passwordVerify($clerkPin, $clerkInfo[$i]['pin_permission']);
            }
            if ($result) {
                $result = array('clerk_id' => $clerkInfo[$i]['id'], 'business_branch_pk' => $clerkInfo[$i]['business_branch_pk']);
                break;
            }
        }
        return $result;
    }

    /**
     * Check if the username or email exists for in specific user in database
     * Used Website (Login), smartphone (iphone/android)
     * @param string $username
     * @param int $userId
     * @return null or userId
     */
    public static function searchUsernameOrEmailExistence($username)
    {
        $userId = null;

        //Searching by username
        if (strpos((string)$username??'', "@") == null) {
            $result = Yii::app()->db->createCommand()
                ->select("u.id")
                ->from('user u')
                ->where('u.username = :username', array(':username' => $username))
                ->queryRow();
            $userId = $result === null ? null : $result['id'] ?? null;
        } else {
            //Searching by email
            $result = Yii::app()->db->createCommand()
                ->select("u.id")
                ->from('user u')
                ->where('u.email = :email', array(':email' => $username))
                ->queryRow();
            $userId = $result === null ? null : $result['id'] ?? null;
        }
        return $userId;
    }

    /*
     * Check if the username, Phone, QRCode or email exists for a specific user in database
     * Note: This version implements UBP
     *
     * Used in : Merchant Android app
     * <AUTHOR> Patricia
     * @version : May - Jun 2015 release V3.3.0
     * @param string $username
     * @return null or userId
     */
    public static function searchUsernameQRPhoneOrEmailExistV420($username, $loginType = null, $countryFk = null, $businessPk = null)
    {
        $userId = null;

        //Searching by username or phone
        if (strpos((string)$username??'', "@") == null) {
            if (ctype_digit($username??'') && $loginType == 1) //phone
            {

                //Patricia - July 16th - Validate the country phone +1, treat CAN and US as the "same country" since both have the same phone country code
                if ($countryFk == 1 || $countryFk == 2) {
                    $result = Yii::app()->db->createCommand()
                        ->select("u.id")
                        ->from('user u')
                        ->where('u.phone = :username AND u.business_fk=:business', array(':username' => $username, ":business" => $businessPk))
                        ->queryRow();
                    $userId = $result === null ? null : $result['id'] ?? null;
                } else {
                    $result = Yii::app()->db->createCommand()
                        ->select("u.id")
                        ->from('user u')
                        ->where('u.phone = :username AND u.country_phone_fk = :countryFk AND u.business_fk=:business', array(':username' => $username, ':countryFk' => $countryFk, ":business" => $businessPk))
                        ->queryRow();
                    $userId = $result === null ? null :  $result['id'] ?? null;
                }
            } elseif ($loginType == 3) //QR code
            {
                //ctype_digit($username) && strlen((string)$username) == 9 &&
                $result = Yii::app()->db->createCommand()
                    ->select("u.id")
                    ->from('user u')
                    ->where('u.qrcode = :username AND u.business_fk=:business', array(':username' => $username, ":business" => $businessPk))
                    ->queryRow();
                $userId = $result === null ? null :  $result['id'] ?? null;
            } elseif ($loginType == 4) //Account Number
            {
                $result = User::checkAccountNumberExistence($username, $businessPk);
                $userId = ($result === false) ? null : $result;
            } else //username
            {
                $result = Yii::app()->db->createCommand()
                    ->select("u.id")
                    ->from('user u')
                    ->where('u.username = :username AND u.business_fk=:business', array(':username' => $username, ":business" => $businessPk))
                    ->queryRow();
                $userId = $result === null ? null :  $result['id'] ?? null;
            }
        } else //Searching by email
        {
            $result = Yii::app()->db->createCommand()
                ->select("u.id")
                ->from('user u')
                ->where('u.email = :email AND u.business_fk=:business', array(':email' => $username, ":business" => $businessPk))
                ->queryRow();
            $userId = $result === null ? null :  $result['id'] ?? null;
        }

        if($loginType == 3 && !$userId) {
            // QR code and user not found
            $result = Yii::app()->db->createCommand()
                ->select('u.id')
                ->from('user u')
                ->where('u.qrcode = :username AND u.business_fk=:business', [
                    ':username' => $username, ':business' => KNG_BUSINESS_ID,
                ])
                ->queryRow();

            if($result) {
                // find The Right user Id for business because the QR code in Members App is different
                $result = Yii::app()->db->createCommand()
                    ->select('user_fk')
                    ->from('relations r')
                    ->where('r.app_user_fk = :userId AND r.business_fk = :business', [
                        ':userId' => $result['id'], ':business' => $businessPk,
                    ])
                    ->queryRow();
                $userId = $result === null ? null : $result['user_fk'] ?? null;
            }
        }
        return $userId;
    }

    /**
     * INTERNATIONAL PhoneNumber - Globalization implementation
     * Check if the username, Phone, QRCode or email exists for a specific user in database
     *
     * Used in : Merchant Android app
     * <AUTHOR> Patricia
     * @version : May - Jun 2015 release V3.3.0
     * @param string $username
     * @return null or userId
     * @todo Delete after UBP release
     */
    public static function searchUsernameQRPhoneOrEmailExistV330($username, $loginType = null, $countryFk = null)
    {
        $userId = null;

        //Searching by username or phone
        if (strpos((string)$username??'', "@") == null) {
            if (ctype_digit($username??'') && $loginType == 1) //phone
            {

                //Patricia - July 16th - Validate the country phone +1, treat CAN and US as the "same country" since both have the same phone country code
                if ($countryFk == 1 || $countryFk == 2) {
                    $result = Yii::app()->db->createCommand()
                        ->select("u.id")
                        ->from('user u')
                        ->where('u.phone = :username', array(':username' => $username))
                        ->queryRow();
                    $userId = $result === null ? null :  $result['id'] ?? null;
                } else {
                    $result = Yii::app()->db->createCommand()
                        ->select("u.id")
                        ->from('user u')
                        ->where('u.phone = :username AND u.country_phone_fk = :countryFk', array(':username' => $username, ':countryFk' => $countryFk))
                        ->queryRow();
                    $userId = $result === null ? null :  $result['id'] ?? null;
                }
            } elseif ($loginType == 3) //QR code
            {
                //ctype_digit($username) && strlen((string)$username) == 9 &&
                $result = Yii::app()->db->createCommand()
                    ->select("u.id")
                    ->from('user u')
                    ->where('u.qrcode = :username', array(':username' => $username))
                    ->queryRow();
                $userId = $result === null ? null :  $result['id'] ?? null;
            } else //username
            {
                $result = Yii::app()->db->createCommand()
                    ->select("u.id")
                    ->from('user u')
                    ->where('u.username = :username', array(':username' => $username))
                    ->queryRow();
                $userId = $result === null ? null :  $result['id'] ?? null;
            }
        } else //Searching by email
        {
            $result = Yii::app()->db->createCommand()
                ->select("u.id")
                ->from('user u')
                ->where('u.email = :email', array(':email' => $username))
                ->queryRow();
            $userId = $result === null ? null :  $result['id'] ?? null;
        }
        return $userId;
    }

    /**
     * Check if the username, Phone, QRCode or email exists for a specific user in database
     * Used in : Merchant Tablet App
     * <AUTHOR> C.
     * @version : Mar - Apr 2015 release V3.2.5
     * @param string $username
     * @return null or userId
     */
//    public static function searchUsernameQRcodePhoneOrEmailExistence($username, $loginType = null)
//    {
//        if (Utils::feature("UBP")) {
//            Yii::log(json_encode([
//              "message" => "Depricated function",
//              "post" => $_POST
//            ]), CLogger::LEVEL_WARNING, __METHOD__);
//            $this->_sendResponse(200, $this->_getJsonResult('NOT_OK', "Depricated version", 200, array()));
//            Yii::app()->end();
//        }
//
//        $userId = null;
//
//        //Searching by username or phone
//        if (strpos((string)$username, "@") == null) {
//            if (ctype_digit($username) && strlen((string)$username) == 10) //phone
//            {
//                $result = Yii::app()->db->createCommand()
//                    ->select("u.id")
//                    ->from('user u')
//                    ->where('u.phone = :username', array(':username' => $username))
//                    ->queryRow();
//                $userId = $result === null ? null :  $result['id'] ?? null;
//            } elseif (ctype_digit($username) && strlen((string)$username) == 9 && $loginType == 3) //QR code
//            {
//                $result = Yii::app()->db->createCommand()
//                    ->select("u.id")
//                    ->from('user u')
//                    ->where('u.qrcode = :username', array(':username' => $username))
//                    ->queryRow();
//                $userId = $result === null ? null :  $result['id'] ?? null;
//            } else //username
//            {
//                $result = Yii::app()->db->createCommand()
//                    ->select("u.id")
//                    ->from('user u')
//                    ->where('u.username = :username', array(':username' => $username))
//                    ->queryRow();
//                $userId = $result === null ? null :  $result['id'] ?? null;
//            }
//        } else //Searching by email
//        {
//            $result = Yii::app()->db->createCommand()
//                ->select("u.id")
//                ->from('user u')
//                ->where('u.email = :email', array(':email' => $username))
//                ->queryRow();
//            $userId = $result === null ? null :  $result['id'] ?? null;
//        }
//        return $userId;
//    }

    /**
     * Update: this update includes the country id that covers the
     * Check if the username, Phone or email exists for a specific user in database
     *
     * Used Website (Login), smartphone (iphone/android), Merchant Tablet app
     * @version May June 2015 release V3.3.0
     * <AUTHOR> N.
     * @param string $username
     * @param int $userId
     * @return null or userId
     */
    public static function searchUsernamePhoneOrEmailExistenceV330($username, $countryPk, $businessId)
    {
//        if (Utils::feature("UBP")) {
//            Yii::log(json_encode([
//              "message" => "Depricated function",
//              "post" => $_POST
//            ]), CLogger::LEVEL_WARNING, __METHOD__);
//            $this->_sendResponse(200, $this->_getJsonResult('NOT_OK', "Depricated version", 200, array()));
//            Yii::app()->end();
//        }

        $userId = null;

        //Searching by username or phone
        if (strpos((string)$username??'', "@") == null) {
            if (ctype_digit($username??'')) //phone
            {
                $result = Yii::app()->db->createCommand()
                    ->select("u.id")
                    ->from('user u')
                    ->where('u.phone = :username and country_phone_fk = :countryId', array(':username' => $username, ':countryId' => $countryPk))
                    ->andWhere('u.business_fk = :businessId', [':businessId' => $businessId])
                    ->queryRow();
                $userId = $result === null ? null :  $result['id'] ?? null;
            } else //username
            {
                $result = Yii::app()->db->createCommand()
                    ->select("u.id")
                    ->from('user u')
                    ->where('u.username = :username', [':username' => $username])
                    ->andWhere('u.business_fk = :businessId', [':businessId' => $businessId])
                    ->queryRow();
                $userId = $result === null ? null :  $result['id'] ?? null;
            }

        } else //Searching by email
        {
            $result = Yii::app()->db->createCommand()
                ->select("u.id")
                ->from('user u')
                ->where('u.email = :email', array(':email' => $username))
                ->andWhere('u.business_fk = :businessId', [':businessId' => $businessId])
                ->queryRow();
            $userId = $result === null ? null :  $result['id'] ?? null;
        }
        return $userId;
    }

    /**
     * Check if the username, Phone or email exists for a specific user in database
     * Used Website (Login), smartphone (iphone/android)
     * @param string $username
     * @param int $userId
     * @return null or userId
     */
//    public static function searchUsernamePhoneOrEmailExistenceV210($username)
//    {
//        if (Utils::feature("UBP")) {
//            Yii::log(json_encode([
//              "message" => "Depricated function",
//              "post" => $_POST
//            ]), CLogger::LEVEL_WARNING, __METHOD__);
//            $this->_sendResponse(200, $this->_getJsonResult('NOT_OK', "Depricated version", 200, array()));
//            Yii::app()->end();
//        }
//
//        $result = null;
//        //Searching by username or phone
//        if (strpos((string)$username, "@") == null) {
//            if (ctype_digit($username)) //phone
//            {
//                $result = Yii::app()->db->createCommand()
//                    ->select("u.id,u.enabled")
//                    ->from('user u')
//                    ->where('u.phone = :username', array(':username' => $username))
//                    ->queryRow();
//                $result = $result === null ? null : $result;
//            } else //username
//            {
//                $result = Yii::app()->db->createCommand()
//                    ->select("u.id,u.enabled")
//                    ->from('user u')
//                    ->where('u.username = :username', array(':username' => $username))
//                    ->queryRow();
//                $result = $result === null ? null : $result;
//            }
//
//        } else //Searching by email
//        {
//            $result = Yii::app()->db->createCommand()
//                ->select("u.id,u.enabled")
//                ->from('user u')
//                ->where('u.email = :email', array(':email' => $username))
//                ->queryRow();
//            $result = $result === null ? null : $result;
//        }
//        return $result;
//    }

    /**
     * Get Client/User Credentials (either Phonenumber or Email) in order to Log in to Tablet App
     * It makes sure the User has the 'user' permission role. Not Admins allowed
     * It also includes the PIN status (enables/disabled).
     *
     * Used in : Android Merchant Tablet
     * @since    Jan - Feb 2015 V3.2.0
     * @access    public
     * @return    Array()
     */
    public static function getUserByPhoneOrEmailV320($userId)
    {
        return Yii::app()->db->createCommand()
            ->select(array('id as client_userid', 'first', 'last', 'gender','language',
                'username as client_username', 'email as client_email', 'phone as client_phonenumber', 'country_phone_fk',
                '(if(TRIM(COALESCE(u.image, "")) ="",if(u.gender is null ,"images/user/default_profile_picture.png",if(u.gender = 1,"images/user/default_profile_picture.png","images/user/default_profile_picture.png")),u.image)) as client_photo',
                'enabled', 'usm.status_manager_fk as status_account', 'pin_enabled', 'up.itemname as role',
                'birth_date as client_birthdate',
            ))
            ->from('user u')
            ->leftJoin('user_permission up', 'up.userid = u.id')
            ->leftJoin('user_status_manager usm', 'usm.user_fk = u.id')
            ->where('usm.current_status = 1 AND u.id = :userId', array(':userId' => $userId))
            ->queryRow();
    }

    /**
     * Get Client/User main info, it includes the latest status manager for an specific clerk email/username
     *
     * Used in    Merchant Android app (ClerkLogin)
     * <AUTHOR>
     * @version    Jul - Aug 2015 release V3.3.1
     * @access    public
     * @return    Array()
     */
    public static function getUserStatusByUsernameOrEmail($username)
    {

        if (strpos((string)$username??'', "@") == null) {
            return Yii::app()->db->createCommand()
                ->select(array(
                    'u.id', 'u.username', 'u.enabled', 'u.email',
                    '(Select usm.old_email_phone from  user_status_manager AS usm WHERE  u.id = usm.user_fk AND usm.current_status = 1 limit 1)',
                    '(Select usm.status_manager_fk from  user_status_manager AS usm WHERE  u.id = usm.user_fk AND usm.current_status = 1 limit 1)',
                ))
                ->from('user u')
                ->where('LOWER(u.username=:username)', array(':username' => $username))
                ->limit(1)
                ->queryRow();
        } else {
            if (Utils::feature('UBP')) {
                return Yii::app()->db->createCommand()
                    ->select(array(
                        'u.id', 'u.username', 'u.enabled', 'u.email',
                        '(Select usm.old_email_phone from  user_status_manager AS usm WHERE  u.id = usm.user_fk AND usm.current_status = 1 limit 1)',
                        '(Select usm.status_manager_fk from  user_status_manager AS usm WHERE  u.id = usm.user_fk AND usm.current_status = 1 limit 1)',
                    ))
                    ->from('user u')
                    ->where('business_fk is NULL and LOWER(u.email)=:username ', array(':username' => $username))
                    ->limit(1)
                    ->queryRow();
            } else {
                return Yii::app()->db->createCommand()
                    ->select(array(
                        'u.id', 'u.username', 'u.enabled', 'u.email',
                        '(Select usm.old_email_phone from  user_status_manager AS usm WHERE  u.id = usm.user_fk AND usm.current_status = 1 limit 1)',
                        '(Select usm.status_manager_fk from  user_status_manager AS usm WHERE  u.id = usm.user_fk AND usm.current_status = 1 limit 1)',
                    ))
                    ->from('user u')
                    ->where('LOWER(u.email)=:username ', array(':username' => $username))
                    ->limit(1)
                    ->queryRow();
            }
        }

//        if (strpos((string)$username, "@") == null) {
//            return Yii::app()->db->createCommand()
//                ->select(array('u.id', 'u.username', 'u.enabled', 'u.email', 'usm.old_email_phone', 'usm.status_manager_fk'))
//                ->from('user u')
//                ->join('user_status_manager usm', 'usm.user_fk = u.id')
//                ->where('LOWER(u.username=:username) and usm.current_status = 1', array(':username' => $username))
//                ->limit(1)
//                ->queryRow();
//        } else {
//            return Yii::app()->db->createCommand()
//                ->select(array('u.id', 'u.username', 'u.enabled', 'u.email', 'usm.old_email_phone', 'usm.status_manager_fk'))
//                ->from('user u')
//                ->join('user_status_manager usm', 'usm.user_fk = u.id')
//                ->where('LOWER(u.email)=:username  and usm.current_status = 1', array(':username' => $username))
//                ->limit(1)
//                ->queryRow();
//        }
    }

    /**
     * Validate if the User's username/email exists
     * It checks the value of <<username/email>> - that's why the lower()
     * Aug - Sep 2014 V3.10 - release
     * Used in : Android Tablet (Clerk info login)
     * @access public
     * @return Array()
     */
    //To DELETE ONCE v331 IS ONLINE
    public static function getUserByUserNameOrEmailV310($username)
    {
        if (strpos((string)$username??'', "@") == null) {
            return Yii::app()->db->createCommand()
                ->select(array('u.id', 'u.merchant_reset_psw', 'u.enabled'))
                ->from('user u')
                ->where('LOWER(u.username=:username)', array(':username' => $username))
                ->queryRow();
        } else {
            return Yii::app()->db->createCommand()
                ->select(array('u.id', 'u.merchant_reset_psw', 'u.enabled'))
                ->from('user u')
                ->where('LOWER(u.email)=:username', array(':username' => $username))
                ->queryRow();
        }
    }

    /**
     * Validate User PIN code before to perform the Redeem
     *
     * Used in : Android Tablet
     * @access public
     * @return Boolean
     */
    public function validateUserPIN($password, $saltpin, $userPIN)
    {
        return ($this->hashPassword($password, $saltpin) === $userPIN);
    }

    /**
     * check facebook userid existance
     *
     * Used in : Website - iPhone/android App
     * @access public
     * @return Boolean
     */
    public static function checkFbUserIdExistance($fbUserId)
    {
        $userExists = Yii::app()->db->createCommand()
            ->select('count(1)')
            ->from('user')
            ->where('facebook_id=:id', array(':id' => $fbUserId))
            ->queryScalar();
        return $userExists > 0 ? true : false;
    }

    /**
     * checks if the combination facebook id and email exists
     * @param string $fbId
     * @param string $email
     * @return boolean
     */
    public static function checkFacebookIdUserEmailExistance($fbId, $email)
    {
        $userExists = Yii::app()->db->createCommand()
            ->select('count(1)')
            ->from('user')
            ->where('facebook_id=:id and email = :email', array(':id' => $fbId, ':email' => $email))
            ->queryScalar();
        return $userExists > 0 ? true : false;
    }

    /**
     * check if this email exists in the system
     * @param string $email
     */
    public static function checkEmailExistance($email)
    {
        if(Utils::feature('UBP')){
            $userExists = Yii::app()->db->createCommand()
                ->select('count(1)')
                ->from('user')
                ->where('email = :email and business_fk is null', array(':email' => $email))
                ->queryScalar();
        }else {
            $userExists = Yii::app()->db->createCommand()
                ->select('count(1)')
                ->from('user')
                ->where('email = :email', array(':email' => $email))
                ->queryScalar();
        }
        return $userExists > 0 ? true : false;
    }

    /**
     * check if facebookId exists for a specific email
     * @param unknown $email
     * @return boolean
     */
    public static function getFaceBookIdByEmail($email)
    {
        $userExists = Yii::app()->db->createCommand()
            ->select('facebook_id')
            ->from('user')
            ->where('email = :email', array(':email' => $email))
            ->queryScalar();
        return strln($userExists) > 0 ? true : false;
    }
    /**
     * if the user changed his email on facebook in this case we change the email in TT db and log the user in
     * @param unknown $email
     * @param unknown $fbId
     * @return Array()
     */
    public static function changeEmailAndLogin($email, $fbId)
    {
        $command = Yii::app()->db->createCommand();
        $command->update('user', array(
            'email' => $email,
        ), 'facebook_id=:fbId', array(':fbId' => $fbId));
        return User::getUserDetailsV210($email);
    }
    /**
     * if the user changed his email on facebook in this case we change the email in TT db and log the user in
     * @param unknown $email
     * @param unknown $fbId
     * @return Array()
     */
//    public static function changeEmailAndLoginV320($email, $fbId)
//    {
//        $command = Yii::app()->db->createCommand();
//        $command->update('user', array(
//            'email' => $email,
//        ), 'facebook_id=:fbId', array(':fbId' => $fbId));
//        return User::model()->findByAttributes(array('email' => $email));
//    }


    /**
     * checks if the user is an admin or regular user and if it's not a user it returns false otherwise it's true
     * @param string $email
     * @return boolean
     */
//    public static function checkIfUserAdmin($email)
//    {
//        $userExists = Yii::app()->db->createCommand()
//            ->select('(select up.itemname from user_permission up where up.userId = u.id LIMIT 1) as permission')
//            ->from('user u')
//            ->where('email = :email', array(':email' => $email))
//            ->queryScalar();
//
//        return $userExists == 'user' ? false : true;
//
//    }
    /**
     * to update the plat form solution flag for the user when he logs in
     * @param string $emailPhone
     * @param string $platForm
     */
    public static function updatePlatformSolutionByEmailPhone($emailPhone, $platForm)
    {
        $command = Yii::app()->db->createCommand();
        if (strpos((string)$emailPhone, "@")) {
            $command->update('user', array(
                'platform_solution_fk' => $platForm,
            ), 'email=:email', array(':email' => $emailPhone));
        } else {
            $command->update('user', array(
                'platform_solution_fk' => $platForm,
            ), 'phone=:phone', array(':phone' => $emailPhone));
        }
    }

    /**
     * Updating the ip for the user every time the user logs in
     *
     * Used smartphone (ios/android)
     *
     * <AUTHOR> N.
     * @version May - June 2015 release V3.3.0
     * @param string $emailPhone
     * @param string $ip
     */
    public static function updateUserIpByUserId($id, $ip)
    {
        $command = Yii::app()->db->createCommand();
        $command->update('user', array(
            'ip' => $ip,
        ), 'id =:userId', array(':userId' => $id));
    }

    /**
     * updating the ip for the user by email address
     *
     * <AUTHOR> N.
     * @since V3.1.0
     * @version V3.3.0
     * @param string $emailPhone
     * @param string $ip
     */
    public static function updateUserIpByEmailPhoneNumber($emailPhone, $ip)
    {
        $command = Yii::app()->db->createCommand();
        $command->update('user', array(
            'ip' => $ip,
        ), 'email=:email', array(':email' => $emailPhone));
    }

    /**
     * update the user gender by user id mainly used
     * for the dynamic profile in smartphone (iPhone/android)
     * @param string $gender
     * @param string $userId
     */
    public static function updateUserGenderById($gender, $userId)
    {
        Yii::app()->db->createCommand()
            ->update('user', array('gender' => $gender),
                'id=:userId', array(':userId' => $userId)
            );
    }

    /**
     * update the user gender by user id mainly used
     * for the dynamic profile in smartphone (iPhone/android)
     * @param string $gender
     * @param string $userId
     */
    public static function updateUserDateOfBirthById($dateOfBirth, $userId)
    {
        Yii::app()->db->createCommand()
            ->update('user', array('birth_date' => $dateOfBirth),
                'id=:userId', array(':userId' => $userId)
            );
    }

    /**
     * Update: this update include the time zone changes
     * gets the user intrests and insert them in database
     * if the user already exists the data will be updated
     *
     * Used on smartPhone(ios/android)
     * @version May June 2015 release V3.3.0
     * <AUTHOR> N.
     * @param string $userId
     * @param string $intrests
     */
    public static function insertUserIntrestsV330($timeZoneId, $userId, $interest)
    {
        $exists = User::checkExsistence('user_interest', 'user_fk', $userId);
        $command = Yii::app()->db->createCommand();
        $date = date('Y-m-d H:i:s');
        if ($exists) {
            $command->update('user_interest', array('utc_updated' => $date, 'bus_categ' => $interest, 'timezone_mysql_fk' => $timeZoneId), 'user_fk=:userId', array(':userId' => $userId));
        } else {
            $command->insert('user_interest', array('utc_created' => $date, 'bus_categ' => $interest, 'user_fk' => $userId, 'timezone_mysql_fk' => $timeZoneId));
        }
    }

    /**
     * gets the user intrests and insert them in database
     * if the user already exists the data will be updated
     * @param string $userId
     * @param string $intrests
     */
    public static function insertUserIntrests($userId, $interest)
    {
        $exists = User::checkExsistence('user_interest', 'user_fk', $userId);
        $command = Yii::app()->db->createCommand();
        $date = date('Y-m-d H:i:s');
        if ($exists) {
            $command->update('user_interest', array('date_modified' => $date, 'bus_categ' => $interest), 'user_fk=:userId', array(':userId' => $userId));
        } else {
            $command->insert('user_interest', array('date_created' => $date, 'bus_categ' => $interest, 'user_fk' => $userId));
        }
    }

    /**
     * insert the user address in postal code and address tables
     * Used in: Website, smartphone
     *
     * @version Mar - Apr 2015 release V3.2.5
     * <AUTHOR>
     * @param string $entityId
     * @param string $entityTypeId
     * @param string $postalCode
     */
    public static function insertUpdateAddressFromPostalCode($entityId, $entityTypeId, $postalCode)
    {
        //check if postal code is valid from google
        $googleAddress = User::getLongLatFromAddress($postalCode);

        if ($googleAddress) //if yes
        {
            //get the postal code from the api to have the same format in the table
            $addressComponents = $googleAddress->results[0]->address_components;
            foreach ($addressComponents as $addressComponent) {
                $types = $addressComponent->types;
                if ($types['0'] == 'postal_code' || $types[0] == 'postal_code_prefix') {
                    $postalCode = $addressComponent->long_name;
                } elseif ($types['0'] == 'country') {
                    $countryName = $addressComponent->long_name;
                    $countryCode = $addressComponent->short_name;
                } elseif ($types['0'] == 'administrative_area_level_1') {
                    $regionName = $addressComponent->long_name;
                } elseif ($types['0'] == 'locality') {
                    $cityName = $addressComponent->long_name;
                }
            }

            //$postalCode = $googleAddress->results[0]->address_components[0]->long_name;
            $postalCodeId = PostalCode::insertGetPostalCode($postalCode); //insert postal code in case not exists return id
            //check if address exists for a specific entity
            $address = Address::model()->findByAttributes(array('entity_type_fk' => $entityTypeId, 'entity_fk' => $entityId));
            if ($address) //if yes
            {
                if ($address->postal_code_fk == $postalCodeId) // check if it didn't change for the same user
                {
                    return true;
                }
// don't do anything
            } else //if no
            {
                //create a new instance
                $address = new Address();
                $address->entity_fk = $entityId;
                $address->entity_type_fk = $entityTypeId;
            }
            //check if country exists if no insert the new country

            //$addressArrayCount = count($addressArray);
            //             $countryName = $addressArray[$addressArrayCount-1]->long_name;
            //             $countryCode = $addressArray[$addressArrayCount-1]->short_name;
            $countryId = Countries::insertGetCountry($countryName, $countryCode);
            //check if region exists if no insert new region.
            //             $regionName = $addressArray[$addressArrayCount-2]->long_name;
            $regionId = Regions::insertGetRegion($regionName, $countryId);
            //check if city exists if no insert new city.
            //             $cityName = $addressArray[$addressArrayCount-3]->long_name;
            $cityId = Cities::insertGetCity($cityName, $regionId);
            //insert update address
            $address->postal_code_fk = $postalCodeId;
            $address->city_fk = $cityId;
            $address->geo_long = $googleAddress->results[0]->geometry->location->lng;
            $address->geo_lat = $googleAddress->results[0]->geometry->location->lat;
            $address->save();
            return true;
        } else //if no
        {
            return false;
        }

    }

    /**
     * Insert the user address in postal code and address tables
     * for this version includes the TIMEZONE Globalization handling
     * Used in: Website, smartphone
     * <AUTHOR> David S.  03/06/2015
     * @version : May - Jun 2015 release V3.3.0
     * @param string $entityId, $entityTypeId, $postalCode, $timeZoneId
     */
    public static function insertUpdateAddressFromPostalCodeV330($entityId, $entityTypeId, $postalCode, $timeZoneId)
    {
        //check if postal code is valid from google
        $googleAddress = User::getLongLatFromAddress($postalCode);

        if ($googleAddress) //if yes
        {
            //get the postal code from the api to have the same format in the table
            $addressComponents = $googleAddress->results[0]->address_components;
            foreach ($addressComponents as $addressComponent) {
                $types = $addressComponent->types;
                if ($types['0'] == 'postal_code' || $types[0] == 'postal_code_prefix') {
                    $postalCode = $addressComponent->long_name;
                } elseif ($types['0'] == 'country') {
                    $countryName = $addressComponent->long_name;
                    $countryCode = $addressComponent->short_name;
                } elseif ($types['0'] == 'administrative_area_level_1') {
                    $regionName = $addressComponent->long_name;
                } elseif ($types['0'] == 'locality') {
                    $cityName = $addressComponent->long_name;
                }
            }

            //$postalCode = $googleAddress->results[0]->address_components[0]->long_name;
            $postalCodeId = PostalCode::insertGetPostalCodeV330($postalCode, $timeZoneId); //insert postal code in case not exists return id
            //check if address exists for a specific entity

            $address = Address::model()->findByAttributes(array('entity_type_fk' => $entityTypeId, 'entity_fk' => $entityId));
            if ($address) //if yes
            {
                if ($address->postal_code_fk == $postalCodeId) // check if it didn't change for the same user
                {
                    return true;
                }
// don't do anything
            } else //if no
            {
                //create a new instance
                $address = new Address();
                $address->entity_fk = $entityId;
                $address->entity_type_fk = $entityTypeId;
            }
            //check if country exists if no insert the new country

            //$addressArrayCount = count($addressArray);
            //             $countryName = $addressArray[$addressArrayCount-1]->long_name;
            //             $countryCode = $addressArray[$addressArrayCount-1]->short_name;
            $countryId = Countries::insertGetCountryV330($countryName, $countryCode, $timeZoneId);
            //check if region exists if no insert new region.
            //             $regionName = $addressArray[$addressArrayCount-2]->long_name;
            $regionId = Regions::insertGetRegionV330($regionName, $countryId, $timeZoneId);
            //check if city exists if no insert new city.
            //             $cityName = $addressArray[$addressArrayCount-3]->long_name;
            $cityId = Cities::insertGetCityV330($cityName, $regionId, $timeZoneId);
            //insert update address
            $address->postal_code_fk = $postalCodeId;
            $address->city_fk = $cityId;
            $address->geo_long = $googleAddress->results[0]->geometry->location->lng;
            $address->geo_lat = $googleAddress->results[0]->geometry->location->lat;
            $address->timezone_mysql_fk = $timeZoneId;
            $address->save();
            return true;
        } else //if no
        {
            return false;
        }

    }

    /**
     * to update the login type fk flag for the user when he logs in
     * @param string $emailPhone
     * @param string $loginType
     */
    public static function updateLoginTypeByEmailPhone($emailPhone, $loginType)
    {
        $command = Yii::app()->db->createCommand();
        if (strpos((string)$emailPhone, "@")) {
            $command->update('user', array(
                'login_type_fk' => $loginType,
                'lastLoginTime' => date('Y-m-d H:i:s'),
            ), 'email=:email', array(':email' => $emailPhone));
        } else {
            $command->update('user', array(
                'login_type_fk' => $loginType,
                'lastLoginTime' => date('Y-m-d H:i:s'),
            ), 'phone=:phone', array(':phone' => $emailPhone));
        }
    }

    /**
     * to update the login type fk flag for the user when he logs in
     * @param string $emailPhone
     * @param string $loginType
     */
    public static function updateLoginTypeById($id, $loginType)
    {
        $command = Yii::app()->db->createCommand();
        $command->update('user', array(
            'login_type_fk' => $loginType,
            'lastLoginTime' => date('Y-m-d H:i:s'),
        ), 'id=:id', array(':id' => $id));
    }

    public static function updatePushDevicetokenById($id, $deviceToken)
    {
        $command = Yii::app()->db->createCommand();

        $command->update('user', array(
            'device_token' => null,
        ), 'id <> :id and device_token = :deviceToken', array(':id' => $id, ':deviceToken' => $deviceToken));

        $command->update('user', array(
            'device_token' => $deviceToken,
        ), 'id=:id and (device_token <> :deviceToken or device_token is null)', array(':id' => $id, ':deviceToken' => $deviceToken));
    }

    public static function updatePushGcmDevicetokenById($id, $gcmDeviceToken)
    {
        $command = Yii::app()->db->createCommand();

        $command->update('user', array(
            'gcm_device_token' => '',
        ), 'id <> :id and gcm_device_token = :deviceToken', array(':id' => $id, ':deviceToken' => $gcmDeviceToken));

        $command->update('user', array(
            'gcm_device_token' => $gcmDeviceToken,
        ), 'id=:id and (gcm_device_token <> :deviceToken or gcm_device_token is null)', array(':id' => $id, ':deviceToken' => $gcmDeviceToken));
    }

    /**
     * returns the user id by email or phone number
     * @param string $emailPhone
     * @param string $businessId
     */
    public static function getUserIdByEmailPhone($emailPhone, $businessId)
    {
        if (Utils::feature('UBP')) {
            if (strpos((string)$emailPhone, "@")) {
                return Yii::app()->db->createCommand()
                    ->select('id')
                    ->from('user')
                    ->where('email = :email and business_fk = :businessId', array(':email' => $emailPhone, ':businessId' => $businessId))
                    ->queryScalar();
            } else {
                return Yii::app()->db->createCommand()
                    ->select('id')
                    ->from('user')
                    ->where('phone = :phone and business_fk = :businessId', array(':phone' => $emailPhone, ':businessId' => $businessId))
                    ->queryScalar();
            }
        } else {
            if (strpos((string)$emailPhone, "@")) {
                return Yii::app()->db->createCommand()
                    ->select('id')
                    ->from('user')
                    ->where('email = :email', array(':email' => $emailPhone))
                    ->queryScalar();
            } else {
                return Yii::app()->db->createCommand()
                    ->select('id')
                    ->from('user')
                    ->where('phone = :phone', array(':phone' => $emailPhone))
                    ->queryScalar();
            }
        }
    }

    /**
     * check if the user is enabled or disabled by email/phone
     * @param string $emailPhone
     * @return boolean
     */
    public static function checkIfUserDisabledByEmailPhone($emailPhone)
    {
        if (strpos((string)$emailPhone, "@")) {
            $result = Yii::app()->db->createCommand()
                ->select('enabled')
                ->from('user')
                ->where('email = :email', array(':email' => $emailPhone))
                ->queryScalar();
        } else {
            $result = Yii::app()->db->createCommand()
                ->select('enabled')
                ->from('user')
                ->where('phone = :phone', array(':phone' => $emailPhone))
                ->queryScalar();
        }

        return $result == 0 ? true : false;
    }


    /**
     * Sending Push Notification IOS. When redemption on Tablet
     *
     * Used in : Android Merchant App
     *
     * @version V4.0
     * @since 3.2.5
     * <AUTHOR> David
     * @access public
     * @return void
     */
    public static function SendPushNotificationGeneral($message, $deviceToken, $fromNotif, $link = null)
    {
        $params = $fromNotif;
        if ($link) {
            $payload = '{
            "aps": {
                "alert": "' . stripslashes((string)$message??'') . '",
                "sound": "default",
                "link_url": "' . $link . '"
                }
            }';
        } else {
            $payload = '{"aps":{"alert":"' . stripslashes((string)$message??'') . '","sound":"default"},"bbeo":"' . $params . '"}';
        }

        //Yii::log('push payload='.$payload, CLogger::LEVEL_INFO, 'SendPushNotification');
        $nJSONPayloadLen = strlen((string)$payload);
        // echo 'payload size = '.$nJSONPayloadLen;
        $apnsHost = Yii::app()->params['apnsHost'];
        $apnsPort = 2195;
        $apnsCert = Yii::app()->params['certificatesPath'] . 'ck.pem';

        // Yii::log('apnsHost='.$apnsHost.' cert='.$apnsCert, CLogger::LEVEL_INFO, __FUNCTION__);

        $streamContext = stream_context_create();
        stream_context_set_option($streamContext, 'ssl', 'local_cert', $apnsCert);
        stream_context_set_option($streamContext, 'ssl', 'passphrase', "Kangaroo_Rewards");
        $apns = stream_socket_client('ssl://' . $apnsHost . ':' . $apnsPort, $error, $errorString, 60, STREAM_CLIENT_CONNECT, $streamContext);

        if (!$apns) {
            Yii::log('failed to connect ' . $error . ' - ' . $errorString, CLogger::LEVEL_ERROR, 'SendPushNotification');
            throw new Exception('Failed to connect ' . $error . ' - ' . $errorString, 1);
        }

        foreach ($deviceToken as $value) {
            $apnsMessage = chr(0) . chr(0) . chr(32) . @pack('H*', str_replace(' ', '', $value)) . chr(0) . chr(strlen((string)$payload)) . $payload;
            fwrite($apns, $apnsMessage);
        }

        fclose($apns);
    }

    /**
     * Sending Push Notification IOS
     *
     * Used in : Website(Marketing Campaign)
     *
     * @param $message
     * @param $deviceToken
     * @param $nbOfNotif
     * @param $branchId
     * @param $offerId
     * @param $isOffer
     * @param $businessId
     * @param $entityTypeId
     * @param null $campaignFk
     * @return void
     * @throws Exception
     * @version January 2016 release V3.5.0
     * @since 3.2.5
     * <AUTHOR>
     * @access public
     */
    public static function SendPushNotification($message, $deviceToken, $nbOfNotif, $branchId, $offerId, $isOffer, $businessId, $entityTypeId, $campaignFk = null, $data = null)
    {
        $image = (isset($data['pushNotificationImage'])) ? Yii::app()->params['serverUrl'] . '/' . ltrim((string)$data['pushNotificationImage'],'/') : '';

        if(isset($data['pushNotificationTitle'],$data['pushNotificationMessage'])) {
            $message = $data['pushNotificationTitle'] . "\n" . $data['pushNotificationMessage'];
        }

        $params = $branchId . '*' . $businessId . '*' . $entityTypeId . '*' . $offerId . '*' . $isOffer . '*' . $campaignFk;
//        $payload = '{"aps":{"alert":"' . stripslashes((string)$message) . '","badge":' . $nbOfNotif . ',"sound":"default","mutable-content":"1"}},"bbeo":"' . $params . '", "image": "'.$image.'"}';
        $payload = json_encode([
            'aps' => [
                'alert' => stripslashes((string)$message??''),
                'badge' => $nbOfNotif,
                'sound' => 'default',
                'mutable-content' => '1',
            ],
            'image' => $image,
            'bbeo' => $params,
        ]);
        $nJSONPayloadLen = strlen((string)$payload);
        // echo 'payload size = '.$nJSONPayloadLen;
        $apnsHost = Yii::app()->params['apnsHost'];
        $apnsPort = 2195;
        $apnsCert = Yii::app()->params['certificatesPath'] . 'ck.pem';

        $rules = BusinessRules::model()->findByAttributes(['business_fk' => $businessId]);

        // Override with custom certificate
        if($rules && $rules->ios_push_certificate_path) {
            $apnsCert = $rules->ios_push_certificate_path;
        }
        // Yii::log('apnsHost='.$apnsHost.' cert='.$apnsCert, CLogger::LEVEL_INFO, __FUNCTION__);

        $streamContext = stream_context_create();
        stream_context_set_option($streamContext, 'ssl', 'local_cert', $apnsCert);
        stream_context_set_option($streamContext, 'ssl', 'passphrase', "Kangaroo_Rewards");
//        stream_context_set_option($streamContext, 'ssl', 'verify_peer', false);
//        stream_context_set_option($streamContext, 'ssl', 'verify_peer_name', false);

        $apns = stream_socket_client('ssl://' . $apnsHost . ':' . $apnsPort, $error, $errorString, 60, STREAM_CLIENT_CONNECT, $streamContext);

        if (!$apns) {
            Utils::logMessage('failed to connect ' . $error . ' - ' . $errorString, __METHOD__,CLogger::LEVEL_ERROR);
            Yii::log('failed to connect ' . $error . ' - ' . $errorString, CLogger::LEVEL_ERROR, 'SendPushNotification');
            throw new Exception('Failed to connect ' . $error . ' - ' . $errorString, 1);
        }

        foreach ($deviceToken as $value) {
            $apnsMessage = chr(0) . chr(0) . chr(32) . @pack('H*', str_replace(' ', '', $value)) . chr(0) . chr(strlen((string)$payload)) . $payload;
            fwrite($apns, $apnsMessage);
        }

        fclose($apns);

        Utils::logMessage('payload=' . $payload . ' certpath=' . $apnsCert
            . ' apnsHost=' . $apnsHost , __METHOD__);

        Yii::log($payload, CLogger::LEVEL_INFO, __METHOD__);
    }

    /**
     * send the push notification to android
     * @param array $registatoin_ids
     * @param array $message
     * @param $key
     * @return bool|string
     * @throws Exception
     */
    private static function sendMessageThroughGCM($registatoin_ids, $message, $key)
    {
        //Google cloud messaging GCM-API url
        $url = 'https://fcm.googleapis.com/fcm/send';
        $fields = array(
            'registration_ids' => $registatoin_ids,
            'data' => $message,
        );
        // Update your Google Cloud Messaging API Key
        //define("GOOGLE_API_KEY", Yii::app()->params['gcmapikey']);//"AIzaSyAe3kGadSv8ET9uBis8eh0Qyq0Ob1lXYOk"
        $headers = array(
            'Authorization: key=' . $key,
            'Content-Type: application/json',
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));
        $result = curl_exec($ch);
        if ($result === false) {
            Yii::log(curl_error($ch), CLogger::LEVEL_ERROR, 'sendMessageThroughGCM');
            throw new Exception("Curl failed:" . curl_error($ch), 1);
        }
        curl_close($ch);
        return json_decode($result);
    }

    /**
     * Prepare the params to send Push notification for Android when the user redeemed in Merchant App
     *
     * Used in : Android Smartphone App
     *
     * @access public
     * @version V4.0
     * <AUTHOR> David
     * @param $pushMessage
     * @param $deviceToken
     * @param $fromNotif
     * @return string
     * @throws Exception
     */
    public static function sendGcmNotifications($pushMessage, $deviceToken, $fromNotif)
    {
        $message = array("m" => $pushMessage, "action" => $fromNotif);
        return User::sendMessageThroughGCM([$deviceToken], $message, Yii::app()->params['gcmapikey']);//after 4.0
    }

    /**
     * Prepare the params to send Push notification for Android
     *
     * Used in : Website()
     *
     * @access public
     * @param $pushMessage
     * @param $deviceToken
     * @param $nbOfNotif
     * @param $branchId
     * @param $offerId
     * @param $isOffer
     * @param $businessId
     * @param $entityTypeId
     * @param $campaignFk
     * @param null|array $data
     * @return string
     * @throws Exception
     */
    public static function sendGcmMessages(
        $pushMessage, $deviceToken, $nbOfNotif, $branchId, $offerId, $isOffer, $businessId,
        $entityTypeId, $campaignFk = null, $data = null, $rules = null
    ) {
        $message = $data['pushNotificationMessage'] ?? '';
        $title = $data['pushNotificationTitle'] ?? '';
        $url = $data['pushNotificationLink'] ?? '';
        $image= (isset($data['pushNotificationImage'])) ? Yii::app()->params['serverUrl'].$data['pushNotificationImage'] : '';

        if (!$rules) {
            $rules = BusinessRules::model()->findByAttributes(['business_fk' => $businessId]);
        }
        $pushConfig = $rules->getConfigByKey('push');
        // Use private key of default one
        $accessKey = $pushConfig->access_key ?? Yii::app()->params['gcmapikey'];

        Utils::logMessage(json_encode([
            'entity_campaign_fk' => $campaignFk,
            'businessId' => $businessId,
            'deviceToken' => substr((string)$deviceToken??'', 0, 10),
            'accessKey' => substr((string)$accessKey, 0, 10),
            'message' => 'Before sending Push to Firebase',
        ]), __METHOD__);

        $firebase = new FirebaseMessage(
            $accessKey,
            $deviceToken,
            $data = [
                "m" => $message,
                "title" => $title,
                "img" => $image,
                "url" => $url,
                'type' => 'campaign',
                'action' => 0,
                "dt" => $deviceToken,
                "nb" => $nbOfNotif,
                "brId" => $branchId,
                "oId" => $offerId,
                "io" => $isOffer,
                "bId" => $businessId,
                "etId" => $entityTypeId,
                "cfk" => $campaignFk
            ]
        );

        $firebase->setNotification([
            'title' => $title,
            'body' => $message,
            'image' => $image,
            'sound' => 'default',
        ]);

        return $firebase->send();
    }

    /**
     * sendPushFirebaseMessage
     *
     * Used in : Website()
     *
     * @access public
     * @param $pushMessage
     * @param $deviceToken
     * @param $nbOfNotif
     * @param $branchId
     * @param $offerId
     * @param $isOffer
     * @param $businessId
     * @param $entityTypeId
     * @param $campaignFk
     * @return string
     * @throws Exception
     */
    public static function sendPushFirebaseMessage($data, $deviceToken)
    {

        try {
            $message = (isset($data['pushNotificationMessage'])) ? $data['pushNotificationMessage'] : '';
            $title = (isset($data['pushNotificationTitle'])) ? $data['pushNotificationTitle'] : '';
            $url = (isset($data['pushNotificationLink'])) ? $data['pushNotificationLink'] : '';
            $image = (isset($data['pushNotificationImage'])) ? Yii::app()->params['serverUrl'] . $data['pushNotificationImage'] : '';
            $Fmessage = new FirebaseMessage(
                Yii::app()->params['gcmapikey'],
                $deviceToken,
                $data = [
                    'm' => $message,
                    'img' => $image,
                    'url' => $url,
                    'title' => $title,
                    'type' => 'campaign',
                    'action' => 0
                ]
            );
            $Fmessage->setNotification([
                'title' => $title,
                'body' => $message,
                'image' => $image,
                'sound' => 'default',
            ]);
            $Fmessage->send();
        } catch (Exception $e) {
            Utils::logMessage($e. ' ' ,__METHOD__, CLogger::LEVEL_ERROR);
        }

    }


    /**
     *    If the user created pin for the first time
     *    An email will be sent with activation link
     **/
    public function sendActivatePinEmail($userId, $random, $link)
    {
        $user = User::model()->findByPk($userId);

        $user->passwordreset = $random;

        //creating the expiry date
        $user->link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
        $username = User::getUserDisplayName($userId);

        if ($user->save()) {
            $emailConfig = Utils::getTransactionalEmailConfig($user->business_fk??null);

            $mail = new YiiMailer();
            $mail->IsSMTP(); // Set mailer to use SMTP

            $mail->setView('pinActivateBilingual');
            $mail->setLayout('bilingual');
            $mail->setData(array(
                'email' => $user->email,
                'username' => $username,
                'link' => $link,
            ));
            $mail->setReplyTo($emailConfig->reply_to);
            $mail->setFrom($emailConfig->from_email, $emailConfig->from_name);
            $mail->setTo(array(
                $user->email,
            ));
            $mail->setSubject(Yii::t('frontend', 'PIN_ACTIVATION', array(), null, 'fr') . ' / ' . Yii::t('frontend', 'PIN_ACTIVATION', array(), null, 'en'));

            if ($mail->send()) {
                //Yii::app()->user->setFlash('success', Yii::t('frontend', 'PIN_ACTIVATION_REQ'));
            } else {

                return CJSON::encode(array('status' => 'error', 'message' => $mail->getError()));
                // Yii::app()->user->setFlash('info', Yii::t('frontend', 'SENTEMAIL_FAIL'));
            }
            return CJSON::encode(array('status' => 'success'));
        } //user save
        else {
            return CJSON::encode(array('status' => 'error', 'message' => $user->getErrors()));
        }
    }

    /**
     * get the user details for authenticating him to login
     * used iphone/smartphone
     * @access public
     * @return Array()
     */
//Patricia - July 30th, 2015 - Cleanning OLD code
    //     public static function getUserDetails($username){
    //         if (strpos((string)$username, "@") == null)
    //         {
    //             return  Yii::app()->db->createCommand()
    //             ->select(array('u.*',
    //                     '(if(first is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),if(last is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),concat(first," ",last)))) as dispname',
    //                     '(select c.name from cities c where c.city_pk=u.city_fk)as city_name',
    //                     '(select r.name from regions r where r.region_pk=(select c.region_fk from cities c where c.city_pk=u.city_fk)) as region_name',
    //                     '(select co.name from countries co where co.country_pk=(select r.country_fk from regions r where  r.region_pk=(select c.region_fk from cities c where c.city_pk=u.city_fk))) as country_name',
    //                     'up.itemname as user_permission',
    //             ))
    //             ->from('user u')
    //             ->join ('user_permission up', 'up.userId=u.id')
    //             ->where('u.username=:username', array(':username'=> $username))
    //             ->queryRow();
    //         }
    //         else
    //         {
    //             return  Yii::app()->db->createCommand()
    //             ->select(array('u.*',
    //                     '(if(first is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),if(last is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),concat(first," ",last)))) as dispname',
    //                     '(select c.name from cities c where c.city_pk=u.city_fk)as city_name',
    //                     '(select r.name from regions r where r.region_pk=(select c.region_fk from cities c where c.city_pk=u.city_fk)) as region_name',
    //                     '(select co.name from countries co where co.country_pk=(select r.country_fk from regions r where  r.region_pk=(select c.region_fk from cities c where c.city_pk=u.city_fk))) as country_name',
    //                     'up.itemname as user_permission',
    //             ))
    //             ->from('user u')
    //             ->join ('user_permission up', 'up.userId=u.id')
    //             ->where('u.email=:username', array(':username'=> $username))
    //             ->queryRow();
    //         }
    //     }

    /**
     * get the user basic profile details
     * used iphone/smartphone
     * @access public
     * @return Array()
     */
    public static function getUserDetailsForBasicProfile($id)
    {
        return Yii::app()->db->createCommand()
            ->select(array('u.id', 'u.username', 'phone', 'pin_permission', 'email',
            ))
            ->from('user u')
            ->where('u.id = :id', array(':id' => $id))
            ->queryRow();
    }

    /**
     * get the user details for authenticating him to login
     * used Smartphone (iOS/Android)
     * @access public
     * @return Array()
     */
    public static function getUserDetailsV210($username)
    {
        if (strpos((string)$username??'', "@") == null) {
            return Yii::app()->db->createCommand()
                ->select(array('u.id', 'u.enabled', 'u.facebook_id', 'u.app_launched', 'u.ip',
                    'if(TRIM(COALESCE(u.image, "")) ="",if(u.gender is null ,"photos/user/user.png",if(u.gender = 1,"photos/user/FemaleDipPicProfile.png","photos/user/MaleDipPicProfile.png")),u.image) as image',
                    '(if(first is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),if(last is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),concat(first," ",last)))) as dispname',
                    'up.itemname as user_permission',
                    '(if(TRIM(COALESCE(u.phone, "")) ="",0,if(TRIM(COALESCE(u.pin_permission,"")) = "", 0, if(TRIM(COALESCE(u.email,"")) = "", 0, 1))))as isBasicProfileComplete',
                    'display_facebook_image', 'login_type_fk', 'basic_profile_reminder', 'phone',
                ))
                ->from('user u')
                ->join('user_permission up', 'up.userId=u.id')
                ->where('u.username=:username || u.phone = :username', array(':username' => $username))
                ->queryRow();
        } else {
            return Yii::app()->db->createCommand()
                ->select(array('u.id', 'u.enabled', 'u.facebook_id', 'u.app_launched', 'u.ip',
                    'if(TRIM(COALESCE(u.image, "")) ="",if(u.gender is null ,"photos/user/user.png",if(u.gender = 1,"photos/user/FemaleDipPicProfile.png","photos/user/MaleDipPicProfile.png")),u.image) as image',
                    '(if(first is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),if(last is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),concat(first," ",last)))) as dispname',
                    'up.itemname as user_permission',
                    '(if(TRIM(COALESCE(u.phone, "")) ="",0,if(TRIM(COALESCE(u.pin_permission,"")) = "", 0, if(TRIM(COALESCE(u.email,"")) = "", 0, 1))))as isBasicProfileComplete',
                    'display_facebook_image', 'login_type_fk', 'basic_profile_reminder', 'phone',
                ))
                ->from('user u')
                ->join('user_permission up', 'up.userId=u.id')
                ->where('u.email=:username', array(':username' => $username))
                ->queryRow();
        }
    }

    /**
     * functions that chacks if the basic profile reminder should open or not on the smartphone
     * @param string username, email or phone number
     * @return 1 or 0 to specify wether the basic profile is complete or not
     *  and the basic profile reminder date
     */
    public static function openBasicProfileReminder($userId)
    {

        return Yii::app()->db->createCommand()
            ->select(array('u.basic_profile_reminder',
                '(if(TRIM(COALESCE(u.phone, "")) ="",0,if(TRIM(COALESCE(u.email,"")) = "", 0, 1)))as isBasicProfileComplete',
            ))
            ->from('user u')
            ->join('user_permission up', 'up.userId=u.id')
            ->where('u.id=:userId', array(':userId' => $userId))
            ->queryRow();

    }

    /**
     * get the user details for authenticating him to login by facebook id
     * used Smartphone (iOS/Android)
     * @access public
     * @return Array()
     */
    public static function getUserDetailsByFaceBookIdV210($faceBookId)
    {

        return Yii::app()->db->createCommand()
            ->select(array('u.id', 'u.enabled',
                'if(TRIM(COALESCE(u.image, "")) ="",if(u.gender is null ,"photos/user/user.png",if(u.gender = 1,"photos/user/FemaleDipPicProfile.png","photos/user/MaleDipPicProfile.png")),u.image) as image',
                '(if(first is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),if(last is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),concat(first," ",last)))) as dispname',
                'up.itemname as user_permission',
                '(if(TRIM(COALESCE(u.phone, "")) ="",0,if(TRIM(COALESCE(u.pin_permission,"")) = "", 0, if(TRIM(COALESCE(u.email,"")) = "", 0, 1))))as isBasicProfileComplete',
                'display_facebook_image', 'login_type_fk', 'basic_profile_reminder', 'phone',
            ))
            ->from('user u')
            ->join('user_permission up', 'up.userId=u.id')
            ->where('u.facebook_id=:facebookId', array(':facebookId' => $faceBookId))
            ->queryRow();

    }

    /**
     * get some basic user info to load the left menu in the smartphone app
     * <AUTHOR> N.
     * @version January february 2016 V3.5.0
     * @access public
     * @param string $userId
     * @return array basic user info
     */
    public static function getUserDetailsToLoadProfileMenu($userId)
    {
        return Yii::app()->db->createCommand()
            ->select(array('(if(first is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),if(last is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),concat(first," ",last)))) as dispname',
                'u.email',
                'u.phone',
                'c.code',
                'u.facebook_id',
                'u.display_facebook_image',
                'if(TRIM(COALESCE(u.image, "")) ="","photos/user/User-No-Photo.png",u.image) as image',
            ))
            ->from('user u')
            ->leftjoin('countries c', 'c.country_pk = u.country_phone_fk')
            ->where('id=:userId', array(':userId' => $userId))
            ->queryRow();
    }

    /**
     * gets the user details by user id
     * used on smartphone (ios/android)
     * <AUTHOR> N
     * @version March April 2016 V4.0
     * @param int $userId
     */
    public static function getUserDetailsByIdV4($userId)
    {
        return Yii::app()->db->createCommand()
            ->select([
                'u.first',
                'u.last',
                'u.gender',
            	'u.username',
                'if(u.birth_date = "0000-00-00",null,u.birth_date) as birth_date',
                'u.display_facebook_image',
                'if(TRIM(COALESCE(u.image, "")) ="","photos/user/User-No-Photo.png",u.image) as image',
                'a.city_fk',
                'a.street as address',
                'c.region_fk as region_fk',
                'r.country_fk as country_fk',
                'p.postal_code as postal_code'])
            ->from('user u')
            ->leftjoin('address a', 'u.id = a.entity_fk and a.entity_type_fk = 4')
            ->leftjoin('cities c', 'a.city_fk = c.city_pk')
            ->leftJoin('regions r', 'r.region_pk = c.region_fk')
            ->leftjoin('countries co', 'r.country_fk = co.country_pk')
            ->leftjoin('postal_code p', ' p.postal_code_pk = a.postal_code_fk')
            ->where('id=:userId', array(':userId' => $userId))
            ->queryRow();
    }

    /**
     * gets the user details by providing the user id
     * used in apicontroller => actionGetUserdetailsById
     * @param int $userId
     */
    public static function getUserDetailsById($userId)
    {
        return Yii::app()->db->createCommand()
            ->select(array('(if(first is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),if(last is NULL,if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username),concat(first," ",last)))) as dispname',
                'u.id',
                'u.username',
                'u.email',
                'u.first',
                'u.last',
                'u.phone',
                'u.gender',
                'u.birth_date',
                'u.facebook_id',
                'u.display_facebook_image',
                'if(TRIM(COALESCE(u.image, "")) ="",if(u.gender is null ,"photos/user/user.png",if(u.gender = 1,"photos/user/FemaleDipPicProfile.png","photos/user/MaleDipPicProfile.png")),u.image) as image',
                'u.language',
                '(select a.city_fk from address a where u.id = a.entity_fk and a.entity_type_fk = 4) as city_fk',
                '(select c.name from cities c where c.city_pk=(select a.city_fk from address a where u.id = a.entity_fk and a.entity_type_fk = 4))as city_name',
                '(select region_fk from cities where city_pk = (select a.city_fk from address a where u.id = a.entity_fk and a.entity_type_fk = 4)) as region_fk',
                '(select r.name from regions r where r.region_pk=(select region_fk from cities where city_pk = (select a.city_fk from address a where u.id = a.entity_fk and a.entity_type_fk = 4))) as region_name',
                '(select r.country_fk from regions r where r.region_pk=(select region_fk from cities where city_pk = (select a.city_fk from address a where u.id = a.entity_fk and a.entity_type_fk = 4))) as country_fk',
                '(select co.name from countries co where co.country_pk=(select r.country_fk from regions r where r.region_pk=(select region_fk from cities where city_pk = (select a.city_fk from address a where u.id = a.entity_fk and a.entity_type_fk = 4)))) as country_name',
                '(select a.street from address a where u.id = a.entity_fk and a.entity_type_fk = 4) as address',
                '(select a.postal_code_fk from address a where u.id = a.entity_fk and a.entity_type_fk = 4) as postal_code_fk',
                '(select p.postal_code from postal_code p where p.postal_code_pk = (select a.postal_code_fk from address a where u.id = a.entity_fk and a.entity_type_fk = 4)) as postal_code',
                '(select co.code from countries co where co.country_pk = u.country_phone_fk) as codeIso2',
                'u.pin_permission'))
            ->from('user u')
            ->where('id=:userId', array(':userId' => $userId))
            ->queryRow();
        //, 'u.basic_profile_reminder'
    }

    /**
     * To specpecify the login type if the user logged in using website or mobile device
     * @param int $userId
     * @param int $loginType
     * used in smartphone (iphone/android)
     */
    public function insertLoginType($userId, $loginType)
    {
        $userModel = User::model()->findByPk($userId);
        $userModel->login_type_fk = $loginType;
        $userModel->save();
    }

    /**
     * on update check if the email is already taken by someone else in the system
     * used in iphone app - update user
     * <AUTHOR> N.
     * @param int $userId
     * @param string $email
     * returns 0 if the email does not exists or 1 if it exists.
     */
    public static function checkUserEmailPhoneExists($emailPhone, $isEmail, $userId = null)
    {
        if ($isEmail) {
            $condition = [':email' => $emailPhone];
            $emailPhoneStr = ' email = :email';
        } else {
            $condition = [':phone' => $emailPhone];
            $emailPhoneStr = ' phone = :phone';
        }
        $user = '';

        if ($userId != null) {
            $condition = array_merge($condition, [':userId' => $userId]);
            $user = 'id <> :userId and ';
        }

        return Yii::app()->db->createCommand()
            ->select('count(1)')
            ->from('user')
            ->where($user . $emailPhoneStr, $condition)
            ->queryScalar() > 0;
    }

    /**
     * on update check if the username is already taken by someone else in the system
     * user in iphone app - update user
     * @param int $userId
     * @param string $username
     * returns 0 if the username does not exists or 1 if it exists.
     */
    public static function checkUserNameExists($userId, $username)
    {
        return Yii::app()->db->createCommand()
            ->select('count(1)')
            ->from('user')
            ->where('id <> :userId and username = :username', array(':userId' => $userId, ':username' => $username))
            ->queryScalar();
    }

    /*
     *  TODO: delete and use userHasFacebookAccount
     */
    public static function userExists($loginUsername)
    {
        // Checking if the user is logging in with email
        if (strpos((string)$loginUsername, "@")) {

            $userExists = User::model()->findByAttributes(array(
                'email' => $loginUsername,
                'enabled' => 1,

            ));
            // Checking if this email exists and is asociated with a facebook account
            if ($userExists != null && $userExists->facebook_id != null) {
                return true;
            }
        } else {
            // Checking if this username exists
            $userExists = User::model()->find('LOWER(username)=?', array(
                strtolower((string)$loginUsername),
            ));
            // Checking if the username exists and is asociated with a facebook account
            if ($userExists != null && $userExists->facebook_id != null) {
                return true;
            }
        }
    }

    /**
     * Checking if an email, username or phone exists in DB and is linked to a FB account
     * Returns true if the user exists and has a FB account
     *
     * Used on : Website(Login, Signup)
     *
     * @version Website June Release
     * @param
     * @access public
     * @return true
     */

    public static function userHasFacebookAccount($loginUsername)
    {
        // Checking if the user is logging in with email
        if (strpos((string)$loginUsername, "@")) {
            $userExists = User::model()->findByAttributes(array(
                'email' => $loginUsername,
                'enabled' => 1,
            ));
            // Checking if this email exists and is asociated with a facebook account
            if ($userExists != null && $userExists->facebook_id != null) {
                return true;
            }

        } elseif (ctype_digit((string)$loginUsername)) {

            $userExists = User::model()->findByAttributes(array(
                'phone' => $loginUsername,
                'enabled' => 1,
            ));
            // Checking if this email exists and is asociated with a facebook account
            if ($userExists != null && $userExists->facebook_id != null) {
                return true;
            }

        } else {
            // Checking if this username exists
            $userExists = User::model()->find('LOWER(username)=?', array(
                strtolower((string)$loginUsername),
            ));
            // Checking if the username exists and is asociated with a facebook account
            if ($userExists != null && $userExists->facebook_id != null) {
                return true;
            }
        }
    }

    /*
     *   Checking if an email exists in DB and is linked to a FB account
     *   Returns true if the user exists and has a FB account
     *   used on smartphone (iphone/android)
     */
    public static function userExistsSmartPhone($loginUsername)
    {
        // Checking if the user is logging in with email
        if (strpos((string)$loginUsername, "@")) {

            $userExists = User::model()->findByAttributes(array(
                'email' => $loginUsername,
                'enabled' => 1,

            ));
            // Checking if this email exists and is asociated with a facebook account
            if ($userExists != null && $userExists->facebook_id != null && $userExists->accounts_merge != 1) {
                return true;
            }
        } else {
            // Checking if this username exists
            $userExists = User::model()->find('LOWER(username)=?', array(
                strtolower((string)$loginUsername),
            ));
            // Checking if the username exists and is asociated with a facebook account
            if ($userExists != null && $userExists->facebook_id != null && $userExists->accounts_merge != 1) {
                return true;
            }
        }
    }

    // Function for basic field validation (present and neither empty nor only white space
    private function IsNullOrEmptyString($theValue)
    {
        return (!isset($theValue) || trim((string)$theValue) === '');
    }

    private static function sendWelcomeEmailFaceBookSignUp($username, $email)
    {
        //Sending a welcome email if user has changed his password
        $mail = new YiiMailer();
        $mail->IsSMTP(); // Set mailer to use SMTP

        $mail->setView('userWelcomeFbBilingual');
        $mail->setLayout('bilingual');
        $mail->setData(array(
            'username' => $username,
            'email' => $email,
            'link' => Yii::app()->params['serverUrl'] . '/site/traktrokRedirect?login=true',

        ));
        $mail->setReplyTo(Yii::app()->params['emailout']);
        $mail->setFrom(Yii::app()->params['emailin'], Yii::app()->params['fullnamemailin']);
        $mail->setTo(array(
            $email,
        ));
        $mail->setSubject(Yii::t('frontend', 'USR_WELCOME'));

        if ($mail->send()) {
        } else {
            Yii::app()->user->setFlash('error', 'Error while sending email: ' . $mail->getError());
            // Yii::app()->user->setFlash('info', Yii::t('frontend', 'SENTEMAIL_FAIL'));
        }
        //sending mail ends
    }

    /**
     * save the user phone number in case the phone number is empty;
     * used on smartphone (iphone/android)
     */
    public static function savePhoneNumber($userId, $phone)
    {
        if (self::checkPhoneExistence($phone, $userId)) {
            return array('status' => 'NOT_OK', 'message' => 'PHONE_EXISTS');
        } else {
            $user = User::model()->findByPk($userId);
            $user->phone = $phone;
            if ($user->save(false)) {
                return array('status' => 'OK', 'message' => 'PHONE_SAVED_SUCCESSFULLY');
            } else {
                return array('status' => 'NOT_OK', 'message' => 'STANDARD_EXCEPTION');
            }
        }
    }

    /**
     *
     * @param unknown $userModel
     * @param unknown $emailView
     * @param unknown $layout
     * @param unknown $emailSubject
     * @param string $addLink
     * Used in Website(SiteControllser/lostpassword, login, singup), Smartphone (iphone/android), Merchant Tablet app
     *
     */
    public static function sendEmailWithTemplate($userModel, $emailView, $layout, $emailSubject, $addLink = true, $isFromIOS = false, $isFromAndroid = false)
    {

        if ($addLink == true) {
            // Create secret reset link
            $random = $userModel->hashPassword($userModel->email . $userModel->username, microtime(true));
            $p = User::model()->generateRandomString(5);
            if ($isFromIOS) {
                $link = Yii::app()->createAbsoluteUrl('site/traktrokRedirect', array(
                    'q' => $random,
                    'isFromIOS' => true,
                    'activate' => true,
                    'returnUrl' => '/site/activate?q=' . $random . '&isFromIOS=true&activate=true&p=' . $p,
                ));
            } elseif ($isFromAndroid) {
                $link = Yii::app()->createAbsoluteUrl('site/traktrokRedirect', array(
                    'q' => $random,
                    'isFromAndroid' => true,
                    'activate' => true,
                    'returnUrl' => '/site/activate?q=' . $random . '&isFromAndroid=true&activate=true&p=' . $p,
                ));
            } else {
                $link = Yii::app()->createAbsoluteUrl('site/traktrokRedirect', array(
                    'q' => $random,
                    'returnUrl' => '/site/activate?q=' . $random . '&activate=true&p=' . $p,
                ));
            }
            $userModel->passwordreset = $random;
            // Give the user a temporary password so they can login the first time; sent to the user with the activation email.

            // Adding the password to the link
            $link .= '&p=' . $p;

            // Now we hash the password and store in database
            //$userModel->password = $userModel->hashPassword($userModel->password, $userModel->username);
            //creating the expiry date
            $userModel->link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));

            $userModel->update();
        } else {
            $link = '';
        }

        if (strpos((string)$userModel->username, "@")) {
            $username = strstr($userModel->username, '@', true);
        } else {
            $username = $userModel->username;
        }

        //Sending the email
        $emailConfig = Utils::getTransactionalEmailConfig($userModel->business_fk??null);

        $mail = new YiiMailer();
        $mail->IsSMTP(); // Set mailer to use SMTP

        $mail->setView($emailView);
        $mail->setLayout($layout);
        $mail->setData(array(
            'username' => $username,
            'email' => $userModel->email,
            'link' => $link,
        ));
        $mail->setReplyTo($emailConfig->reply_to);
        $mail->setFrom($emailConfig->from_email, $emailConfig->from_name);
        $mail->setTo(array(
            $userModel->email,
        ));
        $mail->setSubject($emailSubject);

        if ($mail->send()) {
            return CJSON::encode(array('status' => 'success', 'message' => 'Email Sent'));
        } else {
            return CJSON::encode(array('status' => 'error', 'message' => 'Error while sending email: ', 'errors' => $mail->getError()));
        }

    }

    /**
     * It will make an API call to api.randomuser.me and return an array of random users
     * If the request succeeded than we iterate through array and  check if the username doesn't exist in our db we take it
     * If the request failed than we generate a username with root silverfish plus a random number
     * Updated 28-05-2015: get only nicknames that are not used
     * API call: http://api.randomuser.me/0.3.2/
     *
     * Used on : Website (User signup with cellphone )
     *           Smartphone app
     *           Merchant Tablet app
     * @version May - Jun 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function getRandomNickname()
    {
        $nicknameModel = NicknameSource::model()->find(array(
            'select' => 'nickname, rand() as rand',
            'condition' => 'already_used=0',
            'limit' => '1',
            'order' => 'rand',
        ));

        //return $nicknameModel->nickname;

        $username = User::createUsername($nicknameModel->nickname);

        //checking if random generated username exists in db already
        $usernameExists = User::checkUsernameExistence($username);
        if ($usernameExists == false) {
            //we take it as a username for signup
            return $username;
        } else {
            return $nicknameModel->nickname . User::random_numbers(2);
        }

    }

    /**
     * Validate and Send the SMS
     *
     * Used on : Merchant Tablet app
     * @version Aug - Dec Release V3.1.0
     * @param object userModel
     * @param boolean $resetPassword - true if it is a reset password request
     * @param $createPin = true if it is a create PIN request
     * @access public
     * @return array()
     */
    /*public static function validateAndSendSMS($smsBody, $sendTo)
    {

        // Step 1: Include Twillio
        spl_autoload_unregister(array('YiiBase', 'autoload'));
        require Yii::app()->params['TwillioIncludePath'];
        require Yii::app()->params['libphonenumber'];
        spl_autoload_register(array('YiiBase', 'autoload'));

        $AccountSid = Yii::app()->params['TwillioAccountSid'];
        $AuthToken = Yii::app()->params['TwillioAuthToken'];

        $client = new Services_Twilio($AccountSid, $AuthToken);

        $from = Yii::app()->params['TwillioFrom']; //for testing "+***********";

        //Validating Phone number before sending the sms
        $phonenumber = new libphonenumber\LibPhone($sendTo);
        $isValidPhone = $phonenumber->validate(); //return true if valid
        if ($isValidPhone == false) {
            return array('status' => 'NOT_OK', 'error' => 'NVFN', 'message' => Yii::t('frontend', 'SEND_SMS_INVALID_PHONE_NUMBER'));
        }

        if(FakeContactRule::isFakePhone($sendTo)) {
            Utils::logMessage('Fake phone: ' . $sendTo, __METHOD__, CLogger::LEVEL_INFO);
            return array('status' => 'NOT_OK', 'error' => 'NVFN', 'message' => Yii::t('backend', 'BACKEND_FAKE_CONTACT'));
        }

        try
        {
            $sms = $client->account->messages->sendMessage(
                $from,
                $sendTo,
                $smsBody
            );

            $message = $client->account->messages->get($sms->sid);
            //Yii::log('Send SMS', CLogger::LEVEL_INFO, 'status='.$message->status.' sid='.$sms->sid);

            if ($message->status == 'failed' || $message->status == 'undelivered') {
                Yii::log('sendTo=' . $sendTo . ' errorCode=' . $message->error_code . ' errorMessage=' . $message->error_message . 'messageStatus=' . $message->status, CLogger::LEVEL_ERROR, 'Send SMS');
                //print_r($message); die();
                $result = array('status' => 'NOT_OK', 'error' => 'SMS undelivered: ' . $message->error_message, 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'));
            } else {
                // Display a confirmation message on the screen
                $result = array('status' => 'OK');
            }
        } catch (Services_Twilio_RestException $e) {
            Yii::log('sendTo=' . $sendTo . ' errorCode=' . $e->getCode() . ' errorMessage=' . $e->getMessage(), CLogger::LEVEL_ERROR, 'Send SMS Services_Twilio_RestException');

            //print_r($e->getCode()).'<br>'; print_r($e->getMessage()); die();
            $result = array('status' => 'NOT_OK', 'error' => (array) $e, 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'));
        }

        return $result;
    }*/

    /**
     * Custom validator for email, username or phone
     *
     * Used on : Website (Signup)
     *
     * @version Website/Smartphone May-June 2014 Release
     * @param string $usernameEmailPhone
     * @access public
     * @return array()
     */

    public static function validatePhoneEmail($usernameEmailPhone)
    {
        $validPassed = false;
        $flashMessage = '';
        $signupMethod = null;
        $userExists = false;
        if (strpos((string)$usernameEmailPhone, "@") == null) //username or phone
        {
            $signupMethod = 1; //phone
            if (ctype_digit((string)$usernameEmailPhone)) // all digits -> phone
            {

                if (User::checkPhoneExistence($usernameEmailPhone) && strlen((string)$usernameEmailPhone) == 10) {
                    $validPassed = true;
                    $userExists = true;
                    $flashMessage = Yii::t('frontend', 'SIGNUP_PHONE_EXISTS');
                } elseif (strlen((string)$usernameEmailPhone) != 10) {
                    $validPassed = false;
                    $flashMessage = Yii::t('frontend', 'PHONE_NUMBER_LENGTH');
                } else {
                    $validPassed = true;

                }

            } else {
                $validPassed = false;
                if (ctype_alnum((string)$usernameEmailPhone)) {
                    $flashMessage = Yii::t('frontend', 'ENTER_PHONE_OR_EMAIL');
                } else {
                    $flashMessage = Yii::t('frontend', 'INVALID_PHONE2');
                }

            }
        } else //email
        {
            $signupMethod = 2; //email
            $validator = new CEmailValidator;
            if ($validator->validateValue($usernameEmailPhone)) {
                $emailExists = User::searchUsernameOrEmailExistence($usernameEmailPhone);

                if ($emailExists == false) {
                    $validPassed = true;
                } else {
                    $flashMessage = Yii::t('frontend', 'SIGNUP_EMAIL_EXISTS');
                    $validPassed = false;
                }
            } else {
                $validPassed = false;
                $flashMessage = Yii::t('frontend', 'INVALID_EMAIL');
            }
        }

        return array('validPassed' => $validPassed, 'flashMessage' => $flashMessage, 'signupMethod' => $signupMethod, 'userExists' => $userExists);
    }

    public static function clean($string)
    {
        $string = str_replace(' ', '-', $string); // Replaces all spaces with hyphens.
        $string = preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.

        return preg_replace('/-+/', '', $string); // Replaces multiple hyphens with single one.
    }

    //05-06-2015, Valentin, Old code to be deleted and use Utils:randomNumber
    public static function random_numbers($digits)
    {
        $min = 10 ** ($digits - 1);
        $max = 10 ** $digits - 1;
        return mt_rand($min, $max);
    }

    public static function strongPassword($digits = 12)
    {
        if ($digits < 12) {
            $digits = 12;
        }

        $random_characters = (int)$digits / 4;

        $lower_case = "abcdefghijklmnopqrstuvwxyz";
        $upper_case = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        $numbers = "1234567890";
        $symbols = "!@#$%^&*";

        $lower_case = str_shuffle($lower_case);
        $upper_case = str_shuffle($upper_case);
        $numbers = str_shuffle($numbers);
        $symbols = str_shuffle($symbols);

        $random_password = substr((string)$lower_case, 0, $random_characters);
        $random_password .= substr((string)$upper_case, 0, $random_characters);
        $random_password .= substr((string)$numbers, 0, $random_characters);
        $random_password .= substr((string)$symbols, 0, ($digits - ($random_characters * 3)));

        return str_shuffle($random_password);
    }

    /**
     * Create Username
     * Used on : Website (Signup) / Smartphone / Merchant Tablet app
     *
     * @version Website/Smartphone May-June 2014 Release
     * @param string $usernameEmailPhone
     * @access public
     * @return array()
     */
    public static function createUsername($name)
    {
        if(!$name){
            $name = '';
        }

        if (strpos((string)$name, "@")) {
            $name = strstr($name, '@', true);
        }
        $name = User::clean($name);
        if (strlen((string)$name) < 6) {
            $len = 6 - strlen((string)$name);
            for ($i = 0; $i < $len; $i++) {
                $name = $name . User::random_numbers(1);
            }
        }
        if (!ctype_alpha(substr((string)$name, 0, 1))) {
            $name = substr((string)str_shuffle("abcdefghijklmnopqrstuvwxyz"), 0, 1) . $name;
        }
        if (User::checkUsernameExistence($name)) {
            $name = User::createUsername($name . User::random_numbers(1));
        }
        return $name;
    }

    public function validateUsername($attribute, $params)
    {
        if (strlen((string)$this->$attribute??'') < 6) {
            $this->addError($attribute, Yii::t('frontend', 'USERNAME_MINIMUM_CHARACTERS'));
        }

        if (!ctype_alnum($this->$attribute??'')) {
            $this->addError($attribute, Yii::t('frontend', 'USERNAME_ONLY_ALPHABET'));
        }

        if (is_numeric($this->$attribute??'')) {
            $this->addError($attribute, Yii::t('frontend', 'USERNAME_ATLEAST_ONEALPHABET'));
        }

        if (!ctype_alpha(substr((string)$this->$attribute??'', 0, 1))) {
            $this->addError($attribute, Yii::t('frontend', 'USERNAME_SHOULD_ALPHABET'));
        }

    }

    //Phoene number length validation
    public function validatePhoneLength($attribute, $params)
    {
        if (Yii::app()->user->checkAccess('user')) {
            if (strlen((string)$this->$attribute??'') != 10) {
                $this->addError($attribute, Yii::t('frontend', 'PHONE_NUMBER_LENGTH'));
            }

        }

    }

    //temporary for getting nickames from api randomuser.me
    public static function checkNicknameExistence($username)
    {
        $exists = false;

        $result = Yii::app()->db->createCommand()
            ->select("count(1)")
            ->from('nickname_source nc')
            ->where('nc.nickname = :username', array(':username' => $username))
            ->queryScalar();
        $exists = $result > 0 ? true : false;

        return $exists;
    }

    /**
     * Change folder permisssions recusively
     *
     * Used on : Website(Business Admin, create offer)
     *
     * @version Website May-June 2014 Release
     * @param
     * @access public
     * @return none
     */
    /*
     * PATRICIA - APRIL 26TH 2015 - @TODO MOVE TO Utils class
     */
    public static function chmodRecusively($path)
    {
        $filemode = 0666;
        $dirmode = 0777;
        if (is_dir($path)) {
            if (!chmod($path, $dirmode)) {
                $dirmode_str = decoct($dirmode);
                print "Failed applying filemode '$dirmode_str' on directory '$path'\n";
                print "  `-> the directory '$path' will be skipped from recursive chmod\n";
                return;
            }
            $dh = opendir($path);
            while (($file = readdir($dh)) !== false) {
                if ($file != '.' && $file != '..') {
                    // skip self and parent pointing directories
                    $fullpath = $path . '/' . $file;
                    User::chmodRecusively($fullpath);
                }
            }
            closedir($dh);
        } else {
            if (is_link($path)) {
                print "link '$path' is skipped\n";
                return;
            }
            if (!chmod($path, $filemode)) {
                $filemode_str = decoct($filemode);
                print "Failed applying filemode '$filemode_str' on file '$path'\n";
                return;
            }
        }
    }

    /**
     * Handle facebook login using signUpUserFromFaceBook function from user model
     *
     * Used on : Website (Signup, Login)
     *
     * @version Website May-June 2014 Release
     * @param object UserModel
     * @access public
     * @return JSON
     */

    public static function signupFacebookLogin($facebook)
    {

        $result = User::model()->signUpUserFromFaceBook($facebook, 1, 1);

        if ($result == 'basicProfile') {
            $json = CJSON::encode(array('status' => 'OK', 'redirect' => true, 'redirectUrl' => '/profile/basic', 'message' => 'Redirecting to basic profile'));
        } else if ($result == 'siteIndex') {
            $json = CJSON::encode(array('status' => 'OK', 'redirect' => true, 'redirectUrl' => '/site/index', 'message' => 'Redirecting to index page'));
        } else if ($result == 'offerIndex') {
            $json = CJSON::encode(array('status' => 'OK', 'redirect' => true, 'redirectUrl' => '/offer/index', 'message' => 'Redirecting to spin page'));
        } else if ($result == 'bacUser') //BusinessAdmin, BranchAmdin or Clerk
        {
            $json = CJSON::encode(array('status' => 'NOT_OK', 'redirect' => false, 'redirectUrl' => '#', 'message' => Yii::t('frontend', 'BAC_USER_FACEBOOK_LOGIN_ERROR')));
        }

        return $json;
    }

    /**
     * Getting the current status from UserStatusManager
     * Send Account Activation Reminder Email for user who is not active and signed up from the Website regular signup
     * Send Tablet Welcome Email for user who is not active and signed up from the Tablet
     *
     * Used on : Website(Signup)
     *
     * @version Website June Release
     * @param object UserModel, int currentUserStatus
     * @access public
     * @return JSON
     */
//Patricia - July 30th, 2015 - Cleanning OLD code
    //     public static function smartPhoneSignupSendEmailByCurrentStatus($userId, $login=false,$fromIphone=false,$fromAndroid=false)
    //     {

//         //loading the user record
    //         $userModel = User::model()->findByPk($userId);
    //         //getting the current status
    //         $userCurrentStatus = UserStatusManager::getCurrentUserStatus($userId);

//         $result = array('status'=>'NOT_OK','message'=>Yii::t('frontend', 'SIGNUP_EMAIL_EXISTS'),'results'=>array());

//         //---   START Send Tablet Welcome Email for user who is not active and signed up from the Tablet
    //         if($userCurrentStatus == 10 && $userModel->enabled == 0) //Tablet Sign up and not Active
    //         {

//             $emailView = 'userActivateBilingualAtStoreReminder';
    //             $layout = 'bilingual';
    //             $emailSubject = Yii::t('frontend', 'USR_REG', array(), null, 'fr') . ' / ' . Yii::t('frontend', 'USR_REG', array(), null, 'en');

//             //Sending the email
    //             $sentEmail = User::sendEmailWithTemplate($userModel, $emailView, $layout, $emailSubject, $addLink = true, $fromIphone, $fromAndroid );

//             if ($login===false)
    //                 $result = array('status'=>'NOT_OK','message'=>Yii::t('frontend', 'USER_SIGN_UP_FROM_TABLET_HAS_ACCOUNT'),'results'=>array());
    //             elseif ($login===true)
    //             $result = array('status'=>'NOT_OK', 'message'=>Yii::t('frontend', 'USER_SIGN_UP_FROM_TABLET_MESSAGE'),'results'=>array());
    //         }
    //         //---   END Send Tablet Welcome Email for user who is not active and signed up from the Tablet

//         //---   START Send Account Activation Reminder Email for user who is not active and signed up from the Website regular signup
    //         elseif($userCurrentStatus == 9 && $userModel->enabled == 0) //Regular Sign up and not Active
    //         {

//             $emailView = 'resendActivateBilingual';
    //             $layout = 'bilingual';
    //             $emailSubject = Yii::t('frontend', 'EMAIL_SUBJECT_ACCOUNT_ACTIVATION', array(), null, 'fr') . ' / ' . Yii::t('frontend', 'EMAIL_SUBJECT_ACCOUNT_ACTIVATION', array(), null, 'en');

//             //Sending the email
    //             $sentEmail = User::sendEmailWithTemplate($userModel, $emailView, $layout, $emailSubject, $addLink = true, $fromIphone, $fromAndroid );

//             if ($login===false)
    //                 $result = array('status'=>'NOT_OK', 'message'=>Yii::t('frontend', 'USER_SIGN_UP_FROM_WEBSITE_HAS_ACCOUNT_NOT_ACTIVE_TRY_SIGNUP'),'results'=>array());
    //             elseif ($login===true)
    //             $result = array('status'=>'NOT_OK', 'message'=>Yii::t('frontend', 'USER_SIGN_UP_FROM_WEBSITE_HAS_ACCOUNT_NOT_ACTIVE_TRY_LOGIN'),'results'=>array());
    //         }
    //         elseif(($userCurrentStatus == 19 || $userCurrentStatus == 20) && $userModel->enabled == 0) //Regular Phone Sign up and not Active
    //         {
    //             $results = User::sendSMSV211($userModel);

//             if ($results['status']== 'OK')
    //                 $result = array('status'=>'NOT_OK', 'message'=>Yii::t('frontend','PHONE_USER_SIGN_UP_FROM_WEBSITE_HAS_ACCOUNT_NOT_ACTIVE_TRY_LOGIN'),'results'=>array('activateAccount'=>true,'userId'=>$userId));
    //             else
    //                 $result = array('status'=>'NOT_OK', 'message'=>$results['message'],'results'=>array());
    //         }

//         return $result;
    //     }

    /**
     * Sending email or sms depends of signupMethod(phone, email)
     *
     * Used on : Website(Signup)
     *
     * @todo delete - not used
     * @version Website June Release
     * @param int $signupMethod, object User
     * @access public
     * @return JSON
     */
//Patricia - July 30th, 2015 - Cleanning OLD code
    //     public static function signupSendEmailOrSMS($signupMethod, $Joinmodel)
    //     {
    //         switch ($signupMethod)
    //         {
    //             case 1://phone

//                     //inserting in user Status Manager
    //                     $userStatus = UserStatusManager::createUserStatusManagerRecord($Joinmodel->id, $modifiedBy=null, $status_manager_fk = 19);//Not active Regular Phone Sign up

//                     $results = User::sendSMSV211($Joinmodel);

//                     if ($results['status']== 'OK')
    //                         $json = CJSON::encode(array('status'=>'OK', 'userId'=>$Joinmodel->id, 'page'=>'SIGNUP', 'action'=>'PHONE_SIGNUP', 'message'=>Yii::t('frontend','USER_JOINED_PHONE')));
    //                     else
    //                         $json = CJSON::encode(array('status'=>'NOT_OK', 'userId'=>$Joinmodel->id, 'page'=>'SIGNUP', 'action'=>'PHONE_SIGNUP', 'message'=>$results['message'], 'error'=>$results['error'] ));

//                 break;
    //             case 2://email
    //                     //Creating user status for the user
    //                     $userStatus = UserStatusManager::createUserStatusManagerRecord($Joinmodel->id, $modifiedBy=null, $status_manager_fk = 9);//Not active Regular Sign up

//                     //preparing the email settings
    //                     $emailView = 'userActivateBilingual';
    //                     $layout = 'bilingual';
    //                     $emailSubject = Yii::t('frontend', 'USR_REG', array(), null, 'fr') . ' / ' . Yii::t('frontend', 'USR_REG', array(), null, 'en');

//                     //Sending the email
    //                     $sentEmail = User::sendEmailWithTemplate($Joinmodel, $emailView, $layout, $emailSubject, $addLink = true );

//                     //decoding the response
    //                     $sentEmail = CJSON::decode($sentEmail);

//                     if ($sentEmail['status']== 'success')
    //                         $json = CJSON::encode(array('status'=>'OK', 'userId'=>$Joinmodel->id, 'page'=>'SIGNUP', 'action'=>'EMAIL_SIGNUP', 'message'=>Yii::t('frontend','USER_JOINED') ));
    //                     else
    //                         $json = CJSON::encode(array('status'=>'NOT_OK', 'userId'=>$Joinmodel->id, 'page'=>'SIGNUP', 'action'=>'EMAIL_SIGNUP', 'message'=>Yii::t('frontend','UNKNOWN_ERROR'), 'error'=>$sentEmail['errors'] ));

//                 break;
    //             default:
    //                     $json = CJSON::encode(array('status'=>'NOT_OK', 'page'=>'SIGNUP', 'action'=>'EMAIL_SIGNUP', 'message'=>Yii::t('frontend','UNKNOWN_ERROR'), 'error'=>$sentEmail['errors'] ));
    //                 break;
    //         }

//         return $json;
    //     }

    /**
     * Sending email or sms depends of signupMethod(phone, email)
     *
     * Used on : Website(Signup)
     *
     * @version Website June Release
     * @param int $signupMethod, object User
     * @access public
     * @return JSON
     */
//Patricia - July 30th, 2015 - Cleanning OLD code
    //     public static function smartPhoneSignupSendEmailOrSMS($signupMethod, $Joinmodel,$isFromIOS=false, $isFromAndroid=false)
    //     {
    //         switch ($signupMethod)
    //         {
    //             case 1://phone

//                 //inserting in user Status Manager
    //                 $userStatus = UserStatusManager::createUserStatusManagerRecord($Joinmodel->id, $modifiedBy=null, $status_manager_fk = 19);//Not active Regular Phone Sign up

//                 $results = User::sendSMS($Joinmodel);

//                 if ($results['status']== 'OK')
    //                     $result = array('status'=>'OK', 'message'=>Yii::t('frontend','USER_JOINED_PHONE'));
    //                 else
    //                     $result = array('status'=>'NOT_OK', 'message'=>$results['message'],'action'=>'PHONE_SIGNUP');

//                 break;
    //             case 2://email
    //                 //Creating user status for the user
    //                 $userStatus = UserStatusManager::createUserStatusManagerRecord($Joinmodel->id, $modifiedBy=null, $status_manager_fk = 9);//Not active Regular Sign up

//                 //preparing the email settings
    //                 $emailView = 'userActivateBilingual';
    //                 $layout = 'bilingual';
    //                 $emailSubject = Yii::t('frontend', 'USR_REG', array(), null, 'fr') . ' / ' . Yii::t('frontend', 'USR_REG', array(), null, 'en');

//                 //Sending the email
    //                 $sentEmail = User::sendEmailWithTemplate($Joinmodel, $emailView, $layout, $emailSubject, $addLink = true, $isFromIOS,$isFromAndroid);

//                 //decoding the response
    //                 $sentEmail = CJSON::decode($sentEmail);
    //                 if ($sentEmail['status']== 'success')
    //                     $result = array('status'=>'OK', 'message'=>Yii::t('frontend','USER_JOINED'));
    //                 else
    //                     $result = array('status'=>'NOT_OK', 'message'=>Yii::t('frontend','UNKNOWN_ERROR'), 'action'=>'EMAIL_SIGNUP');

//                 break;
    //             default:
    //                 $result = array('status'=>'NOT_OK', 'message'=>Yii::t('frontend','UNKNOWN_ERROR'));
    //                 break;
    //         }

//         return $result;
    //     }

    /**
     * this is to check if the user is trying to change his email/phone to same old one
     * and preventing him from doing so
     * used on smartphone (ios/android)
     * <AUTHOR> N
     * @version March April 2016 V4.0
     * @param string $userId
     * @param string $newPhoneEmail
     * @return BOOL if true same phone/email if false it's not
     */
    public static function checkifNewPhoneEmailIsSameAsOldV4($userId, $newPhoneEmail, $isPhone)
    {
        if (strlen((string)trim((string)$newPhoneEmail)) > 0) {
            if ($isPhone) {
                return User::model()->findByPk($userId)->phone == $newPhoneEmail;
            } else {
                return User::model()->findByPk($userId)->email == $newPhoneEmail;
            }

        }
        return false;
    }

    /**
     * this is to check if the user is trying to change his email/phone to same old one
     * and preventing him from doing so
     * @param string $userId
     * @param string $newPhoneEmail
     * @return BOOL if true same phone/email if false it's not
     */
    public static function checkifNewPhoneEmailIsSameAsOld($userId, $newPhoneEmail)
    {
        if (strlen((string)trim((string)$newPhoneEmail)) > 0) {
            if (strpos((string)$newPhoneEmail, "@") == null) {
                return User::model()->findByPk($userId)->phone == $newPhoneEmail;
            } else {
                return User::model()->findByPk($userId)->email == $newPhoneEmail;
            }

        }
        return false;
    }

    /**
     * Key for encryption / decryption
     *
     * Used in : Website(TopMenu->acceptReferal)
     *
     * @version July - August release V 2.1.1
     * @access public
     * @return string HEX
     */
    public static function getUrlCryptKey()
    {
        return $key = pack('H*', "bcb04b7e103a0cd8b54763051cef08bc55abe029fdebae5e1d417e2ffb2a00a3");
    }

    /**
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * Encrypting a string by key
     *
     * Used in : Website(TopMenu->acceptReferal)
     *
     * @version July - August release V 2.1.1
     * @access public
     * @return string
     */
    /*
     * PATRICIA - APRIL 26TH 2015 - @TODO MOVE TO Utils class
     */
    public static function encrypString($plaintext)
    {
        # --- ENCRYPTION ---

        $key = User::getUrlCryptKey();

        # show key size use either 16, 24 or 32 byte keys for AES-128, 192
        # and 256 respectively
        $key_size = strlen((string)$key);
        //echo "Key size: " . $key_size . "\n";

        # create a random IV to use with CBC encoding
        $iv_size = mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC);
        $iv = mcrypt_create_iv($iv_size, MCRYPT_DEV_URANDOM);

        # creates a cipher text compatible with AES (Rijndael block size = 128)
        # to keep the text confidential
        # only suitable for encoded input that never ends with value 00h
        # (because of default zero padding)
        $ciphertext = mcrypt_encrypt(MCRYPT_RIJNDAEL_128, $key,
            $plaintext, MCRYPT_MODE_CBC, $iv);

        # prepend the IV for it to be available for decryption
        $ciphertext = $iv . $ciphertext;

        # encode the resulting cipher text so it can be represented by a string
        $ciphertext_base64 = base64_encode($ciphertext);

        return rawurlencode($ciphertext_base64); //important rawurlencode for + symbol in url

    }

    /**
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * Decrypting a string by key
     *
     * Used in : Website(TopMenu->acceptReferal)
     *
     * @version July - August release V 2.1.1
     * @access public
     * @return string
     */
    public static function decryptString($ciphertext_base64)
    {
        # --- DECRYPTION ---

        $key = User::getUrlCryptKey();

        # show key size use either 16, 24 or 32 byte keys for AES-128, 192
        # and 256 respectively
        $key_size = strlen((string)$key);
        //echo "Key size: " . $key_size . "\n";

        # create a random IV to use with CBC encoding
        $iv_size = mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC);
        $iv = mcrypt_create_iv($iv_size, MCRYPT_DEV_URANDOM);

        $ciphertext_dec = base64_decode($ciphertext_base64);

        # retrieves the IV, iv_size should be created using mcrypt_get_iv_size()
        $iv_dec = substr((string)$ciphertext_dec, 0, $iv_size);

        # retrieves the cipher text (everything except the $iv_size in the front)
        $ciphertext_dec = substr((string)$ciphertext_dec, $iv_size);

        # may remove 00h valued characters from end of plain text
        $plaintext_dec = mcrypt_decrypt(MCRYPT_RIJNDAEL_128, $key,
            $ciphertext_dec, MCRYPT_MODE_CBC, $iv_dec);

        return rawurldecode($plaintext_dec);
    }

    /**
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * Used to get params from a string serarated by comma
     *
     * Used in : Website(TopMenu->acceptReferal)
     *
     * @version July - August release V 2.1.1
     * @access public
     * @return array
     */

    public static function getParam($str, $paramKey)
    {
        $arr = explode(',', $str??'');
        $resultArray = array();

        foreach ($arr as $arrkey) {
            $keyVal = explode('=', $arrkey);
            $key = (isset($keyVal[0])) ? $keyVal[0] : null;
            $val = (isset($keyVal[1])) ? $keyVal[1] : null;
            $resultArray[$key] = $val;
        }

        if (isset($resultArray[$paramKey])) {
            return $resultArray[$paramKey];
        } else {
            return 'NOT_OK';
        }

    }

    /**
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * Getting the lat and long from address
     * Used in : Website(Stores page / SysAdmin upload Business's contacts ) / Smartphone (DynamicProfile)
     * <AUTHOR>
     * @version July - August 2014 - release V 2.1.1
     * @access public
     * @return JSON/false
     */
    public static function getLongLatFromAddress($dlocation)
    {
        //echo $dlocation
        $address = $dlocation; // Google HQ
        $prepAddr = str_replace(' ', '+', $address);
        try
        {
            $geocode = file_get_contents('http://maps.google.com/maps/api/geocode/json?address=' . $prepAddr . '&sensor=true');
            $output = json_decode($geocode);
            if ($output->status == 'ZERO_RESULTS') {
                return false;
            } else {
                return $output;
            }

        } catch (Exception $e) {
            return false;
        }

    }

    /**
     * <AUTHOR>
     * gets the user id by password reset fields to vaidate the user
     * @param string $q
     */
    public static function getUserIdByQ($q)
    {
        return Yii::app()->db->createCommand()
            ->select('u.id, u.passwordreset')
            ->from('user u')
            ->where('u.passwordreset = :q', array(':q' => $q))
            ->queryRow();
    }

    /**
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * Returns the Phone number formated
     * Used in : Website(SiteController)
     * <AUTHOR>
     * @version July - August 2014 release V 2.1.1
     * @since 2.1.1
     * @access public
     * @return JSON
     */
    public static function formatPhoneNumber($phone)
    {
        if(!$phone){
            $phone = '';
        }

        return "(" . substr((string)$phone, 0, 3) . ") " . substr((string)$phone, 3, 3) . "-" . substr((string)$phone, 6);
    }

    /**
     * Getting all users from db even disabled
     * Used to send emails regarding name chage to Kangaroo
     * Used in : Website(SystemAdmin)
     * <AUTHOR>
     * @version July - August release V 2.1.1
     * @access public
     * @return array
     */
    public static function getAllUsers()
    {
        $results = Yii::app()->db->createCommand()
            ->select(array(
                'u.id',
                'u.email',
                'u.phone',
                'if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username) as username',
            ))
            ->from('user u')
            ->leftJoin('user_permission up', 'up.userid = u.id')
            ->where('up.itemname = "user"')
            ->order('u.id')
        //->limit(3)
            ->queryAll();

        return $results;
    }

    /**
     * Getting all users from db even disabled
     * Used to send emails regarding name chage to Kangaroo
     * Used in : Website(SystemAdmin)
     * <AUTHOR>
     * @version July - August 2014 release V 2.1.1
     * @access public
     * @return array
     */

    public static function getAllUsersWithFailedEmails()
    {
        $results = Yii::app()->db->createCommand()
            ->select(array(
                'u.id',
                'u.email',
                'u.phone',
                'if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username) as username',
                'mf.mails_failed_pk',
                'mf.sent_email',
                'mf.sent_sms',
            ))
            ->from('user u')
            ->leftJoin('user_permission up', 'up.userid = u.id')
            ->join('mails_failed mf', 'mf.user_fk = u.id')
            ->where('up.itemname = "user" AND triggered_action LIKE "CHANGE_NAME_TO_KANGAROO"')
        //->limit(20)
            ->queryAll();

        return $results;
    }

    /**
     * Getting all business admins
     * Used to send emails regarding name chage to Kangaroo
     * Used in : Website(SystemAdmin)
     * <AUTHOR>
     * @version July - August 2014 release V 2.1.1
     * @access public
     * @return array
     */
    public static function getAllBusinessAdminUsers()
    {
        $results = Yii::app()->db->createCommand()
            ->select(array(
                'u.id',
                'u.email',
                'uep.entity_fk',
                'uep.entity_type_fk',
                'up.itemname',
                'if(INSTR(username,"@")>0,SUBSTRING_INDEX(username, "@",1),username) as username',
            ))
            ->from('user_entity_permission uep')
            ->join('user u', 'u.id=uep.user_fk')
            ->join('user_permission up', 'up.userid=u.id')
            ->where('up.itemname="admin"')
            ->group('u.id')
        //->limit(2)
            ->queryAll();
        return ($results);
    }

    /**
     * Getting all admins with failed emails
     * Used to send emails regarding name chage to Kangaroo
     * Used in : Website(SystemAdmin)
     * <AUTHOR>
     * @version July - August 2014 release V 2.1.1
     * @access public
     * @return array
     */
    public static function getAllAdminsWithFailedEmails()
    {
        $results = Yii::app()->db->createCommand()
            ->select(array(
                'u.id',
                'u.email',
                'uep.entity_fk',
                'uep.entity_type_fk',
                'up.itemname',
                'mf.mails_failed_pk',
                'mf.sent_email',
                'mf.sent_sms',
            ))
            ->from('user_entity_permission uep')
            ->join('user u', 'u.id=uep.user_fk')
            ->join('user_permission up', 'up.userid=u.id')
            ->join('mails_failed mf', 'mf.user_fk = u.id')
            ->where('up.itemname="admin" AND triggered_action LIKE "CHANGE_NAME_TO_KANGAROO_ADMIN"')
            ->group('u.id')
        //->limit(1)
            ->queryAll();

        return $results;
    }

    /**
     * Valentin - May 04, 2015 - Added in Utils, to be removed after the new function will be used everywhere
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     *
     * Generating a random string with a specific length used as a token for activation, redirecting from email
     * Used on : Website (Reset password, sending email, user activation)
     * @todo To be removed. DO NOT USE. Use the one from Utils instead
     * <AUTHOR>
     * @version Website May-June 2014 Release
     * @param int length
     * @access public
     * @return string
     */
    public static function generateRandomString($length)
    {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        return substr((string)microtime(true) . str_shuffle($chars), 0, $length);
    }

    /**
     * Valentin - May 06, 2015 - Added in Utils, to be removed from user model after the function from Utils will be used everywhere
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * Getting IP address
     * Used on : Website(Merchant signup)
     * @todo To be removed. DO NOT USE. Use the one from Utils instead
     * <AUTHOR>
     * @version Aug - Sept 2014 V3.1.1
     * @param
     * @access public
     * @return string
     */
    public static function getExternalIP()
    {
        $ipaddress = null;
        foreach (array(
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR',
        ) as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    if (filter_var($ip, FILTER_VALIDATE_IP) !== false) {
                        $ipaddress = $ip;
                    }
                }
            }
        }

        if ($ipaddress == '127.0.0.1' || $ipaddress == '::1' || substr((string)$ipaddress??'', 0, 8) == '192.168.') {
            //return '*************';
            // is a local ip address
            $externalContent = file_get_contents('http://checkip.dyndns.com/');
            preg_match('/Current IP Address: ([\[\]:.[0-9a-fA-F]+)</', $externalContent, $m);
            return $m[1];
        } else {

            return $ipaddress;
        }

    }

    /**
     * Valentin - May 06, 2015 - Added in Utils, to be removed from user model after the function from Utils will be used everywhere
     * Getting latitude and longitude from user address, current IP or Signup IP
     *
     * Used in : Website(Stores)
     *
     * @todo To be removed. DO NOT USE. Use the one from Utils instead
     * @version August - Sept release V 3.1.1
     * @access public
     * @return array
     */
    public static function getLatLongFromAddressOrIp($userId, $ip = null)
    {
        $lat = null;
        $long = null;
        try
        {
            $userModel = User::model()->findByPk($userId);

            if ($userModel->city_fk != null && $userModel->region_fk != null && ($userModel->address != null || $userModel->address != '')) //get lat, long from user address
            {
                $cityName = Cities::model()->findByPk($userModel->city_fk)->name;
                $stateName = Regions::model()->findByPk($userModel->region_fk)->name;
                $countryName = Countries::model()->findByPk($userModel->country_fk)->name;
                $address = $userModel->address . ', ' . $cityName . ', ' . $stateName . ', ' . $countryName;
                $latLongFromAddress = User::getLongLatFromAddress($address);
                //print_r($latLongFromAddress); die();

                if (isset($latLongFromAddress) && $latLongFromAddress != false) {
                    $lat = $latLongFromAddress->results[0]->geometry->location->lat;
                    $long = $latLongFromAddress->results[0]->geometry->location->lng;
                }
                //Yii::log('info', CLogger::LEVEL_INFO, 'Geo lat, long from address');
            } else //get from IP
            {
                if ($ip == null) {
                    $ip = User::getExternalIP();
                }

                if ($ip != null) //current IP
                {
                    $location = Utils::lookupLocation($ip); // '*************'

                    $lat = $location->latitude;
                    $long = $location->longitude;
                    // Yii::log('info', CLogger::LEVEL_INFO, 'Geo lat, long from Externatl Ip');
                } else // user signup IP
                {
                    $ip = $userModel->ip;
                    $location = Utils::lookupLocation($ip); // '*************'

                    $lat = $location->latitude;
                    $long = $location->longitude;
                }
            }

            return array('lat' => $lat, 'long' => $long);
        } catch (Exception $e) {
            return array('lat' => $lat, 'long' => $long);
        }

    }

    /**
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * Converting string to utf_8
     *
     * Used on : Website(Merchant signup - getting city from geoIp)
     *
     * @version Aug - Sept V3.1.1
     * @param
     * @access public
     * @return boolean
     */

    public static function seems_utf8($str)
    {
        $length = strlen((string)$str??'');
        for ($i = 0; $i < $length; $i++) {
            $c = ord($str[$i]);
            if ($c < 0x80) {
                $n = 0;
            }
            # 0bbbbbbb
            elseif (($c & 0xE0) == 0xC0) {
                $n = 1;
            }
            # 110bbbbb
            elseif (($c & 0xF0) == 0xE0) {
                $n = 2;
            }
            # 1110bbbb
            elseif (($c & 0xF8) == 0xF0) {
                $n = 3;
            }
            # 11110bbb
            elseif (($c & 0xFC) == 0xF8) {
                $n = 4;
            }
            # 111110bb
            elseif (($c & 0xFE) == 0xFC) {
                $n = 5;
            }
            # 1111110b
            else {
                return false;
            }
            # Does not match any model
            for ($j = 0; $j < $n; $j++) {
                # n bytes matching 10bbbbbb follow ?
                if ((++$i == $length) || ((ord($str[$i]) & 0xC0) != 0x80)) {
                    return false;
                }

            }
        }
        return true;
    }

    /**
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * Converts all accent characters to ASCII characters.
     *
     * If there are no accent characters, then the string given is just returned.
     *
     * @version Aug - Sept V3.1.1
     * @param string $string Text that might have accent characters
     * @return string Filtered string with replaced characters.
     */
    public static function removeAccents($string)
    {
        if (!preg_match('/[\x80-\xff]/', $string)) {
            return $string;
        }

        if (User::seems_utf8($string)) {
            $chars = array(
                // Decompositions for Latin-1 Supplement
                chr(195) . chr(128) => 'A', chr(195) . chr(129) => 'A',
                chr(195) . chr(130) => 'A', chr(195) . chr(131) => 'A',
                chr(195) . chr(132) => 'A', chr(195) . chr(133) => 'A',
                chr(195) . chr(135) => 'C', chr(195) . chr(136) => 'E',
                chr(195) . chr(137) => 'E', chr(195) . chr(138) => 'E',
                chr(195) . chr(139) => 'E', chr(195) . chr(140) => 'I',
                chr(195) . chr(141) => 'I', chr(195) . chr(142) => 'I',
                chr(195) . chr(143) => 'I', chr(195) . chr(145) => 'N',
                chr(195) . chr(146) => 'O', chr(195) . chr(147) => 'O',
                chr(195) . chr(148) => 'O', chr(195) . chr(149) => 'O',
                chr(195) . chr(150) => 'O', chr(195) . chr(153) => 'U',
                chr(195) . chr(154) => 'U', chr(195) . chr(155) => 'U',
                chr(195) . chr(156) => 'U', chr(195) . chr(157) => 'Y',
                chr(195) . chr(159) => 's', chr(195) . chr(160) => 'a',
                chr(195) . chr(161) => 'a', chr(195) . chr(162) => 'a',
                chr(195) . chr(163) => 'a', chr(195) . chr(164) => 'a',
                chr(195) . chr(165) => 'a', chr(195) . chr(167) => 'c',
                chr(195) . chr(168) => 'e', chr(195) . chr(169) => 'e',
                chr(195) . chr(170) => 'e', chr(195) . chr(171) => 'e',
                chr(195) . chr(172) => 'i', chr(195) . chr(173) => 'i',
                chr(195) . chr(174) => 'i', chr(195) . chr(175) => 'i',
                chr(195) . chr(177) => 'n', chr(195) . chr(178) => 'o',
                chr(195) . chr(179) => 'o', chr(195) . chr(180) => 'o',
                chr(195) . chr(181) => 'o', chr(195) . chr(182) => 'o',
                chr(195) . chr(182) => 'o', chr(195) . chr(185) => 'u',
                chr(195) . chr(186) => 'u', chr(195) . chr(187) => 'u',
                chr(195) . chr(188) => 'u', chr(195) . chr(189) => 'y',
                chr(195) . chr(191) => 'y',
                // Decompositions for Latin Extended-A
                chr(196) . chr(128) => 'A', chr(196) . chr(129) => 'a',
                chr(196) . chr(130) => 'A', chr(196) . chr(131) => 'a',
                chr(196) . chr(132) => 'A', chr(196) . chr(133) => 'a',
                chr(196) . chr(134) => 'C', chr(196) . chr(135) => 'c',
                chr(196) . chr(136) => 'C', chr(196) . chr(137) => 'c',
                chr(196) . chr(138) => 'C', chr(196) . chr(139) => 'c',
                chr(196) . chr(140) => 'C', chr(196) . chr(141) => 'c',
                chr(196) . chr(142) => 'D', chr(196) . chr(143) => 'd',
                chr(196) . chr(144) => 'D', chr(196) . chr(145) => 'd',
                chr(196) . chr(146) => 'E', chr(196) . chr(147) => 'e',
                chr(196) . chr(148) => 'E', chr(196) . chr(149) => 'e',
                chr(196) . chr(150) => 'E', chr(196) . chr(151) => 'e',
                chr(196) . chr(152) => 'E', chr(196) . chr(153) => 'e',
                chr(196) . chr(154) => 'E', chr(196) . chr(155) => 'e',
                chr(196) . chr(156) => 'G', chr(196) . chr(157) => 'g',
                chr(196) . chr(158) => 'G', chr(196) . chr(159) => 'g',
                chr(196) . chr(160) => 'G', chr(196) . chr(161) => 'g',
                chr(196) . chr(162) => 'G', chr(196) . chr(163) => 'g',
                chr(196) . chr(164) => 'H', chr(196) . chr(165) => 'h',
                chr(196) . chr(166) => 'H', chr(196) . chr(167) => 'h',
                chr(196) . chr(168) => 'I', chr(196) . chr(169) => 'i',
                chr(196) . chr(170) => 'I', chr(196) . chr(171) => 'i',
                chr(196) . chr(172) => 'I', chr(196) . chr(173) => 'i',
                chr(196) . chr(174) => 'I', chr(196) . chr(175) => 'i',
                chr(196) . chr(176) => 'I', chr(196) . chr(177) => 'i',
                chr(196) . chr(178) => 'IJ', chr(196) . chr(179) => 'ij',
                chr(196) . chr(180) => 'J', chr(196) . chr(181) => 'j',
                chr(196) . chr(182) => 'K', chr(196) . chr(183) => 'k',
                chr(196) . chr(184) => 'k', chr(196) . chr(185) => 'L',
                chr(196) . chr(186) => 'l', chr(196) . chr(187) => 'L',
                chr(196) . chr(188) => 'l', chr(196) . chr(189) => 'L',
                chr(196) . chr(190) => 'l', chr(196) . chr(191) => 'L',
                chr(197) . chr(128) => 'l', chr(197) . chr(129) => 'L',
                chr(197) . chr(130) => 'l', chr(197) . chr(131) => 'N',
                chr(197) . chr(132) => 'n', chr(197) . chr(133) => 'N',
                chr(197) . chr(134) => 'n', chr(197) . chr(135) => 'N',
                chr(197) . chr(136) => 'n', chr(197) . chr(137) => 'N',
                chr(197) . chr(138) => 'n', chr(197) . chr(139) => 'N',
                chr(197) . chr(140) => 'O', chr(197) . chr(141) => 'o',
                chr(197) . chr(142) => 'O', chr(197) . chr(143) => 'o',
                chr(197) . chr(144) => 'O', chr(197) . chr(145) => 'o',
                chr(197) . chr(146) => 'OE', chr(197) . chr(147) => 'oe',
                chr(197) . chr(148) => 'R', chr(197) . chr(149) => 'r',
                chr(197) . chr(150) => 'R', chr(197) . chr(151) => 'r',
                chr(197) . chr(152) => 'R', chr(197) . chr(153) => 'r',
                chr(197) . chr(154) => 'S', chr(197) . chr(155) => 's',
                chr(197) . chr(156) => 'S', chr(197) . chr(157) => 's',
                chr(197) . chr(158) => 'S', chr(197) . chr(159) => 's',
                chr(197) . chr(160) => 'S', chr(197) . chr(161) => 's',
                chr(197) . chr(162) => 'T', chr(197) . chr(163) => 't',
                chr(197) . chr(164) => 'T', chr(197) . chr(165) => 't',
                chr(197) . chr(166) => 'T', chr(197) . chr(167) => 't',
                chr(197) . chr(168) => 'U', chr(197) . chr(169) => 'u',
                chr(197) . chr(170) => 'U', chr(197) . chr(171) => 'u',
                chr(197) . chr(172) => 'U', chr(197) . chr(173) => 'u',
                chr(197) . chr(174) => 'U', chr(197) . chr(175) => 'u',
                chr(197) . chr(176) => 'U', chr(197) . chr(177) => 'u',
                chr(197) . chr(178) => 'U', chr(197) . chr(179) => 'u',
                chr(197) . chr(180) => 'W', chr(197) . chr(181) => 'w',
                chr(197) . chr(182) => 'Y', chr(197) . chr(183) => 'y',
                chr(197) . chr(184) => 'Y', chr(197) . chr(185) => 'Z',
                chr(197) . chr(186) => 'z', chr(197) . chr(187) => 'Z',
                chr(197) . chr(188) => 'z', chr(197) . chr(189) => 'Z',
                chr(197) . chr(190) => 'z', chr(197) . chr(191) => 's',
                // Euro Sign
                chr(226) . chr(130) . chr(172) => 'E',
                // GBP (Pound) Sign
                chr(194) . chr(163) => '');

            $string = strtr($string, $chars);
        } else {
            // Assume ISO-8859-1 if not UTF-8
            $chars['in'] = chr(128) . chr(131) . chr(138) . chr(142) . chr(154) . chr(158)
            . chr(159) . chr(162) . chr(165) . chr(181) . chr(192) . chr(193) . chr(194)
            . chr(195) . chr(196) . chr(197) . chr(199) . chr(200) . chr(201) . chr(202)
            . chr(203) . chr(204) . chr(205) . chr(206) . chr(207) . chr(209) . chr(210)
            . chr(211) . chr(212) . chr(213) . chr(214) . chr(216) . chr(217) . chr(218)
            . chr(219) . chr(220) . chr(221) . chr(224) . chr(225) . chr(226) . chr(227)
            . chr(228) . chr(229) . chr(231) . chr(232) . chr(233) . chr(234) . chr(235)
            . chr(236) . chr(237) . chr(238) . chr(239) . chr(241) . chr(242) . chr(243)
            . chr(244) . chr(245) . chr(246) . chr(248) . chr(249) . chr(250) . chr(251)
            . chr(252) . chr(253) . chr(255);

            $chars['out'] = "EfSZszYcYuAAAAAACEEEEIIIINOOOOOOUUUUYaaaaaaceeeeiiiinoooooouuuuyy";

            $string = strtr($string, $chars['in'], $chars['out']);
            $double_chars['in'] = array(chr(140), chr(156), chr(198), chr(208), chr(222), chr(223), chr(230), chr(240), chr(254));
            $double_chars['out'] = array('OE', 'oe', 'AE', 'DH', 'TH', 'ss', 'ae', 'dh', 'th');
            $string = str_replace($double_chars['in'], $double_chars['out'], $string);
        }

        return $string;
    }

    /**
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * Looping thought permissions and checking if a specific permission is granted or declined
     *
     * Using Facebook SDK 4.0
     *
     * @version Aug - Sept V3.1.1
     * @param string $scopeValue can be: email, user_birthday, user_friends etc
     * @param array $graphObjectPermissions is an array of permission objects
     * @return boolean
     */
    public static function checkFacebookPermissionGranted($scopeValue, $graphObjectPermissions)
    {
        $permissions = array();
        foreach ($graphObjectPermissions as $key => $permObject) {
            $permissions[$permObject->permission] = $permObject->status;
        }

        //check for  permission status
        if (array_key_exists($scopeValue, $permissions) && $permissions[$scopeValue] == 'granted') {
            return true;
        }

        return false;
    }

    public static function checkExsistence($tableName, $fieldName, $value)
    {
        return Yii::app()->db->createCommand()
            ->select('count(1)')
            ->from("$tableName")
            ->where("$fieldName=:value", array(':value' => $value))
            ->queryScalar() > 0;
    }
    /**
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * starting a facebook session or use a existing session
     *
     * Using Facebook SDK 4.0
     *
     * @version Aug - Sept V3.1.1
     * @return object $session or null
     */
//    public static function getFacebookSession($redirectUrl)
//    {
//        \Facebook\FacebookSession::setDefaultApplication(Yii::app()->params['fbapikey'], Yii::app()->params['fbapisecret']);
//
//        $helper = new \Facebook\FacebookRedirectLoginHelper($redirectUrl);
//
//        //print_r($helper);
//        // see if a existing session exists
//        if (isset($_SESSION) && isset($_SESSION['fb_token'])) {
//            // create new session from saved access_token
//            $session = new \Facebook\FacebookSession($_SESSION['fb_token']);
//
//            //Yii::log('Facebook SDK 4.0', CLogger::LEVEL_INFO, 'fb_token in session is not set');
//            //print_r($_SESSION['fb_token']); echo '<br>';
//            // validate the access_token to make sure it's still valid
//            try
//            {
//                if (!$session->validate()) {
//                    $session = null;
//                }
//                //Yii::log('Facebook SDK 4.0', CLogger::LEVEL_INFO, 'Try to validate the session');
//            } catch (Exception $e) {
//                // catch any exceptions
//                //Yii::log('Facebook SDK 4.0', CLogger::LEVEL_INFO, 'catch Validate session Exception');
//                $session = null;
//            }
//        }
//
//        if (!isset($session) || $session === null) {
//            // no session exists
//            //Yii::log('Facebook SDK 4.0', CLogger::LEVEL_INFO, 'session is null');
//            try
//            {
//                $session = $helper->getSessionFromRedirect();
//                //print_r($session);
//                //Yii::log('Facebook SDK 4.0', CLogger::LEVEL_INFO, 'try getSessionFromRedirect');
//
//            } catch (\Facebook\FacebookRequestException $ex) {
//                //Yii::log('Facebook SDK 4.0', CLogger::LEVEL_INFO, 'catch FacebookRequestException');
//                print_r($ex);
//                //return null;
//            } catch (Exception $ex) {
//                //Yii::log('Facebook SDK 4.0', CLogger::LEVEL_INFO, 'catch Exception');
//                print_r($ex);
//                //return null;
//            }
//
//        }
//
//        //print_r($session); die();
//        // see if we have a session
//        if (isset($session)) {
//            //Yii::log('Facebook SDK 4.0', CLogger::LEVEL_INFO, 'isset session');
//            // save the session
//            $_SESSION['fb_token'] = $session->getToken();
//            //Yii::log('Facebook SDK 4.0', CLogger::LEVEL_INFO, 'token='.$_SESSION['fb_token']);
//            // create a session using saved token or the new one we generated at login
//            $session = new \Facebook\FacebookSession($session->getToken());
//
//            return $session;
//
//        } else {
//            return null;
//        }
//    }

    /**
     * TIMEZONE - Globalization implementation
     * Getting chart data for monthly users
     * Used in : Website(SystemAdmin)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function chartDataTotalMonthlyUsers()
    {
        $results = Yii::app()->db->createCommand()
            ->select(
                'SUBSTRING(u.utc_created,1,7) as date, count(id) as total'
            )
            ->from('user u')
            ->join('user_permission up', 'u.id=up.userid')
            ->where("up.itemname='user' AND u.utc_created > '2014-08-01'")
            ->group('YEAR(u.utc_created), MONTH(u.utc_created)')
            ->queryAll();
        return $results;
    }

    /**
     * Getting chart data for daily users
     * Used in : SystemAdmin
     * @access public
     * @return array()
     */
    public static function getTotalDailyUsers()
    {
        $results = Yii::app()->db->createCommand()
            ->select(
                'SUBSTRING(created,1,10) as date, count(id) as total'
            )
            ->from('user u')
            ->group('YEAR(created), MONTH(created), DAY(created)')
            ->queryAll();
        return $results;
    }

    /**
     * All users in the system (only with user permission)
     * Used in : CronJob(Send SMS with PIN)
     *
     * @version Dec - Jan V3.2
     * @access public
     * @return array()
     */
    public static function getAllUsersV32()
    {
        $results = Yii::app()->db->createCommand()
            ->select(
                'u.id, u.email, u.phone, u.pin_permission'
            )
            ->from('user u')
            ->join('user_permission up', 'u.id=up.userid')
            ->where("up.itemname='user'")
            ->queryAll();
        return $results;
    }

    public static function anonymizeUser($user, $platform = PLATFORM_WEBSITE)
    {
        try {
            $kangarooApiProvider = new \KangarooRewards\OAuth2\Client\Provider\Kangaroo([
                'clientId' => Yii::app()->params['kangarooApiClientId'],
                'clientSecret' => Yii::app()->params['kangarooApiClientSecret'],
                'urlAccessToken' => Yii::app()->params['kangarooApiUri'] . '/oauth/token',
                'redirectUri' => null,
            ]);

            // Retrieve Access Token using client_credentials grant
            $token = $kangarooApiProvider->getAccessToken('client_credentials', [
                'scope' => 'admin',
            ]);

            $business = Business::model()->findByPk($user->business_fk);
            $branch = BusinessBranch::model()->findByAttributes([
                'business_fk' => $user->business_fk,
                'default_branch' => 1
            ]);

            $api = new KangarooApiClient($business, $branch, $token->getToken(), null);
            $api->internalAnonymizeUser($user, ['platform_id' => $platform]);

        } catch (\Exception $e) {
            ForgottenDataRequest::findOrCreate($user, $platform);
        }
    }

/****************************************************************************************************/
/*********************************** ALL PUBLIC Functions START HERE **********************************/
/****************************************************************************************************/

    /**
     * Getting user info for CRM business
     *
     * Used in : Website(Dashboard, trx list)
     *
     * @version January 2016 release V3.5.0
     * @since 3.5.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function getUserInfoCrm($userId)
    {
        return $results = Yii::app()->db->createCommand()
            ->select(
                'u.id, u.email, u.phone, u.first, u.last'
            )
            ->from('user u')
            ->where('u.id=:userId', [':userId' => $userId])
            ->queryRow();
    }

    /**
     * Getting the current email or cell for a user by userId
     *
     * Used in : Website()
     *
     * @version January 2016 release V3.5.0
     * @since 3.5.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function getUserEmailCell($userId)
    {
        $user = User::model()->findByPk($userId);

        if ($user == null) {
            throw new Exception("User is NULL", 1);
        }

        if ($user->email != null && $user->email != '') {
            return $user->email;
        } elseif ($user->phone != null && $user->phone != '') {
            return $user->phone;
        } else {
            throw new Exception("User doesnt have  neither email nor cell", 1);
        }

    }

    /**
     * User signup for Transfer Points: sending verification email or SMS
     * Include shortUrlKg as a main function for shortening, timezone
     * Gift points email template changes (3.4.1)
     *
     * Used in Website(Signup), Smartphone (iphone/android), Merchant Tablet app
     * @version Oct - Nov 2015 release V3.4.1
     * @since     3.3.1
     * <AUTHOR> David S
     * @param   int $businessId
     * @param   int $transferUnits
     * @param   int $senderUserId
     * @param   int $notifReqFk - 29 - Transfer points  30 - Gift card purchase
     * @param   int $timeZoneId
     * @param   object $userModel
     * @param   array $phoneEmail
     * @param   boolean $isNewUser
     * @param   string $platformName
     * @param     string $strLang - language en, fr
     * @param     string $senderName
     * @param     string $recipientName
     * @param     int $offerId - offer Id in case of a Gift Card offer
     * @param     boolean $merchantAppPurchase - is from the MerchantApp
     * @access  public
     * @return  array
     */
    /*public static function sendTransferPointsEmailSMSV341($businessId, $transferUnits, $senderUserId, $notifReqFk, $timeZoneId, $userModel, $phoneEmail, $isNewUser, $platformName, $strLang = 'en', $senderName = '', $recipientName = '', $offerId, $merchantAppPurchase)
    {
        Yii::log('ARGS=' . CJSON::encode(func_get_args()), CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

        $giftCardImage = null;
        $amount = round($transferUnits / 100, 2);

        if ($isNewUser) {
            $randomToken = User::model()->generateRandomString(60);
            $randomPin = User::generatePin();

            //saving token and expiry date in user model
            $userModel->passwordreset = $randomToken;
            $userModel->enabled = 1;
            $userModel->password = '';
            $userModel->pin_permission = User::cryptPassword($randomPin);
            $userModel->pin_enabled = 1;
            $userModel->utc_link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
            $userModel->timezone_mysql_fk = $timeZoneId;
            $userModel->save();

            UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 35, '', '', $userModel->platform_solution_fk, $timeZoneId); //PIN_CODE_AUTO_GENERATED

            $emailLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 0, 'e' => 1, 'g' => 1, 'o' => $offerId, 'b' => $businessId, 'l' => $strLang, 'a' => $amount]); //c-cell phone , e- email, g - from gift card purchase
            $phoneLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 1, 'e' => 0, 'g' => 1, 'o' => $offerId, 'b' => $businessId, 'l' => $strLang, 'a' => $amount]); //c-cell phone , e- email, g - from gift card purchase

            //Yii::log('New User', CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);
        } else {
            //Yii::log('User exists', CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);
            $emailLink = Yii::app()->createAbsoluteUrl('site/login');
            $phoneLink = Yii::app()->createAbsoluteUrl('site/login');
            $randomPin = '';
        }

        $businessName = Business::getBusinessNameEvenDisabledV220($businessId);
        $mainBranch = BusinessBranch::getDefaultBranch($businessId);
        $businessModel = Business::model()->findByPk($businessId);
        $currency = Currency::getCurrencySymbolByBranchId($mainBranch['business_branch_pk'], $platformName);
        $currencySymbol = $currency['currencySymbol'];

        if ($strLang == 'en') {
            $senderName1 = ($senderName != '') ? $senderName : 'A friend ';
        } elseif ($strLang == 'fr') {
            $senderName1 = ($senderName != '') ? $senderName : 'Un ami ';
        } else {
            $senderName1 = ($senderName != '') ? $senderName : 'A friend ';
        }

        //getting Recipient Name or first name or username
        $recipientName = self::getRecipientName($isNewUser, $recipientName, $userModel);

        if ($offerId != null) {
            $giftCardOffer = Offer::model()->findByPk($offerId);
            $giftCardImage = $giftCardOffer['offer_image' . $giftCardOffer['offer_image'] . '_medium'];
        }

        $emailData = [
            'username' => $userModel->username,
            'recipientName' => $recipientName,
            'pincode' => $randomPin,
            'link' => $emailLink,
            'isNewUser' => $isNewUser,
            'notifReqFk' => $notifReqFk,
            'senderName' => $senderName1,
            'transferUnits' => $transferUnits,
            'businessName' => $businessName,
            'amount' => $amount,
            'currencySymbol' => $currencySymbol,
            'businessLogo' => $mainBranch['logo_image_medium'],
            'giftCardImage' => $giftCardImage,
            'whiteLabel' => $businessModel->white_label_flag,
            'isTransferAmount' => true
        ];

        $emailSubject = '';
        $shortLink = Utils::shortUrlKg($phoneLink, $platformName);

        if ($isNewUser == false && $notifReqFk == 29) {
            //Kangaroo user TRANSFER
            $smsBody = Yii::t('frontend', 'FRONTEND_TRANSFER_PTS_SMS_KNGUSER', ['{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']], null, $strLang);
            $emailSubject = Yii::t('frontend', 'FRONTEND_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName), null, $strLang);
        } elseif ($isNewUser == true && $notifReqFk == 29) {
            //New user TRANSFER
            $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_TRANSFER_PTS_SMS_NEWKNGUSER', ['{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']], null, $strLang);
            $emailSubject = Yii::t('frontend', 'FRONTEND_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName), null, $strLang);
        } elseif ($isNewUser == false && $notifReqFk == 30) {
            //Kangaroo user GIFTCARD
            $smsBody = Yii::t('frontend', 'FRONTEND_GIFTCARD_TRANSFER_PTS_SMS_KNGUSER', ['{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']], null, $strLang);
            $emailSubject = Yii::t('frontend', 'FRONTEND_GIFTCARD_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName), null, $strLang);
        } elseif ($isNewUser == true && $notifReqFk == 30) {
            //New user GIFTCARD
            $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_GIFTCARD_TRANSFER_PTS_SMS_NEWKNGUSER', ['{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']], null, $strLang);
            $emailSubject = Yii::t('frontend', 'FRONTEND_GIFTCARD_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName), null, $strLang);
        }

        // Yii::log('BEFORE inserting in user Status Manager', CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);

        if ($phoneEmail['isPhone']) {
            $country = Countries::model()->findByPk($userModel->country_phone_fk);
            $phoneIsMobile = Utils::phoneNumberType($userModel->phone, $country->code, $platformName);

            if ($phoneIsMobile == 'landline') {
                return ['status' => 'NOT_OK', 'reason' => 'phone_landline', 'message' => Yii::t('frontend', 'FRONTEND_PHONE_CANNOT_RECEIVE_TEXT')];
            }

            if ($isNewUser == true)
            //inserting in user Status Manager
            {
                $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 64, $oldPhoneEmail = '', $userModel->phone, $userModel->platform_solution_fk, $timeZoneId, $userModel->country_phone_fk);
            }
            //PHONE_NOT_VERIFIED

            $result = Utils::validateAndSendSMS($smsBody, $userModel->phone, $userModel->country_phone_fk, $platformName, [], $businessId);

            if ($result['status'] == 'NOT_OK') {
                return $result;
            }

        } elseif ($phoneEmail['isEmail']) {
            if ($isNewUser == true)
            //Creating user status for the user
            {
                $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 66, $oldPhoneEmail = '', $userModel->email, $userModel->platform_solution_fk, $timeZoneId);
            }
            //EMAIL_NOT_VERIFIED

            //Sending the email
            $result = Utils::sendEmailWithTemplate('giftcardTemplate', 'bilingual', $emailSubject, $userModel->email, $emailData, $viewPath = 'application.views.mail.' . $strLang, null, $businessName, $businessId);

            if ($result['status'] == 'NOT_OK') {
                return $result;
            }

            // $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy=null, $status_manager_fk = 38, $oldEmail='', $userModel->email, $userModel->platform_solution_fk, $timeZoneId); //Email failed
        } else {
            throw new Exception("Not valid email/phone", 1);
        }

        //Sending Confirmation Email
        if ($merchantAppPurchase) {
            Utils::sendConfirmGiftCardPurchaseEmail($senderUserId, $senderName, $userModel, $emailData, $timeZoneId, $strLang, $platformName);
        }

        return ['status' => 'OK'];
    }*/

    //TODO delete when 3.4.5 is released
    /**
     * Sending confirmation email for Gift card purchae on the Tablet app
     *
     * Used in : Website()
     *
     * @version Oct - Nov 2015 release V3.4.1
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function sendConfirmGiftCardPurchaseEmail($senderUserId, $senderName, $userModel, $emailData, $timeZoneId, $strLang, $platformName)
    {
        Yii::log('ARGS=' . CJSON::encode(func_get_args()), CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

        $senderUser = User::model()->findByPk($senderUserId);

        $emailSubject = Yii::t('frontend', 'FRONTEND_GIFTCARD_CONFIRMATION_EMAIL_SUBJECT', array('{amount}' => $emailData['amount'], '{currency}' => $emailData['currencySymbol'], '{businessName}' => $emailData['businessName']), null, $strLang);

        $senderName2 = '';
        if ($senderName != '') {
            $senderName2 = $senderName;
        } elseif ($senderUser->first != null && $senderUser->first != '') {
            $senderName2 = ucfirst((string)$senderUser->first);
        } else {
            $senderName2 = 'Client';
        }

        $tmzName = TimezoneMysql::model()->findByPk($timeZoneId)->name;
        $purchaseDate = Utils::convertDateTimeToUserTmz($tmzName, date('Y-m-d H:i:s'), 'Y-m-d H:i');

        $userPhone = ($userModel->phone != '' && $userModel->phone != null) ? Utils::getFormattedPhoneNumber($userModel->phone, $userModel->country_phone_fk) : '';
        //appending to the original aray
        $emailData['username'] = $userModel->username;
        $emailData['receiverEmail'] = $userModel->email;
        $emailData['receiverCell'] = $userPhone;
        $emailData['purchaseDate'] = $purchaseDate;
        $emailData['isNewUser'] = false;
        $emailData['senderName'] = $senderName2;
        $emailData['recipientName'] = '';
        $emailData['pincode'] = '';
        $emailData['link'] = '';
        $emailData['whiteLabel'] = 0;

        $smsBody = Yii::t('frontend', 'FRONTEND_GIFTCARD_CONFIRMATION_SMS', ['{amount}' => $emailData['amount'], '{currency}' => $emailData['currencySymbol'], '{businessName}' => $emailData['businessName']], null, $strLang);

        if ($senderUser->email != '' && $senderUser->email != null) {
            Utils::sendEmailWithTemplate('giftCardPurchaseConfirm', 'bilingual', $emailSubject, $senderUser->email, $emailData, $viewPath = 'application.views.mail.' . $strLang, null, $emailData['businessName'], $senderUser->business_fk);
        } else {
            Utils::validateAndSendSMS($smsBody, $senderUser->phone, $senderUser->country_phone_fk, $platformName, [], $senderUser->business_fk);
        }

    }

    /**
     * User signup for Transfer Points: sending verification email or SMS
     * Include shortUrlKg as a main function for shortening, timezone
     *
     * Used in Website(Signup), Smartphone (iphone/android), Merchant Tablet app
     * @version July - August 2015 release V3.3.1
     * <AUTHOR> David S
     * @param   int $businessId
     * @param   int $transferUnits
     * @param   int $senderUserId
     * @param   int $notifReqFk - 29 - Transfer points  30 - Gift card purchase
     * @param   int $timeZoneId
     * @param   object $userModel
     * @param   array $phoneEmail
     * @param   boolean $isNewUser
     * @param   string $platformName
     * @access  public
     * @return  array
     */
    public static function sendTransferPointsEmailSMS($businessId, $transferUnits, $senderUserId, $notifReqFk, $timeZoneId, $userModel, $phoneEmail, $isNewUser, $platformName)
    {
        if ($isNewUser) {
            $randomToken = User::model()->generateRandomString(60);
            $randomPin = User::generatePin();

            //saving token and expiry date in user model
            $userModel->passwordreset = $randomToken;
            $userModel->enabled = 1;
            $userModel->password = '';
            $userModel->pin_permission = User::cryptPassword($randomPin);
            $userModel->pin_enabled = 1;
            $userModel->utc_link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
            $userModel->timezone_mysql_fk = $timeZoneId;
            $userModel->save();

            UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 35, '', '', $userModel->platform_solution_fk, $timeZoneId); //PIN_CODE_AUTO_GENERATED

            $emailLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 0, 'e' => 1]); //c-cell phone , e- email
            $phoneLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 1, 'e' => 0]); //c-cell phone , e- email

            //Yii::log('New User', CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);
        } else {
            //Yii::log('User exists', CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);
            $emailLink = Yii::app()->createAbsoluteUrl('site/login');
            $phoneLink = Yii::app()->createAbsoluteUrl('site/login');
            $randomPin = '';
        }

        $businessName = Business::getBusinessNameEvenDisabledV220($businessId);
        $senderName = User::getUserDisplayName($senderUserId);
        $mainBranch = BusinessBranch::getDefaultBranch($businessId);
        $currency = Currency::getCurrencySymbolByBranchId($mainBranch['business_branch_pk'], $platformName);
        $currencySymbol = $currency['currencySymbol'];

        $amount = round($transferUnits / 100, 2);

        $emailData = [
            'username' => $userModel->username,
            'pincode' => $randomPin,
            'link' => $emailLink,
            'isNewUser' => $isNewUser,
            'notifReqFk' => $notifReqFk,
            'senderName' => $senderName,
            'transferUnits' => $transferUnits,
            'businessName' => $businessName,
            'amount' => $amount,
            'currencySymbol' => $currencySymbol,
        ];

        $emailSubject = '';
        $shortLink = Utils::shortUrlKg($phoneLink, $platformName);

        if ($isNewUser == false && $notifReqFk == 29) {
//Kangaroo user TRANSFER
            $smsBody = Yii::t('frontend', 'FRONTEND_TRANSFER_PTS_SMS_KNGUSER_FR', ['{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName]);
            $smsBody .= ' / ' . Yii::t('frontend', 'FRONTEND_TRANSFER_PTS_SMS_KNGUSER_EN', ['{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName]);
            $emailSubject = Yii::t('frontend', 'FRONTEND_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName, '{amount}' => $amount, '{currency}' => $currencySymbol), null, 'en');
            $emailSubject .= ' / ' . Yii::t('frontend', 'FRONTEND_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName, '{amount}' => $amount, '{currency}' => $currencySymbol), null, 'fr');
        } elseif ($isNewUser == true && $notifReqFk == 29) {
//New user TRANSFER
            $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_TRANSFER_PTS_SMS_NEWKNGUSER_FR', ['{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']]);
            $smsBody .= ' / ' . Yii::t('frontend', 'FRONTEND_USERSIGNUP_TRANSFER_PTS_SMS_NEWKNGUSER_EN', ['{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']]);
            $emailSubject = Yii::t('frontend', 'FRONTEND_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName, '{amount}' => $amount, '{currency}' => $currencySymbol), null, 'en');
            $emailSubject .= ' / ' . Yii::t('frontend', 'FRONTEND_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName, '{amount}' => $amount, '{currency}' => $currencySymbol), null, 'fr');
        } elseif ($isNewUser == false && $notifReqFk == 30) {
//Kangaroo user GIFTCARD
            $smsBody = Yii::t('frontend', 'FRONTEND_GIFTCARD_TRANSFER_PTS_SMS_KNGUSER_FR', ['{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName]);
            $smsBody .= ' / ' . Yii::t('frontend', 'FRONTEND_GIFTCARD_TRANSFER_PTS_SMS_KNGUSER_EN', ['{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName]);
            $emailSubject = Yii::t('frontend', 'FRONTEND_GIFTCARD_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName, '{amount}' => $amount, '{currency}' => $currencySymbol), null, 'en');
            $emailSubject .= ' / ' . Yii::t('frontend', 'FRONTEND_GIFTCARD_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName, '{amount}' => $amount, '{currency}' => $currencySymbol), null, 'fr');
        } elseif ($isNewUser == true && $notifReqFk == 30) {
//New user GIFTCARD
            $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_GIFTCARD_TRANSFER_PTS_SMS_NEWKNGUSER_FR', ['{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']]);
            $smsBody .= ' / ' . Yii::t('frontend', 'FRONTEND_USERSIGNUP_GIFTCARD_TRANSFER_PTS_SMS_NEWKNGUSER_EN', ['{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']]);
            $emailSubject = Yii::t('frontend', 'FRONTEND_GIFTCARD_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName, '{amount}' => $amount, '{currency}' => $currencySymbol), null, 'en');
            $emailSubject .= ' / ' . Yii::t('frontend', 'FRONTEND_GIFTCARD_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName, '{amount}' => $amount, '{currency}' => $currencySymbol), null, 'fr');
        }

        // Yii::log('BEFORE inserting in user Status Manager', CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);

        if ($phoneEmail['isPhone']) {
            $country = Countries::model()->findByPk($userModel->country_phone_fk);
            $phoneIsMobile = Utils::phoneNumberType($userModel->phone, $country->code, $platformName);

            if ($phoneIsMobile == 'landline') {
                return ['status' => 'NOT_OK', 'reason' => 'phone_landline', 'message' => Yii::t('frontend', 'FRONTEND_PHONE_CANNOT_RECEIVE_TEXT')];
            }

            if ($isNewUser == true)
            //inserting in user Status Manager
            {
                $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 64, $oldPhoneEmail = '', $userModel->phone, $userModel->platform_solution_fk, $timeZoneId, $userModel->country_phone_fk);
            }
            //PHONE_NOT_VERIFIED

            $result = Utils::validateAndSendSMS($smsBody, $userModel->phone, $userModel->country_phone_fk, $platformName, [], $userModel->business_fk);
            if ($result['status'] == 'NOT_OK') {
                $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 37, $oldPhone = '', $userModel->phone, $userModel->platform_solution_fk, $timeZoneId, $userModel->country_phone_fk);
            }
            //SMS Failed
        } elseif ($phoneEmail['isEmail']) {
            if ($isNewUser == true)
            //Creating user status for the user
            {
                $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 66, $oldPhoneEmail = '', $userModel->email, $userModel->platform_solution_fk, $timeZoneId);
            }
            //EMAIL_NOT_VERIFIED
            //Sending the email
            $result = Utils::sendEmailWithTemplate('userTransferUnitsReceiver', 'bilingual', $emailSubject, $userModel->email, $emailData, null, null, null, $userModel->business_fk);
            if ($result['status'] == 'NOT_OK') {
                $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 38, $oldEmail = '', $userModel->email, $userModel->platform_solution_fk, $timeZoneId);
            }
            //Email failed
        } else {
            throw new Exception("Not valid email/phone", 1);
        }

        return ['status' => 'OK'];
    }

    /**
     * TIMEZONE - Globalization implementation
     * Transaction for user signup from Transfer Points
     * It will insert the user in db and assign user permission
     *
     * Used in Website(Signup), Smartphone (iphone/android), Merchant Tablet app
     * <AUTHOR> David S
     * @version July - August 2015 release V3.3.1
     * @param   object $joinModel
     * @param   array $phoneEmail
     * @param   string $platformName
     * @access  public
     * @return  array
     */
    public static function userSignUpFromTransferPoints($timeZoneId, $joinModel, $phoneEmail, $platformName)
    {
        if ($joinModel->email == null && $joinModel->phone == null) {
            Yii::log('No email, no phone User: ' . CJSON::encode($joinModel), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            return array('status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'errorDetails' => 'No email and no phone');
        }

        $joinModel->timezone_mysql_fk = $timeZoneId;
        if ($joinModel->save(false)) {
            //Assign the User Role for this User
            $userId = $joinModel->getPrimaryKey();

            $permission = 'user';
            $entity_fk = $userId;
            $entity_type_fk = 4; //user
            UserEntityPermission::assignEntityPermissionV330($userId, $permission, $entity_fk, $entity_type_fk, $timeZoneId);

            Yii::log('User Model saved', CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

            return ['status' => 'OK', 'userId' => $userId];
        } else {
            //error in the save
            Yii::log('Signup: User Model not saved. Errors=' . CJSON::encode($joinModel->getErrors()), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            throw new Exception("Error Processing Request", 1);
        }
    }

    /**
     * update the user profile with the verification code
     * used on smartphone (ios/android)
     * @todo delete, use activation_tokens table
     * <AUTHOR> N
     * @version March April 2016 V4.0
     * @param unknown $user
     * @param unknown $validationCode
     * @throws Exception
     */
//    public static function addVerificationCode($user, $validationCode)
//    {
//        $user->verification_code = $validationCode;
//        if (!$user->saveAttributes(['verification_code'])) {
//            throw new Exception(CJSON::encode($user->getErrors()));
//        }
//
//    }

    /**
     * Verifies that the verification code that the user entered is correct
     * used on smartphone (ios/android)
     * @todo delete, UBP is not implemented
     * <AUTHOR> N
     * @version March April 2016 V4.0
     * @param unknown $userId
     * @param unknown $verificationCode
     * @return boolean
     */
//    public static function validateVerificationCode($phone, $verificationCode, $countryPhoneCode)
//    {
//        if ($verificationCode != null && strlen((string)trim((string)$verificationCode)) > 0) {
//            $result = User::model()->findByAttributes(['verification_code' => $verificationCode, 'phone' => $phone]);
//            // Do not check country code because the user record may have another country than the input
//            // $result = User::model()->findByAttributes(['verification_code' => $verificationCode, 'phone' => $phone, 'country_phone_fk' => $countryPhoneCode]);
//            if ($result) {
//                return true;
//            }
//
//        }
//        return false;
//    }

    /**
     * Philippe: use the one in the Utils model. stop using this one
     * to be romoved later
     *
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * function that generates a 4 digits number
     *
     * Used in website. smartphone
     *
     * @version Jan - Feb 2015 V3.2.0
     * @return number
     */
    public static function generatePin()
    {
        $digits = 4;
        return random_int(10 ** ($digits - 1), 10 ** $digits - 1);
    }

    /**
     * INTERNATIONAL PhoneNumber - Globalization implementation
     * TIMEZONE - Globalization implementation
     * Takes the phone number checks against the databse
     * and returns the user id in case the phone exists
     *
     * Used in : Website, Smartphone, Merchant app
     * <AUTHOR> Patricia
     * @version : May - Jun 2015 release V3.3.0
     * @param string $phone
     * @return boolean if false and if true it returns the user id
     */
    public static function checkPhoneNumberExistenceV500($phone, $countryFk, $businessPk)
    {

        //Patricia - July 16th - Validate the country phone +1, treat CAN and US as the "same country" since both have the same phone country code
        if ($countryFk == 1 || $countryFk == 2) {
            $result = User::model()->findByAttributes(array('phone' => $phone, "business_fk" => $businessPk));
        } else {
            $result = User::model()->findByAttributes(array('phone' => $phone, 'country_phone_fk' => $countryFk, "business_fk" => $businessPk));
        }

        if ($result == null) {
            return false;
        } else {
            return $result->id;
        }

    }

    /**
     * INTERNATIONAL PhoneNumber - Globalization implementation
     * TIMEZONE - Globalization implementation
     * Takes the phone number checks against the databse
     * and returns the user id in case the phone exists
     *
     * Used in : Website, Smartphone, Merchant app
     * <AUTHOR> Patricia
     * @version : May - Jun 2015 release V3.3.0
     * @param string $phone
     * @return boolean if false and if true it returns the user id
     */
    public static function checkPhoneNumberExistenceV330($phone, $countryFk, $businessId=NULL)
    {

        //Patricia - July 16th - Validate the country phone +1, treat CAN and US as the "same country" since both have the same phone country code
        if ($countryFk == 1 || $countryFk == 2) {
            $params = array('phone' => $phone);
            if (Utils::feature("UBP"))
                $params['business_fk'] = $businessId;
            $result = User::model()->findByAttributes($params);
        } else {
            $params = array('phone' => $phone, 'country_phone_fk' => $countryFk);
            if (Utils::feature("UBP"))
                $params['business_fk'] = $businessId;
            $result = User::model()->findByAttributes($params);
        }

        if ($result == null) {
            return false;
        } else {
            return $result->id;
        }

    }

    /**
     * takes the phone number checks against the databse
     * and returns the user id in case the phone exists
     *
     * Used in : Website, Smartphone, Merchant app
     *
     * @version Jan - Feb 2015 V3.2.0
     * @param string $phone
     * @return boolean if false and if true it returns the user id
     */
    public static function checkPhoneNumberExistence($phone)
    {
        $result = User::model()->findByAttributes(array('phone' => $phone));
        if ($result == null) {
            return false;
        } else {
            return $result->id;
        }

    }

    /**
     * takes the email checks against the databse
     * and returns the user id in case the email exists
     *
     * Used in website. smartphone
     *
     * @version Jan - Feb 2015 V3.2.0
     * @param string $email
     * @return boolean if false and if true it returns the user id
     */
    public static function checkEmailExistence($email, $businessId=null)
    {
        if (Utils::feature("UBP"))
            return self::checkEmailExistenceV500($email, $businessId);
        $result = User::model()->findByAttributes(array('email' => $email));
        if ($result == null) {
            return false;
        } else {
            return $result->id;
        }

    }

    /**
     * takes the email checks against the databse
     * and returns the user id in case the email exists
     *
     * Used in website. smartphone
     *
     * @version Jan - Feb 2015 V3.2.0
     * @param string $email
     * @return boolean if false and if true it returns the user id
     */
    public static function checkEmailExistenceV500($email, $businessFk)
    {
        $result = User::model()->findByAttributes(array('email' => $email, 'business_fk' => $businessFk));
        if ($result == null) {
            return false;
        } else {
            return $result->id;
        }

    }

    /**
     * INTERNATIONAL PhoneNumber - Globalization implementation
     * TIMEZONE - Globalization implementation
     * Preparing for signing up the user
     * Handle platform solution 6 - Merchant web app(3.4.1)
     *
     * Used in Website, Smartphone (iphone/android), Merchant Tablet app
     * <AUTHOR> N.
     * @version Oct - Nov 2015 V3.4.1
     * @since May - June 2015 V3.3.0
     * @param string $input - email or phone
     * @return array
     */
    public static function userSignupV330($timeZoneId, $input, $phoneEmail, $joinModel, $lang = null, $businessId = null)
    {

        $platformName = PlatformSolution::model()->findByPk($joinModel->platform_solution_fk);

        // Check if the user is signing up with his phone number
        if ($phoneEmail['isPhone']) {
            if (Utils::feature("UBP")) {
                $result = User::checkPhoneNumberExistenceV500($input, $joinModel->country_phone_fk, $businessId);
            } else {
                $result = User::checkPhoneNumberExistenceV330($input, $joinModel->country_phone_fk);
            }
            Yii::log('checkPhoneNumberExistence for '.$input .' result=' . $result, CLogger::LEVEL_INFO, $platformName->name .'_' . 'Signup: userSignup');
        } elseif ($phoneEmail['isEmail']) {
            if (Utils::feature("UBP")) {
                $result = User::checkEmailExistenceV500($input, $businessId);
            } else {
                $result = User::checkEmailExistence($input);
            }
            //user is signing up using his email

            Yii::log('checkEmailExistence for '.$input.' result=' . $result, CLogger::LEVEL_INFO, $platformName->name .'_' . 'Signup: userSignup');
        }

        // Check if the user already exists the result will return the user id otherwise it will return false;
        if ($result != false) {

            // The user already exists
            $userModel = User::model()->findByPk($result);
            if ($userModel->enabled == 0) {
                return ['status' => 'NOT_OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_USER_DISABLED'), 'type' => 'error'];
            }

            //verify the phone number
            if ($phoneEmail['isPhone']) {
                Yii::log($platformName->name . ' Before verify phone', CLogger::LEVEL_INFO, 'Signup: userSignup');
                $isVerified = UserStatusManager::verifyPhoneNumber($result, $input);
            } else {
                Yii::log($platformName->name . ' Before verify email', CLogger::LEVEL_INFO, 'Signup: userSignup');
                if (Yii::app()->authManager->checkAccess('admin', $result) || Yii::app()->authManager->checkAccess('clerk', $result)) {
                    return array('status' => 'NOT_OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_ADMIN_INFORMATIVE_MSG'), 'type' => 'error');
                }

                //verify the email
                $isVerified = UserStatusManager::verifyEmail($result, $input);
            }

            //  For Website/Smartphone apps - inform user that this Cell # or Email exists already
            if (in_array($joinModel->platform_solution_fk, [1, 3, 4])) //$joinModel->platform_solution_fk!= 2
            {

                Yii::log($platformName->name . ' User exists. UserId=' . $result, CLogger::LEVEL_INFO, 'Signup: User exists. Verified(value)/isPhone/isEmail? : ' . $isVerified . '/' . $phoneEmail['isPhone'] . '/' . $phoneEmail['isEmail']);

                if ($phoneEmail['isPhone']) //return to the user a message to tell him that the phone already exists
                {
                    return array('status' => 'NOT_OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_PHONE_EXISTS'), 'type' => 'error');
                } elseif ($phoneEmail['isEmail']) //return email already exists to the user
                {
                    return array('status' => 'NOT_OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_EMAIL_EXISTS'), 'type' => 'error');
                }

            } else {
                //  For Merchant Tablet App: If this user already exists, then it will treated as a "Log in"
                Yii::log($platformName->name . ' User exists. UserId=' . $result, CLogger::LEVEL_INFO, 'Signup: User exists. Verified(value)/isPhone/isEmail? : ' . $isVerified . '/' . $phoneEmail['isPhone'] . '/' . $phoneEmail['isEmail']);

                $phoneEmail['isVerified'] = $isVerified;
                $phoneEmail['userId'] = $result;

                return array('status' => 'NOT_OK', 'result' => $phoneEmail);
            }
        } else {
            Yii::log('Signup: User does NOT exists by '.$input.' - BEFORE User::userSignuUpTransaction()', CLogger::LEVEL_INFO, $platformName->name .'_'.__METHOD__);
            // Insert user
            if (Utils::feature("UBP"))
                $joinModel->business_fk = $businessId;
            $insertUserResult = User::userSignuUpTransactionV330($timeZoneId, $joinModel, $phoneEmail, $platformName->name, false, $lang);

            // Return the result
            if (in_array($joinModel->platform_solution_fk, [1, 3, 4])) //$joinModel->platform_solution_fk!= 2
            {
                if ($insertUserResult['status'] == 'OK' && $phoneEmail['isEmail']) {
                    return ['status' => 'OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_EMAIL_SUCCESSFUL_MSG')];
                } elseif ($insertUserResult['status'] == 'OK' && $phoneEmail['isPhone']) {
                    return ['status' => 'OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_PHONE_SUCCESSFUL_MSG')];
                } else {
                    return $insertUserResult;
                }

            } else {
                //  For Merchant Tablet App: It has to return the NEW UserId
                return $insertUserResult;
            }
        }
    }

    /**
     * Preparing for signing up the user
     * Used in Website, Smartphone (iphone/android), Merchant Tablet app
     * @version Jan - Feb 2015 V3.2.0
     * @param string $input - email or phone
     * @return array
     */
    public static function userSignup($input, $phoneEmail, $joinModel)
    {
        $platformName = PlatformSolution::model()->findByPk($joinModel->platform_solution_fk);

        // Check if the user is signing up with his phone number
        if ($phoneEmail['isPhone']) {
            $result = User::checkPhoneNumberExistence($input);
            Yii::log($platformName->name . ' checkPhoneNumberExistence result=' . $result, CLogger::LEVEL_INFO, 'Signup: userSignup');
        } elseif ($phoneEmail['isEmail']) {
            //user is signing up using his email
            $result = User::checkEmailExistence($input);
            Yii::log($platformName->name . ' checkEmailExistence result=' . $result, CLogger::LEVEL_INFO, 'Signup: userSignup');
        }

        // Check if the user already exists the result will return the user id otherwise it will return false;
        if ($result != false) {

            $userModel = User::model()->findByPk($result);
            if ($userModel->enabled == 0) {
                return ['status' => 'NOT_OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_USER_DISABLED'), 'type' => 'error'];
            }

            //verify the phone number
            if ($phoneEmail['isPhone']) {
                Yii::log($platformName->name . ' Before verify phone', CLogger::LEVEL_INFO, 'Signup: userSignup');
                $isVerified = UserStatusManager::verifyPhoneNumber($result, $input);
            } else {
                Yii::log($platformName->name . ' Before verify email', CLogger::LEVEL_INFO, 'Signup: userSignup');
                if (Yii::app()->authManager->checkAccess('admin', $result) || Yii::app()->authManager->checkAccess('clerk', $result)) {
                    return array('status' => 'NOT_OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_ADMIN_INFORMATIVE_MSG'), 'type' => 'error');
                }

                //verify the email
                $isVerified = UserStatusManager::verifyEmail($result, $input);
            }

            //  For Website/Smartphone apps - inform user that this Cell # or Email exists already
            if ($joinModel->platform_solution_fk != 2) {

                Yii::log($platformName->name . ' User exists. UserId=' . $result, CLogger::LEVEL_INFO, 'Signup: User exists. Verified(value)/isPhone/isEmail? : ' . $isVerified . '/' . $phoneEmail['isPhone'] . '/' . $phoneEmail['isEmail']);

                if ($phoneEmail['isPhone']) //return to the user a message to tell him that the phone already exists
                {
                    return array('status' => 'NOT_OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_PHONE_EXISTS'), 'type' => 'error');
                } elseif ($phoneEmail['isEmail']) //return email already exists to the user
                {
                    return array('status' => 'NOT_OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_EMAIL_EXISTS'), 'type' => 'error');
                }

            } else {
                //  For Merchant Tablet App: If this user already exists, then it will treated as a "Log in"
                Yii::log($platformName->name . ' User exists. UserId=' . $result, CLogger::LEVEL_INFO, 'Signup: User exists. Verified(value)/isPhone/isEmail? : ' . $isVerified . '/' . $phoneEmail['isPhone'] . '/' . $phoneEmail['isEmail']);

                $phoneEmail['isVerified'] = $isVerified;
                $phoneEmail['userId'] = $result;

                return array('status' => 'NOT_OK', 'result' => $phoneEmail);
            }
        } else {
            Yii::log($platformName->name . ' User does NOT exists. ', CLogger::LEVEL_INFO, 'Signup: User does NOT exists - BEFORE User::userSignuUpTransaction()');
            // Insert user
            $insertUserResult = User::userSignuUpTransaction($joinModel, $phoneEmail, $platformName->name);

            // Return the result
            if ($joinModel->platform_solution_fk != 2) {
                if ($insertUserResult['status'] == 'OK' && $phoneEmail['isEmail']) {
                    return ['status' => 'OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_EMAIL_SUCCESSFUL_MSG')];
                } elseif ($insertUserResult['status'] == 'OK' && $phoneEmail['isPhone']) {
                    return ['status' => 'OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_PHONE_SUCCESSFUL_MSG')];
                } else {
                    return $insertUserResult;
                }

            } else {
                //  For Merchant Tablet App: It has to return the NEW UserId
                return $insertUserResult;
            }
        }
    }

    /**
     * Send email with template
     * Used in Website(Signup), Smartphone (iphone/android), Merchant Tablet app
     * @version Jan - Feb 2015 V3.2.0
     * @param string     $emailView - view path
     * @param string     $layout - layout path
     * @param string     $emailSubject
     * @param array     $sendTo - array of emails
     * @param array     $data  - array of variables used in email template
     * @return array
     */
    public static function sendEmailWithTemplateV320($emailView, $layout, $emailSubject, $sendTo, $data, $viewPath = null, $businessId = null)
    {
        $mail = new YiiMailer();
        $mail->IsSMTP(); // Set mailer to use SMTP
        if ($viewPath != null) {
            $mail->setViewPath($viewPath);
        }

        $mail->setView($emailView);
        $mail->setLayout($layout);
        $mail->setData($data);
        $emailConfig = Utils::getTransactionalEmailConfig($businessId??null);
        $mail->setReplyTo($emailConfig->reply_to);
        $mail->setFrom($emailConfig->from_email, $emailConfig->from_name);
        $mail->setTo($sendTo);
        if (Yii::app()->params['envParam'] == 'DEV') {
            $mail->setBcc(Yii::app()->params['emailin']);
        }

        $mail->setSubject($emailSubject);

        if ($mail->send()) {
            return ['status' => 'OK', 'message' => 'Email Sent'];
        } else {
            return ['status' => 'NOT_OK', 'message' => Yii::t('frontend', 'ERROR_SENDING_EMAIL') . $mail->getError(), 'errors' => $mail->getError()];
        }
    }

    /**
     * this function takes the userinfo in a dictionary and insert them in the user table
     * <AUTHOR> N.
     * @version January 2016 V 3.5.0
     * @param dictionary $userInfo
     * @return User
     */
    public static function createUpdateUserV350($params)
    {
        if (isset($params['id']) && $params['id'] != null && trim((string)$params['id']) != '') {
            $user = User::model()->findByPk($params['id']);
        } else {
            $user = new User();
        }

        $user->attributes = $params;
        if (!$user->save()) {
            throw new Exception(CJSON::encode($user->getErrors()));
        }

        return $user;
    }

    /**
     * updates the user info by getting the model and throws exceptions
     * used on smartphone (ios/android)
     * <AUTHOR> N
     * @version March April 2016 V4.0
     * @param User $model
     * @throws Exception
     */
    public static function updateUserInfo($model)
    {
    	if(count($model->getErrors()) > 0) {
    		throw new Exception(CJSON::encode($model->getErrors()));
        }

        if (!$model->save()) {
            throw new Exception(CJSON::encode($model->getErrors()));
        }

    }

    /**
     * this function takes the userinfo in a dictionary and insert them in the user table
     * @param array $userInfo
     * @return User
     * @throws Exception
     * @version August September 2015 V 3.3.1
     * <AUTHOR> N.
     */
    public static function insertUser($userInfo)
    {
        $platformName = PlatformSolution::PLATFORM_NAME_SYSTEM;
        $user = new User;
        $user->attributes = $userInfo;
        if (!$user->save()) {
            Yii::log('Inserting user Erros=' . CJSON::encode($user->getErrors()), CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
            throw new Exception("Error Saving user", 1);
        }

        return $user;
    }

    /**
     * V4.14. Refactor the params, removing usermodel param
     * User signup: sending verification by email and SMS
     * Update: 2015-05-20 (Valentin), Additional release V3.2.5.1, using shortUrlKg as a main function for shortening
     * Update: 03 june 2015 adding timezone.
     * Update: 15-06-2015, Valentin adding country code for the phone number in user status manager
     * Update: Send email in user language (v4.0)
     *
     * Used in Merchant Tablet app
     * @version V5.2.0
     * <AUTHOR> David
     * @param string $platformName
     * @param string $lang - language abreviation
     * @access public
     * @return array
     */
    public static function sendSignupVerificationEmailORSMS($userId, $branchId, $platformName, $lang, $timeZoneId, $languageModel = null)
    {
        try {
            $randomToken = User::model()->generateRandomString(60);
            $userModel = User::model()->findByPk($userId);
            $pinInfo = User::generatePinCodeForNewUser($userModel->email, $userModel->phone, $userModel->id);
            $randomPin = $pinInfo['pin_code'];
            $userParams = [
                'id' => $userId,
                // 'passwordreset' => $randomToken, // no need, because the token will be inserted in ActivationToken
                'enabled' => 1,
                'password' => '',
                'pin_permission' => $pinInfo['pin_permission'],
                'pin_enabled' => 1,
                'utc_link_expires' => date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES)),
            ];
            $mainBranch = BusinessBranch::model()->findByPk($branchId);
            $businessLogo = $mainBranch->logo_image_large;
            $businessId = $mainBranch->business_fk;
            $businessRulesRecord = BusinessRules::model()->findByAttributes(array('business_fk' => $businessId));
            $business = Business::model()->findByPk($businessId);

            // Set business name (not to be based on main branch name)
            $businessName = stripslashes((string)$business->name??''); //stripslashes((string)$mainBranch->name);

            $businessProfile = BusinessProfile::model()->findByAttributes(['business_fk' => $businessId]);
            $completeProfileLink = '';
            if ($businessRulesRecord->complete_profile_link == 1) {
                $jwt = \Firebase\JWT\JWT::encode([
                    'business_pk' => $businessId,
                    'user_id' => $userId,
                ], Yii::app()->params['jwtKey'], 'HS256');

                $completeProfileLink = Yii::app()->params['serverUrl'] . '/app/completeProfile?' . http_build_query([
                        't' => $jwt,
                        'showLogo' => 1,
                    ]);
            }
            ActivationToken::create([
                'id' => $randomToken,
                'user_fk' => $userId,
                'utc_created' => date("Y-m-d H:i:s"),
                'email' => $userModel->email,
                'phone' => $userModel->phone,
                'intent' => 'user_signup_welcome',
                'utc_link_expires' => date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES)),
            ]);
            $userModel = User::createUpdateUserV350($userParams);
            UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 35, '', '', $userModel->platform_solution_fk, $userModel->timezone_mysql_fk);
            $emailLink = '';
            $phoneLink = '';
            if ($businessProfile && $businessProfile->web_app_uri) {
                $webUrl = $businessProfile->web_app_uri;
            } else {
                $webUrl = Yii::app()->params['membersUrl'];
            }
            if ($userModel->email) {
                $emailLink = $webUrl . '/site/verify' . '?' . http_build_query(['token' => $randomToken, 'email' => $userModel->email, 'action' => 'add',]);
            } else {
                $phoneLink = $webUrl . '/site/verify' . '?' . http_build_query(['token' => $randomToken, 'phone' => $userModel->phone, 'action' => 'add',]);
                // $shortLink = Utils::shortUrlKg($phoneLink, $platformName);
            }
            if ($businessProfile) {
                $buttonColor = $businessProfile->color_buttons;
            } else {
                $defaultColors = BusinessProfile::getDefaultColors();
                $buttonColor = $defaultColors['color_buttons'];
            }//$redirectTo = '/business/detail?id='.bin2hex($business->business_uid); //User App redirect to Business detail page
            //$emailLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 0, 'e' => 1, 'l' => $lang, 'rt' => $redirectTo]); //c-cell phone , e- email , rt- redirect to
            //$phoneLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 1, 'e' => 0, 'l' => $lang, 'rt' => $redirectTo]); //c-cell phone , e- email, rt- redirect to

            $conglomerateName = '';
            if($business->conglomerate && $business->independent == '0') {
                $conglomerateName = $business->name;
            } elseif($business->conglomerate_id){
                $conglomerateAdmin = Business::model()->findByPk($business->conglomerate_id);
                $conglomerateName = $business->independent == '0' ? $conglomerateAdmin->name : '';
            }

            $emailData = [
                'username' => $userModel->email,
                'pincode' => $randomPin,
                'link' => $emailLink,
                'businessLogo' => $businessLogo,
                'businessId' => $businessId,
                'businessName' => $businessName,
                'businessEmail' => $mainBranch->email,
                'whiteLabel' => $business->white_label_flag,
                'completeProfileLink' => $completeProfileLink,
                'btnVerifyColor' => $buttonColor,
                'conglomerateMember' => $business->conglomerate_id ? true : false,
                'conglomerateAdmin' => (boolean) $business->conglomerate,
                'conglomerateName' => $conglomerateName,
                'name' => trim((string)($userModel->first ?? '') . ' ' . ($userModel->last ?? ''))
            ];
            $smsBody = '';
            $emailSubject = '';
            if ($business->white_label_flag == 0) {
                if (strlen((string)$emailLink) > 0) {
                    $emailSubject = Yii::t('frontend', 'FRONTEND_USERSIGNUP_MAIL_SUBJECT_BUS_NAME', array('{business_name}' => $businessName), null, $lang);
                }
                if (strlen((string)$phoneLink) > 0) {
                    if (isset($randomPin)) {
                        if ($lang == 'en' && ($businessId == 871 || $business->conglomerate_id == 871)) {
                            $smsBody = 'Welcome to the Detail Garage Family! Get Ready for exclusive access to product drops, rewards, insider events, & so much more! Your PIN is ' . $randomPin . '.';
                        } else {
                            $smsVendor = SmsVendor::getVendorForBusiness((int)$mainBranch->country_fk, $businessId);
                            if ($smsVendor->sms_provider == SMS_PROVIDER_BROADNET || $smsVendor->sms_provider == SMS_PROVIDER_BROADNET2 || $smsVendor->sms_provider == SMS_PROVIDER_BROADNET3) {
                                $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_STORE_BROADNET', ['{pin_code}' => $randomPin, '{business_name}' => $businessName], null, $lang);
                            } else {
                                $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_STORE', ['{pin_code}' => $randomPin, '{business_name}' => $businessName], null, $lang);
                            }
                        }
                    } else {
                        if ($lang == 'en' && ($businessId == 871 || $business->conglomerate_id == 871)) {
                            $smsBody = 'Welcome to the Detail Garage Family! Get Ready for exclusive access to product drops, rewards, insider events, & so much more!';
                        } else {
                            $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_STORE_WITHOUT_PIN', ['{business_name}' => $businessName], null, $lang);
                        }
                    }
                }
            } else {
                if (strlen((string)$emailLink) > 0) {
                    $emailSubject = Yii::t('frontend', 'FRONTEND_USERSIGNUP_MAIL_SUBJECT_BUS_NAME_WLABEL', array('{business_name}' => $businessName), null, $lang);
                }
                if (strlen((string)$phoneLink) > 0) {
                    if (isset($randomPin)) {
                        if ($lang == 'en' && ($businessId == 871 || $business->conglomerate_id == 871)) {
                            $smsBody = 'Welcome to the Detail Garage Family! Get Ready for exclusive access to product drops, rewards, insider events, & so much more! Your PIN is ' . $randomPin . '.';
                        } else {
                            $smsVendor = SmsVendor::getVendorForBusiness((int)$mainBranch->country_fk, $businessId);
                            if ($smsVendor->sms_provider == SMS_PROVIDER_BROADNET || $smsVendor->sms_provider == SMS_PROVIDER_BROADNET2 || $smsVendor->sms_provider == SMS_PROVIDER_BROADNET3) {
                                $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_WLABEL_BROADNET', ['{pin_code}' => $randomPin, '{business_name}' => $businessName], null, $lang);
                            } else {
                                $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_WLABEL', ['{pin_code}' => $randomPin, '{business_name}' => $businessName], null, $lang);
                            }
                        }
                    } else {
                        if ($lang == 'en' && ($businessId == 871 || $business->conglomerate_id == 871)) {
                            $smsBody = 'Welcome to the Detail Garage Family! Get Ready for exclusive access to product drops, rewards, insider events, & so much more!';
                        } else {
                            $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_WLABEL_WITHOUT_PIN', ['{business_name}' => $businessName], null, $lang);
                        }
                    }
                }
            }//PREPARING CUSTOM EMAIL SUBJECT
            Utils::logMessage(json_encode([
                'User::sendSignupVerificationEmailAndSMS  for custom Message businessId=' => $businessId,
            ]), __METHOD__, CLogger::LEVEL_INFO);
            if (isset($businessId) && in_array($businessId, [1484])) {
                $emailSubject = 'Welcome to the Seneca Status Program';
            } else if (isset($businessId) && in_array($businessId, [1448])) {
                $emailSubject = 'Welcome to Justin Boot Rewards';
            } else {
                $emailSubject = $businessName . ': ' . Yii::t('frontend', 'FRONTEND_ACTIVATION_LOYALTY_PROGRAM', array(), null, $lang);
            }
            
            // Setup Message data for the SQS queueing
            $welcomeMessageData = [
                'receiverId' => (int)$userModel->id,
                'businessId' => (int)$businessId,
                'branchId' => (int)$mainBranch->business_branch_pk,
                'langId' => (int)$languageModel->language_pk,
            ];
            $emailMessageData = array_merge($welcomeMessageData, [
                'sourceModule' => 'welcome_email',
                'channel' => 1,
                'pinCode' => $randomPin,
                'link' => $emailLink,
                'activationLinkExpires' => preg_replace('/[^0-9]/', '', ACTIVATION_LINK_EXPIRES),
            ]);
            $smsMessageData = array_merge($welcomeMessageData, [
                'sourceModule' => 'welcome_sms',
                'channel' => 2,
                'pinCode' => $randomPin
            ]);
            $queue = new \application\components\queue\ExternalQueue(CUSTOM_QUEUE_DRIVER, CUSTOM_MEMBERS_API_DEFAULT_QUEUE);

            $statusSending = true;//If the user has email and phone, send by email and add
            if ($userModel->email && $userModel->phone) {
                //Creating user status for the user email - not verified and phone not verified
                UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 32, $oldPhoneEmail = '', $userModel->email, $userModel->platform_solution_fk, $timeZoneId); //EMAIL_NOT_VERIFIED
                UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 30, $oldPhoneEmail = '', $userModel->phone, $userModel->platform_solution_fk, $timeZoneId, $userModel->country_phone_fk); //PHONE_NOT_VERIFIED

                if ($businessRulesRecord->notify_user_signup_flag == 0) {
                    return ['status' => 'OK', 'message_status' => 'sent', 'message' => 'Email Sent'];
                }

                if($businessId == 3113){
                    $lang = 'es';
                } elseif($businessId == 3121) {
                    $lang = ($lang == 'es') ? 'es' : 'en';
                    $emailSubject = ($lang == 'es') ? "¡Bienvenido a Baristi Rewards! ☕✨" : "Welcome to Baristi Rewards! ☕✨";
                } elseif($businessId == 3382) {
                    $lang = 'en';
                } elseif($businessId == 3370) {
                    $lang = ($lang == 'fr') ? 'fr' : 'en';
                    $emailSubject = ($lang == 'fr') ? "Gérard Bertrand Le Club : Bienvenue" : "Welcome to the Gérard Bertrand Le Club";
                }
                
                Yii::log(__FUNCTION__ . ' Welcome Message ' . [
                    'emailMessageData' => CJSON::encode($emailMessageData),
                    'businessRulesRecord' => $businessRulesRecord
                ], CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);
                if(Utils::feature("SEND_CUSTOM_NOTIFICATION") && (int)$businessRulesRecord->custom_messages_flag == 1){
                    Yii::log(__FUNCTION__ . ' Welcome Message ' . CJSON::encode($emailMessageData), CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);
                    $queue->enqueue("App\\Jobs\\CustomizedMessageProcessor", $emailMessageData, [
                        'laravelCompatible' => true,
                    ]);
                }elseif (CONFIG_SHOULD_QUEUE === true) {
                    //Enqueue for sending in background
                    MessagesQueue::create([
                        'send_to' => $userModel->email,
                        'cell_email' => MESSAGE_SEND_BY_EMAIL,
                        'user_fk' => $userModel->id,
                        'business_fk' => $businessId,
                        'params' => [
                            'emailView' => 'userWelcomeWithPinStore',
                            'layout' => 'bilingual',
                            'emailSubject' => $emailSubject,
                            'emailData' => $emailData,
                            'viewPath' => 'application.views.mail.' . $lang,
                            'fromExtra' => $businessName,
                            'businessId' => $businessId,
                            'lang' => $lang,
                            'withdraw_credit_flag' => true
                        ],
                    ]);
                    return ['status' => 'OK', 'message_status' => 'sent', 'message' => 'Email Sent'];
                } else {
                    //Sending the email sync
                    return Utils::sendEmailWithTemplate('userWelcomeWithPinStore', 'bilingual', $emailSubject, $userModel->email, $emailData, $viewPath = 'application.views.mail.' . $lang, null, $businessName, $businessId,
                        $userId, null,  true);
                }
            } elseif ($userModel->email) {
                //Creating user status for the user
                $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 32, $oldPhoneEmail = '', $userModel->email, $userModel->platform_solution_fk, $timeZoneId); //EMAIL_NOT_VERIFIED

                if ($businessRulesRecord->notify_user_signup_flag == 0) {
                    return ['status' => 'OK', 'message_status' => 'sent', 'message' => 'Email Sent'];
                }
                if($businessId == 3113){
                    $lang = 'es';
                } elseif($businessId == 3121){
                    $lang = ($lang == 'es') ? 'es' : 'en';
                    $emailSubject = ($lang == 'es') ? "¡Bienvenido a Baristi Rewards! ☕✨" : "Welcome to Baristi Rewards! ☕✨";
                } elseif($businessId == 3382) {
                    $lang = 'en';
                } elseif($businessId == 3370) {
                    $lang = ($lang == 'fr') ? 'fr' : 'en';
                    $emailSubject = ($lang == 'fr') ? "Gérard Bertrand Le Club : Bienvenue" : "Welcome to the Gérard Bertrand Le Club";
                }

                if(Utils::feature("SEND_CUSTOM_NOTIFICATION") && (int)$businessRulesRecord->custom_messages_flag == 1){
                    Yii::log(__FUNCTION__ . ' Welcome Message ' . CJSON::encode($emailMessageData), CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);
                    $queue->enqueue("App\\Jobs\\CustomizedMessageProcessor", $emailMessageData, [
                        'laravelCompatible' => true,
                    ]);
                }elseif (CONFIG_SHOULD_QUEUE === true) {
                    //Enqueue for sending in background
                    MessagesQueue::create([
                        'send_to' => $userModel->email,
                        'cell_email' => MESSAGE_SEND_BY_EMAIL,
                        'user_fk' => $userModel->id,
                        'business_fk' => $businessId,
                        'params' => [
                            'emailView' => 'userWelcomeWithPinStore',
                            'layout' => 'bilingual',
                            'emailSubject' => $emailSubject,
                            'emailData' => $emailData,
                            'viewPath' => 'application.views.mail.' . $lang,
                            'fromExtra' => $businessName,
                            'businessId' => $businessId,
                            'withdraw_credit_flag' => true
                        ],
                    ]);
                    return ['status' => 'OK', 'message_status' => 'sent', 'message' => 'Email Sent'];
                } else {
                    //Sending the email sync
                    return Utils::sendEmailWithTemplate('userWelcomeWithPinStore', 'bilingual', $emailSubject, $userModel->email, $emailData, $viewPath = 'application.views.mail.' . $lang, null, $businessName, $businessId);
                }
            } elseif ($userModel->phone) {
                //inserting in user Status Manager

                Yii::log('BEFORE inserting in user Status Manager', CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

                $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 30, $oldPhoneEmail = '', $userModel->phone, $userModel->platform_solution_fk, $timeZoneId, $userModel->country_phone_fk); //PHONE_NOT_VERIFIED

                if ($businessRulesRecord->notify_user_signup_flag == 0) {
                    return ['status' => 'OK', 'message_status' => 'queued', 'sms_sid' => null];
                }

                if ($business->industryProhibitedSms()) {
                    return ['status' => 'OK', 'message_status' => 'queued', 'sms_sid' => null];
                }

                if(Utils::feature("SEND_CUSTOM_NOTIFICATION") && (int)$businessRulesRecord->custom_messages_flag == 1){
                    Yii::log(__FUNCTION__ . ' Welcome Message SMS ' . CJSON::encode($smsMessageData), CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);
                    $queue->enqueue("App\\Jobs\\CustomizedMessageProcessor", $smsMessageData, [
                        'laravelCompatible' => true,
                    ]);
                }elseif (CONFIG_SHOULD_QUEUE === true) {
                    //Enqueue for sending in background
                    MessagesQueue::create([
                        'send_to' => $userModel->phone,
                        'cell_email' => MESSAGE_SEND_BY_SMS,
                        'country_phone_fk' => $userModel->country_phone_fk,
                        'user_fk' => $userModel->id,
                        'business_fk' => $businessId,
                        'platformName' => $platformName,
                        'params' => ['message_body' => $smsBody, 'withdraw_credit_flag' => true],
                    ]);
                    return ['status' => 'OK', 'message_status' => 'queued', 'sms_sid' => null];
                } else {
                    return Utils::validateAndSendSMS($smsBody, $userModel->phone, $userModel->country_phone_fk, $platformName, [], $businessId);
                }
            } else {
                return array('status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'error' => 'Not valid email/phone');
            }
        } catch (Exception $e) {
            Yii::log('User::sendSignupVerificationEmailORSMS Error when User is been registered. Error = '.json_encode($e) , CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
        }
    }

    /**
     * User signup: sending verification by email and SMS
     * Update: 2015-05-20 (Valentin), Additional release V3.2.5.1, using shortUrlKg as a main function for shortening
     * Update: 03 june 2015 adding timezone.
     * Update: 15-06-2015, Valentin adding country code for the phone number in user status manager
     * Update: Send email in user language (v4.0)
     *
     * Used in Website(Signup), Smartphone (iphone/android), Merchant Tablet app
     * @version V4.10
     * <AUTHOR> David
     * @param $userId
     * @param $businessId
     * @param $hasEmail
     * @param $hasPhone
     * @param $customerInfoFilledUp
     * @param string $platformName
     * @param string $lang - language abbreviation
     * @return array
     * @throws Exception
     * @access public
     */
    public static function sendSignupVerificationEmailAndSMS($userId, $businessId, $hasEmail, $hasPhone, $customerInfoFilledUp, $platformName, $lang)
    {
        if($businessId == 3113){
            //Grupo RIO always send in Spanish
            $lang = 'es';
        } elseif($businessId == 3121 && $lang != 'es'){
            $lang = 'en';
        } elseif($businessId == 3382){
            $lang = 'en';
        } elseif($businessId == 3370 && $lang != 'fr'){
            $lang = 'en';
        }

        $rules = BusinessRules::model()->findByAttributes(['business_fk' => $businessId]);

        $randomToken = User::model()->generateRandomString(60);
        $userModel = User::model()->findByPk($userId);
        $pinInfo = User::generatePinCodeForNewUser($userModel->email, $userModel->phone, $userModel->id);
        $randomPin = $pinInfo['pin_code'];

        $userParams = [
            'id' => $userId,
            // 'passwordreset' => $randomToken, // no need, because the token will be inserted in ActivationToken
            'enabled' => 1,
            'password' => '',
            'pin_permission' => $pinInfo['pin_permission'],
            'pin_enabled' => 1,
            'utc_link_expires' => date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES)),
        ];

        $business = Business::model()->findByPk($businessId);
        $mainBranch = BusinessBranch::model()->findbyAttributes(['business_fk'=>$businessId, 'default_branch'=>1]);
        $userModel = User::createUpdateUserV350($userParams);

        // Set business name
        $businessName = stripslashes((string)$business->name??'');

        Yii::log(json_encode([
            'userId' => $userId,
            'businessId' => $businessId,
            'email' => $mainBranch->email,
            'method' => 'sendSignupVerificationEmailAndSMS:begining',
        ]), CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);

        UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 35, '', '', $userModel->platform_solution_fk, $userModel->timezone_mysql_fk); //PIN_CODE_AUTO_GENERATED
        //$emailLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 0, 'e' => 1]); //c-cell phone , e- email
        //$phoneLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 1, 'e' => 0]); //c-cell phone , e- email
        //$businessProfile = BusinessProfile::where('business_fk', $business->business_pk)->first();
        $businessProfile = BusinessProfile::model()->findbyAttributes(['business_fk'=>$businessId]); //('business_fk', $business->business_pk)->first();

        //Variable to generate the randomToken twice and save in Activation_Tokens table
        $hasEmailPhone = false;
        if ($hasEmail && $hasPhone) {
            $hasEmailPhone = true;
        }

        $paramsActivationToken = [
            'id' => $randomToken,
            'user_fk' => $userId,
            'intent' => 'user_signup_welcome',
            'email' => $userModel->email ?: null,
            'phone' => $userModel->phone ?: null,
            'country_code' => $userModel->country_phone_fk ? Countries::model()->findByPk($userModel->country_phone_fk)->code_iso3: null,
            'utc_created' => date('Y-m-d H:i:s'),
            'utc_link_expires' => date('Y-m-d H:i:s', strtotime((string)ACTIVATION_LINK_EXPIRES))
        ];

        if ($hasPhone && $rules->notify_user_signup_flag && !$business->industryProhibitedSms()) {
            //SMS
            if ($businessProfile && $businessProfile->web_app_uri) {
                # http://webapp.local/site/verify?token=44&email=<EMAIL>
                $phoneLink = $businessProfile->web_app_uri . '/site/verify' . '?' . http_build_query(['token' => $randomToken, 'phone' => $userModel->phone, 'action' => 'add',]);
            } else {
                $phoneLink = Yii::app()->params['membersUrl'] . '/site/verify' . '?' . http_build_query(['token' => $randomToken, 'phone' => $userModel->phone, 'action' => 'add',]);
                //$phoneLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 1, 'e' => 0, 'l' => $lang,]);
            }
            ActivationToken::create($paramsActivationToken);

//            $shortLink = Utils::shortUrlKg($phoneLink, $platformName);
            if(isset($randomPin)) {
                if($lang =='en' && ($businessId == 871 || $business->conglomerate_id == 871)){
                    $smsBody = 'Welcome to the Detail Garage Family! Get Ready for exclusive access to product drops, rewards, insider events, & so much more! Your PIN is ' . $randomPin . '.';
                } else {
                    $smsVendor = SmsVendor::getVendorForBusiness((int)$mainBranch->country_fk, $businessId);
                    if ($smsVendor->sms_provider == SMS_PROVIDER_BROADNET || $smsVendor->sms_provider == SMS_PROVIDER_BROADNET2 || $smsVendor->sms_provider == SMS_PROVIDER_BROADNET3) {
                        $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_WLABEL_BROADNET', ['{pin_code}' => $randomPin, '{business_name}' => $businessName], null, $lang);
                    } else {
                        $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_WLABEL', ['{pin_code}' => $randomPin, '{business_name}' => $businessName], null, $lang);
                    }
                }
            } else {
                if ($lang == 'en' && ($businessId == 871 || $business->conglomerate_id == 871)) {
                    $smsBody = 'Welcome to the Detail Garage Family! Get Ready for exclusive access to product drops, rewards, insider events, & so much more!';
                } else {
                    $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_WLABEL_WITHOUT_PIN', ['{business_name}' => $businessName], null, $lang);
                }
            }
            $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 30, $oldPhoneEmail = '', $userModel->phone, $userModel->platform_solution_fk, $userModel->timezone_mysql_fk, $userModel->country_phone_fk); //PHONE_NOT_VERIFIED

            //Enqueue for sending in background
            MessagesQueue::create([
                'send_to' => $userModel->phone,
                'cell_email' => MESSAGE_SEND_BY_SMS,
                'country_phone_fk' => $userModel->country_phone_fk,
                'user_fk' => $userModel->id,
                'business_fk' => $businessId,
                'params' => ['message_body' => $smsBody, 'withdraw_credit_flag' => true],
            ]);
        }

        if ($hasEmail && $rules->notify_user_signup_flag) {
            //EMAIL
            if ($hasEmailPhone) {
                $randomToken = User::model()->generateRandomString(60);

                $paramsActivationToken = [
                    'id' => $randomToken,
                    'user_fk' => $userId,
                    'utc_created' => date("Y-m-d H:i:s"),
                    'utc_link_expires' => date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES))];
            }

            if ($businessProfile && $businessProfile->web_app_uri) {
                # http://webapp.local/site/verify?token=44&email=<EMAIL>
                $emailLink = $businessProfile->web_app_uri . '/site/verify' . '?' . http_build_query(['token' => $randomToken, 'email' => $userModel->email, 'action' => 'add',]);
            } else {
                $emailLink = Yii::app()->params['membersUrl'] . '/site/verify' . '?' . http_build_query(['token' => $randomToken, 'email' => $userModel->email, 'action' => 'add',]);
                //$emailLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 0, 'e' => 1, 'l' => $lang,]);
            }
            ActivationToken::create($paramsActivationToken);

            if ($businessProfile) {
                $buttonColor = $businessProfile->color_buttons;
            } else {
                $defaultColors = BusinessProfile::getDefaultColors();
                $buttonColor = $defaultColors['color_buttons'];
            }

            $conglomerateName = '';
            if($business->conglomerate && $business->independent == '0') {
                $conglomerateName = $business->name;
            } elseif($business->conglomerate_id){
                $conglomerateAdmin = Business::model()->findByPk($business->conglomerate_id);
                $conglomerateName = $business->independent == '0' ? $conglomerateAdmin->name : '';
            }

            $emailData = [
                'businessname' => $businessName,
                'businesslogo' => Yii::app()->params['serverUrl'].$mainBranch->logo_image_medium,
                'email' => $userModel->email,
                'pincode' => $randomPin,
                'link' => $emailLink,
                'btnVerifyColor' => $buttonColor,
                'businessId' => $businessId,
                'conglomerateName' => $conglomerateName,
                'name' => trim((string)($userModel->first ?? '') . ' ' . ($userModel->last ?? ''))
            ];

            //SAVING A LOG TO ANALIZE DATA
            Utils::logMessage(json_encode([
                'User::sendSignupVerificationEmailAndSMS  for custom Message businessId=' => $businessId,
            ]), __METHOD__, CLogger::LEVEL_INFO);

            if(isset($businessId) && in_array($businessId, [1484])) {
                $emailSubject = 'Welcome to the Seneca Status Program';
            } else if(isset($businessId) && in_array($businessId, [1448])) {
                $emailSubject = 'Welcome to Justin Boot Rewards';
            } else if(isset($businessId) && in_array($businessId, [3121])) {
                if ($lang == 'es') {
                    $emailSubject = "¡Bienvenido a Baristi Rewards! ☕✨";
                } else {
                    $emailSubject = "Welcome to Baristi Rewards! ☕✨";
                }
            } else if(isset($businessId) && in_array($businessId, [3370])) {
                $emailSubject = ($lang == 'fr') ? "Gérard Bertrand Le Club : Bienvenue" : "Welcome to the Gérard Bertrand Le Club";
            } else {
                $emailSubject = $businessName.': '.Yii::t('frontend', 'FRONTEND_ACTIVATION_LOYALTY_PROGRAM', array(), null, $lang);
            }

            UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 32, $oldPhoneEmail = '', $userModel->email, $userModel->platform_solution_fk, $userModel->timezone_mysql_fk); //EMAIL_NOT_VERIFIED

            //Enqueue for sending in background
            MessagesQueue::create([
                'send_to' => $userModel->email,
                'cell_email' => MESSAGE_SEND_BY_EMAIL,
                'user_fk' => $userModel->id,
                'business_fk' => $businessId,
                'params' => [
                    'emailView' => 'userWelcomeWithPinWLabel',
                    'layout' => 'bilingual',
                    'emailSubject' => $emailSubject,
                    'emailData' => $emailData,
                    'fromExtra' => $business->name,
                    'withdraw_credit_flag' => true,
                    'viewPath' => 'application.views.mail.' . $lang,
                ],
            ]);
        }

        //Send Email to the business for let it know that a New Customers just signup
        $userPhone = '';
        if ($hasPhone) {
            $phoneCode = '';
            $countryPhone = Countries::model()->findByAttributes(['country_pk' => $userModel->country_phone_fk]);
            if ($countryPhone) $phoneCode = '+' . $countryPhone->phone_code . ' ';
            $userPhone = $phoneCode . $userModel->phone;
        }

        $emailSubject = Yii::t('frontend', 'FRONTEND_USERSIGNUP_NEW_CUSTOMER_SUBJECT', array(), null, $lang) . ' ' . $businessName;
        $emailData = [
            'businessname' => $mainBranch->name,
            'businesslogo' => Yii::app()->params['serverUrl'].$mainBranch->logo_image_medium,
            'content' => $customerInfoFilledUp,
            'email' => $userModel->email != null ? $userModel->email : '',
            'phone' => $userPhone,
        ];

        Yii::log(json_encode([
            'userId' => $userId,
            'businessId' => $businessId,
            'email' => $mainBranch->email,
            'method' => 'sendSignupVerificationEmailAndSMS:before-message-queue-create',
        ]), CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);

        MessagesQueue::create([
            'send_to' => $mainBranch->email,
            'cell_email' => MESSAGE_SEND_BY_EMAIL,
            'user_fk' => $userModel->id,
            'business_fk' => $businessId,
            'params' => [
                'emailView' => 'notifyUserSignup',
                'layout' => 'bilingual',
                'emailSubject' => $emailSubject,
                'emailData' => $emailData,
                'withdraw_credit_flag' => true
            ],
        ]);

        return ['status' => 'OK', 'message_status' => 'sent', 'sms_sid' => null];
    }

    /**
     * User signup: sending verification email or SMS for users signed up at store
     * Send email and sms in specific language (3.4.1)
     * Update: 2015-05-20 (Valentin), Additional release V3.2.5.1, using shortUrlKg as a main function for shortening
     * Update: 03 june 2015 adding timezone.
     * Update: 15-06-2015, Valentin adding country code for the phone number in user status manager
     *
     * Used in Website(Signup), Smartphone (iphone/android), Merchant Tablet app
     * @version Oct - Nov 2015 release V3.4.1
     * <AUTHOR> 3.2.5 - Philippe 3.3.0
     * @param object $userModel
     * @param array $phoneEmail
     * @param string $platformName
     * @access public
     * @return array
     */
    public static function sendSignupVerificationEmailSMSStore(
        $timeZoneId, $userModel, $phoneEmail, $platformName, $lang,
        $business = null, $businessProfile = null, $conglomerateAdmin = null, $mainBranch = null,
        $businessRulesRecord = null, $sendWelcomeMessage = true
    ) {
        Utils::logMessage(json_encode([
            'userId' => $userModel->id,
            'email' => $userModel->email,
            'phone' => $userModel->phone,
            'lang' => $lang,
            'message' => 'Before Sending Signup Verification',
        ]), __METHOD__, CLogger::LEVEL_INFO);

        if(!$mainBranch) {
            $mainBranch = BusinessBranch::model()->findByPk($userModel->signedupat_fk);
        }
        $businessLogo = $mainBranch->logo_image_large;
        $businessId = $mainBranch->business_fk;

        if(!$businessRulesRecord) {
            $businessRulesRecord = BusinessRules::model()->findByAttributes(array('business_fk' => $businessId));
        }
        if(!$business) {
            $business = Business::model()->findByPk($businessId);
        }
        if(!$businessProfile) {
            $businessProfile = BusinessProfile::model()->findByAttributes(['business_fk' => $businessId]);
        }

        // Set business name (not to be based on main branch name)
        $businessName = stripslashes((string)$business->name??''); //stripslashes((string)$mainBranch->name);

        $randomToken = User::model()->generateRandomString(60);
        $pinInfo = User::generatePinCodeForNewUser($userModel->email, $userModel->phone, $userModel->id);
        $randomPin = $pinInfo['pin_code'];

        $userModel->enabled = 1;
        $userModel->password = '';
        $userModel->pin_permission = $pinInfo['pin_permission'];
        $userModel->pin_enabled = 1;
        $userModel->utc_link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
        $userModel->timezone_mysql_fk = $timeZoneId;
        $userModel = Utils::saveModel($userModel);

        $countryCode = null;
        $country = Countries::model()->findByPk($userModel->country_phone_fk);
        if($country) {
            $countryCode = $country->code;
        }

        if (empty($lang)){
            if ($businessRulesRecord->languages) {
                $defaultLanguageModel = Language::getBusinessDefaultLanguageModel($businessId);
                $defaultLangId = $defaultLanguageModel->language_pk;
                $lang = $defaultLanguageModel->abreviation ?? 'en';
            } else {
                $defaultLangId = LANG_EN;
                $lang = 'en';
            }
        } else {
            $defaultLangId = LANG_EN;
        }

        ActivationToken::create([
            'id' => $randomToken,
            'user_fk' => $userModel->id,
            'utc_created' => date("Y-m-d H:i:s"),
            'utc_link_expires' => date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES)),
            'intent' => 'user_signup_welcome',
            'email' => $userModel->email,
            'phone' => $userModel->phone,
            'country_code' => $countryCode,
        ]);

        UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 35, '', '', $userModel->platform_solution_fk, $timeZoneId); //PIN_CODE_AUTO_GENERATED

        Utils::logMessage(json_encode([
            'userId' => $userModel->id,
            'email' => $userModel->email,
            'phone' => $userModel->phone,
            'notify_user_signup_flag' => $businessRulesRecord->notify_user_signup_flag,
            'message' => 'After createUserStatusManagerRecordV330',
        ]), __METHOD__, CLogger::LEVEL_INFO);

        if (!$businessRulesRecord->notify_user_signup_flag || !$sendWelcomeMessage) {
            $logMessage = json_encode([
                'businessId' => $businessId, 'userId' => $userModel->id,
                'email' => $userModel->email, 'phone' => $userModel->phone,
                'message' => 'notify_user_signup_flag is OFF',
            ]);
            Yii::log($logMessage, CLogger::LEVEL_INFO, __METHOD__);

            Utils::logMessage($logMessage, __METHOD__);

            return ['status' => 'OK', 'message_status' => 'sent', 'message' => 'Email Sent'];
        }

        $redirectTo = '/business/detail?id='.bin2hex($business->business_uid); //User App redirect to Business detail page

        $cell = $userModel->phone ? 1 : 0;
        $email = $userModel->email ? 1 : 0;

        $website = rtrim((string)Yii::app()->params['membersUrl'], '/');
        // Get White label app URL if exists
        if ($businessProfile && $businessProfile->web_app_uri) {
            $website = rtrim((string)$businessProfile->web_app_uri, '/');
        }


        $emailLink = $website . '/site/verify?' . http_build_query([
            'token' => $randomToken, 'email' => $userModel->email, 'action' => 'add',
        ]);

        $phoneLink = $website . '/site/verify?' . http_build_query([
            'token' => $randomToken, 'phone' => $userModel->phone, 'action' => 'add',
        ]);

//        $emailLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => $cell, 'e' => $email, 'l' => $lang, 'rt' => $redirectTo]); //c-cell phone , e- email , rt- redirect to
//        $phoneLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => $cell, 'e' => $email, 'l' => $lang, 'rt' => $redirectTo]); //c-cell phone , e- email, rt- redirect to

        $completeProfileLink = '';
        if ($businessRulesRecord->complete_profile_link == 1) {
             $jwt = \Firebase\JWT\JWT::encode([
                'business_pk' => $businessId,
                'user_id' => $userModel->id,
            ], Yii::app()->params['jwtKey'], 'HS256');

            $completeProfileLink = Yii::app()->params['serverUrl'] . '/app/completeProfile?' . http_build_query([
                't' => $jwt,
                'showLogo' => 1,
            ]);
        }

        if ($businessProfile) {
            $buttonColor = $businessProfile->color_buttons;
        } else {
            $defaultColors = BusinessProfile::getDefaultColors();
            $buttonColor = $defaultColors['color_buttons'];
        }

        $conglomerateName = '';
        if($business->conglomerate && $business->independent == '0') {
            $conglomerateName = $business->name;
        } elseif($business->conglomerate_id) {
            if (!$conglomerateAdmin) {
                $conglomerateAdmin = Business::model()->findByPk($business->conglomerate_id);
            }
            $conglomerateName = $business->independent == '0' ? $conglomerateAdmin->name : '';
        }

        $emailData = [
            'username' => $userModel->email,
            'pincode' => $randomPin,
            'link' => $emailLink,
            'businessLogo' => $businessLogo,
            'businessName' => $businessName,
            'businessId' => $businessId,
            'businessEmail' => $mainBranch->email,
            'whiteLabel' => $business->white_label_flag,
            'completeProfileLink' => $completeProfileLink,
            'btnVerifyColor' => $buttonColor,
            'conglomerateName' => $conglomerateName,
            'name' => trim((string)($userModel->first ?? '') . ' ' . ($userModel->last ?? '')),
        ];

        $shortLink = Utils::shortUrlKg($phoneLink, $platformName);

        if ($business->white_label_flag == 0) {
            $emailSubject = Yii::t('frontend', 'FRONTEND_USERSIGNUP_MAIL_SUBJECT_BUS_NAME', array('{business_name}' => $businessName), null, $lang);
            if (isset($randomPin)) {
                $smsVendor = SmsVendor::getVendorForBusiness((int)$mainBranch->country_fk, $businessId);
                if($lang =='en' && ($businessId == 871 || $business->conglomerate_id == 871)){
                    $smsBody = 'Welcome to the Detail Garage Family! Get Ready for exclusive access to product drops, rewards, insider events, & so much more! Your PIN is ' . $randomPin . '.';
                } else {
                    if ($smsVendor->sms_provider == SMS_PROVIDER_BROADNET || $smsVendor->sms_provider == SMS_PROVIDER_BROADNET2 || $smsVendor->sms_provider == SMS_PROVIDER_BROADNET3) {
                        $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_STORE_BROADNET', ['{pin_code}' => $randomPin, '{business_name}' => $businessName], null, $lang);
                    } else {
                        $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_STORE', ['{pin_code}' => $randomPin, '{business_name}' => $businessName], null, $lang);
                    }
                }
            } else {
                if ($lang == 'en' && ($businessId == 871 || $business->conglomerate_id == 871)) {
                    $smsBody = 'Welcome to the Detail Garage Family! Get Ready for exclusive access to product drops, rewards, insider events, & so much more!';
                } else {
                    $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_STORE_WITHOUT_PIN', ['{business_name}' => $businessName], null, $lang);
                }
            }
        }else {
            $emailSubject = Yii::t('frontend', 'FRONTEND_USERSIGNUP_MAIL_SUBJECT_BUS_NAME_WLABEL', array('{business_name}' => $businessName), null, $lang);
            if (isset($randomPin)) {
                if ($lang == 'en' && ($businessId == 871 || $business->conglomerate_id == 871)) {
                    $smsBody = 'Welcome to the Detail Garage Family! Get Ready for exclusive access to product drops, rewards, insider events, & so much more! Your PIN is ' . $randomPin . '.';
                } else {
                    $smsVendor = SmsVendor::getVendorForBusiness((int)$mainBranch->country_fk, $businessId);
                    if ($smsVendor->sms_provider == SMS_PROVIDER_BROADNET || $smsVendor->sms_provider == SMS_PROVIDER_BROADNET2 || $smsVendor->sms_provider == SMS_PROVIDER_BROADNET3) {
                        $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_WLABEL_BROADNET', ['{pin_code}' => $randomPin, '{business_name}' => $businessName], null, $lang);
                    } else {
                        $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_WLABEL', ['{pin_code}' => $randomPin, '{business_name}' => $businessName], null, $lang);
                    }
                }
            } else {
                if ($lang == 'en' && ($businessId == 871 || $business->conglomerate_id == 871)) {
                    $smsBody = 'Welcome to the Detail Garage Family! Get Ready for exclusive access to product drops, rewards, insider events, & so much more!';
                } else {
                    $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_WLABEL_WITHOUT_PIN', ['{business_name}' => $businessName], null, $lang);
                }
            }
        }

        if(isset($businessId) && in_array($businessId, [1484])) {
            $emailSubject = 'Welcome to the Seneca Status Program';
        } else if(isset($businessId) && in_array($businessId, [1448])) {
            $emailSubject = 'Welcome to Justin Boot Rewards';
        };

        // Setup Message data for the SQS queueing
        $welcomeMessageData = [
            'receiverId' => (int)$userModel->id,
            'businessId' => (int)$businessId,
            'branchId' => (int)$mainBranch->business_branch_pk,
            'langId' => (int)$defaultLangId,
        ];
        $emailMessageData = array_merge($welcomeMessageData, [
            'sourceModule' => 'welcome_email',
            'channel' => 1,
            'pinCode' => $randomPin,
            'link' => $emailLink,
            'activationLinkExpires' => preg_replace('/[^0-9]/', '', ACTIVATION_LINK_EXPIRES),
        ]);
        $smsMessageData = array_merge($welcomeMessageData, [
            'sourceModule' => 'welcome_sms',
            'channel' => 2,
            'pinCode' => $randomPin
        ]);
        Yii::log(__FUNCTION__ . ' Welcome Message ' . CJSON::encode($emailMessageData), CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);
        $queue = new \application\components\queue\ExternalQueue(CUSTOM_QUEUE_DRIVER, CUSTOM_MEMBERS_API_DEFAULT_QUEUE);

        $statusSending = true;
        //Creating user status for the user email - not verified
        if($userModel->email){
            UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 32, $oldPhoneEmail = '', $userModel->email, $userModel->platform_solution_fk, $timeZoneId); //EMAIL_NOT_VERIFIED
        }
        // Creating user status for the user phone - not verified
        if($userModel->phone){
            UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 30, $oldPhoneEmail = '', $userModel->phone, $userModel->platform_solution_fk, $timeZoneId, $userModel->country_phone_fk); //PHONE_NOT_VERIFIED
        }

        if ($businessRulesRecord->notify_user_signup_flag == 0){
            return ['status' => 'OK', 'message_status' => 'sent', 'message' => 'Email / SMS Sent - no signup flag'];
        }

        //If the user has email, send by email
        if ($userModel->email) {
            if($businessId == 3113){
                $lang = 'es';
            } elseif($businessId == 3121){
                $lang = ($lang == 'es') ? 'es' : 'en';
                // TODO Add email subject for business in custom template
                $emailSubject = ($lang == 'es') ? "¡Bienvenido a Baristi Rewards! ☕✨" : "Welcome to Baristi Rewards! ☕✨";
            } elseif($businessId == 3382) {
                $lang = 'en';
            } elseif($businessId == 3370) {
                // TODO Add email subject for business in custom template
                $lang = ($lang == 'fr') ? 'fr' : 'en';
                $emailSubject = ($lang == 'fr') ? "Gérard Bertrand Le Club : Bienvenue" : "Welcome to the Gérard Bertrand Le Club";
            }

            if(Utils::feature("SEND_CUSTOM_NOTIFICATION") && (int)$businessRulesRecord->custom_messages_flag == 1){
                $queue->enqueue("App\\Jobs\\CustomizedMessageProcessor", $emailMessageData, [
                    'laravelCompatible' => true,
                ]);
            }elseif (CONFIG_SHOULD_QUEUE === true) {
                //Enqueue for sending in background
                MessagesQueue::create([
                    'send_to' => $userModel->email,
                    'cell_email' => MESSAGE_SEND_BY_EMAIL,
                    'user_fk' => $userModel->id,
                    'business_fk' => $businessId,
                    'params' => [
                        'emailView' => 'userWelcomeWithPinStore',
                        'layout' => 'bilingual',
                        'emailSubject' => $emailSubject,
                        'emailData' => $emailData,
                        'viewPath' => 'application.views.mail.' . $lang,
                        'fromExtra' => $businessName,
                        'withdraw_credit_flag' => true
                    ],
                ]);
                return ['status' => 'OK', 'message_status' => 'sent', 'message' => 'Email Sent'];
            } else {
                //Sending the email sync
                return Utils::sendEmailWithTemplate('userWelcomeWithPinStore', 'bilingual', $emailSubject, $userModel->email, $emailData, $viewPath = 'application.views.mail.' . $lang, null, $businessName, $businessId);
            }
        } elseif ($userModel->phone) {
            Utils::logMessage(json_encode([
                'userId' => $userModel->id,
                'email' => $userModel->email,
                'phone' => $userModel->phone,
                'notify_user_signup_flag' => $businessRulesRecord->notify_user_signup_flag,
                'message' => 'Before MessagesQueue::create',
            ]), __METHOD__, CLogger::LEVEL_INFO);

            if ($country && !$country->allow_sms) {
                return ['status' => 'OK', 'message_status' => 'queued', 'sms_sid' => null];
            }

            if ($business->industryProhibitedSms()) {
                return ['status' => 'OK', 'message_status' => 'queued', 'sms_sid' => null];
            }

            if(Utils::feature("SEND_CUSTOM_NOTIFICATION") && (int)$businessRulesRecord->custom_messages_flag == 1){
                $queue->enqueue("App\\Jobs\\CustomizedMessageProcessor", $smsMessageData, [
                    'laravelCompatible' => true,
                ]);
            }else{
                //Enqueue for sending in background
                MessagesQueue::create([
                    'send_to' => $userModel->phone,
                    'cell_email' => MESSAGE_SEND_BY_SMS,
                    'country_phone_fk' => $userModel->country_phone_fk,
                    'user_fk' => $userModel->id,
                    'business_fk' => $businessId,
                    'platformName' => $platformName,
                    'params' => ['message_body' => $smsBody, 'withdraw_credit_flag' => true],
                ]);
            }
            return ['status' => 'OK', 'message_status' => 'queued', 'sms_sid' => null];
        } else {
            return array('status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'error' => 'Not valid email/phone');
        }
    }

    /**
     * User signup: sending verification email or SMS
     * Update: 2015-05-20 (Valentin), Additional release V3.2.5.1, using shortUrlKg as a main function for shortening
     * Update: 03 june 2015 adding timezone.
     * Update: 15-06-2015, Valentin adding country code for the phone number in user status manager
     * Update: Send email in user language (v4.0)
     *
     * Used in Website(Signup), Smartphone (iphone/android), Merchant Tablet app
     * @version May - June 2016 release V4.0.0
     * <AUTHOR> 3.2.5 - Philippe 3.3.0
     * @param object $userModel
     * @param array $phoneEmail
     * @param string $platformName
     * @param string $lang - language abreviation
     * @access public
     * @return array
     */
    public static function sendSignupVerificationEmailSMSV330($timeZoneId, $userModel, $phoneEmail, $platformName, $lang)
    {
        $randomToken = User::model()->generateRandomString(60);
        $pinInfo = User::generatePinCodeForNewUser($userModel->email, $userModel->phone, $userModel->id);
        $randomPin = $pinInfo['pin_code'];
        //$encryptedPin     = User::base64UrlEncode($randomPin);

        //saving token and expiry date in user model
        $userModel->passwordreset = $randomToken;
        $userModel->enabled = 1;
        $userModel->password = '';
        $userModel->pin_permission = $pinInfo['pin_permission'];
        $userModel->pin_enabled = 1;
        $userModel->utc_link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
        $userModel->timezone_mysql_fk = $timeZoneId;
        $userModel->save();

        UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 35, '', '', $userModel->platform_solution_fk, $timeZoneId); //PIN_CODE_AUTO_GENERATED

        $emailLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 0, 'e' => 1, 'l' => $lang]); //c-cell phone , e- email , rt- redirect to
        $phoneLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 1, 'e' => 0, 'l' => $lang]); //c-cell phone , e- email, rt- redirect to

        $emailData = [
            'username' => $userModel->email,
            'pincode' => $randomPin,
            'link' => $emailLink,
        ];

        $emailSubject = Yii::t('frontend', 'FRONTEND_USERSIGNUP_MAIL_SUBJECT', array(), null, $lang);
//        $shortLink = Utils::shortUrlKg($phoneLink, $platformName);
        if(isset($randomPin)) {
            $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME', ['{pincode}' => $randomPin]);
        }else{
            $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_WELCOME_WITHOUT_PIN');
        }
        if ($phoneEmail['isPhone']) {
            //inserting in user Status Manager
            $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 30, $oldPhoneEmail = '', $userModel->phone, $userModel->platform_solution_fk, $timeZoneId, $userModel->country_phone_fk); //PHONE_NOT_VERIFIED

            //Enqueue for sending in background
            MessagesQueue::create([
                'send_to' => $userModel->phone,
                'cell_email' => MESSAGE_SEND_BY_SMS,
                'country_phone_fk' => $userModel->country_phone_fk,
                'user_fk' => $userModel->id,
                'business_fk' => $userModel->business_fk,
                'params' => ['message_body' => $smsBody, 'withdraw_credit_flag' => true],
            ]);
            return ['status' => 'OK', 'message_status' => 'queued', 'sms_sid' => null];

            // return Utils::validateAndSendSMS($smsBody, $userModel->phone, $userModel->country_phone_fk, $platformName);
        } elseif ($phoneEmail['isEmail']) {
            //Creating user status for the user
            $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 32, $oldPhoneEmail = '', $userModel->email, $userModel->platform_solution_fk, $timeZoneId); //EMAIL_NOT_VERIFIED

            //Enqueue for sending in background
            MessagesQueue::create([
                'send_to' => $userModel->email,
                'cell_email' => MESSAGE_SEND_BY_EMAIL,
                'user_fk' => $userModel->id,
                'business_fk' => $userModel->business_fk,
                'params' => [
                    'emailView' => 'userWelcomeWithPin',
                    'layout' => 'bilingual',
                    'emailSubject' => $emailSubject,
                    'emailData' => $emailData,
                    'withdraw_credit_flag' => true
                ],
            ]);
            return ['status' => 'OK', 'message_status' => 'sent', 'message' => 'Email Sent'];

            //Sending the email
            // return Utils::sendEmailWithTemplate('userWelcomeWithPin', 'bilingual', $emailSubject, $userModel->email, $emailData, 'application.views.mail.' . $lang, null);
        } else {
            return array('status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'error' => 'Not valid email/phone');
        }
    }

    /**
     * Legacy version - Deprecated // Lei add this comment
     * User signup: sending verification email or SMS
     * Update: 2015-05-20 (Valentin), Additional release V3.2.5.1, using shortUrlKg as a main function for shortening
     * Used in Website(Signup), Smartphone (iphone/android), Merchant Tablet app
     * @version Mar - May 2015 release V3.2.5
     * <AUTHOR>
     * @param object $userModel
     * @param array $phoneEmail
     * @param string $platformName
     * @access public
     * @return array
     */
    public static function sendSignupVerificationEmailSMS($userModel, $phoneEmail, $platformName)
    {
        $randomToken = User::model()->generateRandomString(60);
        $randomPin = User::generatePin();
        //$encryptedPin     = User::base64UrlEncode($randomPin);

        //saving token and expiry date in user model
        $userModel->passwordreset = $randomToken;
        $userModel->enabled = 1;
        $userModel->password = '';
        $userModel->pin_permission = User::cryptPassword($randomPin);
        $userModel->pin_enabled = 1;
        $userModel->link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
        $userModel->save();

        UserStatusManager::createUserStatusManagerRecordV320($userModel->id, $modifiedBy = null, $status_manager_fk = 35, '', '', $userModel->platform_solution_fk); //PIN_CODE_AUTO_GENERATED

        $emailLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 0, 'e' => 1]); //c-cell phone , e- email
        $phoneLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 1, 'e' => 0]); //c-cell phone , e- email

        $emailData = [
            'username' => $userModel->username,
            'pincode' => $randomPin,
            'link' => $emailLink,
        ];

        //$emailSubject = Yii::t('frontend', 'USR_WELCOME', array(), null, 'fr') . ' / ' . Yii::t('frontend', 'USR_WELCOME', array(), null, 'en'); //USR_REG
        $emailSubject = Yii::t('frontend', 'FRONTEND_USERSIGNUP_MAIL_SUBJECT', array(), null, 'en');
        $shortLink = Utils::shortUrlKg($phoneLink, $platformName);
        //$smsBody = 'Please verify your account: ' . User::shortUrl($phoneLink) . ' or use this PIN to login: '. $randomPin;
        $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_VERIFY_SMS_FR_1') . Yii::t('frontend', 'FRONTEND_USERSIGNUP_VERIFY_SMS_EN_1') . $randomPin . ' ' . Yii::t('frontend', 'FRONTEND_USERSIGNUP_VERIFY_SMS_FR_2') . Yii::t('frontend', 'FRONTEND_USERSIGNUP_VERIFY_SMS_EN_2') . ' ' . $shortLink['shortUrl'];
        //$smsBody .= Yii::t('frontend','FRONTEND_USERSIGNUP_VERIFY_SMS_EN_1').$randomPin.' '.Yii::t('frontend','FRONTEND_USERSIGNUP_VERIFY_SMS_EN_2').' '.$shortLink;

        if ($phoneEmail['isPhone']) {
            //inserting in user Status Manager

            Yii::log($platformName . ' - INSIDE $sendSignupVerificationEmailSMS() ', CLogger::LEVEL_INFO, 'BEFORE inserting in user Status Manager ');

            $userStatus = UserStatusManager::createUserStatusManagerRecordV320($userModel->id, $modifiedBy = null, $status_manager_fk = 30, $oldPhoneEmail = '', $userModel->phone, $userModel->platform_solution_fk); //PHONE_NOT_VERIFIED
            //return User::validateAndSendSMS($smsBody, $userModel->phone);
            return Utils::validateAndSendSMS($smsBody, $userModel->phone, $userModel->country_phone_fk,
                $platformName, [], $userModel->business_fk, $userModel->id, null, true);

        } elseif ($phoneEmail['isEmail']) {
            //Creating user status for the user
            $userStatus = UserStatusManager::createUserStatusManagerRecordV320($userModel->id, $modifiedBy = null, $status_manager_fk = 32, $oldPhoneEmail = '', $userModel->email, $userModel->platform_solution_fk); //EMAIL_NOT_VERIFIED
            //Sending the email
            return User::sendEmailWithTemplateV320('userWelcomeWithPin', 'bilingual', $emailSubject, $userModel->email, $emailData, $userModel->business_fk);
        } else {
            return array('status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'error' => 'Not valid email/phone');
        }

    }

    /**
     * TIMEZONE - Globalization implementation
     * Transaction for user signup
     * It will insert the user in db and assign user permission
     *
     * Used in Website(Signup), Smartphone (iphone/android), Merchant Tablet app
     * <AUTHOR> N.
     * @version May - June 2015 V3.3.0
     * @since 2.1.0
     * @param object $joinModel
     * @param array $phoneEmail
     * @param string $platformName
     * @param boolean isFacebook
     * @access public
     * @return array
     */
    public static function userSignuUpTransactionV330($timeZoneId, $joinModel, $phoneEmail, $platformName, $isFacebook = false, $lang = 'en')
    {
        if ($joinModel->email == null && $joinModel->phone == null) {
            $userAgent = (isset($_SERVER['HTTP_USER_AGENT'])) ? $_SERVER['HTTP_USER_AGENT'] : '';
            Yii::log('No email, no phone User: ' . CJSON::encode($joinModel) .' userAgent' . $userAgent , CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            return array('status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'errorDetails' => 'No email and no phone');
        }

        $joinModel->timezone_mysql_fk = $timeZoneId;
        if ($joinModel->save(false)) {
            //Assign the User Role for this User
            $userId = $joinModel->getPrimaryKey();

            $permission = 'user';
            $entity_fk = $userId;
            $entity_type_fk = 4; //user
            UserEntityPermission::assignEntityPermissionV330($userId, $permission, $entity_fk, $entity_type_fk, $timeZoneId);

            //Yii::log($platformName.' Signup: User Model saved UserId='.$userId, CLogger::LEVEL_INFO, 'userSignuUpTransaction');

            //sending SMS or Email
            if ($isFacebook) {
                $isVerified = UserStatusManager::verifyEmail($userId, $joinModel->email);

                if (!$isVerified) {

                    if ($joinModel->accounts_merge == 1) {
                        Yii::log('Existing email log in with Facebook', CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
                        $sentEmailSMS = ['status' => 'OK', 'message' => 'Facebook user verified']; //facebook user with verified email
                        $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 33, '', $joinModel->email, $joinModel->platform_solution_fk, $timeZoneId); //EMAIL_VERIFIED
                    } else {
                        Yii::log('Facebook user not verified sending the email. Change status', CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
                        $sentEmailSMS = User::sendEmailPinCodeFacebookUserV330($timeZoneId, $userId, $lang);
                        UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 35, '', '', $joinModel->platform_solution_fk, $timeZoneId); //PIN_CODE_AUTO_GENERATED
                        $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 33, '', $joinModel->email, $joinModel->platform_solution_fk, $timeZoneId); //EMAIL_VERIFIED
                    }

                } else {
                    Yii::log('Facebook user verified Update', CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
                    $sentEmailSMS = ['status' => 'OK', 'message' => 'Facebook user verified']; //facebook user with verified email
                }
            } else {

                Yii::log('INSIDE userSignuUpTransactionV330() - BEFORE sendSignupVerificationEmailSMSV330() ', CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

                if (in_array($joinModel->platform_solution_fk, [2, 6, 7, 8]) && $joinModel->signedupat_fk != null) {
                    $sentEmailSMS = User::sendSignupVerificationEmailSMSStore($timeZoneId, $joinModel, $phoneEmail, $platformName, $lang);
                } else {
                    $sentEmailSMS = User::sendSignupVerificationEmailSMSV330($timeZoneId, $joinModel, $phoneEmail, $platformName, $lang);
                }
                // For Merchant Tablet App purposes - it has to return the UserId
                $sentEmailSMS['result'] = $userId;
            }

            //Yii::log($platformName.' Sent Email/Phone Response' . CJSON::encode($sentEmailSMS), CLogger::LEVEL_INFO, 'userSignuUpTransaction');
            //end of save
        } else {
            //error in the save
            Yii::log('Signup: User Model not saved. Errors=' . CJSON::encode($joinModel->getErrors()), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            return array('status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'errorDetails' => 'User model could not be saved');
        }

        if (isset($sentEmailSMS) && $sentEmailSMS['status'] == 'NOT_OK') {
            //sms or email failed
            if ($phoneEmail['isPhone']) {
                $userStatus = UserStatusManager::createUserStatusManagerRecordV330($joinModel->id, $modifiedBy = null, $status_manager_fk = 37, $oldPhone = '', $joinModel->phone, $joinModel->platform_solution_fk, $timeZoneId, $joinModel->country_phone_fk);
            }
            //SMS Failed
            elseif ($phoneEmail['isEmail']) {
                $userStatus = UserStatusManager::createUserStatusManagerRecordV330($joinModel->id, $modifiedBy = null, $status_manager_fk = 38, $oldEmail = '', $joinModel->email, $joinModel->platform_solution_fk, $timeZoneId);
            }
            //Email failed

            Yii::log('Email/SMS was not sent ' . CJSON::encode($sentEmailSMS), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);

            // Making sure the Sign Up process is not stopped because the Sent SMS/Email failed
            $sentEmailSMS['status'] = 'OK';
        }

        return $sentEmailSMS;
    }

    /**
     * Transaction for user signup
     * It will insert the user in db and assign user permission
     * Used in Website(Signup), Smartphone (iphone/android), Merchant Tablet app
     * @version Jan - Feb 2015 V3.2.0
     * @since 2.1.0
     * @param object $joinModel
     * @param array $phoneEmail
     * @param string $platformName
     * @param boolean isFacebook
     * @access public
     * @return array
     */
    public static function userSignuUpTransaction($joinModel, $phoneEmail, $platformName, $isFacebook = false)
    {
        if ($joinModel->email == null && $joinModel->phone == null) {
            Yii::log($platformName . ' No email, no phone User: ' . CJSON::encode($joinModel), CLogger::LEVEL_ERROR, 'userSignuUpTransaction');
            return array('status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'errorDetails' => 'No email and no phone');
        }

        if ($joinModel->save(false)) {
            //Assign the User Role for this User
            $userId = $joinModel->getPrimaryKey();

            $permission = 'user';
            $entity_fk = $userId;
            $entity_type_fk = 4; //user
            UserEntityPermission::assignEntityPermission($userId, $permission, $entity_fk, $entity_type_fk);

            //Yii::log($platformName.' Signup: User Model saved UserId='.$userId, CLogger::LEVEL_INFO, 'userSignuUpTransaction');

            //sending SMS or Email
            if ($isFacebook) {
                $isVerified = UserStatusManager::verifyEmail($userId, $joinModel->email);

                if (!$isVerified) {

                    if ($joinModel->accounts_merge == 1) {
                        Yii::log($platformName . ' Existing email log in with Facebook', CLogger::LEVEL_INFO, 'userSignuUpTransaction FB');
                        $sentEmailSMS = ['status' => 'OK', 'message' => 'Facebook user verified']; //facebook user with verified email
                        $userStatus = UserStatusManager::createUserStatusManagerRecordV320($userId, $modifiedBy = null, $status_manager_fk = 33, '', $joinModel->email, $joinModel->platform_solution_fk); //EMAIL_VERIFIED
                    } else {
                        Yii::log($platformName . ' Facebook user not verified sending the email. Change status', CLogger::LEVEL_INFO, 'userSignuUpTransaction FB');
                        $sentEmailSMS = User::sendEmailPinCodeFacebookUser($userId);
                        UserStatusManager::createUserStatusManagerRecordV320($userId, $modifiedBy = null, $status_manager_fk = 35, '', '', $joinModel->platform_solution_fk); //PIN_CODE_AUTO_GENERATED
                        $userStatus = UserStatusManager::createUserStatusManagerRecordV320($userId, $modifiedBy = null, $status_manager_fk = 33, '', $joinModel->email, $joinModel->platform_solution_fk); //EMAIL_VERIFIED
                    }

                    // $statusManager = UserStatusManager::getPhoneEmailLastStatus($userId, $joinModel->email);
                    // if($statusManager!=null && $statusManager[0]->status_manager_fk != 32)
                    // {
                    //     Yii::log($platformName.' Facebook user not verified sending the email. Change status', CLogger::LEVEL_INFO, 'userSignuUpTransaction FB');
                    //     $sentEmailSMS = User::sendEmailPinCodeFacebookUser($userId);
                    //     $userStatus = UserStatusManager::createUserStatusManagerRecordV320($userId, $modifiedBy=null, $status_manager_fk = 33, '', $joinModel->email, $joinModel->platform_solution_fk);//EMAIL_VERIFIED
                    // }
                    // else
                    // {
                    //     $userStatus = UserStatusManager::createUserStatusManagerRecordV320($userId, $modifiedBy=null, $status_manager_fk = 33, '', $joinModel->email, $joinModel->platform_solution_fk);//EMAIL_VERIFIED
                    //     $sentEmailSMS = ['status'=>'OK', 'message'=>'Facebook user verified'];
                    // }
                } else {
                    Yii::log($platformName . ' Facebook user verified Update', CLogger::LEVEL_INFO, 'userSignuUpTransaction FB');
                    $sentEmailSMS = ['status' => 'OK', 'message' => 'Facebook user verified']; //facebook user with verified email
                }
            } else {

                Yii::log($platformName . ' INSIDE userSignuUpTransaction() - BEFORE sendSignupVerificationEmailSMS() ', CLogger::LEVEL_INFO, 'userSignuUpTransaction');

                // For Merchant Tablet App purposes - it has to return the UserId
                $sentEmailSMS = User::sendSignupVerificationEmailSMS($joinModel, $phoneEmail, $platformName);
                $sentEmailSMS['result'] = $userId;
            }

            //Yii::log($platformName.' Sent Email/Phone Response' . CJSON::encode($sentEmailSMS), CLogger::LEVEL_INFO, 'userSignuUpTransaction');
            //end of save
        } else {
            //error in the save
            Yii::log($platformName . ' Signup: User Model not saved. Errors=' . $joinModel->getErrors(), CLogger::LEVEL_ERROR, 'userSignuUpTransaction');
            return array('status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'errorDetails' => 'User model could not be saved');
        }

        if (isset($sentEmailSMS) && $sentEmailSMS['status'] == 'NOT_OK') {
            //sms or email failed
            if ($phoneEmail['isPhone']) {
                $userStatus = UserStatusManager::createUserStatusManagerRecordV320($joinModel->id, $modifiedBy = null, $status_manager_fk = 37, $oldPhone = '', $joinModel->phone, $joinModel->platform_solution_fk);
            }
            //SMS Failed
            elseif ($phoneEmail['isEmail']) {
                $userStatus = UserStatusManager::createUserStatusManagerRecordV320($joinModel->id, $modifiedBy = null, $status_manager_fk = 38, $oldEmail = '', $joinModel->email, $joinModel->platform_solution_fk);
            }
            //Email failed

            Yii::log($platformName . ' Email/SMS was not sent ' . CJSON::encode($sentEmailSMS), CLogger::LEVEL_ERROR, 'userSignuUpTransaction');

            // Making sure the Sign Up process is not stopped because the Sent SMS/Email failed
            $sentEmailSMS['status'] = 'OK';
        }

        return $sentEmailSMS;
    }

    /**
     * INTERNATIONAL PhoneNumber - Globalization implementation
     * Custom validator for email, username or phone and QR code
     * QRCode Login (login type: 1=phone/2=email/3=QRcode) in order to distinguish phone login from qrcode
     *
     * Used in : Merchant Tablet App
     * <AUTHOR> Patricia
     * @version : May - Jun 2015 release V3.3.0
     * @param string $usernameEmailPhoneQR
     * @access public
     * @return array
     */
    public static function phoneEmailUsernameQRValidatorV330($usernameEmailPhoneQR, $loginType, $platfName = 'NOT_DEFINED', $log = false)
    {
        $result = ['isEmail' => 0, 'isPhone' => 0, 'isUsername' => 0, 'isQRcode' => 0, 'isAccountNumber' => 0];

        if (strpos((string)$usernameEmailPhoneQR, "@") == null) //username or phone or QRcode or Account Number
        {
            if (ctype_digit((string)$usernameEmailPhoneQR) && $loginType == 1) // all digits -> phone
            {
                if ($log) {
                    Yii::log('Credential detail>> - isPhone: ' . $usernameEmailPhoneQR, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
                }

                $result['isPhone'] = 1;return $result;
            } elseif ($loginType == 3) // all digits -> QRcode
            {
                //ctype_digit($usernameEmailPhoneQR) && strlen((string)$usernameEmailPhoneQR) == 9 &&
                if ($log) {
                    Yii::log('Credential detail>> - isQRcode: ' . $usernameEmailPhoneQR, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
                }

                $result['isQRcode'] = 1;return $result;
            } elseif ($loginType == 4) // all digits -> Account Number
            {
                if ($log) {
                    Yii::log('Credential detail>> - isAccountNumber: ' . $usernameEmailPhoneQR, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
                }

                $result['isAccountNumber'] = 1;return $result;
            } elseif (ctype_alnum((string)$usernameEmailPhoneQR) && strlen((string)$usernameEmailPhoneQR) > 5) {
                if ($log) {
                    Yii::log('Credential detail>> - isUsername: ' . $usernameEmailPhoneQR, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
                }

                $result['isUsername'] = 1;return $result;
            }

            if ($log) {
                Yii::log('Credential detail>> - isString: ' . $usernameEmailPhoneQR, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
            }

        } else //email
        {
            $validator = new CEmailValidator;
            if ($validator->validateValue($usernameEmailPhoneQR)) {
                if ($log) {
                    Yii::log('Credential detail>> - isEmail: ' . $usernameEmailPhoneQR, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
                }

                $result['isEmail'] = 1;return $result;
            }

            if ($log) {
                Yii::log('Credential detail>> - not valid email: ' . $usernameEmailPhoneQR, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
            }

        }

        return $result;
    }

    /**
     * TO DELETE ONCE V330 IS ON PROD
     * Custom validator for email, username or phone and QR code
     * QRCode Login (login type: 1=phone/2=email/3=QRcode) in order to distinguish phone login from qrcode
     *
     * Used in : Merchant Tablet App
     * <AUTHOR> C.
     * @version : Mar - Apr 2015 release V3.2.5
     * @param string $usernameEmailPhoneQR
     * @access public
     * @return array
     */
    public static function phoneEmailUsernameQRValidator($usernameEmailPhoneQR, $loginType, $platfName = 'NOT_DEFINED', $log = false)
    {
        $result = ['isEmail' => 0, 'isPhone' => 0, 'isUsername' => 0, 'isQRcode' => 0];

        if (strpos((string)$usernameEmailPhoneQR, "@") == null) //username or phone or QRcode
        {
            if (ctype_digit((string)$usernameEmailPhoneQR) && strlen((string)$usernameEmailPhoneQR) == 10 && $loginType == 1) // all digits -> phone
            {
                if ($log) {
                    Yii::log('Credential detail>> - isPhone: ' . $usernameEmailPhoneQR, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
                }

                $result['isPhone'] = 1;return $result;
            } elseif (ctype_digit((string)$usernameEmailPhoneQR) && strlen((string)$usernameEmailPhoneQR) == 9 && $loginType == 3) // all digits -> QRcode
            {
                if ($log) {
                    Yii::log('Credential detail>> - isQRcode: ' . $usernameEmailPhoneQR, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
                }

                $result['isQRcode'] = 1;return $result;
            } elseif (ctype_alnum((string)$usernameEmailPhoneQR) && strlen((string)$usernameEmailPhoneQR) > 5) {
                if ($log) {
                    Yii::log('Credential detail>> - isUsername: ' . $usernameEmailPhoneQR, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
                }

                $result['isUsername'] = 1;return $result;
            }

            if ($log) {
                Yii::log('Credential detail>> - isString: ' . $usernameEmailPhoneQR, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
            }

        } else //email
        {
            $validator = new CEmailValidator;
            if ($validator->validateValue($usernameEmailPhoneQR)) {
                if ($log) {
                    Yii::log('Credential detail>> - isEmail: ' . $usernameEmailPhoneQR, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
                }

                $result['isEmail'] = 1;return $result;
            }

            if ($log) {
                Yii::log('Credential detail>> - not valid email: ' . $usernameEmailPhoneQR, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
            }

        }

        return $result;
    }

    /**
     * INTERNATIONAL PhoneNumber - Globalization implementation
     * Custom validator for email, username or phone
     * Used in Website(Signup), Smartphone (iphone/android), Merchant Tablet app (V320)
     *
     * <AUTHOR> Patricia
     * @version : May - Jun 2015 release V3.3.0
     * @param string $usernameEmailPhone
     * @access public
     * @return array
     */
    public static function phoneEmailUsernameValidatorV330($usernameEmailPhone, $platfName = 'NOT_DEFINED', $log = false)
    {
        $result = ['isEmail' => 0, 'isPhone' => 0, 'isUsername' => 0, 'isQrCode' => 0];

        if (strpos((string)$usernameEmailPhone, "@") == null) //username or phone
        {
            if (ctype_digit((string)$usernameEmailPhone)) // all digits -> phone
            {
                if ($log) {
                    Yii::log('Input details>> - isPhone: ' . $usernameEmailPhone, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
                }

                $result['isPhone'] = 1;return $result;
            } elseif (ctype_alnum((string)$usernameEmailPhone) && strlen((string)$usernameEmailPhone) > 5) {
                if ($log) {
                    Yii::log('Input details>> - isUsername: ' . $usernameEmailPhone, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
                }

                $result['isUsername'] = 1;return $result;
            }
            if ($log) {
                Yii::log('Input details>> - isString: ' . $usernameEmailPhone, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
            }

        } else //email
        {
            $validator = new CEmailValidator;
            if ($validator->validateValue($usernameEmailPhone)) {
                if ($log) {
                    Yii::log('Input details>> - isEmail: ' . $usernameEmailPhone, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
                }

                $result['isEmail'] = 1;return $result;
            }

            if ($log) {
                Yii::log('Input details>> - not valid email: ' . $usernameEmailPhone, CLogger::LEVEL_INFO, $platfName . '_' . __FUNCTION__);
            }

        }

        return $result;
    }

    /**
     * Check if it's a QR code
     * @version v4.35
     * <AUTHOR>
     * @param $usernameEmailPhoneQRcode
     * @return bool
     */
    private static function QRCodeValidator($usernameEmailPhoneQRcode)
    {
        if (strlen((string)$usernameEmailPhoneQRcode??'') == QRCODE_VALID_LENGTH) {
            if (ctype_digit((string)$usernameEmailPhoneQRcode)) {
                return true;
            } elseif (substr((string)$usernameEmailPhoneQRcode, 0, 1) === 'A' && ctype_digit((string)substr((string)$usernameEmailPhoneQRcode, 1))) {
                return true;
            }
        }
        return false;
    }

    /**
     * TO DELETE ONCE V330 IS ON PROD
     * Custom validator for email, username or phone
     * Used in Website(Signup), Smartphone (iphone/android), Merchant Tablet app
     * @version Website/Smartphone Jan - Feb 2015 V3.2.0
     * @param string $usernameEmailPhone
     * @access public
     * @return array
     */
    public static function phoneEmailUsernameValidator($usernameEmailPhone, $platfName = 'NOT_DEFINED', $log = true)
    {
        $result = ['isEmail' => 0, 'isPhone' => 0, 'isUsername' => 0];

        if (strpos((string)$usernameEmailPhone, "@") == null) //username or phone
        {
            if (ctype_digit((string)$usernameEmailPhone)) // all digits -> phone
            {
                if ($log) {
                    Yii::log($platfName . ' - ' . $usernameEmailPhone, CLogger::LEVEL_INFO, 'phoneEmailUsernameValidator: isPhone');
                }

                $result['isPhone'] = 1;return $result;
            } elseif (ctype_alnum((string)$usernameEmailPhone) && strlen((string)$usernameEmailPhone) > 5) {
                if ($log) {
                    Yii::log($platfName . ' - ' . $usernameEmailPhone, CLogger::LEVEL_INFO, 'phoneEmailUsernameValidator: isUsername');
                }

                $result['isUsername'] = 1;return $result;
            }

            if ($log) {
                Yii::log($platfName . ' - ' . $usernameEmailPhone, CLogger::LEVEL_INFO, 'phoneEmailUsernameValidator: isString');
            }

        } else //email
        {
            $validator = new CEmailValidator;
            if ($validator->validateValue($usernameEmailPhone)) {
                if ($log) {
                    Yii::log($platfName . ' - ' . $usernameEmailPhone, CLogger::LEVEL_INFO, 'phoneEmailUsernameValidator: isEmail');
                }

                $result['isEmail'] = 1;return $result;
            }

            if ($log) {
                Yii::log($platfName . ' - ' . $usernameEmailPhone, CLogger::LEVEL_INFO, 'phoneEmailUsernameValidator: not valid email');
            }

        }

        return $result;
    }

    /**
     * Valentin - May 20, 2015 - Copied in Utils, to be removed after the new function will be used everywhere
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * Short a long url.
     * First it will short with Google it limit exceded or error than short with bitly
     *
     * Used on : Website. Smartphone, Tablet
     *
     * @version Website/Smartphone Jan - Feb 2015 V3.2.0
     * @param string $longUrl
     * @access public
     * @return string
     */
    public static function shortUrl($longUrl)
    {

        $googleResonse = User::shortUrlGoogle($longUrl);

        if ($googleResonse['status'] == 'OK') {
            return $googleResonse['shortUrl'];
        } else {
            return $longUrl;
        }
    }

    /**
     * Valentin - May 20, 2015 - Copied in Utils, to be removed after the new function will be used everywhere
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * Short a long url.
     * First it will short with Google if limit exceded or error than short with bitly
     * Update: Not implemented yet in v3.2.5
     * Used on :
     *
     * @version Mar - Apr 2015 release V3.2.5
     * <AUTHOR>
     * @param string $longUrl
     * @access public
     * @return array
     */
    public static function shortUrlV325($longUrl)
    {
        $googleResonse = User::shortUrlGoogleV325($longUrl);

        if ($googleResonse['status'] == 'OK') {
            return ['status' => 'OK', 'shortUrl' => $googleResonse['shortUrl'], 'longUrl' => $longUrl];
        } else {
            return ['status' => 'NOT_OK', 'shortUrl' => $longUrl, 'longUrl' => $longUrl];
        }
    }

    /**
     * update the info of the user after every login
     * used on all the platforms
     * <AUTHOR> N
     * @version March April 2016 V4.0
     * @param unknown $user
     * @param unknown $params
     */
    public static function updateUserLoginInfo($user, $params)
    {
        $user->timezone_mysql_fk = $params['timezone_mysql_fk'];
        // $user->country_phone_fk = $params['country_phone_fk']; // Do not update every time on login. Valentin
        $user->login_type_fk = $params['login_type_fk'];
        $user->device_token = isset($params['device_token']) ? $params['device_token'] : null;
        $user->gcm_device_token = isset($params['gcm_device_token']) ? $params['gcm_device_token'] : null;
        $user->ip = $params['ip'];
        // $user->platform_solution_fk = $params['platform_solution_fk']; // Do not update every time on login. Valentin
        $user->utc_last_login = date('Y-m-d H:i:s');

        return Utils::saveModel($user);
    }

    /**
     * sign up the user using facebook if the user doesn't exist
     * used on all the platforms
     * <AUTHOR> N
     * @version March April 2016 V4.0
     * @param unknown $params
     */
//    public static function signupLoginFacebookUser($params, $isLogin = true)
//    {
//        if (User::checkFbUserIdExistance($params['id'])) {
//            if (self::checkFacebookIdUserEmailExistance($params['id'], $params['email'])) {
//                $user = User::model()->findByAttributes(['facebook_id' => $params['id']]);
//            } else {
//                $otherUser = User::model()->findByAttributes(['facebook_id' => $params['id']]);
//                $otherUser->facebook_id = null;
//                $otherUser->save();
//                $user = User::model()->findByAttributes(['email' => $params['email']]);
//                if ($user) {
//                    $user->facebook_id = $params['id'];
//                } else {
//                    $user = new User;
//                    $user->facebook_id = $params['id'];
//                    $user->enabled = ENABLED;
//                }
//            }
//        } else {
//            $user = new User;
//            $user->facebook_id = $params['id'];
//            $user->enabled = ENABLED;
//        }
//        $user->username = Utils::generateUniqueUsername();
//        $user->language = $params['language'];
//        $user->first = ($user->first == null || $user->first == '') ? $params['first_name'] : $user->first;
//        $user->last = ($user->last == null || $user->last == '') ? $params['last_name'] : $user->last;
//        $user->email = $params['email'];
//        $user->gender = $params['gender'] == 'male' ? GENDER_MALE_FK : GENDER_FEMALE_FK;
//        $user->birth_date = ($user->birth_date == null || $user->birth_date == '') ? $params['birthday'] : $user->birth_date;
//        if (!$isLogin) {
//            $user->qrcode = User::generateQRcode();
//        }
//
//        return User::updateUserLoginInfo($user, $params);
//    }


    /**
     * It will send email or sms to the user that asked for reset password
     * Update: 2015-05-20 (Valentin), Additional release V3.2.5.1, using shortUrlKg as a main function for shortening
     * Used on : Website(Forgot password)/smartphone (android/iphone)
     *
     * @version Mar - Apr 2015 release V3.2.5
     * <AUTHOR>
     * @param string emailOrPhone
     * @param object $user
     * @param string $link
     * @param string $random
     * @param int $platform
     * @access public
     * @return array
     */
    public static function resetPinSendEmailOrSMS($emailOrPhone, $user/*Model*/, $link, $random, $platform)
    {
        if (Yii::app()->authManager->checkAccess('admin', $user->id) || Yii::app()->authManager->checkAccess('clerk', $user->id)) {
            return array('status' => 'NOT_OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERRESETPIN_ADMIN_NOT_ALLOWED'), 'redirect' => Yii::app()->createUrl('site/ForgotPin'));
        }

        $platformSolution = PlatformSolution::model()->findByPk($platform);
        $platformName = $platformSolution->name;

        if (strpos((string)$emailOrPhone, "@") == null) //sending sms
        {
            $shortUrl = Utils::shortUrlKg($link, $platformName);

            if ($user->language == 2) {
                $smsBody = Yii::t('landingpages', 'LANDINGPAGE_USERRESETPIN_EMAILPHONE_RESET_LINK', array('{link}' => $shortUrl['shortUrl']), null, 'fr');
            } else {
                $smsBody = Yii::t('landingpages', 'LANDINGPAGE_USERRESETPIN_EMAILPHONE_RESET_LINK', array('{link}' => $shortUrl['shortUrl']), null, 'en');
            }

            // $smsResults = User::validateAndSendSMS($smsBody, $user->phone);
            $smsResults = Utils::validateAndSendSMS($smsBody, $user->phone, $user->country_phone_fk,
                $platformName, [], $user->business_fk, $user->id, null, true);

            if ($smsResults['status'] == 'OK') {
                $user->passwordreset = $random;
                $user->link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
                $user->save();

                UserStatusManager::createUserStatusManagerRecordV320($user->id, $modifiedBy = null, $status_manager_fk = 55, '', '', $platform); //FORGOT_PIN_CODE_REQUEST

                return array('status' => 'OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERRESETPIN_SMS_SENT_SUCCESS'), 'redirect' => Yii::app()->createUrl('site/ForgotPin'));
            } else {
                return array('status' => 'NOT_OK', 'message' => $smsResults['message'], 'redirect' => Yii::app()->createUrl('site/ForgotPin'));
            }
        } else { //email
            $languages = Language::loadLanguages();
            foreach ($languages as $key => $value) {
                //Getting the user's language (abreviation)
                if ($value['language_pk'] == $user->language) {
                    $strLang = $value['abreviation'];
                }

            }
            $strLang = strtolower((string)$strLang);

            $emailData = ['username' => $user->username, 'link' => $link];
            $emailSubject = Yii::t('frontend', 'PIN_RESET', array(), null, $strLang);
            $sendTo = [$user->email => $user->first];
            // /, 'application.views.mail.'.$strLang
            $emailSentResult = User::sendEmailWithTemplateV320('pinResetBilingual', 'bilingual', $emailSubject, $sendTo, $emailData);

            if ($emailSentResult['status'] == 'OK') {
                $user->passwordreset = $random;
                $user->link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
                $user->save();

                UserStatusManager::createUserStatusManagerRecordV320($user->id, $modifiedBy = null, $status_manager_fk = 55, '', '', $platform); //FORGOT_PIN_CODE_REQUEST

                return array('status' => 'OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERRESETPIN_EMAIL_SENT_SUCCESS'), 'redirect' => Yii::app()->createUrl('site/ForgotPin'));
            } else {
                return array('status' => 'NOT_OK', 'message' => $emailSentResult['message'], 'redirect' => Yii::app()->createUrl('site/ForgotPin'));
            }
        }
    }

    /**
     * TIMEZONE - Globalization implementation
     * Updating send email function (3.4.0)
     * It will send email or sms to the user that asked for reset password
     * Using shortUrlKg as a main function for shortening
     * for this version includes the TIMEZONE Globalization handling
     *
     * Used in : Website(Forgot password)/smartphone (android/iphone)
     * <AUTHOR> Juan David S.
     * @version : Aug - oct 2015 release V3.4.0
     * @version : May - Jun 2015 release V3.3.0
     * @param String $emailOrPhone, $link, $random, $platform, $timeZoneName
     * @return array
     */
//    public static function resetPinSendEmailOrSMSV330($emailOrPhone, $user/*Model*/, $link, $random, $platform, $timeZoneId, $countryId)
//    {
//        if (Yii::app()->authManager->checkAccess('admin', $user->id) || Yii::app()->authManager->checkAccess('clerk', $user->id)) {
//            return array('status' => 'NOT_OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERRESETPIN_ADMIN_NOT_ALLOWED'), 'redirect' => Yii::app()->createUrl('site/ForgotPin'));
//        }
//
//        $platformSolution = PlatformSolution::model()->findByPk($platform);
//        $platformName = $platformSolution->name;
//
//        if (strpos((string)$emailOrPhone, "@") == null) //sending sms
//        {
//            $shortUrl = Utils::shortUrlKg($link, $platformName);
//
//            if ($user->language == 2) {
//                $smsBody = Yii::t('landingpages', 'LANDINGPAGE_USERRESETPIN_EMAILPHONE_RESET_LINK', array('{link}' => $shortUrl['shortUrl']), null, 'fr');
//            } else {
//                $smsBody = Yii::t('landingpages', 'LANDINGPAGE_USERRESETPIN_EMAILPHONE_RESET_LINK', array('{link}' => $shortUrl['shortUrl']), null, 'en');
//            }
//
//            $smsResults = Utils::validateAndSendSMS($smsBody, $user->phone, $countryId, $platformName, [], $user->business_fk);
//
//            if ($smsResults['status'] == 'OK') {
//                $user->passwordreset = $random;
//                $user->utc_link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
//                $user->utc_updated = date("Y-m-d H:i:s");
//                $user->timezone_mysql_fk = $timeZoneId;
//                $user->save();
//
//                UserStatusManager::createUserStatusManagerRecordV330($user->id, $modifiedBy = null, $status_manager_fk = 55, '', '', $platform, $timeZoneId, $countryId); //FORGOT_PIN_CODE_REQUEST
//
//                return array('status' => 'OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERRESETPIN_SMS_SENT_SUCCESS'), 'redirect' => Yii::app()->createUrl('site/ForgotPin'));
//            } else {
//                if ($smsResults['errorCode'] == 21610) {
//                    return array('status' => 'NOT_OK', 'message' => Yii::t('commonlabels', 'COMMON_VALIDATESEND_SMS_ERROR_CODE21610', array('{twilio_phone}' => Utils::getFormattedPhoneNumber($smsResults['phoneFrom'], 2))), 'redirect' => Yii::app()->createUrl('site/ForgotPin'));
//                }
//
//                return array('status' => 'NOT_OK', 'message' => $smsResults['message'], 'redirect' => Yii::app()->createUrl('site/ForgotPin'));
//            }
//        } else //send reset password email
//        {
//            $strLang = strtolower((string)Language::model()->findByPk($user->language)->abreviation);
//
//            $emailData = ['username' => $user->username, 'link' => $link];
//            $emailSubject = Yii::t('frontend', 'PIN_RESET', array(), null, $strLang);
//            $sendTo = [$user->email => $user->first];
//            $emailSentResult = Utils::sendEmailWithTemplate('pinReset', 'bilingual', $emailSubject, $sendTo, $emailData, 'application.views.mail.' . $strLang,
//                null, null, $user->business_fk);
//
//            if ($emailSentResult['status'] == 'OK') {
//                $user->passwordreset = $random;
//                $user->utc_link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
//                $user->utc_updated = date("Y-m-d H:i:s");
//                $user->timezone_mysql_fk = $timeZoneId;
//                $user->save();
//
//                UserStatusManager::createUserStatusManagerRecordV330($user->id, $modifiedBy = null, $status_manager_fk = 55, '', '', $platform, $timeZoneId); //FORGOT_PIN_CODE_REQUEST
//
//                return array('status' => 'OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERRESETPIN_EMAIL_SENT_SUCCESS'), 'redirect' => Yii::app()->createUrl('site/ForgotPin'));
//            } else {
//                return array('status' => 'NOT_OK', 'message' => $emailSentResult['message'], 'redirect' => Yii::app()->createUrl('site/ForgotPin'));
//            }
//        }
//    }

    /**
     * Create user model from Facebook profile info
     *
     * used in website, smartphone
     * @version Jan - Feb 2015 V3.2.0
     * @param array $facebookUserProfile
     * @param int $platformApp
     * @param string $fbToken
     * @param string $ip
     * @access public
     * @return object
     */
//    public static function createUserModelFromFacebookProfile($facebookUserProfile, $platformApp, $ip, $doUpdate = null)
//    {
//        if ($doUpdate == null) {
//            $userModel = new User('join');
//        } else {
//            $userModel = User::model()->findByAttributes(array('email' => $facebookUserProfile['email']));
//            $userModel->accounts_merge = 1;
//        }
//
//        $userModel->facebook_id = $facebookUserProfile['id'];
//        $userModel->username = User::model()->createUsername($facebookUserProfile['email']);
//        $userModel->email = $facebookUserProfile['email'];
//        $userModel->first = isset($facebookUserProfile['first_name']) ? $facebookUserProfile['first_name'] : $facebookUserProfile['middle_name'];
//        $userModel->last = $facebookUserProfile['last_name'];
//        $userModel->basic_profile_reminder = date('Y-m-d');
//        $userModel->enabled = 1;
//        $userModel->display_facebook_image = 0;
//        $userModel->birth_date = isset($facebookUserProfile['birthday']) ? date('Y-m-d', strtotime((string)$facebookUserProfile['birthday'])) : null;
//        $userModel->gender = $facebookUserProfile['gender'] == 'male' ? 2 : 1;
//        $userModel->login_type_fk = 2;
//        $userModel->platform_solution_fk = $platformApp;
//        $userModel->qrcode = User::generateQRcode();
//        $userModel->ip = $ip;
//
//        return $userModel;
//    }

    /**
     * TIMEZONE - Globalization implementation
     * Create user model from Facebook profile info
     *
     * Used in website, smartphone(ios/Android)
     * <AUTHOR> Philippe N.
     * @version : May - Jun 2015 release V3.3.0
     * @param array $facebookUserProfile
     * @param int $platformApp
     * @param string $fbToken
     * @param string $ip
     * @access public
     * @return object
     */
//    public static function createUserModelFromFacebookProfileV330($timeZoneId, $facebookUserProfile, $platformApp, $ip, $doUpdate = null)
//    {
//        if ($doUpdate == null) {
//            $userModel = new User('join');
//        } else {
//            $userModel = User::model()->findByAttributes(array('email' => $facebookUserProfile['email']));
//            $userModel->accounts_merge = 1;
//        }
//
//        $userModel->facebook_id = $facebookUserProfile['id'];
//        $userModel->username = User::model()->createUsername($facebookUserProfile['email']);
//        $userModel->email = $facebookUserProfile['email'];
//        $userModel->first = isset($facebookUserProfile['first_name']) ? $facebookUserProfile['first_name'] : $facebookUserProfile['middle_name'];
//        $userModel->last = $facebookUserProfile['last_name'];
//        $userModel->enabled = 1;
//        $userModel->display_facebook_image = 0;
//        $userModel->birth_date = isset($facebookUserProfile['birthday']) ? date('Y-m-d', strtotime((string)$facebookUserProfile['birthday'])) : null;
//        $userModel->gender = $facebookUserProfile['gender'] == 'male' ? 2 : 1;
//        $userModel->login_type_fk = 2;
//        $userModel->platform_solution_fk = $platformApp;
//        $userModel->qrcode = User::generateQRcode();
//        $userModel->ip = $ip;
//        $userModel->timezone_mysql_fk = $timeZoneId;
//        return $userModel;
//    }


    /**
     * 16-09-2015, Valentin, Ask user for merging account all the time
     * It will check for transactions in account1->userId and account2->verifiedPhoneEmail
     * Handle timeone
     * Used on : Website(HandleFacebookUser), smartphone
     *
     * @version Aug - Sept 2015 V3.4.0
     * @version 3.2.0
     * @param int $userId
     * @param string $verifiedPhoneEmail
     * @access public
     * @return array
     */
//    public static function prepareMergeAccounts($userId, $verifiedPhoneEmail, $status, $platform, $timezoneId, $countryPhoneFk)
//    {
//        $platformName = PlatformSolution::model()->findByPk($platform);
//        $platformName = $platformName->name;
//
//        Yii::log('IP:' . Yii::app()->request->userHostAddress . ' account1=' . $userId . ' account2=' . $verifiedPhoneEmail, CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
//
//        $user1 = User::model()->findByPk($userId);
//
//        //$credential = User::phoneEmailUsernameValidator($verifiedPhoneEmail, false);
//        $credential = User::phoneEmailUsernameValidatorV330($verifiedPhoneEmail, $platformName);
//
//        if ($credential['isPhone']) {
//            $user2 = User::model()->findByAttributes(['phone' => $verifiedPhoneEmail]);
//        } elseif ($credential['isEmail']) {
//            $user2 = User::model()->findByAttributes(['email' => $verifiedPhoneEmail]);
//        }
//
//        if ($user2 == null) //email or phone doesnt exist
//        {
//            Yii::log('IP:' . Yii::app()->request->userHostAddress . ' ' . $verifiedPhoneEmail . ' does not exist. Will be added to user1=' . $userId, CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
//
//            return User::addEmailPhoneToUser($user1, $credential, $verifiedPhoneEmail, $status, $platform, $timezoneId, $countryPhoneFk);
//        } elseif ($user2->id != $user1->id) //email or phone exists
//        {
//            //checking the balance for user2 and user1
//            $userBalance2 = User::getUserBalance($user2->id);
//            $userBalance1 = User::getUserBalance($user1->id);
//
//            Yii::log('Ask user to delete one account UserId1=' . $user1->id . 'UserId2=' . $user2->id . ' Balance1=' . CJSON::encode($userBalance1) . ' Balance2=' . CJSON::encode($userBalance2), CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
//            $userBalance1['userId'] = $userId;
//            $userBalance2['userId'] = $user2->id;
//
//            return ['status' => 'OK', 'askUserMerge' => true, 'user1' => $userBalance1, 'user2' => $userBalance2];
//
//        } else {
//            Yii::log('Else UserId1=' . $user1->id . 'UserId2=' . $user2->id, CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
//            if ($credential['isPhone']) {
//                return ['status' => 'OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_PHONE_VERIFIED_MSG'), 'askUserMerge' => false];
//            } elseif ($credential['isEmail']) {
//                return ['status' => 'OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_EMAIL_VERIFIED_MSG'), 'askUserMerge' => false];
//            }
//
//        }
//    }

    /**
     * Add email or phone to a user that verified that email/phone
     * Handle timezone
     * Used in : Website
     *
     * @version May - June 2015 V3.3.0
     * @since 3.2.0
     * @param object $userModel
     * @param array $credential
     * @param string $verifiedPhoneEmail
     * @access public
     * @return array
     */
//    public static function addEmailPhoneToUser($userModel, $credential, $verifiedPhoneEmail, $status, $platform, $timezoneId, $countryPhoneFk)
//    {
//        $transaction = Yii::app()->db->beginTransaction();
//        try
//        {
//            if ($credential['isPhone']) {
//                $userModel->passwordreset = '';
//                $userModel->phone = $verifiedPhoneEmail; //save new added phone to user1
//                $userModel->country_phone_fk = $countryPhoneFk; //save country fk user1
//                $userModel->timezone_mysql_fk = $timezoneId; //save new added phone to user1
//                $userModel->save(false);
//
//                User::updateUserStatusPhoneEmailVerified($userModel->id, $verifiedPhoneEmail, $status, $platform, $timezoneId, $countryPhoneFk);
//                $transaction->commit();
//
//                return ['status' => 'OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_PHONE_VERIFIED_MSG'), 'askUserMerge' => false, 'userModel' => $userModel];
//            } elseif ($credential['isEmail']) {
//                $userModel->passwordreset = '';
//                $userModel->email = $verifiedPhoneEmail; //save new added email to user1
//                $userModel->save(false);
//
//                User::updateUserStatusPhoneEmailVerified($userModel->id, $verifiedPhoneEmail, $status, $platform, $timezoneId, $countryPhoneFk);
//                $transaction->commit();
//
//                return ['status' => 'OK', 'message' => Yii::t('landingpages', 'LANDINGPAGE_USERSIGNUP_EMAIL_VERIFIED_MSG'), 'askUserMerge' => false, 'userModel' => $userModel];
//            }
//        } //try
//         catch (CDbException $e) {
//            $transaction->rollback();
//            Yii::log('DB transaction rollback' . CJSON::encode($e), CLogger::LEVEL_ERROR, 'addEmailPhoneToUser');
//            return ['status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'errorDetails' => 'DB transaction rollback'];
//        } catch (Exception $e) {
//            Yii::log('DB transaction rollback' . CJSON::encode($e), CLogger::LEVEL_ERROR, 'addEmailPhoneToUser');
//            return ['status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'errorDetails' => 'DB transaction rollback'];
//        }
//    }

    /**
     * Updated the user status for phone/email verified
     * Handle timezone
     * Used in : Website()
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return void
     */
    public static function updateUserStatusPhoneEmailVerified($userId, $verifiedPhoneEmail, $status, $platform, $timezoneId, $countryPhoneFk)
    {
        if ($status == 31) //PHONE_VERIFIED
        {
            $lastStatus = UserStatusManager::lastStatusByUserIdCredentialStatus($userId, $verifiedPhoneEmail, [31]);
            if ($lastStatus == null) {
                UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 31, '', $verifiedPhoneEmail, $platform, $timezoneId, $countryPhoneFk);
            }
//PHONE_VERIFIED
        } elseif ($status == 33) //EMAIL_VERIFIED
        {
            $lastStatus = UserStatusManager::lastStatusByUserIdCredentialStatus($userId, $verifiedPhoneEmail, [33]);
            if ($lastStatus == null) {
                UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 33, '', $verifiedPhoneEmail, $platform, $timezoneId);
            }
//EMAIL_VERIFIED
        } elseif ($status == 30) //PHONE_NOT_VERIFIED
        {
            UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 31, '', $verifiedPhoneEmail, $platform, $timezoneId, $countryPhoneFk); //PHONE_VERIFIED
        } elseif ($status == 32) //EMAIL_NOT_VERIFIED
        {
            UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 33, '', $verifiedPhoneEmail, $platform, $timezoneId); //EMAIL_VERIFIED
        } elseif ($status == 39 || $status == 45) //ADD_PHONE_REQUEST, ADD_PHONE_REQUEST_RESEND
        {
            $lastStatus = UserStatusManager::lastStatusByUserIdCredentialStatus($userId, $verifiedPhoneEmail, [41]);
            if ($lastStatus == null) {
                UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 41, '', $verifiedPhoneEmail, $platform, $timezoneId, $countryPhoneFk);
            }
//ADD_PHONE_VERIFIED
        } elseif ($status == 40 || $status == 46) //ADD_EMAIL_REQUEST, ADD_EMAIL_REQUEST_RESEND
        {
            $lastStatus = UserStatusManager::lastStatusByUserIdCredentialStatus($userId, $verifiedPhoneEmail, [42]);
            if ($lastStatus == null) {
                UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 42, '', $verifiedPhoneEmail, $platform, $timezoneId);
            }
//ADD_EMAIL_VERIFIED
        } elseif ($status == 47 || $status == 53) //CHANGE_PHONE_REQUEST, CHANGE_PHONE_REQUEST_RESEND
        {
            UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 49, '', $verifiedPhoneEmail, $platform, $timezoneId, $countryPhoneFk); //CHANGED_PHONE_VERIFIED
        } elseif ($status == 48 || $status == 54) //CHANGE_EMAIL_REQUEST, CHANGE_EMAIL_REQUEST_RESEND
        {
            UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 50, '', $verifiedPhoneEmail, $platform, $timezoneId); //CHANGED_EMAIL_VERIFIED
        } elseif ($status == 56) //PHONE_NOT_VERIFIED_INVITED_BYSTORE
        {
            $lastStatus = UserStatusManager::lastStatusByUserIdCredentialStatus($userId, $verifiedPhoneEmail, [57]);
            if ($lastStatus == null) {
                UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 57, '', $verifiedPhoneEmail, $platform, $timezoneId, $countryPhoneFk);
            }
//PHONE_VERIFIED_INVITED_BYSTORE
        } elseif ($status == 58) //EMAIL_NOT_VERIFIED_INVITED_BYSTORE
        {
            $lastStatus = UserStatusManager::lastStatusByUserIdCredentialStatus($userId, $verifiedPhoneEmail, [59]);
            if ($lastStatus == null) {
                UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 59, '', $verifiedPhoneEmail, $platform, $timezoneId);
            }
//EMAIL_VERIFIED_INVITED_BYSTORE
        } elseif ($status == 64) //PHONE_NOT_VERIFIED_UNITS_TRANSFER
        {
            $lastStatus = UserStatusManager::lastStatusByUserIdCredentialStatus($userId, $verifiedPhoneEmail, [65]);
            if ($lastStatus == null) {
                UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 65, '', $verifiedPhoneEmail, $platform, $timezoneId, $countryPhoneFk);
            }
//PHONE_VERIFIED_UNITS_TRANSFER
        } elseif ($status == 66) //EMAIL_NOT_VERIFIED_UNITS_TRANSFER
        {
            $lastStatus = UserStatusManager::lastStatusByUserIdCredentialStatus($userId, $verifiedPhoneEmail, [67]);
            if ($lastStatus == null) {
                UserStatusManager::createUserStatusManagerRecordV330($userId, $modifiedBy = null, $status_manager_fk = 67, '', $verifiedPhoneEmail, $platform, $timezoneId);
            }
//EMAIL_VERIFIED_UNITS_TRANSFER
        }
    }

    /**
     * Getting user balance points and punches
     *
     * Used in : Website
     *
     * @version Jan -Feb 2015 V3.2.0
     * @param int $userId
     * @access public
     * @return array
     */
    public static function getUserBalance($userId)
    {
        $totalUserPoints = UserEntityPoints::getUserBusinessBalanceWeb($userId, $businessId = null, $entity_type_fk = 1);
        if ($totalUserPoints == null) {
            $totalUserPoints = 0;
        }

        $totalUserPunches = UserPunchItem::getUserPunchesBalance($userId);
        if ($totalUserPunches == null) {
            $totalUserPunches = 0;
        }

        return ['points' => $totalUserPoints, 'punches' => $totalUserPunches];
    }

    /**
     * Merge two accounts
     * Handle timezone
     * Used in : Website()
     *
     * @version May - June 2015 V3.3.0
     * @since 3.2.0
     * @param object $userMerged
     * @param object $userMergedWith
     * @access public
     * @return array
     */
    public static function mergeAccounts($userMerged, $userMergedWith, $verifiedPhoneEmail, $status, $platform, $timezoneId, $countryPhoneFk)
    {
        $platformName = PlatformSolution::model()->findByPk($platform);
        $platformName = $platformName->name;

        Yii::log('Merged UserId=' . $userMerged->id . ' Will be disabled=' . $userMergedWith->id, CLogger::LEVEL_INFO, 'mergeAccounts_' . $platformName);

        $transaction = Yii::app()->db->beginTransaction();
        try
        {
            if ($userMerged->phone == null || $userMerged->phone == '') //if the user doesn't have an phone
            {
                if (($userMergedWith->phone != null && $userMergedWith->phone != '')) //if the second user has phone
                {
                    UserStatusManager::createUserStatusManagerRecordV330($userMerged->id, $modifiedBy = null, $satus = 41, '', $userMergedWith->phone, $platform, $timezoneId, $countryPhoneFk);
                }

            }

            if ($userMerged->email == null || $userMerged->email == '') //if the user doesn't have an email
            {
                if (($userMergedWith->email != null && $userMergedWith->email != '')) //if the second user has email
                {
                    UserStatusManager::createUserStatusManagerRecordV330($userMerged->id, $modifiedBy = null, $satus = 42, '', $userMergedWith->email, $platform, $timezoneId);
                }

            }

            if (strpos((string)$verifiedPhoneEmail, "@") == null) //phone
            {
                $userMerged->phone = $verifiedPhoneEmail;
                $userMerged->email = ($userMergedWith->email == null || $userMergedWith->email == '') ? $userMerged->email : $userMergedWith->email;
                $userMerged->country_phone_fk = $countryPhoneFk;

                $userMergedWith->phone = null;
                $userMergedWith->email = null;
                $userMergedWith->country_phone_fk = null;
            } else {
                $userMerged->email = $verifiedPhoneEmail;
                $userMerged->phone = ($userMergedWith->phone == null || $userMergedWith->phone == '') ? $userMerged->phone : $userMergedWith->phone;
                $userMerged->country_phone_fk = ($userMergedWith->phone == null || $userMergedWith->phone == '') ? $userMerged->country_phone_fk : $userMergedWith->country_phone_fk;

                $userMergedWith->email = null;
                $userMergedWith->phone = null;
                $userMergedWith->country_phone_fk = null;
            }

            $userMerged->passwordreset = '';
            $userMerged->first = ($userMerged->first == null || $userMerged->first == '') ? $userMergedWith->first : $userMerged->first;
            $userMerged->last = ($userMerged->last == null || $userMerged->last == '') ? $userMergedWith->last : $userMerged->last;
            $userMerged->facebook_id = ($userMerged->facebook_id == null || $userMerged->facebook_id == '') ? $userMergedWith->facebook_id : $userMerged->facebook_id;
            $userMerged->accounts_merge = 1;
            $userMerged->birth_date = ($userMerged->birth_date == null || $userMerged->birth_date == '') ? $userMergedWith->birth_date : $userMerged->birth_date;
            $userMerged->gender = ($userMerged->gender == null || $userMerged->gender == '') ? $userMergedWith->gender : $userMerged->gender;
            $userMerged->timezone_mysql_fk = $timezoneId;
            $userMerged->save(false);

            //disable user2 , take email/phone and add to user1
            $userMergedWith->passwordreset = '';
            $userMergedWith->enabled = 0;
            $userMergedWith->facebook_id = null;
            $userMergedWith->timezone_mysql_fk = $timezoneId;
            $userMergedWith->save(false);

            UserToken::killTokenByUserIdV330($userMergedWith->id, $timezoneId); //disable all user tokens

            $lastStatusMgrKeepUser = UserStatusManager::lastStatusByUserIdCredentialStatus($userMerged->id, $verifiedPhoneEmail, [30, 31, 32, 33, 39, 40, 45, 46, 47, 48, 53, 54, 56, 58, 64, 66]);
            if ($lastStatusMgrKeepUser != null) {
                $status = $lastStatusMgrKeepUser[0]['status_manager_fk'];
                User::updateUserStatusPhoneEmailVerified($userMerged->id, $verifiedPhoneEmail, $status, $platform, $timezoneId, $countryPhoneFk); //add the status for the active user
            } else {
                $transaction->rollback();
                Yii::log('DB transaction rollback Keep usr status null KeepUID=' . $userMerged->id . ' DeleteUID=' . $userMergedWith->id . ' verifiedPhoneEmail=' . $verifiedPhoneEmail, CLogger::LEVEL_ERROR, $platformName . '_mergeAccounts');
                return ['status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'errorDetails' => 'DB transaction rollback'];
            }

            UserStatusManager::createUserStatusManagerRecordV330($userMergedWith->id, $modifiedBy = null, $status_manager_fk = 34, '', '', $platform, $timezoneId); //NOT_ACTIVE_USER_MERGING_REQUEST

            //print_r($lastStatusMgrKeepUser); die;
            $transaction->commit();

            return ['status' => 'OK', 'userMerged' => $userMerged, 'message' => Yii::t('frontend', 'FRONTEND_USERREQUEST_MERGE_ACCOUNTS_SUCCESS')];
        } //try
         catch (CDbException $e) {
            $transaction->rollback();
            Yii::log('DB transaction rollback' . CJSON::encode($e), CLogger::LEVEL_ERROR, $platformName . '_mergeAccounts');
            return ['status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'errorDetails' => 'DB transaction rollback'];
        }
    }

    /**
     * Getting email value and status to display on user dropdown menu
     *
     * Used in : Website()
     *
     * @version Jan - Feb 201 V3.2.0
     * @access public
     * @return string
     */
    public static function getEmailValueStatusHtml($userId)
    {
        $userModel = User::model()->findByPk($userId);

        $emailValue = '';
        $emailStatus = null;

        $currentStatus = UserStatusManager::checkLastEmailStatusForUser($userId);
        //Yii::log('userId='.$userId.' status='.CJSON::encode($currentStatus), CLogger::LEVEL_INFO, 'getEmailValueStatusHtml');

        if ($currentStatus) {
            $emailStatus = $currentStatus['status_manager_fk'];
        }

        $emailValue = ($userModel->email == null || $userModel->email == '') ? '' : '<span class="dropdown-email-ellipsis" title="' . $userModel->email . '">' . $userModel->email . '</span>' . '<br>';

        if (in_array($emailStatus, [40, 46, 48, 54])) {
            $emailString = $emailValue . '<span class="dropdown-change">' . Yii::t('frontend', 'FRONTEND_USERDROPDOWN_MENU_EMAIL_PENDING') . '</span>';
        } elseif ($userModel->email == null || $userModel->email == '') {
            $emailString = '<span class="dropdown-change">' . Yii::t('frontend', 'FRONTEND_USERDROPDOWN_MENU_ADD_EMAIL') . '</span>';
        } else {
            $emailString = $emailValue; // . '<span class="dropdown-change">'. Yii::t('frontend','FRONTEND_GENERIC_CHANGE').'</span>';
        }

        return $emailString;
    }

    /**
     * Getting phone value and status to display on user dropdown menu
     *
     * Used in : Website()
     *
     * @version Jan - Feb 201 V3.2.0
     * @access public
     * @return string
     */
    public static function getPhoneValueStatusHtml($userId)
    {
        $userModel = User::model()->findByPk($userId);

        $phoneValue = '';
        $phoneStatus = null;

        $currentStatus = UserStatusManager::checkLastPhoneStatusForUser($userId);
        //Yii::log('userId='.$userId.' status='.CJSON::encode($currentStatus), CLogger::LEVEL_INFO, 'getPhoneValueStatusHtml');

        if ($currentStatus) {
            $phoneStatus = $currentStatus['status_manager_fk'];
        }

        $phoneValue = ($userModel->phone == null || $userModel->phone == '') ? '' : '<span class="dropdown-email-ellipsis">' . Utils::getFormattedPhoneNumber($userModel->phone, $userModel->country_phone_fk) . '</span>' . '<br>';

        if (in_array($phoneStatus, [39, 45, 47, 53])) {
            $phoneString = $phoneValue . Yii::t('frontend', 'FRONTEND_USERDROPDOWN_MENU_PHONE_PENDING') . '</span>'; //FRONTEND_GENERIC_PENDING
        } elseif ($userModel->phone == null || $userModel->phone == '') {
            $phoneString = '<span class="dropdown-change">' . Yii::t('frontend', 'FRONTEND_USERDROPDOWN_MENU_ADD_PHONE') . '</span>';
        } else {
            $phoneString = $phoneValue; //. '<span class="dropdown-change">'. Yii::t('frontend','FRONTEND_GENERIC_CHANGE').'</span>';
        }
        return $phoneString;
    }

    /**
     * Valentin - May 20, 2015 - Copied in Utils, to be removed after the new function will be used everywhere
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * Short url with google
     *
     * @version Jan - Feb 201 V3.2.0
     * @param string $longUrl
     * @return array
     */
    public static function shortUrlGoogle($longUrl)
    {
        // Get API key from : http://code.google.com/apis/console/
        $apiKey = Yii::app()->params['googleShortenerKey'];

        $postData = array('longUrl' => $longUrl);
        $jsonData = json_encode($postData);

        $curlObj = curl_init();

        curl_setopt($curlObj, CURLOPT_URL, 'https://www.googleapis.com/urlshortener/v1/url?key=' . $apiKey);
        curl_setopt($curlObj, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curlObj, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curlObj, CURLOPT_HEADER, 0);
        curl_setopt($curlObj, CURLOPT_HTTPHEADER, array('Content-type:application/json'));
        curl_setopt($curlObj, CURLOPT_POST, 1);
        curl_setopt($curlObj, CURLOPT_POSTFIELDS, $jsonData);

        $response = curl_exec($curlObj);

        if (curl_errno($curlObj)) {
            Yii::log('CURL ERROR=' . CJSON::encode(curl_error($curlObj)), CLogger::LEVEL_ERROR, 'shortUrlGoogle');
            return ['status' => 'NOT_OK', 'shortUrl' => '', 'message' => 'Url was not shortened'];
        }

        // Change the response json string to object
        $json = json_decode($response);

        curl_close($curlObj);

        if (isset($json->id)) {
            //Yii::log('Google Response='.CJSON::encode($json), CLogger::LEVEL_INFO, 'shortUrlGoogle');
            return ['status' => 'OK', 'shortUrl' => $json->id];
        } else {
            Yii::log('Google Response=' . CJSON::encode($json), CLogger::LEVEL_ERROR, 'shortUrlGoogle');
            return ['status' => 'NOT_OK', 'shortUrl' => '', 'message' => 'Url was not shortened'];
        }
    }

    /**
     * Valentin - May 20, 2015 - Copied in Utils, to be removed after the new function will be used everywhere
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     * Short url with google
     *
     * @version Mar - Apr 2015 V3.2.5
     * <AUTHOR>
     * @param string $longUrl
     * @return array
     */
    public static function shortUrlGoogleV325($longUrl)
    {
        // Get API key from : http://code.google.com/apis/console/
        $apiKey = Yii::app()->params['googleShortenerKey'];

        $postData = array('longUrl' => $longUrl);
        $jsonData = json_encode($postData);

        $curlObj = curl_init();

        curl_setopt($curlObj, CURLOPT_URL, 'https://www.googleapis.com/urlshortener/v1/url?key=' . $apiKey);
        curl_setopt($curlObj, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curlObj, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curlObj, CURLOPT_HEADER, 0);
        curl_setopt($curlObj, CURLOPT_HTTPHEADER, array('Content-type:application/json'));
        curl_setopt($curlObj, CURLOPT_POST, 1);
        curl_setopt($curlObj, CURLOPT_POSTFIELDS, $jsonData);

        $response = curl_exec($curlObj);

        if (curl_errno($curlObj)) {
            Yii::log('CURL ERROR=' . CJSON::encode(curl_error($curlObj)), CLogger::LEVEL_ERROR, 'shortUrlGoogle');
            return ['status' => 'NOT_OK', 'shortUrl' => '', 'message' => 'Url was not shortened'];
        }

        // Change the response json string to object
        $json = json_decode($response);

        curl_close($curlObj);

        if (isset($json->id)) {
            //Yii::log('Google Response='.CJSON::encode($json), CLogger::LEVEL_INFO, 'shortUrlGoogle');
            return ['status' => 'OK', 'shortUrl' => $json->id];
        } else {
            Yii::log('Google Response=' . CJSON::encode($json), CLogger::LEVEL_ERROR, 'shortUrlGoogle');
            return ['status' => 'NOT_OK', 'shortUrl' => '', 'message' => 'Url was not shortened'];
        }
    }

    /**
     * Generate a random and unique QRcode for the user
     * Used in the Migration process and in the User's SIgn Up
     *
     * Used in    Website, Smartphone, Merchant tablet app
     * <AUTHOR>
     * @version    Mar - Apr 2015 V3.2.5
     * @return    string QRcode
     */
    public static function generateQRcode()
    {
        $qrcode = str_pad((string)random_int(100000, 999999999), 9, '0', STR_PAD_LEFT);
        $user = User::model()->findByAttributes(array('qrcode' => $qrcode));
        if ($user) {
            return User::generateQRcode();
        }

        return $qrcode;
    }

    /**
     * This is used in the QR migration process (SysAdmin >actionAddQRcodeToExistingUsers())
     * Update a random and unique QRcode for the user;
     *
     * Used in    Website, Smartphone, Merchant tablet app
     * <AUTHOR>
     * @version    Mar - Apr 2015 V3.2.5
     * @return    string QRcode
     */
    public static function updateQRCodesForExistingUsers()
    {
        $results = Yii::app()->db->createCommand()
            ->select(
                'u.id'
            )
            ->from('user u')
            ->join('user_permission up', 'u.id=up.userid')
            ->where("up.itemname='user'")
            ->order('u.id', 'ASC')
            ->queryAll();
        foreach ($results as $result) {
            $sqlUpdate = 'update `user` set `qrcode` = ? where id = ?';
            $cmd = Yii::app()->db->createCommand($sqlUpdate);
            $cmd->execute(array(User::generateQRcode(), $result['id']));
        }
    }

    /**
     * Preparing for signing up an Invited Merchant user
     * Feature: Business Contacts list
     * Used in : Website - SysAdmin (CronJob)
     * <AUTHOR> C.
     * @version : Mar - Apr 2015 release V3.2.5
     * @param string $input - email or phone
     * @return array
     */
    public static function merchantContactUserSignup($input, $phoneEmail, $joinModel)
    {
        $platformName = PlatformSolution::model()->findByPk($joinModel->platform_solution_fk);

        // Check if the user is signing up with his phone number
        if ($phoneEmail['isPhone']) {
            $result = User::checkPhoneNumberExistence($input);
            Yii::log($platformName->name . ' checkPhoneNumberExistence result=' . $result, CLogger::LEVEL_INFO, 'Signup: userSignup');
        } elseif ($phoneEmail['isEmail']) {
            $result = User::checkEmailExistence($input);
            Yii::log($platformName->name . ' checkEmailExistence result=' . $result, CLogger::LEVEL_INFO, 'Signup: userSignup');
        }

        // Check if the user already exists the result will return the user id otherwise it will return false;
        if ($result != false) {
            return array('status' => 'NOT_OK', 'result' => $result);
        } else {
            // Insert user
            $insertUserResult = User::merchantContactUserSignUpTrx($joinModel, $phoneEmail, $platformName->name);

            // Return the result
            return $insertUserResult;
        }
    }

    /**
     * Transaction for signing up an Invited Merchant user
     * Feature: Business Contacts list
     * Used in : Website - SysAdmin (CronJob)
     * <AUTHOR> C.
     * @version : Mar - Apr 2015 release V3.2.5
     * @since 3.2.5
     * @param object $joinModel
     * @param array $phoneEmail
     * @param string $platformName
     * @param boolean isFacebook
     * @access public
     * @return array
     */
    public static function merchantContactUserSignUpTrx($joinModel, $phoneEmail, $platformName)
    {
        if ($joinModel->email == null && $joinModel->phone == null) {
            Yii::log($platformName . ' No email, no phone User: ' . CJSON::encode($joinModel), CLogger::LEVEL_ERROR, 'merchantContactUserSignUpTrx');
            return array('status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'errorDetails' => 'No email and no phone');
        }

        if ($joinModel->save(false)) {
            //Assign the User Role for this User
            $userId = $joinModel->getPrimaryKey();

            $permission = 'user';
            $entity_fk = $userId;
            $entity_type_fk = 4; //user
            UserEntityPermission::assignEntityPermission($userId, $permission, $entity_fk, $entity_type_fk);

            //Insert a new record in the UserInterest table
            $newUserInterest = new UserInterest();
            $newUserInterest->current_cem_flag = 2; //2 not answered yet
            $newUserInterest->user_fk = $userId;
            $newUserInterest->save(false);
            $userInterestId = $newUserInterest->primaryKey;

            $casl = new CaslHistory();
            $casl->cem_flag = 2; //2 not answered yet
            $casl->user_interest_fk = $userInterestId;
            $casl->save(false);

            //sending SMS or Email
            Yii::log($platformName . ' INSIDE merchantContactUserSignUpTrx() - BEFORE sendSignupVerificationEmailSMS() ', CLogger::LEVEL_INFO, 'merchantContactUserSignUpTrx');

            $sentEmailSMS = User::sendUniqueSignupVerifEmailSMS($joinModel, $phoneEmail, $platformName, false);
        } else {
            //error in the save
            Yii::log($platformName . ' Signup: User Model not saved. Errors=' . $joinModel->getErrors(), CLogger::LEVEL_ERROR, 'merchantContactUserSignUpTrx');
            return array('status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'errorDetails' => 'User model could not be saved');
        }

        if (isset($sentEmailSMS) && $sentEmailSMS['status'] == 'NOT_OK') {
            //sms or email failed
            if ($phoneEmail['isPhone']) {
                $userStatus = UserStatusManager::createUserStatusManagerRecordV320($joinModel->id, $modifiedBy = null, $status_manager_fk = 37, $oldPhone = '', $joinModel->phone, $joinModel->platform_solution_fk);
            }
            //SMS Failed
            elseif ($phoneEmail['isEmail']) {
                $userStatus = UserStatusManager::createUserStatusManagerRecordV320($joinModel->id, $modifiedBy = null, $status_manager_fk = 38, $oldEmail = '', $joinModel->email, $joinModel->platform_solution_fk);
            }
            //Email failed

            Yii::log($platformName . ' Email/SMS was not sent ' . CJSON::encode($sentEmailSMS), CLogger::LEVEL_ERROR, 'merchantContactUserSignUpTrx');

            // Making sure the Sign Up process is not stopped because the Sent SMS/Email failed
            $sentEmailSMS['status'] = 'OK';
            $sentEmailSMS['result'] = $userId;
        }
        return $sentEmailSMS;
    }

    /**
     * Legacy version - Deprecated // Lei add this comment
     * New UNIQUE function
     * For now implemented for the Feature: Business Contacts list(Signing up an Invited Merchant user)
     * IMPORTANT: This function has to be used for ALL platforms
     * Used in : Website - SysAdmin(cronJob)
     * <AUTHOR> C.
     * @version : Mar - Apr 2015 release V3.2.5
     * @since 3.2.5
     * @param object $userModel, $isRegularSignUp (false if this call from "Business Contacts list - Cron Job")
     * @access public
     * @return array
     */
    public static function sendUniqueSignupVerifEmailSMS($userModel, $phoneEmail, $platformName, $isRegularSignUp = true)
    {
        $randomToken = User::model()->generateRandomString(60);
        $randomPin = User::generatePin();

        //saving token and expiry date in user model
        $userModel->passwordreset = $randomToken;
        $userModel->enabled = 1;
        $userModel->password = '';
        $userModel->pin_permission = User::cryptPassword($randomPin);
        $userModel->pin_enabled = 1;
        $userModel->link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
        $userModel->update();

        UserStatusManager::createUserStatusManagerRecordV320($userModel->id, $modifiedBy = null, $statusManagerFk = 35, '', '', $userModel->platform_solution_fk); //PIN_CODE_AUTO_GENERATED

        if ($phoneEmail['isPhone']) {

            //$smsArray['linkToShort'] = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p'=>$randomPin, 'c'=>1, 'e'=>0]);//c-cell phone , e- email
            $smsArray['linkToShort'] = Yii::app()->params['serverUrl'] . '/site/verifyAccount?q=' . $randomToken . '&p=' . $randomPin . '&c=1&e=0'; //It is been call ALSO from CRON JOB
            $smsArray['sendTo'] = $userModel->phone;

            if ($isRegularSignUp) {
                //Regular Sign Up
                $smsArray['smsMessage'] = Yii::t('frontend', 'FRONTEND_USERSIGNUP_VERIFY_SMS_FR_1') . Yii::t('frontend', 'FRONTEND_USERSIGNUP_VERIFY_SMS_EN_1') . $randomPin . ' ' . Yii::t('frontend', 'FRONTEND_USERSIGNUP_VERIFY_SMS_FR_2') . Yii::t('frontend', 'FRONTEND_USERSIGNUP_VERIFY_SMS_EN_2');
                $statusManagerFk = 30;
            } else {
                //For now this is been used for Cron Job - Business Contacts list

                //Getting the BusinessDefault name
                $businessId = Business::getBusinessIdByBranchId($userModel->signedupat_fk);
                $businessName = Business::getBusinessName($businessId);

                $smsArray['smsMessage'] = Yii::t('frontend', 'FRONTEND_BUSINESSCONTACTS_USERSIGNUP_VERIFY_SMS_FR_1', array('{business_name}' => $businessName), null, 'en') . ' ' . Yii::t('frontend', 'FRONTEND_BUSINESSCONTACTS_USERSIGNUP_VERIFY_SMS_EN_1', array(), null, 'en') . ' ' . Yii::t('frontend', 'FRONTEND_BUSINESSCONTACTS_USERSIGNUP_VERIFY_SMS_EN_2', array(), null, 'en');
                $statusManagerFk = 56;
            }

            //inserting in user Status Manager
            Yii::log($platformName . ' - INSIDE $sendUniqueSignupVerifEmailSMS() ', CLogger::LEVEL_INFO, 'BEFORE inserting in user Status Manager ');
            $userStatus = UserStatusManager::createUserStatusManagerRecordV320($userModel->id, $modifiedBy = null, $statusManagerFk, $oldPhoneEmail = '', $userModel->phone, $userModel->platform_solution_fk); //PHONE_NOT_VERIFIED

            $resultSMSSent['result'] = $userModel->id;

            //May 2015 - This call does not work from a CRON Job
            //$resultSMSSent = Utils::validateSendSMS($smsArray, true);
            //Forcing the return OK without sending the SMS
            $resultSMSSent['status'] = 'OK';
            $resultSMSSent['params'] = $smsArray;

            return $resultSMSSent;

        } elseif ($phoneEmail['isEmail']) {

            //$emailLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p'=>$randomPin, 'c'=>0, 'e'=>1]);//c-cell phone , e- email
            $emailLink = Yii::app()->params['serverUrl'] . '/site/verifyAccount?q=' . $randomToken . '&p=' . $randomPin . '&c=0&e=1'; //It is been call ALSO from CRON JOB

            if ($isRegularSignUp) {
                //Regular Sign Up
                $emailView = 'userWelcomeWithPin';
                $emailSubject = Yii::t('frontend', 'FRONTEND_USERSIGNUP_MAIL_SUBJECT', array(), null, 'en');
                $emailData = [
                    'username' => $userModel->username,
                    'pincode' => $randomPin,
                    'link' => $emailLink,
                ];

                $statusManagerFk = 32;
            } else {
                //For now this is been used for Cron Job - Business Contacts list

                //Getting the BusinessDefault name
                $businessId = Business::getBusinessIdByBranchId($userModel->signedupat_fk);
                $businessName = Business::getBusinessName($businessId);

                $defaultBranch = BusinessBranch::getDefaultBranch($businessId);
                $businessLogo = $defaultBranch['logo_image'];

                $emailView = 'merchantContactInvitation';
                $emailSubject = Yii::t('frontend', 'FRONTEND_BUSINESSCONTACTS_USERSIGNUP_MAIL_SUBJECT', array('{business_name}' => $businessName), null, 'en');

                $emailData = [
                    'username' => $userModel->username,
                    'pincode' => $randomPin,
                    'link' => $emailLink,
                    'businessLogo' => $businessLogo,
                    'businessName' => $businessName,
                ];

                $statusManagerFk = 58;
            }

            //Creating user status for the user
            $userStatus = UserStatusManager::createUserStatusManagerRecordV320($userModel->id, $modifiedBy = null, $statusManagerFk, $oldPhoneEmail = '', $userModel->email, $userModel->platform_solution_fk); //EMAIL_NOT_VERIFIED

            //Sending the email
            $resultEmailSent = Utils::sendEmailWithTemplate($emailView, 'bilingual', $emailSubject, $userModel->email, $emailData,
                null, null, null, $userModel->business_fk);
            $resultEmailSent['result'] = $userModel->id;

            return $resultEmailSent;

        } else {
            return array('status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'error' => 'Not valid email/phone');
        }

    }

    /**
     * @param $qrcode
     * @return bool
     */
    public static function checkQrCodeExistenceV500($qrcode, $businessFk)
    {
        $result = User::model()->findByAttributes(array('qrcode' => $qrcode, "business_fk" => $businessFk));
        if ($result == null) {
            return false;
        } else {
            return $result->id;
        }
    }

    public static function checkAccountNumberExistence($accountNumber, $businessId)
    {
        $userBusinessProfile = UserBusinessProfile::model()->findByAttributes([
            'account_number' => trim((string)$accountNumber ?? ''),
            'business_fk' => $businessId
        ]);
        if ($userBusinessProfile == null) {
            return false;
        } else {
            return $userBusinessProfile->user_fk;
        }
    }

    /**
     * @param $qrcode
     * @return bool
     */
    public static function checkQrCodeExistence($qrcode)
    {
        $result = User::model()->findByAttributes(array('qrcode' => $qrcode));
        if ($result == null) {
            return false;
        } else {
            return $result->id;
        }
    }

    public static function getVerifyLinkForChangeEmail($userModel, $businessId, $email)
    {
        $randomToken = User::model()->generateRandomString(60);
        $paramsActivationToken = [
            'id' => $randomToken,
            'user_fk' => $userModel->id,
            'utc_created' => date("Y-m-d H:i:s"),
            'email' => $userModel->email,
            'phone' => $userModel->phone,
            'intent' => 'change_email',
            'utc_link_expires' => date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES))
        ];

        $paramsActivationToken['intent'] = 'change_email';
        $paramsActivationToken['email'] = $email;

        $businessProfile = BusinessProfile::model()->findbyAttributes(['business_fk' => $businessId]);

        if ($businessProfile && $businessProfile->web_app_uri) {
            $emailLink = $businessProfile->web_app_uri . '/site/verify' . '?' . http_build_query(['token' => $randomToken, 'email' => $email, 'action' => 'change',]);
        } else {
            $emailLink = Yii::app()->params['membersUrl'] . '/site/verify' . '?' . http_build_query(['token' => $randomToken, 'email' => $email, 'action' => 'change',]);
        }
        ActivationToken::create($paramsActivationToken);
        return $emailLink;
    }

    public static function getVerifyLinkForChangePhone($userModel, $businessId, $phone)
    {
        $randomToken = User::model()->generateRandomString(60);
        $paramsActivationToken = [
            'id' => $randomToken,
            'user_fk' => $userModel->id,
            'utc_created' => date("Y-m-d H:i:s"),
            'email' => $userModel->email,
            'phone' => $userModel->phone,
            'intent' => 'change_phone',
            'utc_link_expires' => date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES))
        ];

        $paramsActivationToken['intent'] = 'change_phone';
        $paramsActivationToken['phone'] = $phone;


        $businessProfile = BusinessProfile::model()->findbyAttributes(['business_fk' => $businessId]);

        if ($businessProfile && $businessProfile->web_app_uri) {
            $smsLink = $businessProfile->web_app_uri . '/site/verify' . '?' . http_build_query(['token' => $randomToken, 'phone' => $phone, 'action' => 'change',]);
        } else {
            $smsLink = Yii::app()->params['membersUrl'] . '/site/verify' . '?' . http_build_query(['token' => $randomToken, 'phone' => $phone, 'action' => 'change',]);
        }
        ActivationToken::create($paramsActivationToken);
        return $smsLink;
    }

    public static function findUserByAttributes($attributes, $businessFk)
    {
        $attributes['enabled'] = 1;
        if (Utils::feature('UBP')) {
            $attributes['business_fk'] = $businessFk;
        }
        return self::model()->findByAttributes($attributes);
    }

    public static function findBusinessUserByAttributes($attributes)
    {
//        $attributes['enabled'] = 1;
        if (Utils::feature('UBP')) {
            return self::model()->findByAttributes($attributes, [
                'condition' => 'business_fk IS NULL',
            ]);
        } else {
            return self::model()->findByAttributes($attributes);
        }
    }

    /**
     * Create pin code for new user
     * @auther Lei
     * @return array
     */
    public static function generatePinCodeForNewUser($email, $phone, $currentUserId)
    {
        $data['pin_code'] = User::generatePin();
        $data['pin_permission'] = User::cryptPassword($data['pin_code']);
        if (Utils::feature('UBP')) {
            $email = empty($email) ? null : $email;
            $phone = empty($phone) ? null : $phone;
            $userPrevious = null;
            if ($email && $phone) {
                $userPrevious = User::model()->findByAttributes(['enabled' => 1],
                    [
                        'condition' => 'business_fk IS NOT NULL AND (email=:email or phone=:phone)',
                        'params' => [
                            ':email' => $email,
                            ':phone' => $phone
                        ]
                    ]);
            } elseif ($email) {
                $userPrevious = User::model()->findByAttributes(['enabled' => 1],
                    [
                        'condition' => 'business_fk IS NOT NULL AND email=:email',
                        'params' => [
                            ':email' => $email
                        ]
                    ]);
            } elseif ($phone) {
                $userPrevious = User::model()->findByAttributes(['enabled' => 1],
                    [
                        'condition' => 'business_fk IS NOT NULL AND phone=:phone',
                        'params' => [
                            ':phone' => $phone
                        ]
                    ]);
            }
            if (isset($userPrevious, $userPrevious->pin_permission) && ($userPrevious->id != $currentUserId)) {
                $data['pin_code'] = null;
                $data['pin_permission'] = $userPrevious->pin_permission;
            }
        }
        return $data;
    }

    public static function getCustomerByLanguages($businessId, $languageIds)
    {
        return Yii::app()->db->createCommand()
            ->select(array(
                'u.id',
                'u.email'
            ))
            ->from('user u')
            ->where('u.enabled=1 AND u.business_fk=:businessId', array('businessId' => $businessId))
            ->andwhere(array('in', 'u.language', $languageIds))
            ->queryAll();
    }

    /**
     * Find user by email or phone number
     *
     * <AUTHOR>
     * @since 03/03/2022
     * @param integer $businessId
     * @param string $email
     * @param string $phone
     * @return array|mixed|User|null
     */
    public static function getUserByEmailOrPhone($businessId, $email, $phone)
    {
        return User::model()->findByAttributes(['enabled' => 1],
            [
                'condition' => 'business_fk=:businessId AND (email=:email or phone=:phone)',
                'params' => [
                    ':email' => $email,
                    ':phone' => $phone,
                    ':businessId' => $businessId
                ]
            ]);
    }

    /**
     * Return the number of employees for business portal by excluding the pos employees
     *
     * <AUTHOR>
     * @since 30/05/2022
     * @access public
     * @param int $businessId
     * @return int|mixed
     */
    public static function getBusinessEmployeesNumberWithoutPosRelation($businessId, $posBranchesIds)
    {
        $result = Yii::app()->db->createCommand()
            ->select('count(1)')
            ->from('user_entity_permission uep')
            ->join('user u', 'u.id=uep.user_fk')
            ->where('(u.enabled=1 AND uep.entity_type_fk=1) and uep.entity_fk=:businessId', array(':businessId' => $businessId));
            if (!empty($posBranchesIds)) {
                $result->andwhere('u.id NOT IN (select user_clerk_fk from pos_employee_clerk where pos_employee_clerk.enabled = 1 AND pos_employee_clerk.pos_branch_fk IN ('.$posBranchesIds.') )');
            }

        return $result->queryScalar();
    }


    /****************************************************************************************************/
/*********************************** ALL PUBLIC Functions END HERE **********************************/
/****************************************************************************************************/

/***************************************************************************************************/
/********************************* ALL PRIVATE Functions START HERE ********************************/
/***************************************************************************************************/

    /**
     * Getting recipient name or first name of username
     *
     * Used in : Website()
     *
     * @version Oct - Nov 2015 release V3.4.1
     * <AUTHOR>
     * @access private
     * @return string
     */
    public static function getRecipientName($isNewUser, $recipientName, $userModel)
    {
        if (!$isNewUser) {
            if ($recipientName != '' && $recipientName != null) //check for first name
            {
                return ucfirst((string)$recipientName);
            } elseif ($userModel->first != '' && $userModel->first != null) {
                return ucfirst((string)$userModel->first);
            }

        } else {
            return $recipientName; //returns without any changes empty or what has been entered in input
        }
    }

    /*
     * PATRICIA - APRIL 26TH 2015 - @TODO MOVE TO Utils class
     */
    private static function generateLoginWithFacebookPassword($fbUserId = null)
    {
        $alphabet = User::model()->alphabetLetters();
        $specialCharachters = User::model()->specialCharachter();
        $alphabetLettersCapital = User::model()->alphabetLettersCapital();
        $tmpPassword = $fbUserId;

        $x = ''; $passLength = strlen((string)$tmpPassword??'');
        for ($i = 0; $i < $passLength; $i++) {
            if (is_numeric($tmpPassword[$i])) {
                if ($i % 2) {
                    $x .= $specialCharachters[$tmpPassword[$i]];
                } else
                if ($i % 3) {
                    $x .= $alphabet[$tmpPassword[$i]];
                } else
                if ($i % 5) {
                    $x .= $alphabetLettersCapital[$tmpPassword[$i]];
                } else {
                    $x .= $tmpPassword[$i];
                }
            } else {
                $x .= $tmpPassword[$i];
            }
        }
        return $x;
    }

    /*
     * PATRICIA - APRIL 26TH 2015 - @TODO MOVE TO Utils class
     */
    private function alphabetLetters()
    {
        return array(
            '0' => 'a',
            '1' => 'b',
            '2' => 'c',
            '3' => 'd',
            '4' => 'e',
            '5' => 'f',
            '6' => 'g',
            '7' => 'h',
            '8' => 'i',
            '9' => 'j',
            '10' => 'k',
            '11' => 'l',
            '12' => 'm',
            '13' => 'n',
            '14' => 'o',
            '15' => 'p',
            '16' => 'q',
            '17' => 'r',
            '18' => 's',
            '19' => 't',
            '20' => 'u',
            '21' => 'v',
            '22' => 'w',
            '23' => 'x',
            '24' => 'y',
            '25' => 'z',
        );
    }

    /*
     * PATRICIA - APRIL 26TH 2015 - TODO MOVE TO Utils class
     */
    private function alphabetLettersCapital()
    {
        return array(
            '0' => 'A',
            '1' => 'B',
            '2' => 'C',
            '3' => 'D',
            '4' => 'E',
            '5' => 'F',
            '6' => 'G',
            '7' => 'H',
            '8' => 'I',
            '9' => 'J',
            '10' => 'K',
            '11' => 'L',
            '12' => 'M',
            '13' => 'N',
            '14' => 'O',
            '15' => 'P',
            '16' => 'Q',
            '17' => 'R',
            '18' => 'S',
            '19' => 'T',
            '20' => 'U',
            '21' => 'V',
            '22' => 'W',
            '23' => 'X',
            '24' => 'Y',
            '25' => 'Z',
        );
    }

    /*
     * PATRICIA - APRIL 26TH 2015 - @TODO MOVE TO Utils class
     */
    private function specialCharachter()
    {
        return array(
            '1' => '!',
            '2' => '@',
            '3' => '#',
            '4' => '$',
            '5' => '%',
            '6' => '^',
            '7' => '&',
            '8' => '*',
            '9' => '(',
            '0' => ')',
        );
    }









/***************************************************************************************************/
/********************************* ALL PRIVATE Functions END HERE **********************************/
/***************************************************************************************************/

}
