<?php

namespace KangarooRewards\Workflows\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class WorkflowConditionCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return (new WorkflowConditionResource($item))->response()->getData()->data;
            }),
        ];
    }
}