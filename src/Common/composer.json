{"name": "kangaroorewards/common", "description": "The Common Components", "keywords": ["common"], "type": "library", "version": "1.0.0", "require": {"php": "^8.0.2", "illuminate/support": "^9.0", "illuminate/database": "^9.0"}, "require-dev": {"filp/whoops": "~2.0", "fzaninotto/faker": "~1.4", "phpunit/phpunit": "^9.5.10"}, "autoload": {"classmap": [], "psr-4": {"KangarooRewards\\Common\\": "/"}, "exclude-from-classmap": ["**/Tests/"]}, "autoload-dev": {"psr-4": {"Common\\Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["KangarooRewards\\Common\\CommonServiceProvider"]}, "branch-alias": {"dev-master": "1.0-dev"}}, "scripts": {"test": "vendor/bin/phpunit"}, "config": {"allow-plugins": {"kylekatarnls/update-helper": true}}}