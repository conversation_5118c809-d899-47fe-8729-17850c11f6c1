<?php
/* @var $this MessagesQueueController */
/* @var $model MessagesQueue */

$this->breadcrumbs=array(
	'Messages Queues'=>array('index'),
	$model->message_queue_pk,
);

$this->menu=array(
	array('label'=>'List MessagesQueue', 'url'=>array('index')),
	array('label'=>'Create MessagesQueue', 'url'=>array('create')),
	array('label'=>'Update MessagesQueue', 'url'=>array('update', 'id'=>$model->message_queue_pk)),
	array('label'=>'Delete MessagesQueue', 'url'=>'#', 'linkOptions'=>array('submit'=>array('delete','id'=>$model->message_queue_pk),'confirm'=>'Are you sure you want to delete this item?')),
	array('label'=>'Manage MessagesQueue', 'url'=>array('admin')),
);
?>

<h1>View MessagesQueue #<?php echo $model->message_queue_pk; ?></h1>

<?php $this->widget('zii.widgets.CDetailView', array(
	'data'=>$model,
	'attributes'=>array(
		'message_queue_pk',
		'send_to',
		'sms_sid',
		'params',
		'max_attempts',
		'attempts',
		'cell_email',
		'error_log',
		'utc_created',
		'lastattempt',
		'utc_sent',
		'country_phone_fk',
		'email_sms_status_fk',
		'user_fk',
	),
)); ?>
