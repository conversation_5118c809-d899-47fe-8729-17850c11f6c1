<?php

namespace App\Http\Requests\V1;

use App\Models\V1\BusinessBranch;
use App\Rules\FilterDateValidation;
use App\Rules\FilterValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class CustomerActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        // TODO: validate product rewards belong to the business
        $business = null;
        if (isset($this->branch['id'])) {
            $business = $this->appBusiness();
            $branch = BusinessBranch::findUid(
                $this->branch['id'],
                ['business_branch_pk', 'business_fk', 'name', 'branch_timezone_fk'],
                ['business_fk' => $business->business_pk]
            // or  [['business_fk',$business->business_pk]]
            );

            if (!$branch) {
                return false;
            }
        }

        if (isset($this->items)) {
            foreach ($this->items as $item) {
                if (isset($item['branch']['id'])) {
                    if (!$business) {
                        $business = $this->appBusiness();
                    }
                    $branch = BusinessBranch::findUid(
                        $item['branch']['id'],
                        ['business_branch_pk', 'business_fk', 'name', 'branch_timezone_fk'],
                        ['business_fk' => $business->business_pk]
                    );

                    if (!$branch) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $commonRules = [
            'type' => 'required|in:recalculate,calculate,batch_calculate',
            'resource' => 'required|in:tier_level,reward',
        ];

        if ($this->type === 'calculate' && $this->resource === 'reward' && $this->intent === 'reward') {
            $calcReward = $this->getCalculateRewardRules();
            $rules = array_merge($calcReward, $commonRules);
        } elseif ($this->type === 'calculate' && $this->resource === 'reward' && $this->intent === 'reward_store_credit') {
            $calcReward = $this->getCalculateRewardStoreCreditRules();
            $rules = array_merge($calcReward, $commonRules);
        } elseif ($this->type === 'batch_calculate' && $this->resource === 'reward' && $this->intent === 'reward') {
            $calcReward = $this->getBatchCalculateRewardRules();
            $rules = array_merge($calcReward, $commonRules);
        } elseif ($this->type === 'batch_calculate' && $this->resource === 'reward' && $this->intent === 'reward_store_credit') {
            $calcReward = $this->getBatchCalculateRewardStoreCreditRules();
            $rules = array_merge($calcReward, $commonRules);
        } else {
            $rules = $commonRules;
        }

        return $rules;
    }

    private function getCalculateRewardRules(): array
    {
        return [
            'intent' => 'required|in:reward,reward_store_credit',
            'amount' => 'required_without_all:points,visits|numeric|min:0.01',
            'points' => 'required_without_all:amount,visits|integer|min:0',
            'visits' => 'required_without_all:amount,points|integer|min:1',
            'branch' => 'sometimes|required|array',
            'branch.id' => 'sometimes|required|string|exists:tenant.business_branch,branch_uid',
            'product_rewards' => 'sometimes|required|array',
            'product_rewards.*.id' => 'required_with:product_rewards|integer|exists:tenant.product_reward,product_reward_pk',
            'product_rewards.*.quantity' => 'required_with:product_rewards|integer|min:1',
        ];
    }

    private function getCalculateRewardStoreCreditRules(): array
    {
        return [
            'intent' => 'required|in:reward,reward_store_credit',
            'amount' => 'required_without_all:points,visits|numeric|min:0.01',
            'branch' => 'sometimes|required|array',
            'branch.id' => 'sometimes|required|string|exists:tenant.business_branch,branch_uid',
        ];
    }

    private function getBatchCalculateRewardRules(): array
    {
        return [
            'intent' => 'required|in:reward,reward_store_credit',
            'items.*.amount' => 'required_without_all:items.*.points,items.*.visits|numeric|min:0.01',
            'items.*.points' => 'required_without_all:items.*.amount,items.*.visits|integer|min:0',
            'items.*.visits' => 'required_without_all:items.*.amount,items.*.points|integer|min:1',
            'items.*.branch' => 'sometimes|required|array',
            'items.*.branch.id' => 'sometimes|required|string|exists:tenant.business_branch,branch_uid',
            'items.*.product_rewards' => 'sometimes|required|array',
            'items.*.product_rewards.*.id' => 'required_with:items.*.product_rewards|integer|exists:tenant.product_reward,product_reward_pk',
            'items.*.product_rewards.*.quantity' => 'required_with:product_rewards|integer|min:1',
        ];
    }

    private function getBatchCalculateRewardStoreCreditRules(): array
    {
        return [
            'intent' => 'required|in:reward,reward_store_credit',
            'items.*.amount' => 'required|numeric|min:0.01',
            'items.*.branch' => 'sometimes|required|array',
            'items.*.branch.id' => 'sometimes|required|string|exists:tenant.business_branch,branch_uid',
        ];
    }

    /**
     * @throws ValidationException
     */
    public function prepareForValidation()
    {
        try {
            if (!empty($this->branch['id'])) {
                $this->merge([
                    'branch' => array_merge($this->branch, [
                        'id' => hex2bin($this->branch['id']),
                    ]),
                ]);
            }

            if (!empty($this->items)) {
                $items = [];
                foreach ($this->items as $item) {
                    if (!empty($item['branch']['id'])) {
                        $item['branch']['id'] = hex2bin($item['branch']['id']);
                    }
                    $items[] = $item;
                }
                $this->merge([
                    'items' => $items,
                ]);
            }
        } catch (\Exception $e) {
            throw new ValidationException($this->getValidatorInstance());
        }
    }

//    public function withValidator($validator)
//    {
//        $validator->after(function ($validator) {
//            if (!$this->isValidCreatedDate()) {
//                $validator->errors()->add('created_at', 'The created at does not match the format '.\DateTime::ATOM);
//            }
//        });
//    }


//    private function isValidCreatedDate()
//    {
//        return true;
//    }
}
