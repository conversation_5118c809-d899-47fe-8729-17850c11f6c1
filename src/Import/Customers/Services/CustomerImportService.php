<?php

namespace KangarooRewards\Import\Customers\Services;

use KangarooRewards\Import\Common\Services\ImportTempInsertionService;
use KangarooRewards\Import\Customers\Requests\CustomerImportRequest;

class CustomerImportService extends ImportTempInsertionService
{
    /**
     * Returns the name of the temporary table used for importing customer records
     * @return string
     */
    public function getTempTable(): string
    {
        return "import_customers";
    }

    /**
     * @param array $row
     * @return array
     */
    protected function mapRowToDTO(array $row): array
    {
        $notApplicableKey = 'n/a';

        $firstName = CustomerImportRequest::FIRST_NAME_MAPPING_KEY;
        $lastName = CustomerImportRequest::LAST_NAME_MAPPING_KEY;
        $email = CustomerImportRequest::EMAIL_MAPPING_KEY;
        $phone = CustomerImportRequest::PHONE_MAPPING_KEY;
        $language = CustomerImportRequest::LANGUAGE_MAPPING_KEY;
        $dob = CustomerImportRequest::DOB_MAPPING_KEY;
        $tags = CustomerImportRequest::TAGS_MAPPING_KEY;
        $pointsBalance = CustomerImportRequest::POINTS_BALANCE;
        $emailConsent = CustomerImportRequest::EMAIL_CONSENT_MAPPING_KEY;
        $smsConsent = CustomerImportRequest::SMS_CONSENT_MAPPING_KEY;
        $pushConsent = CustomerImportRequest::PUSH_CONSENT_MAPPING_KEY;
        $customField1 = CustomerImportRequest::CUSTOM_FIELD_1_MAPPING_KEY;
        $customField2 = CustomerImportRequest::CUSTOM_FIELD_2_MAPPING_KEY;
        $customField3 = CustomerImportRequest::CUSTOM_FIELD_3_MAPPING_KEY;
        $customField4 = CustomerImportRequest::CUSTOM_FIELD_4_MAPPING_KEY;
        $customField5 = CustomerImportRequest::CUSTOM_FIELD_5_MAPPING_KEY;

        // Set the mapping fields
        $fields = [
            $firstName => $firstName,
            $lastName => $lastName,
            $email => $email,
            $phone => $phone,
            $language => $language,
            $dob => $dob,
            $tags => $tags,
            $pointsBalance => $pointsBalance,
            $emailConsent => $emailConsent,
            $smsConsent => $smsConsent,
            $pushConsent => $pushConsent,
            $customField1 => $customField1,
            $customField2 => $customField2,
            $customField3 => $customField3,
            $customField4 => $customField4,
            $customField5 => $customField5
        ];

        $result = [];

        foreach ($fields as $field) {
            $mappedField = $this->mappingFields[$field] ?? null;
            $result[$field] = empty($mappedField) ? $notApplicableKey : ($row[$mappedField] ?? null);
        }

        return $result;
    }
}