<?php


namespace App\Traits;

use App\Helpers\V1\Utils;
use App\Models\V1\Activity;
use App\Models\V1\Business;
use App\Models\V1\Follower;
use App\Models\V1\Purchase;
use App\Models\V1\Transaction;
use App\Models\V1\TransactionDetails;
use App\Models\V1\User;
use App\Models\V1\UserBusinessNotification;
use App\Models\V1\UserBusinessProfile;
use App\Models\V1\UserEntityPoints;
use App\Models\V1\UserStatusManager;
use App\Models\V1\UserTag;
use Illuminate\Support\Facades\Log;

trait FollowerTrait
{
    /**
     * Deletes user account and all memberships, including balance and notification settings
     *
     * @param $user
     */
    public static function deleteAccount($user)
    {
//        $followers = Follower::where('user_fk', $user->id)->get();
//
//        foreach ($followers as $follower) {
//            $business = Business::findByPk($follower->entity_fk);
//            FollowerTrait::leaveMembershipForBusiness($user, $business);
//            $follower->delete();
//        }
//
//        UserTag::where('user_fk', $user->id)
//            ->delete();
//
//        UserStatusManager::where('user_fk', $user->id)
//            ->delete();

        Log::info(__METHOD__, [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_phone' => $user->phone,
            'business_pk' => $user->business_fk,
            'message' => 'Delete User Data',
        ]);

        $user->email = null;
        $user->phone = null;
        $user->first = 'Delete';
        $user->last = 'Account';
        $user->enabled = 0;
        $user->passwordreset = null;
        $user->qrcode = null;
        $user->ip = null;
        $user->pin_permission = null;
        $user->pin_enabled = 0;
        $user->device_token = null;
        $user->gcm_device_token = null;
        $user->birth_date = null;
        $user->gender = null;
        $user->save();
    }

    /**
     * @param $user
     * @param $business
     */
//    public static function leaveMembershipForBusiness($user, $business)
//    {
//        if (!$business) {
//            return;
//        }
//
//        $userBalance = UserEntityPoints::forBusiness(
//            $user->id, $business->business_pk, $business->entity_type_fk
//        );
//
//        if ($userBalance) {
//            self::createLeaveMembershipTransaction($user, $business, $userBalance);
//        }
//
//        UserBusinessNotification::where([
//            'user_fk' => $user->id,
//            'business_fk' => $business->business_pk,
//        ])->update([
//            'allow_email' => 0,
//            'allow_sms' => 0,
//            'allow_push' => 0,
//            'cem_flag' => 0,
//        ]);
//
//        UserBusinessProfile::where([
//            'user_fk' => $user->id,
//            'business_fk' => $business->business_pk,
//        ])->update([
//            'customer_type' => null
//        ]);
//    }

    /**
     * @param $user
     * @param $business
     * @param $userBalance
     */
    private static function createLeaveMembershipTransaction($user, $business, $userBalance)
    {
        $points = (int) $userBalance->current_active_points;

        $newUserBalance = UserEntityPoints::decreaseBalance($user, $business, $points);

        $transaction = Transaction::create([
            'user_fk' => $user->id,
            'entity_fk' => $business->business_branch_pk,
            'entity_type_fk' => BRANCH_POINTS,
            'transaction_type_fk' => RESET_BALANCE,
            'transaction_sub_type_fk' => TRX_SUBTYPE_LEAVE_MEMBERSHIP,
            'timezone_mysql_fk' => $business->branch_timezone_fk,
        ]);

        // Create transaction details
        TransactionDetails::create([
            'transaction_fk' => $transaction->transaction_pk,
            'points' => -1 * $points,
            'amount' => 0,
            'points_balance' => (int) $newUserBalance->current_active_points,
            'giftcard_balance' => (float) $newUserBalance->giftcard,
        ]);

        Activity::create([
            'description' => 'ACTIVITY_LEAVE_MEMBERSHIP',
            'units_exchange' => $points,
            'transaction_fk' => $transaction->transaction_pk,
            'hidden' => 1,
            'timezone_mysql_fk' => $business->branch_timezone_fk,
        ]);
    }

    /** Return array of specific business ID or business IDs that user is following
     *
     * @since 2021-12
     * @return array
     * <AUTHOR>
     * @access public
     * @version 1.0
     */
    public static function getUserBusinesses($business, $userIds)
    {
        if ($business->business_pk === KNG_BUSINESS_ID || $business->conglomerate === 1) {
            $businessIds = Follower::followedBusinessIds($userIds);
        } else {
            $businessIds = [$business->business_pk];
        }
        return $businessIds;
    }

    /**
     * Get business follower by email or phone number
     *
     * @param string $emailPhone
     * @param int $businessId
     * @return mixed
     */
    public static function getFollowerByEmailOrPhone($emailPhone, $businessId)
    {
        if (filter_var($emailPhone, FILTER_VALIDATE_EMAIL)) {
            $fieldName = "u.email";
        } else {
            $fieldName = "u.phone";
            $emailPhone = Utils::normalizeDigitsOnly($emailPhone);
        }

        if (empty($emailPhone)) {
            return null;
        }

        return User::from('user as u')
            ->join('followers as f', 'f.user_fk', '=', 'u.id')
            ->where($fieldName, $emailPhone)
            ->where('u.business_fk', $businessId)
            ->where('u.enabled', 1)
            ->first();
    }

    /**
     * Get business follower by email or phone number
     *
     * @param array $emailPhoneArray
     * @param int $businessId
     * @return array
     */
    public static function getFollowerListByEmailOrPhone(array $emailPhoneArray, int $businessId): array
    {
        $emailsList = $phonesList = [];

        foreach ($emailPhoneArray as $customerContact) {
            if (filter_var($customerContact, FILTER_VALIDATE_EMAIL)) {
                $emailsList [] = $customerContact;
            } else {
                $phonesList [] = $customerContact;
            }
        }

        $query = User::from('user as u')
            ->select('u.id')
            ->join('followers as f', 'f.user_fk', '=', 'u.id')
            ->where('u.business_fk', $businessId)
            ->where('u.enabled', 1);

        if (!empty($emailsList) && !empty($phonesList)) {
            $query->where(function ($query) use ($emailsList, $phonesList) {
                $query->whereIn('u.email', $emailsList);
                $query->orWhereIn('u.phone', $phonesList);
            });
        } elseif (!empty($emailsList)) {
            $query->whereIn('u.email', $emailsList);
        } elseif (!empty($phonesList)) {
            $query->whereIn('u.phone', $phonesList);
        } else {
            return [];
        }

        return $query->pluck('u.id')->toArray();
    }

    /**
     * Return list of customer Ids following business
     *
     * <AUTHOR>
     * @since 14/09/2022
     * @param int $businessId
     * @param string $startDate
     * @param string $endDate
     * @param int $entityTypeId
     * @return array
     */
    public static function getBusinessFollowersBetweenDates($businessId, $startDate, $endDate, $entityTypeId = 1) :array
    {
        return Follower::from("followers as f")
            ->join('user as u', 'u.id', '=', 'f.user_fk')
            ->where('f.entity_fk', $businessId)
            ->where('f.entity_type_fk', $entityTypeId)
            ->where('u.enabled', 1)
            ->whereBetween('f.utc_created', [$startDate, $endDate])
            ->pluck('u.id')
            ->toArray();
    }

    /**
     * Check follower by email or phone if exist
     * @param $businessId
     * @param $email
     * @param $phone
     * @return false
     */
    public function checkFollowerIfExists($businessId, $email = null, $phone = null): bool
    {
        $query = User::from('user as u')
            ->select('u.id')
            ->join('followers as f', 'f.user_fk', '=', 'u.id')
            ->where('u.business_fk', $businessId)
            ->where('u.enabled', 1);

        if (!empty($email)) {
            $query->where('email', $email);
        } elseif (!empty($phone)) {
            $query->where('phone', $phone);
        } else {
            return false;
        }

        return $query->exists();
    }
}
