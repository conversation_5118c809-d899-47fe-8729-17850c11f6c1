<?php

namespace App\Services;

use App\Models\Relation;
use App\Models\V1\Business;
use App\Models\V1\Customer;
use App\Models\V1\Follower;
use App\Models\V1\UserPermission;
use App\Traits\HasFeatureFlag;
use App\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AppUserProfileService
{
    private $user;
    private $username;
    private $appKey;

    /**
     * MembershipService constructor.
     * @param $user
     * @param $username
     * @param $appKey
     */
    public function __construct($user, $username, $appKey)
    {
        $this->user = $user;
        $this->username = $username;
        $this->appKey = $appKey;
    }

    public function create()
    {
        $featureUbp = HasFeatureFlag::feature('UBP');
        $user = null;

        // For Members only add memberships
        if ($featureUbp && $this->appKey && (int) $this->appKey->businessId === KNG_BUSINESS_ID) {
            $user = $this->createAppUserProfile();
        } elseif ($featureUbp && $this->appKey && (int) $this->appKey->conglomerate === 1) {
            $user = $this->createAppUserProfile();
        }

        return $user ?: $this->user;
    }

    public function createAppUserProfile()
    {
        $businessId = (int) $this->appKey->businessId;
        $branchId = (int) $this->appKey->branchId;

        $conglomerateIds = Business::select(['business_pk'])
            ->where('conglomerate', 1)
            ->pluck('business_pk');

        $newUser = null;

        $phone = preg_replace('/[^0-9]+/', '', $this->username);

        \DB::connection('tenant')->beginTransaction();
        try {
            if ($this->user && $this->user->isUser()) {
                $this->addRelations($this->user);
            } elseif (!$this->user) {
                if (filter_var($this->username, FILTER_VALIDATE_EMAIL)) {
                    $profile = User::where('email', $this->username)
                        ->whereRaw('business_fk IS NOT NULL')
                        ->where('business_fk', '<>', $businessId)
                        ->whereNotIn('business_fk', $conglomerateIds->toArray())
                        ->first();
                } elseif ($phone) {
                    $profile = User::where('phone', $phone)
                        ->whereRaw('business_fk IS NOT NULL')
                        ->where('business_fk', '<>', $businessId)
                        ->whereNotIn('business_fk', $conglomerateIds->toArray())
                        ->first();
                }

                \Log::info(__METHOD__, [
                    'input_username' => $this->username,
                    'user' => $this->user ? [
                        'id' => $this->user->id,
                        'email' => $this->user->email,
                        'phone' => $this->user->phone,
                    ] : null,
                    'user_business_profile' => $profile ? [
                        'id' => $profile->id,
                        'email' => $profile->email,
                        'phone' => $profile->phone,
                    ] : null,
                    'businessId' => $businessId,
                ]);

                if ($profile) {
                    $kngProfile = $profile->getAttributes();
                    unset($kngProfile['id'], $kngProfile['passwordreset'], $kngProfile['user_uid']);
                    $kngProfile['business_fk'] = $businessId;
                    $kngProfile['signedupat_fk'] = $branchId;
                    $kngProfile['username'] = Customer::generateUniqueUsername();
                    $kngProfile['platform_solution_fk'] = config('app.platform_id', 10);
                    $newUser = Customer::create($kngProfile);

                    UserPermission::create([
                        'itemname' => 'user',
                        'userid' => $newUser->id,
                        'timezone_mysql_fk' => null,
                    ]);

                    $newUser->crateUserStatusManager();

                    Follower::updateOrCreate([
                        'user_fk' => $newUser->id,
                        'entity_fk' => $businessId,
                        'entity_type_fk' => 1,
                    ], [
                        'user_fk' => $newUser->id,
                        'entity_fk' => $businessId,
                        'entity_type_fk' => 1,
                        'timezone_mysql_fk' => null,
                    ]);

                    $this->addRelations($newUser);
                }
            }
            DB::connection('tenant')->commit();
        } catch (\Exception $e) {
            DB::connection('tenant')->rollback();
            Log::warning(__METHOD__ . ' ' . $e, [
                'input_username' => $this->username,
                'user' => $this->user ? [
                    'id' => $this->user->id,
                    'email' => $this->user->email,
                    'phone' => $this->user->phone,
                ] : null,
                'businessId' => $businessId,
            ]);
        }

        return $newUser ?: $this->user;
    }

    public function addRelations($user)
    {
        $profiles = [];

        $businessId = (int) $this->appKey->businessId;

        if ($businessId === KNG_BUSINESS_ID) {
            $conglomerateIds = Business::select(['business_pk'])
                ->where('conglomerate', 1)
                ->pluck('business_pk');

            if ($user->email) {
                $profiles = User::where('email', $user->email)
                    ->where('business_fk', '<>', KNG_BUSINESS_ID)
                    ->whereNotIn('business_fk', $conglomerateIds->toArray())
                    ->get();
            } elseif ($user->phone) {
                $profiles = User::where('phone', $user->phone)
                    ->where('business_fk', '<>', KNG_BUSINESS_ID)
                    ->whereNotIn('business_fk', $conglomerateIds->toArray())
                    ->get();
            }
        } elseif ((int) $this->appKey->conglomerate === 1) {
            $conglomerateMembersIds = Business::select(['business_pk'])
                ->where('conglomerate_id', $businessId)
                ->where('enabled', 1)
                ->pluck('business_pk');

            if ($user->phone) {
                $profiles = User::where('phone', $user->phone)
                    ->where('business_fk', '<>', KNG_BUSINESS_ID)
                    ->whereIn('business_fk', $conglomerateMembersIds->toArray())
                    ->get();
            } elseif ($user->email) {
                $profiles = User::where('email', $user->email)
                    ->where('business_fk', '<>', KNG_BUSINESS_ID)
                    ->whereIn('business_fk', $conglomerateMembersIds->toArray())
                    ->get();
            }
        }

        \Log::info(__METHOD__, [
            'found_profiles_count' => count($profiles),
            'user' => $user ? [
                'id' => $user->id,
                'email' => $user->email,
                'phone' => $user->phone,
            ] : null,
            'appBusinessId' => $businessId,
            'appKey' => (array) $this->appKey,
            'conglomerateMembersIds' => isset($conglomerateMembersIds) ? $conglomerateMembersIds->toArray() : null,
        ]);

        foreach ($profiles as $profile) {
            $relation = Relation::where('app_user_fk', $user->id)
                ->where('business_fk', $profile->business_fk)
                ->first();

            if (!$relation) {
                Relation::create([
                    'app_user_fk' => $user->id,// logged in user
                    'user_fk' => $profile->id,
                    'business_fk' => $profile->business_fk,
                ]);
            }
        }
    }
}
