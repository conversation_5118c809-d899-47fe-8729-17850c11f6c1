<?php

namespace KangarooRewards\Common\Repositories;
 
use App\Repositories\V1\RepositoryInterface;
use App\Repositories\V1\Repository;
 
class BusinessTiersSetupRepository extends Repository
{
    /**
     * Specify Model class name
     *
     * @return mixed
     */
    public function model()
    {
        return 'KangarooRewards\Common\Models\BusinessTiersSetup';
    }

    public function getBusinessTierProgram($businessId)
    {
        return $this->model->where('business_fk', $businessId)->get();
    }

}
