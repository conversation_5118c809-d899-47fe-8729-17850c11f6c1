<?php

namespace App\Traits;

use App\Models\V1\BusinessTier;
use App\Models\V1\BusinessTierSetup;
use App\Models\V1\Transaction;
use Illuminate\Support\Facades\DB;

class BusinessTierTrait
{
    /**
     * @param $businessId
     * @param $relations
     * @return mixed
     */
    public static function forBusinessAdminPaginated($businessId, $relations)
    {
        $fields = [
            'ts.business_tiers_setup_pk',
            'ts.tiers_sequence',
            'ts.tiers_relation',
            'ts.reset_type',
            'ts.reset_period_month',
            'ts.base_previous_period',
            'ts.enabled',
            'ts.reset_registration_month',
            'ts.grace_period',
            'ts.tier_upgrade_option',
            'ts.tier_downgrade_option',
            'ts.business_fk'
        ];

        $with = [];
        if (in_array('tier_levels', $relations, true)) {
            $with[] = 'businessTiers';
        }
        
        $nbp = [];
        if (in_array('nbp', $relations, true)) {
            $nbp = true;
        }

        $query = BusinessTierSetup::from('business_tiers_setup AS ts')
            ->select($fields)
            ->where('ts.business_fk', $businessId)
            ->orderBy('ts.business_tiers_setup_pk', 'DESC');

        if(!$nbp) {
            $query = $query->where(function ($query) {
                return $query->where('ts.enabled', 1)
                    ->orWhere('ts.base_previous_period', '!=', 0);
            });
        }

        return $query->with($with);
    }

    /**
     * @param $userId
     * @param $trxType
     * @param $trxSubType
     * @param $trxSubTypeNegative
     * @param $branchesIds
     * @param $firstDate
     * @param $lastDate
     * @return mixed
     */
    public static function getUserTransactionsStats($userId, $trxType, $trxSubType, $trxSubTypeNegative, $branchesIds, $firstDate, $lastDate)
    {
        $positiveStatus = Transaction::from('transaction as t')
            ->select([
                DB::raw('SUM(p.amount) as total_amount'),
                DB::raw('SUM(COALESCE(a.units_exchange, 0)) as total_points'),
                DB::raw('COUNT(t.transaction_pk) as total_visits'),
            ])
            ->join('activity AS a', 'a.transaction_fk', '=', 't.transaction_pk')
            ->leftJoin('purchases AS p', 'p.transaction_fk', '=', 't.transaction_pk')
            ->where('t.entity_type_fk', 2)//Lei added
            ->where('t.user_fk', $userId)
            ->whereIn('t.entity_fk', $branchesIds)
            ->whereIn('t.transaction_type_fk', $trxType)//Lei added
            ->whereIn('t.transaction_sub_type_fk', $trxSubType)
            ->whereBetween('t.utc_created', [$firstDate, $lastDate])
            ->groupBy('t.user_fk')
            ->first();

        $ecomFullRefundAmount = 0;
        if (($key = array_search(45, $trxSubTypeNegative)) !== false) {
            //TRX_SUBTYPE_CLERK_CANCELATION - ecom sales fully refund, previous version missing purchase record
            //So get the amount from transaction_detail table
            $eComFullRefund = Transaction::from('transaction as t')
                ->select([
                    DB::raw('SUM(td.amount) as total_amount'),
                ])
                ->join('transaction_details AS td', 'td.transaction_fk', '=', 't.transaction_pk')
                ->where('t.entity_type_fk', 2)
                ->where('t.user_fk', $userId)
                ->whereIn('t.entity_fk', $branchesIds)
                ->whereIn('t.transaction_type_fk', $trxType)
                ->where('t.transaction_sub_type_fk', 45)
                ->whereBetween('t.utc_created', [$firstDate, $lastDate])
                ->groupBy('t.user_fk')
                ->first();
            unset($trxSubTypeNegative[$key]);
            $ecomFullRefundAmount = isset($eComFullRefund) ? $eComFullRefund->total_amount : 0;
        }

        //take consider of refund into tier calculation
        $negativeStatus = Transaction::from('transaction as t')
            ->select([
                DB::raw('SUM(p.amount) as total_amount'),
                DB::raw('SUM(COALESCE(a.units_exchange, 0)) as total_points'),
                DB::raw('COUNT(t.transaction_pk) as total_visits'),
            ])
            ->join('activity AS a', 'a.transaction_fk', '=', 't.transaction_pk')
            ->leftJoin('purchases AS p', 'p.transaction_fk', '=', 't.transaction_pk')
            ->where('t.entity_type_fk', 2)
            ->where('t.user_fk', $userId)
            ->whereIn('t.entity_fk', $branchesIds)
            ->whereIn('t.transaction_type_fk', $trxType)
            ->whereIn('t.transaction_sub_type_fk', $trxSubTypeNegative)
            ->whereBetween('t.utc_created', [$firstDate, $lastDate])
            ->groupBy('t.user_fk')
            ->first();

        return (object)[
            'user_fk' => $userId,
            'total_amount' => ($positiveStatus->total_amount ?? 0) - ($negativeStatus->total_amount ?? 0) - $ecomFullRefundAmount,
            'total_points' => ($positiveStatus->total_points ?? 0) - ($negativeStatus->total_points ?? 0),
            'total_visits' => ($positiveStatus->total_visits ?? 0) - ($negativeStatus->total_visits ?? 0),
        ];
    }

    /**
     * @param $year
     * @param $month
     * @param $addYears
     * @param $isFuturePeriod
     * @return array
     * @throws \Exception
     */
    public static function getTierStartEndDate($year, $month, $addYears, $isFuturePeriod, $lastResetDate)
    {
        if (isset($lastResetDate)) {
            $startDate = new \DateTime($lastResetDate);
            $currentDate = new \DateTimeImmutable();

            $endDate = clone $startDate;
            $endDate->add(new \DateInterval('P' . $addYears . 'Y0M0DT0H0M0S'));
            $endDate->sub(new \DateInterval('P0Y0M0DT0H0M1S')); // subtract 1 sec

            if ($endDate < $currentDate) {
                // Change the reset date
                do {
                    $endDate->add(new \DateInterval('P' . $addYears . 'Y0M0DT0H0M0S'));
                    $startDate->add(new \DateInterval('P' . $addYears . 'Y0M0DT0H0M0S'));
                } while ($endDate < $currentDate);
            }
        } else {
            if ($isFuturePeriod) {
                $sDate = $year . '-' . $month . '-01';
            } else {
                $sDate = ($year - $addYears) . '-' . $month . '-01';
            }

            $startDate = new \DateTime($sDate);
            $startDate->setTime(0, 0, 0);

            $endDate = clone $startDate;
            $endDate->add(new \DateInterval('P' . $addYears . 'Y0M0DT0H0M0S'));
            $endDate->sub(new \DateInterval('P0Y0M0DT0H0M1S')); // subtract 1 sec
        }

        return [
            $startDate->format('Y-m-d H:i:s'),
            $endDate->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Get tiers by order low -> high
     * @param $businessId
     * @param $tierSetup
     * @return mixed
     */
    public static function getTiers($businessId, $tierSetup)
    {
        $tiersSequences = json_decode($tierSetup->tiers_sequence);
        if (count($tiersSequences) == 0) {
            throw new \RuntimeException('Tier criteria not found');
        }
        $tiersSequence = $tiersSequences[0];
        $businessTiers = BusinessTier::where('business_fk', $businessId)
            ->where('enabled', 1);
        if ($tiersSequence == '1') {
            $businessTiers->orderBy('reach_spend', 'ASC');
        } elseif ($tiersSequence == '2') {
            $businessTiers->orderBy('reach_points', 'ASC');
        } elseif ($tiersSequence == '3') {
            $businessTiers->orderBy('reach_visits', 'ASC');
        }
        return $businessTiers->get();
    }

    /**
     * Get tier details
     * <AUTHOR>
     * @since 2022-01-25
     * @param $tierId
     * @return BusinessTier|false
     */
    public static function getTierById($tierPk){
        if(!$tierPk){
            return false;
        }
        return BusinessTier::where('business_tiers_pk', $tierPk)
            ->first();
    }

    /**
     * Return an array of start & end date for customer based on registration month
     * <AUTHOR>
     * @since 2022-01-25
     * @param $year
     * @param $month
     * @param $addYears
     * @param $gracePeriodMonths
     * @param $gracePeriodYears
     * @param $isFuturePeriod
     * @return array
     */
    public static function getTierDateBasedOnRegistrationMonth($userResetDate, $addYears, $gracePeriodMonths, $gracePeriodYears, $isFuturePeriod): array
    {
        if($isFuturePeriod){
            $firstDate = date("Y-m-d", strtotime($userResetDate." -".$addYears." year"));
            $lastDate = date("Y-m-d", strtotime($userResetDate));
            
            // TO BE DISCUSSED
            // $lastDate = date("Y-m-d", strtotime($userResetDate." +".$gracePeriodYears." year "." +".$gracePeriodMonths." month"));
        }else{
            $firstDate = '';
            $lastDate = '';
        }

        return [
            'start_date' => $firstDate,
            'end_date' => $lastDate
        ];
    }

    /**
     * Return the start and end date to be considered in the tier reset calculation
     *
     * <AUTHOR>
     * @since 24/02/2022
     * @param int $startResetYear
     * @param int $tierKickOffMonth
     * @param int $resetByRegistrationMonth
     * @param string $userRegistrationDate
     * @return array
     */
    public static function getTierDateWithNoResetPeriod($startResetYear, $tierKickOffMonth, $resetByRegistrationMonth, $userRegistrationDate): array
    {
        if (empty($startResetYear)) {
            $startResetYear = date("Y");
        }

        //Get the start reset month
        if ($resetByRegistrationMonth == 1) {
            $startResetMonth = date("m", strtotime($userRegistrationDate));
        } else {
            $startResetMonth = $tierKickOffMonth;
        }

        //Set the start & end date
        $startDate = $startResetYear ."-". $startResetMonth ."-01";
        $endDate = date("Y-m-d 23:59:59");

        return [
          "start_date" => date("Y-m-d 00:00:00", strtotime($startDate)),
          "end_date" => $endDate
        ];
    }

    /**
     * Get business tiers
     * @param int $businessId
     * @param bool $count
     * @param bool $pluck
     * <AUTHOR>
     * @since 16/02/2024
     * @return mixed
     */
    public static function getBusinessTiers(int $businessId, bool $count = false, bool $pluck = false): mixed
    {
        $query = BusinessTier::where('business_fk', $businessId)
            ->where('enabled', 1);

        if ($count) {
            return $query->count();
        } elseif ($pluck) {
            return $query->pluck('type_name', 'business_tiers_pk')->toArray();
        } else {
            return $query->get();
        }
    }
}
