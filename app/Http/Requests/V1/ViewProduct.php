<?php

namespace App\Http\Requests\V1;

use App\Models\V1\Business;
use App\Models\V1\BusinessBranch;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class ViewProduct extends FormRequest
{
    private $product;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     * @throws AuthorizationException
     */
    public function authorize(): bool
    {
        $this->product = $this->route()->parameter('product');

        return $this->product && $this->user()->can('viewSingleProduct', $this->product);
    }
}
