<?php

namespace App\Traits;

trait FilterValidationTrait
{
    /**
     * @param string $input
     * @return ?array
     */
    public function extractOperatorAndValue(string $input): ?array
    {
        if (!str_contains($input, '|')) {
            return null;
        }

        [$operator, $value] = explode('|', $input, 2);

        return [
            'operator' => $operator,
            'value' => $value,
        ];
    }
}