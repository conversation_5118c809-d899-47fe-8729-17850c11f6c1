<?php

namespace App\Helpers\PartnerReward;

use Illuminate\Support\Facades\Log;

class AmazonApiClient
{
    const SERVICE_NAME = "AGCODService";
    const ACCEPT_HEADER = "accept";
    const HOST_HEADER = "host";
    const XAMZDATE_HEADER = "x-amz-date";
    const XAMZTARGET_HEADER = "x-amz-target";
    const AUTHORIZATION_HEADER = "Authorization";
    const AWS_SHA256_ALGORITHM = "AWS4-HMAC-SHA256";
    const KEY_QUALIFIER = "AWS4";
    const TERMINATION_STRING = "aws4_request";

    private $partnerId;
    private $baseUrlDomain;
    private $currencyCode;
    private $awsSecretKey;
    private $awsAccessKey;

    public function __construct($currencyCode, $countryCode)
    {
        $this->currencyCode = strtoupper($currencyCode);

        if ($currencyCode === 'USD') {
            $this->partnerId = config("app.amazon_partner_id_us");
            $this->awsAccessKey = config("app.aws_access_key_us");
            $this->awsSecretKey = config("app.aws_secret_key_us");
            $this->baseUrlDomain = config("app.amazon_base_url_domain_na");
        } elseif ($currencyCode === 'CAD') {
            $this->partnerId = config("app.amazon_partner_id_ca");
            $this->awsAccessKey = config("app.aws_access_key_ca");
            $this->awsSecretKey = config("app.aws_secret_key_ca");
            $this->baseUrlDomain = config("app.amazon_base_url_domain_na");
        } elseif ($currencyCode === 'GBP' && $countryCode === 'GB') {
            $this->partnerId = config("app.amazon_partner_id_uk");
            $this->awsAccessKey = config("app.aws_access_key_uk");
            $this->awsSecretKey = config("app.aws_secret_key_uk");
            $this->baseUrlDomain = config("app.amazon_base_url_domain_eu");
        }

//        elseif ($currencyCode == 'EUR') {
//            $this->baseUrlDomain = config("app.amazon_eu_base_domain"); //without https://
//        }
        else {
            Log::error(__METHOD__, ['currency' => $currencyCode, 'county' => $countryCode]);
            throw new \RuntimeException('Amazon gift card is not implemented in this region');
        }
    }

    public function getPartnerId()
    {
        return $this->partnerId;
    }

    public function generateRandomString($length):string
    {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        return substr(str_shuffle($chars), 0, $length);
    }

    private $timeStamp;

    private function getTimestamp()
    {
        if (!isset($this->timeStamp)) {
            $this->timeStamp = gmdate('Ymd\THis\Z');
        }
//        info(__METHOD__ . ' ' . $this->timeStamp);
        return $this->timeStamp;
    }

    private function  getDateString()
    {
        $dateTimeString = $this->getTimeStamp();
        return substr($dateTimeString, 0, 8);
    }

    private function hash($string)
    {
        return hash("sha256", $string);
    }

    private function hmac($data, $key, $raw = true) {
        return hash_hmac("sha256", $data, $key, $raw);
    }


    private function buildCanonicalRequest($serviceOperation, $payload)
    {
        $ACCEPT_HEADER = self::ACCEPT_HEADER;
        $HOST_HEADER = self::HOST_HEADER;
        $XAMZDATE_HEADER = self::XAMZDATE_HEADER;
        $XAMZTARGET_HEADER = self::XAMZTARGET_HEADER;

        $dateTimeString = $this->getTimestamp();
        $payloadHash = $this->hash($payload);
        $contentType = "application/json";
        $header1 = "$ACCEPT_HEADER:$contentType\n$HOST_HEADER:$this->baseUrlDomain\n$XAMZDATE_HEADER:$dateTimeString\n$XAMZTARGET_HEADER:com.amazonaws.agcod.AGCODService.$serviceOperation";
//        info(__METHOD__, ['payloadHash' => $payloadHash]);
        return "POST\n/$serviceOperation\n\n$header1\n\n$ACCEPT_HEADER;$HOST_HEADER;$XAMZDATE_HEADER;$XAMZTARGET_HEADER\n$payloadHash";
    }

    private function getRegion()
    {
        $regionName = "us-east-1";

        if ($this->baseUrlDomain == "agcod-v2-eu.amazon.com" || $this->baseUrlDomain == "agcod-v2-eu-gamma.amazon.com") {
            $regionName = "eu-west-1";
        } else if ($this->baseUrlDomain == "agcod-v2-fe.amazon.com" || $this->baseUrlDomain == "agcod-v2-fe-gamma.amazon.com") {
            $regionName = "us-west-2";
        }
        return $regionName;
    }
    
    private function buildStringToSign($canonicalRequestHash)
    {
        $SERVICE_NAME = self::SERVICE_NAME;
        $AWS_SHA256_ALGORITHM = self::AWS_SHA256_ALGORITHM;
        $TERMINATION_STRING = self::TERMINATION_STRING;

        $regionName = $this->getRegion();
        $dateTimeString = $this->getTimeStamp();
        $dateString = $this->getDateString();
        return "$AWS_SHA256_ALGORITHM\n$dateTimeString\n$dateString/$regionName/$SERVICE_NAME/$TERMINATION_STRING\n$canonicalRequestHash";
    }

    private function buildDerivedKey($rawOutput = true)
    {
        // Append Key Qaulifier, "AWS4", to secret key per http://docs.aws.amazon.com/general/latest/gr/signature-v4-examples.html
        $signatureAWSKey = self::KEY_QUALIFIER . $this->awsSecretKey;
        $regionName = $this->getRegion();
        $dateString = $this->getDateString();

        $kDate = $this->hmac($dateString, $signatureAWSKey);
        $kRegion = $this->hmac($regionName, $kDate);
        $kService = $this->hmac(self::SERVICE_NAME, $kRegion);

        // Derived the Signing key (derivedKey aka kSigning)
        return $this->hmac(self::TERMINATION_STRING, $kService, $rawOutput);
    }


    private function buildAuthSignature($stringToSign) {
        $SERVICE_NAME = self::SERVICE_NAME;
        $ACCEPT_HEADER = self::ACCEPT_HEADER;
        $HOST_HEADER = self::HOST_HEADER;
        $XAMZDATE_HEADER = self::XAMZDATE_HEADER;
        $XAMZTARGET_HEADER = self::XAMZTARGET_HEADER;
        $AWS_SHA256_ALGORITHM = self::AWS_SHA256_ALGORITHM;
        $TERMINATION_STRING = self::TERMINATION_STRING;

        $regionName= $this->getRegion();

        $dateString = $this->getDateString();
        $derivedKey = $this->buildDerivedKey();

        // Calculate signature per http://docs.aws.amazon.com/general/latest/gr/sigv4-calculate-signature.html
//        info(__METHOD__, ['stringtosign' => $stringToSign, 'derived'=> $this->buildDerivedKey(false)]);
        $finalSignature = $this->hmac($stringToSign, $derivedKey, false);

        // Assemble Authorization Header with signing information
        // per http://docs.aws.amazon.com/general/latest/gr/sigv4-add-signature-to-request.html
        $authorizationValue =
            $AWS_SHA256_ALGORITHM
            . " Credential="   . $this->awsAccessKey
            . "/" . $dateString . "/" . $regionName . "/" . $SERVICE_NAME . "/" . $TERMINATION_STRING . ","
            . " SignedHeaders="
            . $ACCEPT_HEADER . ";"  . $HOST_HEADER . ";" . $XAMZDATE_HEADER . ";" . $XAMZTARGET_HEADER . ","
            . " Signature="
            . $finalSignature;

        return $authorizationValue;
    }

    private function request($serviceOperation, $payload)
    {
        //Perpare the data that needs to get sent
        $dateTimeString = $this->getTimeStamp();
        $canonicalRequest = $this->buildCanonicalRequest($serviceOperation, $payload);
        $canonicalRequestHash = $this->hash($canonicalRequest);

//        info(__METHOD__, [
//            '$payload' => $payload,
//            '$canonicalRequest' => $canonicalRequest,
//            '$canonicalRequestHash' => $canonicalRequestHash,
//        ]);
        $stringToSign = $this->buildStringToSign($canonicalRequestHash);
        $serviceTarget = "com.amazonaws.agcod." . self::SERVICE_NAME . "." . $serviceOperation;
        $authorizationValue = $this->buildAuthSignature($stringToSign);

//        info(__METHOD__, [
//            '$authorizationValue' => $authorizationValue,
//            '$dateTimeString' => $dateTimeString,
//            '$serviceTarget' => $serviceTarget
//        ]);
        $url = "https://" . $this->baseUrlDomain . "/" . $serviceOperation;

        //Prepare to send the data to the server
        $handle = curl_init($url);

        //Yes, do POST not GET
        curl_setopt($handle, CURLOPT_POST, true);

        //This is header, not post fields
        curl_setopt($handle, CURLOPT_HTTPHEADER, array(
            "Content-Type:application/json",
            'Content-Length: ' . strlen($payload),
            self::AUTHORIZATION_HEADER . ":" . $authorizationValue,
            self::XAMZDATE_HEADER . ":" . $dateTimeString,
            self::XAMZTARGET_HEADER . ":" . $serviceTarget,
            self::ACCEPT_HEADER . ":application/json"
        ));

        //Unlike most post requests, this is not a key-value pair, but just the XML/JSON.
        curl_setopt($handle, CURLOPT_POSTFIELDS, $payload);

        //Yes, don't print the result to the web page, just give it to us in a string.
        curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);

        //Do the request
        $result = curl_exec($handle);

        $errno = curl_errno($handle);
        $error = curl_error($handle);
        curl_close($handle);

        if ($errno) {
            Log::error(__METHOD__ . ' Request error', [
                'url' => $url,
                'payload' => $payload,
                'error' => $error,
                'errno' => $errno,
            ]);
            throw new \RuntimeException($error, $errno);
        }

        return json_decode($result, false);
    }

    /**
     * A unique identifier for every CreateGiftCard request.
     * You must generate a new value for every Create request (except for retries).
     * https://developer.amazon.com/docs/incentives-api/digital-gift-cards.html
     * https://developer.amazon.com/docs/incentives-api/incentives-api.html#data-storage-guidelines
     *
     * @param $gcRequestId string
     * @param $amount float|string
     */
    public function createGiftCard($gcRequestId, $amount)
    {
        $data = array(
            "creationRequestId" => trim($gcRequestId),
            "partnerId" => trim($this->partnerId),
            "value" =>
                array(
                    "currencyCode" => trim($this->currencyCode),
                    "amount" => intval($amount)
                )
        );


        $payload = json_encode($data);

        return $this->request('CreateGiftCard', $payload);
    }

    public function cancelGiftCard($gcRequestId)
    {
        $data = array(
            "creationRequestId" => trim($gcRequestId),
            "partnerId" => trim($this->partnerId),
        );


        $payload = json_encode($data);

        return $this->request('CancelGiftCard', $payload);
    }
}