<?php

/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 6/18/2021
 * Time: 1:51 PM
 */

namespace App\Jobs;

use App\Models\MarketingCreditHistory;
use App\Models\MessageQueue;
use App\Models\V1\Business;
use App\Models\V1\BusinessRules;
use App\Models\V1\EmailProvider;
use App\Models\V1\EntityMktgPackageCredit;
use App\Services\ResqueQueueLogService;
use App\Traits\FakeContactRuleTrait;
use App\Traits\HasFeatureFlag;
use App\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use KangarooRewards\Common\Traits\EntityMarketingPackageCreditTrait;

class TransactionalMailMessage extends MailMessage
{
    private $marketingCredit;
    private $businessId;
    private $business;
    private $userId;
    private $user;
    private $countingCredit;
    private $createMessageQueue;
    private $featureFlagTransactionalCredit;
    private $jobId;

    /** @var bool
     *  Read from business rule to know if always sending the transactional message when credit is not enough.
     *  false: always sending the msg
     *  true: do not send msg when the credit is not enough
     */
    private bool $creditBasedSending = false;

    /**
     * @var bool
     * This used to only count the credits and skip sending Email
     */
    private bool $skipSendingEmail;

    /**
     * Create a new job instance.
     *
     * @param $userId
     * @param $businessId
     * @param EmailProvider|null $emailProvider
     * @param bool $countingCredit
     * @param bool $skipSendingEmail
     * @param bool $alwaysSending Always allow sending email when there is no enough credit
     * @param bool $createMessageQueue
     */
    public function __construct(
        $userId,
        $businessId,
        EmailProvider $emailProvider = null,
        bool $countingCredit = true,
        bool $skipSendingEmail = false,
        bool $alwaysSending = false,
        bool $createMessageQueue = true
    ) {
        parent::__construct($emailProvider);
        $this->userId = $userId;
        $this->businessId = $businessId;
        $businessRule = BusinessRules::where('business_fk', $businessId)->first();
        $this->featureFlagTransactionalCredit = (bool)$businessRule->txnal_msg_flag;
        $this->skipSendingEmail = $skipSendingEmail;
        if ($businessId === KNG_BUSINESS_ID) {
            $this->countingCredit = false;
        } else {
            if ($alwaysSending) {
                $this->creditBasedSending = false;
            } else {
                $this->creditBasedSending = (bool)$businessRule->txnal_msg_credit_based;
            }
            $this->countingCredit = $countingCredit;
        }
        $this->createMessageQueue = $createMessageQueue;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Throwable
     */
    public function handle(): void
    {
        Log::info(__METHOD__, ['message' => 'Validate TransactionalTextMessage', 'email' => $this->to]);

        if (!$this->isValid()) {
            Log::info(__METHOD__, ['message' => 'INVALID TransactionalTextMessage', 'email' => $this->to]);
            return;
        }

        Log::info(__METHOD__ . ' Transactional email start', $this->getLogArray());

        DB::connection('tenant')->beginTransaction();
        try {
            if (!$this->skipSendingEmail) {
                parent::handle();
            }

            $this->createTransactionalMail();

            DB::connection('tenant')->commit();
        } catch (\Exception $e) {
            DB::connection('tenant')->rollback();
            $resqueQueueLogService = new ResqueQueueLogService();
            $resqueQueueLogService->create([
                'job_id' =>  $this->job->uuid(),
                'account_id' => $this->businessId,
                'task_name' => 'transactional.mail.sent',
                'created_at' => now(),
                'finished_at' => now(),
                'log_info' => [
                    'message' => 'Transactional Email Message Not Sent',
                    'data' => $this->getLogArray(),
                    'exception' => $e->getMessage()
                ]
            ]);
            Log::error($e->getTraceAsString(), ['data' => $this->getLogArray(),]);
        }
    }

    /**
     * @throws \Exception
     */
    private function isValid(): bool
    {
        if (FakeContactRuleTrait::isFakeEmail($this->to)) {
            info(__METHOD__ . ' Fake email', [
                'userId' => $this->userId,
                'email' => $this->to,
            ]);
            return false;
        }

        if (!$this->featureFlagTransactionalCredit) {
            return true;
        }

        if (!$this->countingCredit) {
            return true;
        }

        $credit = $this->getMarketingCredit();
        if (!$this->creditBasedSending) {
            return true;
        }

        if (!$this->skipSendingEmail) {
            if ($credit->current_email <= 0) {
                info(__METHOD__ . ' No enough credit - transactional email', $this->getLogArray());
                return false;
            }
        }
        return true;
    }

    private function getMarketingCredit()
    {
        if (!$this->marketingCredit) {
            if ($this->countingCredit && $this->featureFlagTransactionalCredit) {
                $business = $this->getBusiness();
                $credit = EntityMarketingPackageCreditTrait::getPackageCreditCurrentMonth(
                    $business->business_pk, $business->entity_type_fk, $business->branch_timezone_fk, null
                );
                if (!$credit) {
                    throw new \Exception('No entity marketing credit found');
                }

                $this->marketingCredit = EntityMarketingPackageCreditTrait::selectForUpdate(
                    $credit->entity_mktg_package_credit_pk
                );
            } else {
                $this->marketingCredit = null;
            }
        }

        return $this->marketingCredit;
    }

    private function getBusiness()
    {
        if (!isset($this->business)) {
            $this->business = Business::findByPk($this->businessId);
        }

        return $this->business;
    }

    private function getUser()
    {
        if (!isset($this->user) && isset($this->userId)) {
            $this->user = User::find($this->userId);
        }

        return $this->user;
    }

    private function createTransactionalMail()
    {
        $messageQueueRecord = $this->createMessageQueue();
        if ($this->countingCredit && $this->featureFlagTransactionalCredit) {
            $credit = $this->getMarketingCredit();
            $newCredit = $credit->current_email - 1;
            $credit->update(['current_email' => $newCredit]);
            info(__METHOD__ . ' credit updated new credit: ' . $newCredit, $this->getLogArray());
            $this->createMarketCreditHistory($credit, $messageQueueRecord->message_queue_pk ?? null);
        }
    }

    private function createMessageQueue()
    {
        if (!$this->createMessageQueue) {
            return null;
        }
        $business = $this->getBusiness();
        $user = $this->getUser();
        if (isset($this->message->to[0]['address'])) {
            $sendTo = $this->message->to[0]['address'];
        } elseif (isset($user->email)) {
            $sendTo = $user->email;
        } else {
            $sendTo = $this->to;
        }

        $messageQueue = MessageQueue::create([
            'send_to' => $sendTo,
            'cell_email' => MESSAGE_SEND_BY_EMAIL,
            'user_fk' => $user->id ?? null,
            'business_fk' => $business->business_pk,
            'email_sms_status_fk' => 3,  //2: queued; 3: sent; 4: delivered; 5 failed;
            'params' => json_encode([
                'subject' => $this->message->subject ?? null,
                'view' => $this->message->view ?? null,
                'text_view' => $this->message->textView ?? null,
                'view_data' => $this->message->viewData ?? null,
                'from' => $this->message->from ?? null,
                'html' => $this->message->view ?? $this->message->render(),
                'platformName' => 'Members-API'
            ]),
            'lastattempt' => date('Y-m-d H:i:s')
        ]);
        info(__METHOD__ . ' message queue created id:' . $messageQueue->message_queue_pk, $this->getLogArray());
        return $messageQueue;
    }

    private function createMarketCreditHistory($credit, $messageQueueRecordId)
    {
        $business = $this->getBusiness();
        $history = MarketingCreditHistory::create([
                'transaction_type' => 'SUB',
                'credits_email' => -1,
                'credits_sms' => 0,
                'credits_push' => 0,
                'balance_email' => $credit->current_email,
                'balance_sms' => $credit->current_sms,
                'balance_push' => $credit->current_push_notif,
                'entity_campaign_fk' => null,
                'survey_broadcast_fk' => null,
                'business_fk' => $business->business_pk,
                'entity_mktg_package_credit_fk' => $credit->entity_mktg_package_credit_pk,
                'message_queue_fk' => $messageQueueRecordId,
                'type' => 3
            ]);
        info(__METHOD__ . ' marketing credit history created id:' . $history->id, $this->getLogArray());
    }

    private function getLogArray(): array
    {
        return [
            'userId' => $this->user->id ?? $this->userId ?? null,
            'email' => $this->to,
            'businessId' => $this->business->business_pk ?? $this->businessId ?? null,
            'credit' => $this->marketingCredit->current_email ?? null,
            'skipSendingEmail' => $this->skipSendingEmail,
            'creditBasedSending' => $this->creditBasedSending,
            'countingCredit' => $this->countingCredit,
            'featureFlagTransactionalCredit' => $this->featureFlagTransactionalCredit,
        ];
    }
}
