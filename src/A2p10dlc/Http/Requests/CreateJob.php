<?php

namespace KangarooRewards\A2p10dlc\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Log;
use KangarooRewards\A2p10dlc\A2p;
use KangarooRewards\Common\Helpers\Utils;

class CreateJob extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $traceId = A2p::TRACE_ID . 'F-CJ-RUL-' . ($this->business_id ?? '');

        Log::info(__METHOD__, [
            'traceId' => $traceId,
            'data' => $this->all(),
        ]);

        $commonRules = [
            'intent' => 'required|in:a2p_registration,delete_sub_account',
        ];

        $a2pRegistration = [
            'business_id' => 'required|exists:tenant.business,business_pk',
            'name' => 'required|max:50',
            'first_name' => 'required|max:30',
            'last_name' => 'required|max:30',
            'phone_number' => 'required|max:20',
            'address' => 'required|array',
            'address.street' => 'required|max:30',
            'address.city' => 'required|max:30',
            'address.region' => 'required|max:30',
            'address.postal_code' => 'required|max:10',
            'address.country_iso' => 'required|max:2|in:US',
            'email' => 'required|email',
            'business_title'=> 'required|max:30',
            'job_position'=> 'required|max:100',
            'website_url'=> 'required|url|max:100',
            'business_regions_of_operation' => 'required|max:100',
            'business_type' => 'required|max:100',
            'business_registration_identifier' => 'required|in:EIN,DUNS,CCN,CN,ACN,CIN,VAT,VATRN,RN,Other',
            'business_identity' => 'required|max:50',
            'business_industry' => 'required|max:50',
            'business_registration_number' => 'required|max:50',
            'company_type' => 'required|max:50',
            'daily_volume' => 'required|in:low,high',
        ];

        $deleteSubAccount = [
            'business_id' => 'required|exists:tenant.business,business_pk',
        ];

        if ($this->intent === 'a2p_registration') {
            return array_merge($a2pRegistration, $commonRules);
        }

        if ($this->intent === 'delete_sub_account') {
            return array_merge($deleteSubAccount, $commonRules);
        }

        return $commonRules;
    }

    public function prepareForValidation()
    {
        if (isset($this->phone_number)) {
            $phone = Utils::getFormattedPhoneNumber($this->phone_number, $this->address['country_iso'], 'E164');
            $this->merge(['phone_number' => $phone]);
        }
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if (isset($this->phone_number)) {
                if (!Utils::isValidPhone($this->phone_number, $this->address['country_iso'])) {
                    $validator->errors()->add('phone_number', 'The phone number is invalid');
                }
            }
        });
    }
}
