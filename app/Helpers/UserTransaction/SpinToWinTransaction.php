<?php

namespace App\Helpers\UserTransaction;

use App\Models\PrizeWin;
use App\Models\SpinToWin;
use App\Models\UserPrizeWin;
use App\Models\V1\Activity;
use App\Models\V1\BusinessRules;
use App\Models\V1\Offer;
use App\Models\V1\Customer;
use App\Models\V1\Transaction;
use App\Models\V1\TransactionDetails;
use App\Models\V1\UserEntityPoints;
use App\Traits\SpinToWinTrait;
use App\Traits\UserPrizeWinTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class SpinToWinTransaction extends TransactionBuilder
{
    protected $defaultBranch;
    protected $businessRules;
    protected $spinToWin;
    protected $userPrizeWin;
    protected $prizeWin;
    protected $prizePoints;
    protected $prizeOffer;

    /**
     * Set the initial details
     *
     * <AUTHOR>
     * @since 13/07/2022
     */
    public function init()
    {
        $business = $this->getFinalBusiness();

        //Set the default branch
        $this->defaultBranch = $this->business->defaultBranch();

        $this->userPrizeWin = UserPrizeWin::getUserPrizeSpinByPk($this->data['user_prize_win_id']);

        $this->spinToWin = SpinToWinTrait::getActiveSpinToWinById($this->business->business_pk, $this->userPrizeWin['spin_win_fk']);

        //Set prize win details
        $this->prizeWin = PrizeWin::getByPk($this->userPrizeWin['prize_win_fk']);
        
        //Set the prize points
        $this->prizePoints = ($this->prizeWin['prize_spin_type_fk'] == 2) ? $this->prizeWin['value'] : 0;

        //Set the prize offer
        if(!empty($this->prizeWin) && $this->prizeWin['prize_spin_type_fk'] == 4){
            $this->prizeOffer = Offer::getDetails((int) $this->prizeWin['offer_fk']);
        }else{
            $this->prizeOffer = null;
        }

        //Set the business rules
        $this->businessRules = BusinessRules::forBusinessWithTaxes($business->business_pk);
    }

    /**
     * Validate
     *
     * <AUTHOR>
     * @since 13/07/2022
     * @throws \Exception
     */
    public function validate()
    {
        //Validate prize win
        if (empty($this->userPrizeWin)) {
            throw new \Exception("Invalid prize win");
        }

        //Validate prize win
        if ($this->userPrizeWin->enabled = 0 || !empty($this->userPrizeWin->transaction_fk)) {
            throw new \Exception("Invalid prize win");
        }

        //Validate trx sub type
        if (empty($this->getTrxSubType())) {
            throw new \Exception("Invalid transaction sub type ID");
        }

        if (!$this->businessRules) {
            throw new \Exception("Business Rules not found for business: " . $this->businessRules->business_pk, 1);
        }
    }

    /**
     * Prepare Attributes for Transaction Model to be created
     *
     * @since 13/07/2022
     * <AUTHOR>
     * @access protected
     * @return mixed - attributes for Transaction model
     */
    protected function prepareTransactionParams()
    {
        $businessType = $this->business->entity_type_fk;

        $t = [];
        if ($businessType == BUSINESS_POINTS) {
            $t['entity_fk'] = $this->defaultBranch->business_branch_pk;
            $t['entity_type_fk'] = BRANCH_POINTS;
            $t['transaction_type_fk'] = TRX_TYPE_DRAW; // 13
            $t['transaction_sub_type_fk'] = $this->getTrxSubType(); // 58
        }  else {
            throw new \LogicException("Error Preparing Transaction Params", 1);
        }

        $t['user_fk'] = $this->customer->id;
        $t['timezone_mysql_fk'] = $this->defaultBranch->branch_timezone_fk;

        return $t;
    }

    protected function preparePurchaseParams()
    {
        // There is no purchase
    }

    /**
     * Return the trx sub type id for the prize spin
     *
     * <AUTHOR>
     * @since 13/07/2022
     * @return int|null
     */
    protected function getTrxSubType()
    {
        $trxSubType = null;

        if ($this->prizeWin->prize_spin_type_fk == 1) {
            $trxSubType = TRX_SUBTYPE_SPIN_DRAW_FREE_PRODUCT;
        } else if ($this->prizeWin->prize_spin_type_fk == 2) {
            $trxSubType = TRX_SUBTYPE_SPIN_DRAW_FREE_POINTS;
        } else if ($this->prizeWin->prize_spin_type_fk == 3) {
            $trxSubType = TRX_SUBTYPE_SPIN_DRAW_PORCENTAGE_OFF;
        } else if ($this->prizeWin->prize_spin_type_fk == 4) {
            $trxSubType = TRX_SUBTYPE_SPIN_DRAW_OFFER;
        }

        return $trxSubType;
    }

    /**
     * Updates user balance for a specific business & create transaction details
     *
     * @since 13/07/2022
     * <AUTHOR>
     * @access protected
     * @return void
     * @throws \Exception
     */
    protected function updateBalance()
    {
        if (!$this->transaction) {
            throw new \Exception("A Transaction MUST be created first", 1);
        }

        if ($this->prizeWin->prize_spin_type_fk == 2) {
            //Increase user points
            $balance = UserEntityPoints::increaseBalance($this->customer, $this->business, $this->prizeWin->value);
        } elseif ($this->prizeWin->prize_spin_type_fk == 4) {

            DB::connection('tenant')->beginTransaction();
            try {
                //Set the intent for the transaction builder
                $data['intent'] = "get_coupon";

                $trxFactory = new TransactionFactory($data, $this->business, $this->customer);
                $trxFactory->validate();
                $trxFactory->makeTransaction();

                $claimCouponResult  = $trxFactory->getResult();

                echo json_encode(['updateBalance' => $claimCouponResult]);
                exit;
                    
                DB::connection('tenant')->commit();
            } catch (\LogicException $e) {
                DB::connection('tenant')->rollBack();
                Log::error(__METHOD__, ['exception' => $e]);
                throw new \LogicException($e);
            } catch (Exception $e) {
                DB::connection('tenant')->rollBack();
                Log::error(__METHOD__, ['exception' => $e]);
                throw new Exception($e);
            }

            //Increase user points
            $balance = $this->getBalance();
        } else {
            //Return user balance
            $balance = $this->getBalance();
        }

        // Create Transaction Details
        TransactionDetails::create([
            'transaction_fk' => $this->transaction->transaction_pk,
            'points' => $this->prizePoints,
            'amount' => 0,
            'points_balance' => $balance->current_active_points,
            'giftcard_balance' => (float) $balance->giftcard,
        ]);
    }

    /**
     * Build transaction. The order is important
     *
     * @return mixed
     * @throws \Exception
     */
    public function build()
    {
        $this->createTransaction();

        $this->transaction = $this->getTransaction();

        $this->updateBalance();

        $this->createActivity();

        $this->createNotification();

        return $this->getTransaction();
    }

    public function createActivity()
    {
        return Activity::create([
            'description' => 'ACTIVITY_SPIN_WIN',
            'utc_publish' => date('Y-m-d H:i:s'),
            'units_exchange' => (int) $this->prizePoints,
            'transaction_fk' => $this->transaction->transaction_pk,
            'hidden' => $this->businessRules->silo_flag,
            'timezone_mysql_fk' => $this->defaultBranch->branch_timezone_fk,
        ]);
    }

    public function getResult()
    {
        $balance = UserEntityPoints::transformItem($this->getBalance());

        return [
            "show_spin_win" => UserPrizeWinTrait::showSpinWin($this->customer->id, $this->business->business_pk, $this->businessRules->activate_spin_win, $this->spinToWin),
            "balance" => $balance
        ];
    }
}