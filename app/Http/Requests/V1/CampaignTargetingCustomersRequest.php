<?php

namespace App\Http\Requests\V1;

use App\Helpers\Campaign\CampaignBinder;
use App\Models\V1\Business;
use App\Models\V1\BusinessBranch;
use App\Rules\BusinessBranchUidExists;
use App\Rules\BusinessCampaignValidation;
use App\Rules\BusinessOfferValidation;
use App\Rules\BusinessProductRewardsValidation;
use App\Rules\BusinessProductValidation;
use App\Rules\BusinessRedemptionValidation;
use App\Rules\BusinessTierExists;
use App\Rules\CustomerEmailOrPhoneValidation;
use App\Rules\PosEcomAccountValidation;
use App\Traits\EntityCampaignTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class CampaignTargetingCustomersRequest extends FormRequest
{
    protected object $business;

    public function authorize(): bool
    {
        $this->setBusiness();

        return true;
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        $business = $this->business;
        $businessBranches = BusinessBranch::allowedBranchIdsForBusiness($business->business_pk);

        return [
            'send_by' => 'required|integer|in:1,2,3,4,5', //email_sms:1, push:2, email:3, sms:4, 5:sms_push
            'send_by_priority' => [
                'required', //'required_if:send_by,1',
                'string',
                Rule::in(['email,sms', 'sms,email', 'email,sms,push', 'email,push,sms', 'sms,email,push', 'sms,push,email', 'push,email,sms', 'push,sms,email', 'sms,push', 'push,sms', 'email', 'sms', 'push']),
            ],
            'apply_and_criteria' => 'sometimes|boolean',
            'exclude_dormants' => 'boolean',
            'auto' => 'required|boolean',
            'exclude_customer_in_previous_campaign' => 'sometimes|boolean',
            'exclude_customer_in_previous_campaign_criteria' => 'required_with:exclude_customer_in_previous_campaign_ids|integer|in:0,1', // or, and
            'exclude_customer_in_previous_campaign_ids' => 'required_with:exclude_customer_in_previous_campaign_criteria|array',
            'exclude_customer_in_previous_campaign_ids.*' => [
                'required_with:exclude_customer_in_previous_campaign_ids',
                'integer',
                new BusinessCampaignValidation($business, false)
            ],
            'exclude_customer_in_previous_campaign_past_days' => 'integer|min:0|max:999999',
            'exclude_customer_in_previous_campaign_past_hours' => 'integer|min:0|max:999999',
            'exclude_customer_in_previous_automated_campaign' => 'boolean',
            'exclude_customer_in_previous_automated_campaign_criteria' => 'required_with:exclude_customer_in_previous_automated_campaign_ids|integer|in:0,1', // or, and
            'exclude_customer_in_previous_automated_campaign_ids' => 'required_with:exclude_customer_in_previous_automated_campaign_criteria|array',
            'exclude_customer_in_previous_automated_campaign_ids.*' => [
                'required_with:exclude_customer_in_previous_automated_campaign_ids',
                'integer',
                new BusinessCampaignValidation($business, true)
            ],
            'exclude_customer_in_previous_automated_campaign_past_days' => 'integer|min:0|max:999999',
            'exclude_customer_in_previous_automated_campaign_past_hours' => 'integer|min:0|max:999999',

            'targeted_customer_criteria' => 'required|array|min:1',
            'targeted_customer_criteria.*.id' => 'required_with:targeted_customer_criteria|integer|in:' . implode(",", CampaignBinder::getAllFilterIds()), //9:birthday campaign

            // Customers made X purchases in last X weeks
            'targeted_customer_criteria.*.purchase_weeks_number' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WEEKS_PURCHASES.'|integer|min:1|max:100',
            'targeted_customer_criteria.*.purchases_number' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WEEKS_PURCHASES.'|integer|min:0|max:999',
            'targeted_customer_criteria.*.purchase_weeks_operator' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WEEKS_PURCHASES.'|integer|in:1,2', //1: more than or equal, 2: less than or equal

            // Top spending customer filter
            'targeted_customer_criteria.*.top_spending_customers' => 'required_if:targeted_customer_criteria.*.id,6|integer|min:1|max:500',

            // Upcoming birthday filters
            'targeted_customer_criteria.*.birthday_customers_criteria' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_UPCOMING_BIRTHDAYS.'|integer|in:1,2,3', // 1: customer, 2: customers without tiers, 3: customer tiers list
            'targeted_customer_criteria.*.birthday_notification_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_UPCOMING_BIRTHDAYS.'|integer|min:1|max:364',
            'targeted_customer_criteria.*.birthday_customers_types_list' => 'required_if:targeted_customer_criteria.*.birthday_customers_criteria,3|array',
            'targeted_customer_criteria.*.birthday_customers_types_list.*' => [
                'required_with:targeted_customer_criteria.*.birthday_customers_types_list',
                'integer',
                'digits_between:1,15',
                new BusinessTierExists($business)
            ],

            // Auto - Customers who have made their last purchase exactly X days ago
            'targeted_customer_criteria.*.last_purchase_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_LAST_PURCHASE.'|integer|min:0|max:999',
            'targeted_customer_criteria.*.last_purchase_days_range_operator' => 'sometimes|integer|in:1,2',
            'targeted_customer_criteria.*.last_purchase_days_range_end' => 'required_if:targeted_customer_criteria.*.last_purchase_days_range_operator,2|integer|gt:targeted_customer_criteria.*.last_purchase_days',
            'targeted_customer_criteria.*.last_purchase_branches' => 'sometimes|array',
            'targeted_customer_criteria.*.last_purchase_branches.*' => [
                'required_with:targeted_customer_criteria.*.last_purchase_branches',
                'string',
                new BusinessBranchUidExists($business)
            ],

            // Customers who have Postal / Zip Code
            'targeted_customer_criteria.*.customers_postal_code' => [
                'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_ALL_CUSTOMERS_HAVING_POSTAL_CODE,
                'string',
                'min:3',
                'max:20',
                Rule::exists('postal_code', 'postal_code')
            ],

            // Customer branches
            'targeted_customer_criteria.*.customer_branches' => 'sometimes|array',
            'targeted_customer_criteria.*.customer_branches.*' => [
                'required_with:targeted_customer_criteria.*.customer_branches',
                'string',
                new BusinessBranchUidExists($business)
            ],

            // New clients, X days after the First visit at Y branch
            'targeted_customer_criteria.*.after_first_visit_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_NEW_CLIENTS_AFTER_FIRST_VISIT.'|integer|min:0|max:999',
            'targeted_customer_criteria.*.after_first_visit_branches' => 'sometimes|array',
            'targeted_customer_criteria.*.after_first_visit_branches.*' => [
                'required_with:targeted_customer_criteria.*.after_first_visit_branches',
                'string',
                new BusinessBranchUidExists($business)
            ],

            // Specific customer
            'targeted_customer_criteria.*.specific_customer_email_phone' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_SPECIFIC_CUSTOMER.'|string|min:5|max:200',

            // Product bought from À La Carte Rewards
            'targeted_customer_criteria.*.product_reward_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_BOUGHT_FROM_A_LA_CARTE.'|array',
            'targeted_customer_criteria.*.product_reward_list.*' => [
                'required_with:targeted_customer_criteria.*.product_reward_list',
                'integer',
                new BusinessProductRewardsValidation($businessBranches)
            ],

            // Customers who have less/more/between X points
            'targeted_customer_criteria.*.customer_points_operator' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_POINTS.'|integer|in:1,2,3', // 1:gte, 2: Lte, 3:btw
            'targeted_customer_criteria.*.customer_have_points' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_POINTS.'|integer|min:0|max:99999',
            'targeted_customer_criteria.*.customer_have_end_points' => 'required_if:targeted_customer_criteria.*.customer_points_operator,3|integer|min:0|max:99999|gt:targeted_customer_criteria.*.customer_have_points',

            // Customers with first visit between X and Y dates at specific branches
            'targeted_customer_criteria.*.visits_start_date' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WITH_FIRST_VISIT.'|date|date_format:Y-m-d',
            'targeted_customer_criteria.*.visits_end_date' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WITH_FIRST_VISIT.'|date|date_format:Y-m-d|after_or_equal:targeted_customer_criteria.*.visits_start_date',
            'targeted_customer_criteria.*.visits_branches' => 'sometimes|array',
            'targeted_customer_criteria.*.visits_branches.*' => [
                'required_with:targeted_customer_criteria.*.visits_branches',
                'string',
                new BusinessBranchUidExists($business)
            ],

            // Customers who spent more than / less than X $ in the last X weeks
            'targeted_customer_criteria.*.spending_weeks_operator' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WEEKS_SPENDING.'|integer|in:1,2', // 1:gte, 2: Lte
            'targeted_customer_criteria.*.spending_weeks_number' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WEEKS_SPENDING.'|integer|min:0|max:999',
            'targeted_customer_criteria.*.spending_weeks_amount' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WEEKS_SPENDING.'|integer|min:0|max:99999',

            // Customer tiers status
            'targeted_customer_criteria.*.customer_types_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_TIERS.'|array',
            'targeted_customer_criteria.*.customer_types_list.*' => [
                'required_with:targeted_customer_criteria.*.customer_types_list',
                'integer',
                'digits_between:1,15',
                new BusinessTierExists($business)
            ],

            // Segments
            'targeted_customer_criteria.*.segments_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_SEGMENTS.'|array',
            'targeted_customer_criteria.*.segments_list.*' => [
                'required_with:targeted_customer_criteria.*.segments_list',
                'integer',
                Rule::exists('user_segments', 'user_segment_pk')
            ],

            // Customers with lifetime balance more than / less than X pts
            'targeted_customer_criteria.*.lifetime_balance_operator' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_LIFETIME_BALANCE.'|integer|in:1,2', // 1:gte, 2: Lte
            'targeted_customer_criteria.*.lifetime_balance' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_LIFETIME_BALANCE.'|integer|min:0|max:9999',

            // Customer tags
            'targeted_customer_criteria.*.customer_tags_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_TAGS.'|array',
            'targeted_customer_criteria.*.customer_tags_list.*' => [
                'required_with:targeted_customer_criteria.*.customer_tags_list',
                'integer',
                Rule::exists('tenant.tags', 'tag_pk')->where(function ($query) use ($business) {
                    return $query->where([
                        'business_fk' => $business->business_pk,
                        'enabled' => 1
                    ]);
                })
            ],

            // Customers who haven't made purchases in the last X days
            'targeted_customer_criteria.*.no_purchases_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_NOT_MADE_PURCHASES.'|integer|min:0|max:999',
            'targeted_customer_criteria.*.no_purchases_flag' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_NOT_MADE_PURCHASES.'|integer|in:1,2', // 1:have, 2: have not
            'targeted_customer_criteria.*.no_purchases_days_range_operator' => 'sometimes|integer|in:1,2',
            'targeted_customer_criteria.*.no_purchases_days_range_end' => 'required_if:targeted_customer_criteria.*.no_purchases_days_range_operator,2|integer|gt:targeted_customer_criteria.*.no_purchases_days',
            'targeted_customer_criteria.*.no_purchases_branches' => 'sometimes|array',
            'targeted_customer_criteria.*.no_purchases_branches.*' => [
                'required_with:targeted_customer_criteria.*.no_purchases_branches',
                'string',
                new BusinessBranchUidExists($business)
            ],

            // Auto - Customers who have made X earning/redemptions
            'targeted_customer_criteria.*.earn_or_reward_option' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_MADE_EARN_REWARD.'|integer|in:1,2', // 1: Reward, 2:Redemption
            'targeted_customer_criteria.*.earn_or_reward_number' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_MADE_EARN_REWARD.'|integer|min:1|max:999',


            // Customers signed up through referrals and status
            'targeted_customer_criteria.*.referral_status_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REFERRALS_SIGNED_UP.'|array',
            'targeted_customer_criteria.*.referral_status_list.*' => 'required_with:referral_status_list|integer|in:1,2,3', // 1:pending, 2: cond. met, 3:cod. not met

            // Customers whose preferred languages
            'targeted_customer_criteria.*.customers_languages_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_PREFERRED_LANGUAGE.'|array',
            'targeted_customer_criteria.*.customers_languages_list.*' => [
                'required_with:targeted_customer_criteria.*.customers_languages_list',
                'integer',
                Rule::exists('tenant.language', 'language_pk')->where(function ($query) use ($business) {
                    return $query->where([
                        'enabled' => 1
                    ]);
                })
            ],

            // Auto - Customers who registered, but didn't make a purchase in the last X days
            'targeted_customer_criteria.*.register_no_transaction_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REGISTER_NO_TRANSACTION.'|integer|min:0|max:999',

            // Customers who have/not left a product review on the website in the last X days
            'targeted_customer_criteria.*.product_review_operator' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_PRODUCT_REVIEWS.'|integer|in:1,2', // 1: Have, 2: Have not
            'targeted_customer_criteria.*.product_review_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_PRODUCT_REVIEWS.'|integer|min:0|max:999',

            // Customers who redeemed specific offer(s) in the X days at specific branches
            'targeted_customer_criteria.*.customers_offer_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REDEEMED_OFFERS.'|integer|min:0|max:999',
            'targeted_customer_criteria.*.customers_offer_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REDEEMED_OFFERS.'|array',
            'targeted_customer_criteria.*.customers_offer_list.*' => [
                'required_with:targeted_customer_criteria.*.customers_offer_list',
                'integer',
                new BusinessOfferValidation($business, $businessBranches)
            ],
            'targeted_customer_criteria.*.customers_offer_branches' => 'sometimes|array',
            'targeted_customer_criteria.*.customers_offer_branches.*' => [
                'required_with:targeted_customer_criteria.*.customers_offer_branches',
                'string',
                new BusinessBranchUidExists($business)
            ],

            // Customers who redeemed specific redemption(s) in the X days at specific branches
            'targeted_customer_criteria.*.customers_redemption_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REDEEMED_REDEMPTION.'|integer|min:0|max:999',
            'targeted_customer_criteria.*.customers_redemption_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REDEEMED_REDEMPTION.'|array',
            'targeted_customer_criteria.*.customers_redemption_list.*' => [
                'required_with:targeted_customer_criteria.*.customers_redemption_list',
                'integer',
                new BusinessRedemptionValidation($businessBranches)
            ],
            'targeted_customer_criteria.*.customers_redemption_branches' => 'sometimes|array',
            'targeted_customer_criteria.*.customers_redemption_branches.*' => [
                'required_with:targeted_customer_criteria.*.customers_redemption_branches',
                'string',
                new BusinessBranchUidExists($business)
            ],

            // Customers who have more than/less than/between X store credit
            'targeted_customer_criteria.*.store_credit_operator' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_STORE_CREDIT.'|integer|in:1,2,3', // 1:more than, 2:less than, 3:btw
            'targeted_customer_criteria.*.store_credit_amount' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_STORE_CREDIT.'|integer|min:0|max:9999',
            'targeted_customer_criteria.*.store_credit_end_amount' => 'required_if:targeted_customer_criteria.*.store_credit_operator,3|integer|max:9999|gt:targeted_customer_criteria.*.store_credit_amount',

            // Customers who have referred between X and Y dates
            'targeted_customer_criteria.*.referred_start_date' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REFERRED.'|date|date_format:Y-m-d',
            'targeted_customer_criteria.*.referred_end_date' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REFERRED.'|date|date_format:Y-m-d|after_or_equal:targeted_customer_criteria.*.referred_start_date',

            // Auto - Remind customers their points will expire X days before expiration
            'targeted_customer_criteria.*.pts_expire_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_POINT_EXPIRE.'|integer|min:1|max:99',

            // Auto - Remind customers their points will expire for inactivity
            'targeted_customer_criteria.*.pts_expire_inactive_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_POINT_EXPIRE_FOR_INACTIVITY.'|integer|min:1|max:99',

            // Customers who have booked appointment types between X & Y dates
            'targeted_customer_criteria.*.booked_appointment_start_date' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_BOOKED_APPOINTMENTS_BETWEEN_DATES.'|date|date_format:Y-m-d',
            'targeted_customer_criteria.*.booked_appointment_end_date' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_BOOKED_APPOINTMENTS_BETWEEN_DATES.'|date|date_format:Y-m-d|after_or_equal:targeted_customer_criteria.*.booked_appointment_start_date',

            //Customers who have/not booked the following appointment types in the last X days
            'targeted_customer_criteria.*.booking_appointment_operator' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_BOOKED_SPECIFIC_APPOINTMENTS_PER_DAYS.'|integer|in:1,2', // 1:Have, 2:Have not
            'targeted_customer_criteria.*.booking_appointment_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_BOOKED_SPECIFIC_APPOINTMENTS_PER_DAYS.'|integer|min:0|max:999',
            'targeted_customer_criteria.*.booking_appointments_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_BOOKED_SPECIFIC_APPOINTMENTS_PER_DAYS.'|array',
            'targeted_customer_criteria.*.booking_appointments_list.*' => [
                'required_with:targeted_customer_criteria.*.booking_appointments_list',
                'integer',
                new BusinessProductValidation($businessBranches)
            ],

            // Auto - Customers who have reached a specific tier
            'targeted_customer_criteria.*.moved_from_tiers_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_CUSTOMERS_REACHED_SPECIFIC_TIER.'|array',
            'targeted_customer_criteria.*.moved_from_tiers_list.*' => [
                'required_with:targeted_customer_criteria.*.moved_from_tiers_list',
                'integer',
                'digits_between:1,15',
                new BusinessTierExists($business)
            ],
            'targeted_customer_criteria.*.moved_to_tiers_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_CUSTOMERS_REACHED_SPECIFIC_TIER.'|array|different:targeted_customer_criteria.*.moved_from_tiers_list',
            'targeted_customer_criteria.*.moved_to_tiers_list.*' => [
                'required_with:targeted_customer_criteria.*.moved_to_tiers_list',
                'integer',
                'digits_between:1,15',
                new BusinessTierExists($business)
            ],

            // Customers who registered between dates
            'targeted_customer_criteria.*.customers_registered_start_date' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REGISTERED_BETWEEN_DATES.'|date|date_format:Y-m-d',
            'targeted_customer_criteria.*.customers_registered_end_date' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REGISTERED_BETWEEN_DATES.'|date|date_format:Y-m-d|after_or_equal:targeted_customer_criteria.*.customers_registered_start_date',

            // Send to a specific customers list (email/phone)
            'targeted_customer_criteria.*.specific_customer_email_phone_array' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_SPECIFIC_CUSTOMERS_LIST.'|array',
            'targeted_customer_criteria.*.specific_customer_email_phone_array.*' => [
                'required_with:specific_customer_email_phone_array',
                new CustomerEmailOrPhoneValidation($business)
            ],

            // Customers who made more than x referrals with status
            'targeted_customer_criteria.*.referred_customers_number' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_REFERRED_CUSTOMERS_NUMBER.'|integer|min:0|max:999',
            'targeted_customer_criteria.*.referred_customers_status_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_REFERRED_CUSTOMERS_NUMBER.'|array',
            'targeted_customer_criteria.*.referred_customers_status_list.*' => 'required_with:referred_customers_status_list|integer|in:1,2,3', // 1:pending, 2: cond. met, 3:cod. not met

            // Campaigned Offers/Redemptions
            'targeted_customer_criteria.*.customers_campaigned_offers_redemptions_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_CAMPAIGNED_OFFERS_REDEMPTIONS.'|integer|min:0|max:999',
            'targeted_customer_criteria.*.customers_campaigned_offers_redemptions_operator' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_CAMPAIGNED_OFFERS_REDEMPTIONS.'|integer|in:1,2', //1: have, 2:have not
            'targeted_customer_criteria.*.customers_campaigned_offers_redemptions_campaign_ids' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_CAMPAIGNED_OFFERS_REDEMPTIONS.'|array',
            'targeted_customer_criteria.*.customers_campaigned_offers_redemptions_campaign_ids.*' => [
                'required_with:targeted_customer_criteria.*.customers_campaigned_offers_redemptions_campaign_ids',
                'integer',
                'digits_between:1,15',
                new BusinessCampaignValidation($business, false, true)
            ],
            'targeted_customer_criteria.*.customers_campaigned_offers_redemptions_offers_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_CAMPAIGNED_OFFERS_REDEMPTIONS.'|array',
            'targeted_customer_criteria.*.customers_campaigned_offers_redemptions_offers_list.*' => [
                'required_with:targeted_customer_criteria.*.customers_campaigned_offers_redemptions_offers_list',
                'integer',
                'digits_between:1,15',
                new BusinessOfferValidation($business, $businessBranches)
            ],
            'targeted_customer_criteria.*.customers_campaigned_offers_redemptions_redemptions_list' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_CAMPAIGNED_OFFERS_REDEMPTIONS.'|array',
            'targeted_customer_criteria.*.customers_campaigned_offers_redemptions_redemptions_list.*' => [
                'required_with:targeted_customer_criteria.*.customers_campaigned_offers_redemptions_redemptions_list',
                'integer',
                'digits_between:1,15',
                new BusinessRedemptionValidation($businessBranches)
            ],
            'targeted_customer_criteria.*.customers_campaigned_offers_redemptions_branches' => 'sometimes|array',
            'targeted_customer_criteria.*.customers_campaigned_offers_redemptions_branches.*' => [
                'required_with:targeted_customer_criteria.*.customers_campaigned_offers_redemptions_branches',
                'string',
                new BusinessBranchUidExists($business)
            ],

            // Shopify filters
            'targeted_customer_criteria.*.shopify_account' => [
//                'required_if:targeted_customer_criteria.*.id,' . implode(',', CampaignBinder::getShopifyFiltersIds()),
                'string',
                new PosEcomAccountValidation($business, POS_SYSTEM_SHOPIFY)
            ],
            'targeted_customer_criteria.*.shopify_categories' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_CATEGORIES.'|array',
            'targeted_customer_criteria.*.shopify_categories.*.account_id' => [
                'required_without:targeted_customer_criteria.*.shopify_account',
                'string',
                new PosEcomAccountValidation($business, POS_SYSTEM_SHOPIFY)
            ],
            'targeted_customer_criteria.*.shopify_categories.*.id' => 'required_with:targeted_customer_criteria.*.shopify_categories|string',
            'targeted_customer_criteria.*.shopify_categories.*.name' => 'required_with:targeted_customer_criteria.*.shopify_categories|string',
            'targeted_customer_criteria.*.shopify_categories_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_CATEGORIES.'|integer|min:1|max:999',

            'targeted_customer_criteria.*.shopify_products' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCTS.'|array',
            'targeted_customer_criteria.*.shopify_products.*.account_id' => [
                'required_without:targeted_customer_criteria.*.shopify_account',
                'string',
                new PosEcomAccountValidation($business, POS_SYSTEM_SHOPIFY)
            ],
            'targeted_customer_criteria.*.shopify_products.*.id' => 'required_with:targeted_customer_criteria.*.shopify_products|string',
            'targeted_customer_criteria.*.shopify_products.*.name' => 'required_with:targeted_customer_criteria.*.shopify_products|string',
            'targeted_customer_criteria.*.shopify_products_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCTS.'|integer|min:1|max:999',

            'targeted_customer_criteria.*.shopify_product_vendors' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_VENDORS.'|array',
            'targeted_customer_criteria.*.shopify_product_vendors.*.account_id' => [
                'required_without:targeted_customer_criteria.*.shopify_account',
                'string',
                new PosEcomAccountValidation($business, POS_SYSTEM_SHOPIFY)
            ],
            'targeted_customer_criteria.*.shopify_product_vendors.*.id' => 'required_with:targeted_customer_criteria.*.shopify_product_vendors|string',
            'targeted_customer_criteria.*.shopify_product_vendors.*.name' => 'required_with:targeted_customer_criteria.*.shopify_product_vendors|string',
            'targeted_customer_criteria.*.shopify_product_vendors_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_VENDORS.'|integer|min:1|max:999',

            'targeted_customer_criteria.*.shopify_product_tags' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_TAGS.'|array',
            'targeted_customer_criteria.*.shopify_product_tags.*.account_id' => [
                'required_without:targeted_customer_criteria.*.shopify_account',
                'string',
                new PosEcomAccountValidation($business, POS_SYSTEM_SHOPIFY)
            ],
            'targeted_customer_criteria.*.shopify_product_tags.*.id' => 'required_with:targeted_customer_criteria.*.shopify_product_tags|string',
            'targeted_customer_criteria.*.shopify_product_tags.*.name' => 'required_with:targeted_customer_criteria.*.shopify_product_tags|string',
            'targeted_customer_criteria.*.shopify_product_tags_days' => 'required_if:targeted_customer_criteria.*.id,'.CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_TAGS.'|integer|min:1|max:999',

        ];
    }

    /**
     * Convert binary branch IDs to numeric IDs
     * @param $binaryBranchIds
     * @return array|mixed|null
     */
    protected function toNumericBranchIds($binaryBranchIds): mixed
    {
        if (is_array($binaryBranchIds)) {
            $numericBranchIds = [];
            foreach ($binaryBranchIds as $branBinaryId) {
                $branch = BusinessBranch::findUid($branBinaryId, ['business_branch_pk']);
                $numericBranchIds [] = $branch?->business_branch_pk;
            }

            return $numericBranchIds;
        } else {
            $branch = BusinessBranch::findUid($binaryBranchIds, ['business_branch_pk']);

            return $branch?->business_branch_pk;
        }
    }

    /**
     * @param Validator $validator
     * @return void
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function ($validator) {
            $this->validateExcludePrevRegularCampaigns($validator);
            $this->validateExcludePrevAutoCampaigns($validator);
        });
    }

    /**
     * Used to merge the targeted criteria, specifically to merge the converted branch IDs
     * @param $key
     * @param $default
     * @return array|array[]
     */
    public function validated($key = null, $default = null): array
    {
        return array_merge(
            parent::validated(),
            [
                'targeted_customer_criteria' => $this->transformTargetedCriteria()
            ]
        );
    }

    /**
     * Merge the request data with the transforming the branches values in the criteria filters
     * Used in OfferRewardTargetingService by instantiating the request separately, without reliance on the controller's request instance
     * @return array
     */
    public function allWithTargetedCriteria(): array
    {
        return array_merge(
            $this->all(),
            [
                'targeted_customer_criteria' => $this->transformTargetedCriteria()
            ]
        );
    }

    /**
     * Transform criteria after validation
     * Used to convert branch IDs from hex to numeric
     * @return array
     */
    private function transformTargetedCriteria(): array
    {
        // Get the targeted criteria request data
        $targetedCustomerCriteria = $this->targeted_customer_criteria ?? [];

        foreach ($targetedCustomerCriteria as $key => &$criteria) {
            // Convert the value of branch IDs filters if any exists
            $this->transformBranchIdsValues($criteria);

            // Transform pos & E-com attributes
            EntityCampaignTrait::transformPosEcomAttributes($criteria, false);
        }

        return $targetedCustomerCriteria;
    }

    /**
     * Convert the value of branch IDs filters from hex to numeric
     * @param $criteria
     * @return void
     */
    public function transformBranchIdsValues(&$criteria): void
    {
        // Get branch filters
        $branchIdFilters = CampaignBinder::fetchBranchFilterNames();

        // Convert the value of branch IDs filters if any exists
        foreach ($criteria as $filterName => &$filterValue) {
            if (in_array($filterName, $branchIdFilters)) {
                $filterValue = $this->toNumericBranchIds($filterValue);
            }
        }
    }

    /**
     * Set business object
     * @param object|null $business
     * @return void
     */
    public function setBusiness(object $business = null): void
    {
        if (is_null($business)) {
            $user = $this->user();
            $this->business = Business::forUser($user);
        } else {
            $this->business = $business;
        }
    }

    /**
     * Validate excluding customers in previous regular campaigns
     * @param $validator
     * @return void
     */
    protected function validateExcludePrevRegularCampaigns($validator): void
    {
        $excludeRegCampField = $this->input('exclude_customer_in_previous_campaign');
        $excludeRegDays = $this->input('exclude_customer_in_previous_campaign_past_days');
        $excludeRegHours = $this->input('exclude_customer_in_previous_campaign_past_hours');

        // Check if the exclude field is true and both days and hours fields are missing
        if ($excludeRegCampField && empty($excludeRegDays) && empty($excludeRegHours)) {
            // Add an error message to the validator
            $validator->errors()->add('exclude_customer_in_previous_campaign', 'The exclude_customer_in_previous_campaign field requires either exclude_customer_in_previous_campaign_past_days or exclude_customer_in_previous_campaign_past_hours to be present.');
        }

        // Check if both days and hours fields are present
        if (!empty($excludeRegDays) && !empty($excludeRegHours)) {
            // Add an error message to the validator
            $validator->errors()->add('exclude_customer_in_previous_campaign_past_days', 'Only one of exclude_customer_in_previous_campaign_past_days or exclude_customer_in_previous_campaign_past_hours should be present.');
            $validator->errors()->add('exclude_customer_in_previous_campaign_past_hours', 'Only one of exclude_customer_in_previous_campaign_past_days or exclude_customer_in_previous_campaign_past_hours should be present.');
        }
    }

    /**
     * Validate excluding customers in previous auto campaigns
     * @param $validator
     * @return void
     */
    protected function validateExcludePrevAutoCampaigns($validator): void
    {
        $excludeAutoCampField = $this->input('exclude_customer_in_previous_automated_campaign');
        $excludeAutoDays = $this->input('exclude_customer_in_previous_automated_campaign_past_days');
        $excludeAutoHours = $this->input('exclude_customer_in_previous_automated_campaign_past_hours');

        // Check if the exclude field is true and both days and hours fields are missing
        if ($excludeAutoCampField && empty($excludeAutoDays) && empty($excludeAutoHours)) {
            // Add an error message to the validator
            $validator->errors()->add('exclude_customer_in_previous_automated_campaign', 'The exclude_customer_in_previous_automated_campaign field requires either exclude_customer_in_previous_automated_campaign_past_days or exclude_customer_in_previous_automated_campaign_past_hours to be present.');
        }

        // Check if both days and hours fields are present
        if (!empty($excludeAutoDays) && !empty($excludeAutoHours)) {
            // Add an error message to the validator
            $validator->errors()->add('exclude_customer_in_previous_automated_campaign_past_days', 'Only one of exclude_customer_in_previous_automated_campaign_past_days or exclude_customer_in_previous_automated_campaign_past_hours should be present.');
            $validator->errors()->add('exclude_customer_in_previous_automated_campaign_past_hours', 'Only one of exclude_customer_in_previous_automated_campaign_past_days or exclude_customer_in_previous_automated_campaign_past_hours should be present.');
        }
    }
}