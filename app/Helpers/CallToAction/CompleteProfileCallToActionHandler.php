<?php
/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 7/28/2022
 * Time: 8:50 AM
 */

namespace App\Helpers\CallToAction;


use App\Models\BusinessCallToAction;
use Illuminate\Support\Facades\DB;

class CompleteProfileCallToActionHandler extends CallToActionHandler
{
    private $requires = [];

    private function prepareForValidation(BusinessCallToAction $action)
    {
        $requires = [];
        if (isset($action->conditions_ext['requires'])) {
            foreach ($action->conditions_ext['requires'] as $require) {
                switch ($require) {
                    case 'first':
                    case 'last':
                    case 'birth_date':
                        $requires['user'][] = $require;
                        break;
                    case 'address':
                        $requires['address'][] = 'street';
                        break;
                    case 'postal_code':
                        $requires['address'][] = 'postal_code_fk';
                        break;
                    case 'consent':
                        $requires['user_business_notifications'][] = 'user_business_notifications_pk';
                        break;
                    case 'custom_field_1':
                    case 'custom_field_2':
                    case 'custom_field_3':
                    case 'custom_field_4':
                    case 'custom_field_5':
                        $requires['user_business_profile'][] = $require;
                        break;
                }
            }
        }

        return $requires;
    }

    protected function isConditionSatisfied(BusinessCallToAction $action): bool
    {
        $valid = true;
        $resources = $this->prepareForValidation($action);
        foreach ($resources as $table => $fields) {
            if ($table == 'user') {
                $queryConditions = [
                    'id' => $this->user->id,
                    'business_fk' => $this->business->business_pk
                ];
            } elseif ($table == 'address') {
                $queryConditions = [
                    'entity_fk' => $this->user->id,
                    'entity_type_fk' => 4, // USER_ENTITY,
                ];
            } else {
                $queryConditions = [
                    'user_fk' => $this->user->id,
                    'business_fk' => $this->business->business_pk
                ];
            }

            $result = DB::table($table)
                ->select($fields)
                ->where($queryConditions)
                ->first();

            $this->requires[$table] = $result;

            if ($result && $valid) {
                foreach ($fields as $field) {
                    if (empty($result->$field)) {
                        $valid = false;
                        break;
                    }
                }
            } else {
                $valid = false;
            }
        }

        if ($valid) {
            return parent::isConditionSatisfied($action);
        } else {
            return false;
        }
    }

    protected function getActionDetails(BusinessCallToAction $action, $periodSatisfied = true): array
    {
        $actionDetails = parent::getActionDetails($action, $periodSatisfied);

        return array_merge($actionDetails, ['requires' => $this->requires]);
    }
}