<?php

namespace App\Helpers\V1;

use App\Models\V1\Activity;
use App\Models\V1\BusinessRules;
use App\Models\V1\Customer;
use App\Models\V1\Notification;
use App\Models\V1\Offer;
use App\Models\V1\Purchase;
use App\Models\V1\TransactionDetails;
use App\Models\V1\UserEntityPoints;
use App\Models\V1\UserOffer;
use App\Models\V1\UserOfferStory;

class ReturnStoreCreditTransaction extends GiftCardPaymentTransaction
{
    /**
     * Create Transaction, Purchase and Activity
     * In specified order
     *
     * @return self
     * @throws \Exception
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @version 1.0
     */
    public function process(): self
    {
        /** @var ReturnStoreCreditTransaction $this */
        $this->createTransaction()
            ->createReceipt()
            ->createPurchase()
            ->updateBalance()
            ->createOrUpdateCustomizableTransactionDetails()
            ->createActivity()
            ->createNotification();

        return $this;
    }

    /**
     * Prepare Attributes for Transaction Model to be created
     *
     * @return mixed - attributes for Transaction model
     * @since 1.0
     * <AUTHOR>
     * @access protected
     * @version 1.0
     */
    protected function prepareTransactionParams(): array
    {
        $t = [];
        $t['entity_fk'] = $this->branch->business_branch_pk;
        $t['entity_type_fk'] = BRANCH_POINTS;
        $t['transaction_type_fk'] = $this->getTrxTypeId();
        $t['transaction_sub_type_fk'] = $this->getTrxSubTypeId();
        $t['user_fk'] = $this->customer->id;
        $t['timezone_mysql_fk'] = $this->branch->branch_timezone_fk;

        return $t;
    }


    protected function preparePurchaseParams()
    {
        //
    }

    protected function getTrxSubType()
    {
        //
    }

    /**
     * @return int
     */
    protected function getTrxSubTypeId(): int
    {
        return TRX_SUBTYPE_GIFTCARD_AMOUNT_REFUND_STORE_CREDIT;
    }

    /**
     * @return int
     */
    protected function getTrxTypeId(): int
    {
        return TRX_TYPE_GIFTCARD_AMOUNT;
    }

    /**
     * @return float
     */
    private function getAmount(): float
    {
        return (float) $this->data['amount'];
    }
}
