<?php
namespace KangarooRewards\Common\Scopes;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Database\Eloquent\Builder;

/**
 * Description of BusinessScope
 *
 * <AUTHOR>
 */
class BusinessScope implements Scope
{

    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $builder
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {
//        $builder->has('branches');
        $builder->where($model->getTable() . '.enabled', 1);
    }
}
