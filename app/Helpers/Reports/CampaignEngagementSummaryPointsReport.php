<?php

namespace App\Helpers\Reports;

use App\Repositories\ReportRepository;
use App\Traits\EntityCampaignTrait;
use App\Models\V1\Campaign;
use Illuminate\Database\Query\Builder;

class CampaignEngagementSummaryPointsReport extends CampaignEngagementSummaryAmountReport
{
    public const REPORT_ID = 'campaign-engagement-summary-points';

    public const CURRENCY_FIELDS = [];

    /**
     * @return Builder
     */
    protected function prepareQuery(): ?Builder
    {
        // setting campaignId if present in filtering
        $campaignId = null;
        if ($this->data->filters) {
            foreach ($this->data->filters as $filter) {
                // filter by campaign
                if ($filter->field === 'campaign_id') {
                    if ($filter->operator === 'eq') {
                        $campaignId = (int) $filter->value;
                    }
                }
            }
        }

        if (!$campaignId) {
            return null;
            //throw new \RuntimeException('Campaign ID is required');
        }

        $campaign = Campaign::findOrFail($campaignId);

        $this->data->date_from = $campaign->utc_start;
        $this->data->date_to = date('Y-m-d H:i:s');

        $previousCampaigns = EntityCampaignTrait::getAllCampaignsAfter($campaignId, $this->business->business_pk);
        $childCampaigns = EntityCampaignTrait::getAllChildrenCampaigns($campaignId, $previousCampaigns);

        $childCampaigns[] = $campaignId; // add the parent campaign

        $branchTimeZone = $this->getTimeZone();

        \Log::info(__METHOD__, [
            'business_pk' => $this->business->business_pk,
            'time_zone' => $branchTimeZone,
            'campaignId' => $campaignId,
            'campaigns' => $childCampaigns,
        ]);

        $dateIntervals = ReportRepository::getIntervalDates(new \DateTime($this->data->date_from), [7, 14, 30, 60, 90, 180, 360], $branchTimeZone);
        foreach ($dateIntervals as $key => $value) {
            $dateIntervals[$key]['campaign_ids'] = EntityCampaignTrait::getCampaignIdsInPeriod($this->business->business_pk, $value['start_date'], $value['end_date'], $childCampaigns);
        }

        $this->selectFields = [
            //'q.entity_campaign_fk',
            'ec.campaign_name AS campaign_name',
        ];

        foreach ($dateIntervals as $key => $value) {
            if (!empty($value['campaign_ids'])) {
                $campaignIds = implode(',', $value['campaign_ids']);
                $this->selectFields[] = "SUM(CASE WHEN t.utc_created BETWEEN '{$value['start_date']}' AND '{$value['end_date']}' AND q.entity_campaign_fk IN ({$campaignIds}) THEN a.units_exchange ELSE 0 END) AS `{$key}d`";
            } else {
                $this->selectFields[] = "SUM(CASE WHEN t.utc_created BETWEEN '{$value['start_date']}' AND '{$value['end_date']}' THEN a.units_exchange ELSE 0 END) AS `{$key}d`";
            }
        }

        $query = \DB::table($this->dataset . 'email_sms_queue AS q')
            ->selectRaw(implode(',', $this->selectFields))
            ->join($this->dataset . 'entity_campaign AS ec', 'ec.entity_campaign_pk', '=', 'q.entity_campaign_fk')
            ->join($this->dataset . 'transaction AS t', 't.user_fk', '=', 'q.user_fk')
            ->join($this->dataset . 'activity AS a', 't.transaction_pk', '=', 'a.transaction_fk')
            // mandatory constraints
            ->where('q.business_fk', $this->business->business_pk)
            ->where('q.utc_created', '>=', $this->data->date_from)
            ->where('a.utc_created', '>=', $this->data->date_from)
            ->where('t.utc_created', '>=', $this->data->date_from)
            ->whereIn('q.entity_campaign_fk', $childCampaigns)
            ->groupBy('q.entity_campaign_fk', 'ec.campaign_name')
            ->orderBy('q.entity_campaign_fk', 'ASC')
            ->limit($this->getLimit())
            ->offset($this->getOffset());

        /*if ($this->sorting) {
            foreach ($this->sorting as $sorting) {
                $query->orderBy(self::SUPPORT_SORTING_FIELDS[$sorting['field']], $sorting['order']);
            }
        }*/

        return $query;
    }
}
