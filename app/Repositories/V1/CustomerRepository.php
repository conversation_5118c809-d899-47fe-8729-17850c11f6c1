<?php

namespace App\Repositories\V1;
 
use App\Repositories\V1\RepositoryInterface;
use App\Repositories\V1\Repository;
 
class CustomerRepository extends Repository
{
    /**
     * Specify Model class name
     *
     * @return mixed
     */
    public function model()
    {
        return 'App\Models\V1\Customer';
    }

    public function forBusiness($busiessId)
    {
        return $this->model->select('id', 'first', 'last', 'email', 'phone', 'country_phone_fk as phone_country_id')
            ->offset(0)->limit(100)->get();
    }
}
