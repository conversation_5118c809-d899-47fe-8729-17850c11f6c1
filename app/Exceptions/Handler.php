<?php

namespace App\Exceptions;

use App\Helpers\V1\Utils;
use App\Jobs\LogFailedLoginAttemptJob;
use Illuminate\Validation\UnauthorizedException;
use KangarooRewards\Common\Exceptions\ApiErrorHandler;
use Throwable;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Session\TokenMismatchException;
use Illuminate\Validation\ValidationException;
use InvalidArgumentException;
use League\OAuth2\Server\Exception\OAuthServerException;
use Laravel\Passport\Exceptions\OAuthServerException as LaravelOAuthServerException;
use Log;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Illuminate\Http\Request;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that should not be reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        AuthenticationException::class,
        AuthorizationException::class,
        HttpException::class,
        ModelNotFoundException::class,
        TokenMismatchException::class,
        ValidationException::class,
        OAuthServerException::class,
        LaravelOAuthServerException::class,
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
        'pin_code',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Convert a validation exception into a JSON response.
     *
     * @param Request $request
     * @param ValidationException $exception
     * @return JsonResponse
     */
    protected function invalidJson($request, ValidationException $exception)
    {
        $oauthClientId = $request->user() ? $request->user()->token()->getAttribute('client_id') : null;
        $userId = $request->user() ? $request->user()->id : null;

        Log::warning($exception->getMessage(), [
            'ip' => $request->ip(),
            'oauth_client_id' => $oauthClientId,
            'user_id' => $userId,
            'input' => json_decode($request->getContent(), true),
            'appKey' => (array) $request->appKey,
            'x-application-key' => $request->header('x-application-key'),
            'errors' => (array) $exception->validator->errors()->getMessages(),
        ]);

        return response()->json($exception->errors(), $exception->status);
    }

    /**
     * Report or log an exception.
     *
     * This is a great spot to send exceptions to Sentry, Bugsnag, etc.
     *
     * @param Throwable $exception
     * @return void
     * @throws Exception
     */
    public function report(Throwable $exception)
    {
        if ($this->shouldntReport($exception)) {
            return;
        }

        try {
            $request = request();
            $oauthClientId = $request->user() ? $request->user()->token()->getAttribute('client_id') : null;
            $userId = $request->user() ? $request->user()->id : null;

            Log::warning($exception, [
                'ip' => $request->ip(),
                'oauth_client_id' => $oauthClientId,
                'user_id' => $userId,
                'input' => json_decode($request->getContent(), true),
                'appKey' => (array) $request->appKey,
                'x-application-key' => $request->header('x-application-key'),
                'errors' => $exception->getMessage(),
            ]);
        } catch (Throwable $ex) {
//            parent::report($exception);
            throw $exception; // throw the original exception
        }
//        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param Request $request
     * @param Throwable $e
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function render($request, Throwable $e)
    {
        if ($request->route() && str_starts_with($request->route()->action['prefix'], 'workflows')) {
            return (new ApiErrorHandler())->handleException($request, $e);
        }

        if ($e instanceof TokenMismatchException) {
            return redirect('/')->with('message', 'Whoops, your session seems to have expired. Please login again.');
        }

        if ($e instanceof \Laravel\Passport\Exceptions\OAuthServerException) {
            dispatch(new LogFailedLoginAttemptJob([
                'created_at' => date('Y-m-d H:i:s'),
                'username' => $request->input('username'),
                'client_id' => $request->input('client_id'),
                'ip' => Utils::getIpFromRequest($request),
                'user_agent' => $request->userAgent(),
                'platform_id' => config('app.PLATFORM_ID', 10),
            ]));

            Log::info('FailedLoginAttempt', [
                'username' => $request->input('username'),
                'client_id' => $request->input('client_id'),
                'ip' => Utils::getIpFromRequest($request),
                'user_agent' => $request->userAgent(),
            ]);

            return $this->errorResponse($e->getMessage(), 400);
        }

        if (!$request->expectsJson()) {
            return parent::render($request, $e);
        }

        if ($e instanceof \Illuminate\Http\Exceptions\ThrottleRequestsException) {
            return $this->errorResponse('Too Many Attempts. Please try again later', 429, 'If you need help please contact ' . config('app.ADMIN_EMAIL'));
        }

        if ($e instanceof AuthenticationException) {
            return $this->errorResponse('Unauthenticated.', 401);
        }

        if ($e instanceof ValidationException) {
            $errors = $e->validator->errors()->getMessages();
            return response()->json($errors, 422);
        }

        if ($e instanceof ModelNotFoundException) {
            return $this->errorResponse('Resource not found', 404);
        }

        if ($e instanceof ResourceNotFoundException) {
            return $this->errorResponse($e->getMessage(), 404);
        }

        if ($e instanceof InvalidArgumentException) {
            return $this->errorResponse($e->getMessage(), 400);
        }

        if (
            $e instanceof AuthorizationException ||
            $e instanceof UnauthorizedException  ||
            $e instanceof AccessDeniedHttpException
        ) {
            return $this->errorResponse($e->getMessage(), 403);
        }

        if ($e instanceof MethodNotAllowedHttpException) {
            return $this->errorResponse('The specified method for the request is invalid', 405);
        }

        if ($e instanceof NotFoundHttpException) {
            return $this->errorResponse('The specified URL cannot be found', 404);
        }

        if ($e instanceof HttpException) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode());
        }

        if ($e instanceof \RuntimeException) {
            return $this->errorResponse($e->getMessage(), 400);
        }

        $oauthClientId = $request->user() ? $request->user()->token()->getAttribute('client_id') : null;
        $userId = $request->user() ? $request->user()->id : null;

        Log::error($e, [
            'ip' => $request->ip(),
            'oauth_client_id' => $oauthClientId,
            'user_id' => $userId,
            'input' => json_decode($request->getContent(), true),
            'input_' => $request->all(),
            'appKey' => (array) $request->appKey,
            'x-application-key' => $request->header('x-application-key'),
        ]);


        if (config('app.debug')) {
            return parent::render($request, $e);
        }

        return $this->errorResponse('An error has occurred. Please try again '
                                 . 'and if this problem persists <NAME_EMAIL>', 500);

        return parent::render($request, $e);
    }

    /**
     * Convert an authentication exception into an unauthenticated response.
     *
     * @param Request $request
     * @param AuthenticationException $exception
     * @return Response
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        if ($request->expectsJson()) {
            return response()->json(['error' => 'Unauthenticated.'], 401);
        }

        return redirect()->guest(route('login'));
    }

    /**
     * @param $message
     * @param $code
     * @return JsonResponse
     */
    protected function errorResponse($message, $code, $description = '')
    {
        return response()->json(['error' => [
            'message' => $message,
            'description' => $description ?: $message,
            'status_code' => $code,
            'link' => 'https://api.kangaroorewards.com/docs',
        ]], $code);
    }
}
