<?php

namespace App\Http\Requests\V1;

use App\Models\V1\BranchReferralRule;
use Illuminate\Foundation\Http\FormRequest;

class CreateUserBusinessNotification extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $isAdmin = $this->user()->isBusinessAdmin() || $this->user()->isBusinessClerk();
        if (!$isAdmin) {
            return false;
        }

        $customer = $this->route()->parameter('customer');

        $business = $this->appBusiness();

        // The user belongs to the business
        return $customer && $customer->business_fk === $business->business_pk;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     * @throws \Exception
     */
    public function rules()
    {
        return [
            'allow_sms' => 'required|boolean',
            'allow_email' => 'required|boolean',
            'allow_push' => 'required|boolean',
        ];
    }
}
