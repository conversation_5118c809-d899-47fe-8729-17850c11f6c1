<?php

declare(strict_types=1);

namespace KangarooRewards\Workflows\Models;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use KangarooRewards\Workflows\Database\Factories\WorkflowTriggerFactory;

/**
 * KangarooRewards\Workflows\Models\WorkflowTrigger
 *
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $main_trigger_id
 * @property int $workflow_id
 * @property int $business_fk
 * @property int $conditions_relation;
 * @property array $conditions;
 * @property array $frequency;
 * @property-read \KangarooRewards\Workflows\Models\MainTrigger $mainTrigger
 * @property-read \KangarooRewards\Workflows\Models\Workflow $workflow
 * @method static \Illuminate\Database\Eloquent\Builder|WorkflowTrigger newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkflowTrigger newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkflowTrigger query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkflowTrigger whereBusinessFk($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkflowTrigger whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkflowTrigger whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkflowTrigger whereMainTriggerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkflowTrigger whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkflowTrigger whereWorkflowId($value)
 * @mixin \Eloquent
 */
class WorkflowTrigger extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'workflow_triggers';
    protected $connection = 'workflows_db';

    public const CREATED_AT = 'created_at';
    public const UPDATED_AT = 'updated_at';

    protected $guarded = [
        //
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'conditions' => 'array',
        'frequency' => 'array'
    ];

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): Factory
    {
        return WorkflowTriggerFactory::new();
    }

    public function mainTrigger(): BelongsTo
    {
        return $this->belongsTo(MainTrigger::class, 'main_trigger_id');
    }

    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class, 'workflow_id');
    }

    public static function getActiveWorkflowTrigger($businessId, $mainTriggerId, $excludeWorkflowId = null, $publishedAt = null, $expiresAt = null)
    {
        if (!isset($publishedAt, $expiresAt) && $excludeWorkflowId) {
            $workflow = Workflow::whereId($excludeWorkflowId)->first();
            if ($workflow) {
                $publishedAt = $workflow->published_at;
                $expiresAt = $workflow->expires_at ?? TIMESTAMP_RANGE_MAX;
            }
        } else {
            $expiresAt = $expiresAt ?? TIMESTAMP_RANGE_MAX;
        }

        $query = WorkflowTrigger::join('workflows', 'workflows.id', '=', 'workflow_triggers.workflow_id')
            ->where('workflow_triggers.main_trigger_id', $mainTriggerId)
            ->where('workflows.business_fk', $businessId)
            ->whereNull('workflows.deleted_at');
        if (isset($excludeWorkflowId)) {
            $query->where('workflows.id', '!=', $excludeWorkflowId);
        }

        if (isset($publishedAt, $expiresAt)) {
            $query->where(function ($query) use ($publishedAt, $expiresAt) {
                return $query->whereBetween('workflows.published_at', [$publishedAt, $expiresAt])
                    ->orWhereBetween('workflows.expires_at', [$publishedAt, $expiresAt])
                    ->orWhere(function ($query) use ($publishedAt, $expiresAt) {
                        $query->where('workflows.published_at', '<=', $publishedAt)
                            ->where('workflows.expires_at', '>=', $expiresAt);
                    });
            });
        }
//        info(__METHOD__ . $query->toSql());
        return $query->get();
    }
}
