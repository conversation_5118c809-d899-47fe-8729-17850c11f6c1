<?php

namespace App\Http\Controllers\Api\V1;

use App\Exceptions\ResourceNotFoundException;
use App\Helpers\ApiHelper;
use App\Helpers\DataTransferObject\DataTransferObjectError;
use App\Helpers\Dto\UpdateOfferData;
use App\Helpers\Offer\OfferFactory;
use App\Helpers\Offer\OfferUpdater;
use App\Http\Requests\V1\CreateOffer;
use App\Http\Requests\V1\DeleteOffer;
use App\Http\Requests\V1\UpdateOffer;
use App\Http\Resources\OfferCollection;
use App\Services\AttachmentSyncerService;
use App\Services\ImageUploader;
use App\Services\OfferRewardTargetingService;
use App\Traits\CampaignTrait;
use App\Traits\LogContextTrait;
use App\Traits\OfferTrait;
use App\Traits\TierPerkTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\V1\Business;
use App\Models\V1\Offer;
use App\Repositories\OfferSearcher;
use App\Http\Resources\OfferResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\TierPerkCollection;
use Illuminate\Validation\ValidationException;

/**
 * Offers resource representation.
 *
 * @Resource("Offers", uri="/offers")
 */
class OfferController extends ApiController
{
    use LogContextTrait;
    use OfferTrait;
    use CampaignTrait;
    use TierPerkTrait {
        TierPerkTrait::byOfferId as tierPerksByOfferId;
        TierPerkTrait::deleteTierPerk as deleteTierPerk;
        TierPerkTrait::deleteAssociatedTierPerksWithOffer as deleteOfferTierPerks;
    }

    /**
    * List Offers
    * Get a JSON representation of all offers for a business
    *
    * GET /offers
    *
    * @Get("/offers{?page}")
    * @Versions({"v1"})
    * @Response(200, body={"data":[{"id":12345678,"first":"Sara","last":"Silverman","email":"<EMAIL>"},{"id":12345679,"first":"victoria","last":"Leblanc","email":"<EMAIL>"}],"cursor": {"next": 2,"next_uri": "/offers?page=2"}})
    * @Parameters({
    *      @Parameter("page", type="integer", required=false description="The page of results to view.", default=1)
    * })
    *
    * @param \Illuminate\Http\Request $request
    *
    * @return JsonResponse
    */
    public function index(Request $request)
    {
        try {
//            $user = Auth::user();//merchant user id
//            $business = $this->getBusinessForAppKey();

            $business = $request->appBusiness();

            // There is no user - new instance is passed
            $offerSearcher = new OfferSearcher($request, (new \App\User()), $business);

            $conditions = ApiHelper::buildQueryConditions(
                $request->only(['offer_expired_at', 'offer_type_id', 'targeted_offer_flag', 'offer_start_date', 'offer_end_date'])
            );

            $offerSearcher->setFilterBy([1,2,3,8,9,10,11]);
            $offerSearcher->setFilterByQueryConditions($conditions);
            return $offerSearcher->searchForAdmin()->getCollection()->response();
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\LogicException $e) {
            Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            Log::error($e);
            return $this->errorInternalError();
        }
    }

    /**
     * Retrieve an Offer by ID
     * Get a JSON representation of a specific offer
     *
     * GET /offers/{offer_id}
     *
     * @Get("/offers/{offer_id}")
     * @Versions({"v1"})
     * @Transaction({
     *      @Response(200, body={"data":{}})
     *      @Response(404, body={})
     * })
     * @Parameters({
     *      @Parameter("include", type="string", required=false description="You can optionally request that the response include the offers's relations. Example: include=resource")
     * })
     *
     * @param \Illuminate\Http\Request $request
     * @param int $offerId
     *
     * @return OfferResource|mixed
     */
    public function show(Request $request, $offerId)
    {
        try {
//            $user = Auth::user(); //merchant user id

//            $business = $this->getBusinessForAppKey();
            $business = $request->appBusiness();
            // get offer by ID and make sure it belongs to the branch allowed (for conglomeration, coalition etc)
            $offer = Offer::findOneForBusiness((int) $offerId, $business, $langId = 1, $giftcards = false);

            if (!$offer) {
                return $this->errorNotFound("Offer $offerId Not Found");
            }

            return new OfferResource($offer);
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\LogicException $e) {
            Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            Log::error($e);
            return $this->errorInternalError();
        }
    }

    public function store(CreateOffer $request)
    {
        $this->addLogContext(['data' => $request->all()]);
        try {
            $user = $request->user(); //merchant user id
            $this->addLogContext(['userId' => $request->user()->id ?? null]);
            $business = Business::forUser($user);
            $this->addLogContext(['business' => $business->business_pk ?? null]);

            $factory = new OfferFactory($request, $business);

            $factory->validate();
        } catch (ValidationException $e) {
            throw $e;
        } catch (\InvalidArgumentException $e) {
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (DataTransferObjectError $e) {
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (ResourceNotFoundException $e) {
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorNotFound('Not found', $e->getMessage());
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorInternalError();
        }

        \DB::connection('tenant')->beginTransaction();
        try {
            $offer = $factory->createOffer();
            \DB::connection('tenant')->commit();
        } catch (\InvalidArgumentException $e) {
            \DB::connection('tenant')->rollBack();
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \DB::connection('tenant')->rollBack();
            Log::error(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorInternalError();
        }

        return new OfferResource($offer);
    }

    public function update(UpdateOffer $request, Offer $offer)
    {
        $this->addLogContext(['data' => $request->all()]);
        try {
            $user = $request->user(); //merchant user id
            $this->addLogContext(['userId' => $request->user()->id ?? null]);
            $business = Business::forUser($user);
            $this->addLogContext(['business' => $business->business_pk ?? null]);

            $offerData = new UpdateOfferData($request->all());
            $factory = new OfferUpdater($offer, $offerData, $business);
            $factory->validate();
        } catch (ValidationException $e) {
            throw $e;
        } catch (\InvalidArgumentException $e) {
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (DataTransferObjectError $e) {
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorWrongArgs(
                'Bad Request',
                str_replace([
                    'App\\Helpers\\Dto\\UpdateOfferData::',
                    'App\\Helpers\\Dto\\OfferLanguageData::',
                    'App\\Helpers\\Dto\\OfferBranchData::',
                ], '', $e->getMessage())
            );
        } catch (ResourceNotFoundException $e) {
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorNotFound('Not found', $e->getMessage());
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorInternalError();
        }

        \DB::connection('tenant')->beginTransaction();
        try {
            $offer = $factory->updateOffer();
            \DB::connection('tenant')->commit();
        } catch (\InvalidArgumentException $e) {
            \DB::connection('tenant')->rollBack();
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \DB::connection('tenant')->rollBack();
            Log::error(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorInternalError();
        }

        return new OfferResource($offer);
    }

    /**
     * Delete offer and any associated resources; as of tier perks & silent campaign
     * @param DeleteOffer $request
     * @param Offer $offer
     * @return JsonResponse
     * @throws \Throwable
     */
    public function destroy(DeleteOffer $request, Offer $offer): JsonResponse
    {
        $business = $request->appBusiness();

        DB::connection('tenant')->beginTransaction();
        try {
            // Validate offer
            if ($offer->enabled == 0) {
                return $this->errorNotFound();
            }

            // Delete the associated tier perks
            $this->deleteOfferTierPerks($offer, $business->business_pk);

            // Cancel the associated targeted campaign
            if (!$this->cancelCampaignByOfferOrReward($offer->entity_campaign_fk)) {
                throw new \Exception('Failed to cancel the linked campaign');
            }

            // Delete offer
            $this->deleteOffer($offer);

            DB::connection('tenant')->commit();

            return $this->respondNoContent();
        } catch (\InvalidArgumentException $e) {
            DB::connection('tenant')->rollBack();

            Log::warning(__METHOD__ . ' ' . $e);

            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            DB::connection('tenant')->rollBack();

            Log::error(__METHOD__ . ' ' . $e);

            return $this->errorInternalError();
        }
    }

    /**
     * Retrieve an Offer by slug
     * Get a JSON representation of a specific offer
     *
     * GET /public/offers/{offer_slug}
     *
     * @Get("/public/offers/{offer_slug}")
     * @Versions({"v1"})
     * @Transaction({
     *      @Response(200, body={"data":{}})
     *      @Response(404, body={})
     * })
     * @Parameters({
     *      @Parameter("include", type="string", required=false description="You can optionally request that the response include additional resources. Example: include=resource")
     *      @Parameter("relationships", type="string", required=false description="You can optionally request that the response include the offers's relations. Example: relationships=branch")
     * })
     *
     * @param \Illuminate\Http\Request $request
     * @param string $offerSlug
     *
     * @return OfferResource|mixed
     */
    public function showBySlug(Request $request, $offerSlug)
    {
        # find by slug
        $slug = \DB::table('slug')
            ->where('slugname', $offerSlug)
            ->first();

        if (!$slug) {
            return $this->errorNotFound('Offer Not Found');
        }

        $queryString = json_decode($slug->querystring, false);

        if (!isset($queryString->offerId)) {
            return $this->errorNotFound("Offer $offerSlug Not Found");
        }

        // Get offer Id from query string
        $offerId = (int) $queryString->offerId;
        $offer = Offer::getDetails($offerId);

        if (!$offer) {
            return $this->errorNotFound("Offer $offerSlug Not Found");
        }

        return new OfferResource($offer);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function uploadImage(Request $request): JsonResponse
    {
        $this->validate($request, [
            'image_original' => 'required|mimes:jpeg,jpg,png,gif|max:20480',
            'image_large' => 'required|mimes:jpeg,jpg,png,gif|max:2048',
            'image_medium' => 'required|mimes:jpeg,jpg,png,gif|max:512',
            'image_thumbnail' => 'required|mimes:jpeg,jpg,png,gif|max:256',
        ]);

        $this->addLogContext(['data' => $request->all()]);
        try {
            $user = $request->user(); //merchant user id
            $this->addLogContext(['userId' => $request->user()->id ?? null]);
            $business = Business::forUser($user);
            $this->addLogContext(['business' => $business->business_pk ?? null]);

            $modelName = (new \ReflectionClass(Offer::class))->getShortName();
            $im = new ImageUploader($request, $business, $modelName);
            $imgPaths = $im->upload();
            $im->logProfiling();

            // Sync the uploaded images to the DEV main server (only for DEV)
            AttachmentSyncerService::syncToMainServer($business->business_pk, 'offers', $imgPaths);

            return $this->respondWithArray(['data' => $imgPaths]);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorInternalError();
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function tierTargetedOffers(Request $request){
        try {
            $user = $request->user(); //user id
            $business = Business::forUser($user); //business of the user
            // $business = $this->getBusinessForAppKey();
            $tierOffers = Offer::getTierOffersByBusiness($business);
            Log::info(__METHOD__ . ' - user::' . json_encode($user->id) . ' - business::' . json_encode($business->business_pk), $this->getLogContext());
            return $this->respondWithArray(['tierOffers' => $tierOffers]);
        }catch (\Exception $e) {
            Log::error(__METHOD__ . ' tierTargetedOffers -' . $e, $this->getLogContext());
            return $this->errorInternalError();
        }
    }
    
    /**
     * @param Request $request
     * @param int $offerId
     * @throws \Exception
     * @return mixed
     */
    public function tierPerksForOffer(Request $request, $offerId){
        try {
            $user = $request->user(); //user id
            $business = Business::forUser($user); //business of the user
            // $business = $this->getBusinessForAppKey();
            $tierPerks = $this->tierPerksByOfferId($business->business_pk, $offerId);
            Log::info(__METHOD__ . ' - tier perks for offer::' . json_encode($tierPerks), $this->getLogContext());
            return new TierPerkCollection($tierPerks);
        }catch (\Exception $e) {
            Log::error(__METHOD__ . ' tierTargetedRewards -' . $e, $this->getLogContext());
            return $this->errorInternalError();
        }
    } 
    
    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function drawPrizeOffers(Request $request)
    {
        try {
            $user = $request->user(); //user id
            $business = Business::forUser($user); //business of the user
            $drawPrizeOffers = Offer::getDrawOffersByBusiness($business);
            Log::info(__METHOD__ . ' - user::' . json_encode($user->id) . ' - business::' . json_encode($business->business_pk), $this->getLogContext());
            return $this->respondWithArray(['drawPrizeOffers' => $drawPrizeOffers]);
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\LogicException $e) {
            Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            Log::error($e);
            return $this->errorInternalError();
        }
    }
    
    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function offerTypes(Request $request)
    {
        try {
            $user = $request->user(); //user id
            $business = Business::forUser($user); //business of the user
            $offerPunchItemTypes = Offer::getTypeStringArrays();
            Log::info(__METHOD__ . ' - user::' . json_encode($user->id) . ' - business::' . json_encode($business->business_pk), $this->getLogContext());
            return $this->respondWithArray(['data' => $offerPunchItemTypes]);
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\LogicException $e) {
            Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            Log::error($e);
            return $this->errorInternalError();
        }
    }

    /**
     * Create offer with targeted filters (using a silent campaign)
     * @param CreateOffer $request
     * @return OfferResource|JsonResponse
     * @throws \Throwable
     */
    public function createOfferWithFilters(CreateOffer $request): OfferResource|JsonResponse
    {
        $user = $request->user();
        $business = Business::forUser($user);

        $this->addLogContext([
            'userId' => $user->id,
            'businessId' => $business->business_pk,
            'offerWithFiltersRequest' => $request->all(),
        ]);

        DB::connection('tenant')->beginTransaction();
        try {
            // Initiate targeting service
            $targetingService = new OfferRewardTargetingService($business, $request);

            // Create offer with targeted filters
            $offer = $targetingService->createOfferWithFilters();

            DB::connection('tenant')->commit();

            // Return resource
            $offerResource = new OfferResource($offer);

            return $offerResource->setTargetedFilters($targetingService->getTargetedFiltersForResource());
        } catch (\InvalidArgumentException $e) {
            DB::connection('tenant')->rollBack();

            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());

            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (DataTransferObjectError $e) {
            DB::connection('tenant')->rollBack();

            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());

            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (ResourceNotFoundException $e) {
            DB::connection('tenant')->rollBack();

            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());

            return $this->errorNotFound('Not found', $e->getMessage());
        } catch (ValidationException $e) {
            DB::connection('tenant')->rollBack();

            $errors = $e->errors() ?? [];

            $this->addLogContext(['validationErrors' => $errors]);

            Log::error(__METHOD__ . ' ' . $e, $this->getLogContext());

            return $this->errorUnprocessableEntity($errors);
        } catch (\Exception $e) {
            DB::connection('tenant')->rollBack();

            Log::error(__METHOD__ . ' ' . $e, $this->getLogContext());

            return $this->errorInternalError();
        }
    }
        
    /**
     * @param Request $request
     * @return OfferCollection|\Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function spinToWinPrizeOffers(Request $request)
    {
        try {
            $user = $request->user(); //user id
            $business = $request->appBusiness(); //business of the user
            $spinToWinPrizeOffers = Offer::getSpinToWinOffersByBusiness($business);
            Log::info(__METHOD__ . ' - user::' . json_encode($user->id) . ' - business::' . json_encode($business->business_pk), $this->getLogContext());
            return new OfferCollection($spinToWinPrizeOffers);
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\LogicException $e) {
            Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            Log::error($e);
            return $this->errorInternalError();
        }
    }
}
