<?php

namespace App\Services;

use App\Helpers\Dto\CampaignData;
use App\Http\Resources\CampaignResource;
use App\Models\User;
use Exception;
use KangarooRewards\Common\Models\BusinessGeofence;
use KangarooRewards\Common\Models\Campaign;
//use App\Models\V1\Campaign;
use KangarooRewards\Common\Repositories\BusinessGeofenceRepository;
use KangarooRewards\Common\Traits\EntityCampaignTrait;
use App\Exceptions\CustomValidationException;
use Carbon\Carbon;
use KangarooRewards\Common\Traits\UserTrait;

class UserGeofenceService
{
    use EntityCampaignTrait;
    use UserTrait;

    private array $data;
    private int $businessId;
    private string $businessTimeZone;
    private string $deliveryChannel;
    private ?BusinessGeofence $geofence;
    private ?Campaign $campaign;
    private ?CampaignData $campaignCriteria;
    private User $user;

    public function __construct(
        protected BusinessGeofenceRepository $businessGeofenceRepository
    ) {}

    /**
     * @param array $data
     * @param int $userId
     * @param int $businessId
     * @param string $businessTimeZone
     * @return CampaignResource|null
     * @throws CustomValidationException
     */
    public function process(array $data, int $userId, int $businessId, string $businessTimeZone): ?CampaignResource
    {
        $this->data = $data;
        $this->businessId = $businessId;
        $this->businessTimeZone = $businessTimeZone;
        $this->user = $this->getUser($userId);

        $this->getGeofence();
        $this->getCampaign();
        $this->verify();

        $this->deliveryChannel = 'push';

        if ($this->deliveryChannel == 'push') {
            return new CampaignResource($this->campaign);
        }

        return null;
    }

    /**
     * @return void
     * @throws CustomValidationException
     */
    private function getGeofence(): void
    {
        $this->geofence = $this->businessGeofenceRepository->findActiveGeofenceByBusiness($this->data['geofence_id'], $this->businessId);
        if (!$this->geofence) {
            throw new CustomValidationException('Geofence not found or inactive.', [
                'geofence_id' => $this->data['geofence_id'],
                'business_id' => $this->businessId,
            ]);
        }

        if (empty($this->geofence->campaign_fk)) {
            throw new CustomValidationException('Geofence is missing a linked campaign.');
        }
    }

    /**
     * @throws CustomValidationException
     * @throws Exception
     */
    private function getCampaign(): void
    {
        $this->campaign = $this->getGeofenceCampaign($this->geofence->campaign_fk);
        if (!$this->campaign) {
            throw new CustomValidationException('Campaign not found or invalid.', [
                'campaign_id' => $this->geofence->campaign_fk,
            ]);
        }
    }

    /**
     * @return void
     * @throws CustomValidationException
     */
    private function verify(): void
    {
        // Verify campaign start and end time
        $this->verifyCampaignScheduling();
        
        // Verify trigger schedule timing
        $this->verifyTriggerScheduleTiming();

        // Verify campaign options
        $this->verifyCampaignOptions();

        // Verify user consent and set the delivery channel
        $this->deliveryChannel = $this->verifyUserConsentAndSetChannel();
    }

    /**
     * @throws CustomValidationException
     */
    private function verifyCampaignScheduling(): void
    {
        $now = Carbon::now();

        // Check if the campaign's start date is in the future
        if ($this->campaign->utc_start > $now) {
            throw new CustomValidationException('Campaign is not applicable yet.', [
                'campaign_id' => $this->campaign->entity_campaign_pk,
                'utc_start' => $this->campaign->utc_start,
            ]);
        }

        // Check if the campaign's end date is in the past
        if (!empty($this->campaign->utc_end) && $this->campaign->utc_end < $now) {
            throw new CustomValidationException('Campaign has expired.', [
                'campaign_id' => $this->campaign->entity_campaign_pk,
                'utc_end' => $this->campaign->utc_end,
            ]);
        }
    }

    /**
     * Verify the campaign's trigger schedule timing.
     *
     * @throws CustomValidationException
     */
    private function verifyTriggerScheduleTiming(): void
    {
        $triggerSchedule = $this->campaign->trigger_schedule_timing;

        // If the schedule timing is empty, move to the next step
        if (empty($triggerSchedule)) {
            return;
        }

        // Decode the JSON schedule
        $schedule = json_decode($triggerSchedule, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new CustomValidationException('Invalid trigger schedule timing format.', [
                'campaign_id' => $this->campaign->entity_campaign_pk,
                'trigger_schedule_timing' => $triggerSchedule
            ]);
        }

        // Get current day and time based on business timezone
        $currentDay = Carbon::now($this->businessTimeZone)->dayOfWeek; // 0 (Sunday) to 6 (Saturday)
        $currentTime = Carbon::now($this->businessTimeZone)->format('H:i');

        // Check if the current day is in the allowed days
        if (!empty($schedule['days']) && !in_array($currentDay, $schedule['days'])) {
            throw new CustomValidationException('Campaign is not applicable on this day.', [
                'campaign_id' => $this->campaign->entity_campaign_pk,
                'current_day' => $currentDay,
                'allowed_days' => $schedule['days'],
            ]);
        }

        // Check if the current time is within the allowed time range
        if (!empty($schedule['time'])) {
            $startTime = $schedule['time']['start'] ?? null;
            $endTime = $schedule['time']['end'] ?? null;

            if ($startTime && $currentTime < $startTime) {
                throw new CustomValidationException('Campaign is not applicable at this time.', [
                    'campaign_id' => $this->campaign->entity_campaign_pk,
                    'current_time' => $currentTime,
                    'start_time' => $startTime,
                ]);
            }

            if ($endTime && $currentTime > $endTime) {
                throw new CustomValidationException('Campaign is not applicable at this time.', [
                    'campaign_id' => $this->campaign->entity_campaign_pk,
                    'current_time' => $currentTime,
                    'end_time' => $endTime,
                ]);
            }
        }
    }

    private function transformCampaignOptions($data): void
    {
        if (isset($data['campaign_data'])) {
            $this->campaignCriteria = CampaignData::transformRawData($data['campaign_data']);
        } else {
            $this->campaignCriteria = CampaignData::fromPostRequest($data);
        }
    }

    /**
     * @throws CustomValidationException
     */
    private function verifyCampaignOptions(): void
    {
        $options = $this->campaign->options;

        // Decode the JSON options
        if (!is_array($options)) {
            $options = json_decode($options, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new CustomValidationException('Invalid campaign options format.', [
                    'campaign_id' => $this->campaign->entity_campaign_pk,
                    'options' => $options,
                ]);
            }
        }

        // Transform campaign options
        $this->transformCampaignOptions($options);

        // Check the "whoWillReceive" array
        if (empty($options['whoWillReceive']) || count($options['whoWillReceive']) !== 1 || $options['whoWillReceive'][0] !== CAMPAIGN_WHO_WILL_RECEIVE_GEOFENCING) {
            throw new CustomValidationException('Campaign is not applicable for the specified audience.', [
                'campaign_id' => $this->campaign->entity_campaign_pk,
                'whoWillReceive' => $options['whoWillReceive'] ?? null,
            ]);
        }

        // Check the "geofences" array
        if (empty($options['geofences']) || !is_array($options['geofences'])) {
            throw new CustomValidationException('No geofences configured for the campaign.', [
                'campaign_id' => $this->campaign->entity_campaign_pk,
            ]);
        }

        $geofenceId = $this->data['geofence_id'];
        $triggerCondition = $this->data['trigger_condition'];
        $geofenceMatch = false;

        foreach ($options['geofences'] as $geofence) {
            if (isset($geofence['id'], $geofence['condition']) && $geofence['id'] == $geofenceId && $geofence['condition'] === $triggerCondition) {
                $geofenceMatch = true;
                break;
            }
        }

        if (!$geofenceMatch) {
            throw new CustomValidationException('Geofence or trigger condition does not match the campaign configuration.', [
                'campaign_id' => $this->campaign->entity_campaign_pk,
                'geofence_id' => $geofenceId,
                'trigger_condition' => $triggerCondition,
            ]);
        }
    }

    /**
     * Detect the delivery channel based on campaign criteria and user consent.
     *
     * @return string
     * @throws CustomValidationException
     */
    private function verifyUserConsentAndSetChannel(): string
    {
        // Initialize the UserConsentService
        $userConsentService = new UserConsentService($this->user);

        // Check the `send_by` field in campaign criteria
        if ($this->campaignCriteria->send_by === CAMPAIGN_SEND_BY_PUSH) {
            if ($userConsentService->hasPushConsent()) {
                return 'push';
            }
            throw new CustomValidationException('Push consent is not available for the user.');
        }

        if ($this->campaignCriteria->send_by === CAMPAIGN_SEND_BY_EMAIL) {
            if ($userConsentService->hasEmailConsent()) {
                return 'email';
            }
            throw new CustomValidationException('Email consent is not available for the user.');
        }

        if ($this->campaignCriteria->send_by === CAMPAIGN_SEND_BY_SMS) {
            if ($userConsentService->hasSmsConsent()) {
                return 'sms';
            }
            throw new CustomValidationException('SMS consent is not available for the user.');
        }

        if ($this->campaignCriteria->send_by === CAMPAIGN_SEND_BY_EMAIL_SMS) {
            // Explode `send_by_priority` into an array
            $priorities = explode(',', $this->campaignCriteria->send_by_priority);
            $priorities  [] = 'push';

            foreach ($priorities as $priority) {
                switch (trim($priority)) {
                    case 'email':
                        if ($userConsentService->hasEmailConsent()) {
                            return 'email';
                        }
                        break;
                    case 'sms':
                        if ($userConsentService->hasSmsConsent()) {
                            return 'sms';
                        }
                        break;
                    case 'push':
                        if ($userConsentService->hasPushConsent()) {
                            return 'push';
                        }
                        break;
                }
            }

            // If no consent is available for any priority
            throw new CustomValidationException('No valid consent is available for the user based on the priority.');
        }

        // If `send_by` does not match any known value
        throw new CustomValidationException('Invalid send_by value in campaign criteria.');
    }

    /**
     * Get the user object.
     *
     * @param $userId
     * @return User
     */
    private function getUser($userId): User
    {
        return $this->getUserById($userId, $this->businessId);
    }
}
