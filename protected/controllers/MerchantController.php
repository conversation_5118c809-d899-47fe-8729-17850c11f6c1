<?php

//require_once (Yii::app()->params['fbSdkPath'].'/facebook.php');

use GuzzleHttp\Exception\GuzzleException;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;

class MerchantController extends Controller
{
    const OFFER_TABLE_NAME = 'offer'; //uset to create the slug for offer

    public $layout = '//layouts/merchant';
    public $attempts = 2; //number of login attempts, 3 attempts
    public $counter; // for login attempts starts from 0

    public $source = '';

    protected function beforeAction($action)
    {
        parent::beforeAction($action);
        if (null == Yii::app()->session->get('language')) {
            Yii::app()->session->add('language', 'en');
            Yii::log('addinSession English= '.Yii::app()->language, CLogger::LEVEL_INFO, __METHOD__);
        }

        $this->setappLanguage();
        return true;
    }

    private function setappLanguage()
    {
        // checks the user's language at landing page pre-login

        $language = strtolower((string)substr((string)Yii::app()->session->get('language'), 0,2));
        Yii::app()->language = $language;

        //Yii::log('currentAppLanguage='.Yii::app()->language, CLogger::LEVEL_INFO, 'setappLanguage');
    }

    /**
     * Declares class-based actions.
     */
    public function actions()
    {
        return array(
            // captcha action renders the CAPTCHA image displayed on the contact page
            'captcha' => array(
                'class' => 'CCaptchaAction',
                'backColor' => 0xFFFFFF,
            ),
            // page action renders "static" pages stored under 'protected/views/site/pages'
            // They can be accessed via: index.php?r=site/page&view=FileName
            'page' => array(
                'class' => 'CViewAction',
            ),
        );
    }

    /**
     * This is the default 'index' action that is invoked
     * when an action is not explicitly requested by users.
     */
    public function actionIndex()
    {
        $this->redirect(array('/merchant/login'));

        //$this->setappLanguage();

        $this->render('index', array());
    }

    public function actionHealthCheck()
    {
        new JsTrans('frontend', Yii::app()->language);

//        $user = User::model()->findByPk(rand(1,3000000));

        $this->render('index', array());
    }
    /**
     * Processing the Paypal email verification
     * Used in : Website(MerchantController)
     *
     * @version July - Aug 2015 release V3.3.1
     * @since 3.3.1
     * <AUTHOR>
     * @access public
     * @return view
     */
    public function actionVerifyEmail()
    {
        $platformId = 1;
        $platformName = PlatformSolution::model()->findByPk($platformId);
        $platformName = $platformName->name;
        $dataView = []; //used to send data to the view
        $mainBranchId = null;

        Yii::log('Step1 queryString=' . $_SERVER["QUERY_STRING"] . ' IP:' . Yii::app()->request->userHostAddress, CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

        if (isset($_GET['q']) && $_GET['q'] != '') {
            $q = Yii::app()->format->text($_GET['q']);
            //Yii::log('Step1 queryString='.$_SERVER["QUERY_STRING"], CLogger::LEVEL_INFO, self::PLATFORM_NAME.'_'.'VerifyAccount');
        } else {
            Yii::log('TOKEN IS EMPTY=' . $_SERVER["QUERY_STRING"] . ' IP:' . Yii::app()->request->userHostAddress, CLogger::LEVEL_WARNING, $platformName . '_' . __FUNCTION__);
            throw new CHttpException(404, Yii::t('frontend', 'EX_NOT_FOUND'));
        }

        if (isset($_GET['b'])) {
            $businessId = (int) $_GET['b'];
        } else {
            Yii::log('businessId IS EMPTY=' . $_SERVER["QUERY_STRING"] . ' IP:' . Yii::app()->request->userHostAddress, CLogger::LEVEL_WARNING, $platformName . '_' . __FUNCTION__);
            throw new CHttpException(404, Yii::t('frontend', 'EX_NOT_FOUND'));
        }

        $user = User::model()->findByAttributes(array('passwordreset' => $q));

        if ($user == null) {
            Yii::log('Token does not exist q=' . $_SERVER["QUERY_STRING"] . ' IP:' . Yii::app()->request->userHostAddress, CLogger::LEVEL_WARNING, $platformName . '_' . __FUNCTION__);
            Yii::app()->user->setFlash('error', Yii::t('backend', 'BACKEND_PAYPAL_SETUP_ACCOUNT_EMAIL_INVALID_VERIF_LINK'));
            $this->redirect(Yii::app()->createUrl('merchant/login'));
        }

        $now = date("Y-m-d H:i:s");
        //Checking if the link has expired
        if ($now > $user->utc_link_expires) {
            Yii::app()->user->setFlash('error', Yii::t('frontend', 'LINK_EXPIRED'));
            Yii::log("userId = $user->id now=$now utc_link_expires= $user->utc_link_expires" . 'queryString=' . $_SERVER["QUERY_STRING"] . ' IP:' . Yii::app()->request->userHostAddress, CLogger::LEVEL_WARNING, $platformName . '_' . __FUNCTION__);
            $this->redirect(Yii::app()->createUrl('merchant/login'));
        }

        if ($user != null) //verify the user's email
        {
            $timezoneId = Utils::getTimezoneSession();

            $bankAccountModel = BusinessBankAccount::getLastAccountRecord($businessId);

            $lastStatus = UserStatusManager::lastStatusByUserIdCredentialStatus($user->id, $bankAccountModel->email_account, [62]);
            if ($lastStatus != null) //MERCHANT_PAYPAL_EMAIL_NOT_VERIFIED
            {
                UserStatusManager::createUserStatusManagerRecordV330($user->id, $modifiedBy = null, $status_manager_fk = 63, '', $bankAccountModel->email_account, $platformId, $timezoneId); //MERCHANT_PAYPAL_EMAIL_VERIFIED

                $isAccountVerified = BusinessBankAccount::isAccountVerified($user->id, $businessId, $platformName);

                if ($isAccountVerified) {
                    $bankAccountModel->enabled = 1;
                    $bankAccountModel->update('enabled');
                }

                $user->passwordreset = '';
                $user->update('passwordreset');

                Yii::app()->user->setFlash('success', Yii::t('backend', 'BACKEND_PAYPAL_SETUP_ACCOUNT_EMAIL_SUCCESS_VERIFIED'));
                $this->redirect(Yii::app()->createUrl('merchant/login'));
            } else {
                Yii::log('userId = ' . $user->id . ' lastStatus = ' . CJSON::encode($lastStatus) . ' queryString=' . $_SERVER["QUERY_STRING"], CLogger::LEVEL_WARNING, $platformName . '_' . __FUNCTION__);
                Yii::app()->user->setFlash('error', Yii::t('backend', 'BACKEND_PAYPAL_SETUP_ACCOUNT_EMAIL_INVALID_VERIF_LINK'));
                $this->redirect(Yii::app()->createUrl('merchant/login'));
            }
        }

        $this->redirect(Yii::app()->createUrl('merchant/login'));
    }

    /**
     * Merchant signup STEP 1
     * Insertin in Business, Business Branch, User, user_ermissions tables
     * sending the email to business Admin and to sales
     * Handle timezone
     * Used in : Website(MerchantController)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return void
     * @throws CHttpException
     */
    public function actionSignup()
    {
        // header("Access-Control-Allow-Origin: http://lp.kangaroorewards.com");
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Methods", "GET, POST, PUT, OPTIONS, HEAD");
        header("Access-Control-Allow-Headers", "Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
        header("X-Robots-Tag: noindex", true);

        if (isset($_GET['lang'])) {
            $lang = strtolower((string)substr((string)$_GET['lang'], 0,2));
            if (in_array($lang, Language::getEnabledAbrev())) {
                Yii::app()->session->add('language', $lang);
            }

            $this->setappLanguage();
        }
        Yii::log("GET Request param here is ".json_encode($_GET)." at function ".__FUNCTION__,CLogger::LEVEL_INFO,__METHOD__);

        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $ip = (Yii::app()->request->userHostAddress == '127.0.0.1') ? Utils::getExternalIPV330($platformName)['ip'] : Yii::app()->request->userHostAddress;

        if (!Yii::app()->user->isGuest) {
            Yii::log('IsGuest='. Yii::app()->user->getID() .' IP='. $ip . ' _GET='. CJSON::encode($_GET), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            Yii::app()->user->logout(false);
        }

        $currentLanguage = Yii::app()->language;
        $utmSource = 'unknown';
        $utmPackage = isset($_GET['package']) ? ' package:'.$_GET['package'] : '';
        $platformId = PLATFORM_WEBSITE;

        if(isset($_GET['utm_source']) && $_GET['utm_source'] == 'webapp') {
            $utmSource = $_GET['utm_source'] . $utmPackage;
            $platformId = PLATFORM_MERCHANT_WEB_APP;
        } elseif (isset($_GET['utm_source'])) {
            $utmSource = $_GET['utm_source'] . $utmPackage;
            $platformId = PLATFORM_WEBSITE;
        }

        Yii::app()->session['lead_platform_id'] = $platformId;
        Yii::app()->session['lead_utm_source'] = $utmSource;

        // Add a business for Coalition
        if (isset($_GET['coalitionId'])) {
            // Get business model for coalition uuid
            $coalition = Business::findByUuid($_GET['coalitionId']);

            if (!$coalition) {
                Yii::log('Coalition not found=' . $_SERVER["QUERY_STRING"] . ' IP:' . $ip, CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
                throw new CHttpException(404, Yii::t('frontend', 'EX_NOT_FOUND'));
            }
            // store internal Business ID (Coalition)
            Yii::app()->session['lead_coalition_id'] = $coalition->business_pk;
        }

        new JsTrans('frontend', $currentLanguage);

        $model = new MerchantSignupForm('step1');

        Yii::log('New Lead IP=' . $ip . ' platformId='. $platformId.' GET=' . CJSON::encode($_GET), CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

//        TODO: Remove this code. It can be usesd to create business, send email
        // bypassing captcha in GET Request. Dangerous
//        if (isset($_GET['MerchantSignupForm'])) {
//            Yii::log('New Business Signup request IP=' . $ip . ' GET=' . CJSON::encode($_GET) . ' platformId='. $platformId, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
//
//            if (isset($_GET['utm_source'])) {
//                $utmSource = $_GET['utm_source'];
//            }
//
//            // TODO: Remove after attack
//            Utils::sendResponse(200, CJSON::encode([
//                'status' => 'OK',
//                'message' => Yii::t('frontend', 'BUSINESS_SAVED_STEP1'),
//            ]));
//
//            //Do not continue if is comming from Unbounce
//            if ($utmSource == 'unbounce') {
//                Yii::log('From Unbounce IP=' . $ip . ' GET=' . CJSON::encode($_GET), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
//
//                Utils::sendResponse(200, CJSON::encode([
//                    'status' => 'OK',
//                    'message' => Yii::t('frontend', 'BUSINESS_SAVED_STEP1'),
//                ]));
//            }
//
//            $model->business_name = trim((string)$_GET['MerchantSignupForm']['business_name']);
//            $model->email = isset($_GET['MerchantSignupForm']['email']) ? trim((string)$_GET['MerchantSignupForm']['email']) : null;
//
//            $model->contact_name = isset($_GET['MerchantSignupForm']['contact_name']) ? $_GET['MerchantSignupForm']['contact_name'] : null;
//            $model->phone = isset($_GET['MerchantSignupForm']['phone']) ? $_GET['MerchantSignupForm']['phone'] : null;
//
//            if (!$model->validate()) {
//                Yii::log('Validation errors IP=' . $ip . ' error=' . CJSON::encode($model->getErrors()), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
//                Utils::sendResponse(200, CJSON::encode(array('status' => 'errors', 'errors' => $model->getErrors())));
//            }
//
//            $reqInfo['apiUrl'] = '/api/api/MerchantAppLead';
//
//            //standard form data
//            $formData['appToken'] = APP_TOKEN_WEBSITE;
//            $formData['business_name'] = $model->business_name;
//            $formData['email'] = $model->email;
//            $formData['platformSolutionFk'] = $platformId;
//            $formData['lang'] = Yii::app()->language;
//            $formData['device_id'] = null;
//            $formData['utm_source'] = $utmSource;
//            $formData['utm_package'] = $utmPackage;
//
//            $resultJson = Utils::httpRequest($reqInfo, $formData, $platformName);
//            $r = CJSON::decode($resultJson);
//            Yii::log('Lead after API request IP=' . $ip . ' Result=' . $resultJson, CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
//
//            if ($r['status'] == 'OK') {
//                Utils::sendResponse(200, CJSON::encode([
//                    'status' => 'OK',
//                    'message' => Yii::t('frontend', 'BUSINESS_SAVED_STEP1'),
//                ]));
//            } else {
//                Utils::sendResponse(200, CJSON::encode($r));
//            }
//        }

        $this->render('lead', array('model' => $model));
    }

    private function checkLimitCreateLead()
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $ip = (Yii::app()->request->userHostAddress == '127.0.0.1') ? Utils::getExternalIPV330($platformName)['ip'] : Yii::app()->request->userHostAddress;

        $cacheKey = $ip . '_create_business';
        $expires = 60 * 10; //10 min
        $attempts = (int) Yii::app()->cache->get($cacheKey);

//        TODO: Remove after attack
//        Utils::sendResponse(200, CJSON::encode(['status' => 'NOT_OK', 'message' => 'Limit reached']));

        if ($attempts >= 2) {
            Yii::log(json_encode([
                'ip' => $ip,
                'GET' => $_GET,
                'POST' => $_POST,
                'message' => '**LIMIT REACHED** Create business',
            ]), CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);

            Utils::sendResponse(200, CJSON::encode(['status' => 'NOT_OK', 'message' => 'Limit reached']));
        }

        // Store attempts in cache
        Yii::app()->cache->set($cacheKey, $attempts + 1, $expires);
    }

    /**
     * Step1. Create new Lead
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.2.0
     * @since 4.2.0
     * <AUTHOR>
     * @access public
     * @return json
     */
    public function actionLead()
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $ip = (Yii::app()->request->userHostAddress == '127.0.0.1') ? Utils::getExternalIPV330($platformName)['ip'] : Yii::app()->request->userHostAddress;

        if (!Yii::app()->user->isGuest) {
            Yii::log('IsGuest='. Yii::app()->user->getID() .' IP='. $ip . ' POST='. CJSON::encode($_POST), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            Yii::app()->user->logout(false);
            // Yii::app()->user->setFlash('info', Yii::t('frontend', 'FRONTEND_SESSION_EXPIRED'));
            // $this->redirect('/merchant/signup');
        }

        if(!Utils::feature('CREATE_BUSINESS')) {
            Utils::sendResponse(200, CJSON::encode([
                'status' => 'NOT_OK',
                'message' => 'Business cannot be created at this moment',
            ]));
        }

        $model = new MerchantSignupForm('step1_website');

        if (!isset($_POST['MerchantSignupForm'])) {
            exit;
        }

        $model->business_name = trim((string)$_POST['MerchantSignupForm']['business_name']??'');
        $model->email = isset($_POST['MerchantSignupForm']['email']) ? trim((string)$_POST['MerchantSignupForm']['email']??'') : null;
        $model->reCaptchaResponse = $_POST['g-recaptcha-response'];
        $model->agreement = $_POST['MerchantSignupForm']['agreement'];
        $platformId = (Yii::app()->session['lead_platform_id']==null) ? 1 : Yii::app()->session['lead_platform_id'];
        $utmSource = (Yii::app()->session['lead_utm_source']==null) ? 'unknown' : Yii::app()->session['lead_utm_source'];

        Yii::log('New Business Signup request IP=' . $ip . ' POST=' . CJSON::encode($_POST) . ' platformId='. $platformId, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);

        if ($model->validate()) {
            if(Utils::feature('EMAIL_DEEP_VERIFICATION')) {
                $isValid = Utils::emailDeepVerification($model->email);
                if (!$isValid) {
                    Utils::sendResponse(200, CJSON::encode([
                        'status' => 'NOT_OK',
                        'message' => 'The email is not a valid email address',
                    ]));
                }
            }

            $this->checkLimitCreateLead();
            $reqInfo['apiUrl'] = '/api/api/MerchantAppLead';

            //standard form data
            $formData['appToken'] = APP_TOKEN_WEBSITE;
            $formData['business_name'] = $model->business_name;
            $formData['email'] = $model->email;
            $formData['platformSolutionFk'] = $platformId;
            $formData['lang'] = Yii::app()->language;
            $formData['device_id'] = null;
            $formData['utm_source'] = $utmSource;
            if($utmSource == 'integration-page') {
                $formData['emailView'] = 'businessWelcomeEssentialPackage';
            }
            $resultJson = Utils::httpRequest($reqInfo, $formData, $platformName);
            $r = CJSON::decode($resultJson);

            Yii::log('Lead created request IP=' . $ip . ' Result=' . $resultJson, CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

            if ($r['status'] == 'OK') {
                //Store in session
                Yii::app()->session['lead_verify_code'] = $r['results']['lead_random'];
                Yii::app()->session['lead_merchant_lead_id'] = $r['results']['merchant_lead_id'];
                Yii::app()->session['lead_user_name'] = $r['results']['lead_user_name'];
                Yii::app()->session['lead_verified'] = false;

                Utils::sendResponse(200, CJSON::encode([
                    'status' => 'OK',
                    'message' => '', //$r['message'],
                    'next' => 'verify_code',
                ]));
            } else {
                Utils::sendResponse(200, $resultJson);
            }

        } else {
            Yii::log('Validation errors IP=' . $ip . ' error=' . CJSON::encode($model->getErrors()), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            Utils::sendResponse(200, CJSON::encode(array('status' => 'errors', 'errors' => $model->getErrors())));
        }
    }

    /**
     * Create the business from email link
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.2.0
     * @since 4.2.0
     * <AUTHOR>
     * @access public
     * @return json
     */
    public function actionStart()
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $ip = (Yii::app()->request->userHostAddress == '127.0.0.1') ? Utils::getExternalIPV330($platformName)['ip'] : Yii::app()->request->userHostAddress;

        Yii::log('Step1 queryString='.$_SERVER["QUERY_STRING"], CLogger::LEVEL_INFO, $platformName.'_'.__METHOD__);

        if (isset($_GET['q']) && $_GET['q'] != '') {
            $q = Yii::app()->format->text($_GET['q']);
        } else {
            Yii::log('TOKEN IS EMPTY=' . $_SERVER["QUERY_STRING"] . ' IP:' . $ip, CLogger::LEVEL_WARNING, $platformName . '_' . __METHOD__);
            throw new CHttpException(404, Yii::t('frontend', 'EX_NOT_FOUND'));
        }

        $leadModel = MerchantAppLead::model()->findByAttributes(['token' => $q]);

        if ($leadModel == null) {
            Yii::log('Token does not exist q=' . $_SERVER["QUERY_STRING"] . ' IP:' . $ip, CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
            // Yii::app()->user->setFlash('error', Yii::t('backend', 'EX_NOT_FOUND'));
            $this->redirect(Yii::app()->createUrl('merchant/login'));
        } elseif ($leadModel->business_fk) {
            //business is already created redirect to login
            $this->redirect(Yii::app()->createUrl('merchant/login'));
        }

        Yii::app()->session['lead_verified'] = true;
        Yii::app()->session['lead_merchant_lead_id'] = $leadModel->merchant_app_lead_pk;
        Yii::app()->session['lead_platform_id'] = PLATFORM_WEBSITE;
        Yii::app()->session['lead_verify_code'] = $leadModel->temp_code;
        Yii::app()->session['lead_user_name'] = User::createUsername($leadModel->email);

        $timezoneId = Utils::getTimezoneSession();
        $timeZoneName = TimezoneMysql::model()->findByPk($timezoneId)->name;

        $reqInfo['apiUrl'] = '/api/api/MerchantSignup';

        $formData['appToken'] = APP_TOKEN_WEBSITE;
        $formData['business_name'] = $leadModel->name;
        $formData['timeZoneName'] = $timeZoneName;
        $formData['email'] = $leadModel->email;
        $formData['ip'] = $ip;
        $formData['random_psw'] = Yii::app()->session['lead_verify_code'];
        $formData['business_user_name'] = Yii::app()->session['lead_user_name'];
        $formData['platformSolutionFk'] = Yii::app()->session['lead_platform_id'];
        $formData['lang'] = Yii::app()->language;
        $formData['device_id'] = null;
        $formData['coalition_id'] = Yii::app()->session['lead_coalition_id'];

        $resultJson = Utils::httpRequest($reqInfo, $formData, $platformName);
        Yii::log('New Business ' . $resultJson, CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);
        $r = CJSON::decode($resultJson);

        if ($r['status'] == 'OK') {
            Yii::app()->session['signup_business_id'] = $r['results']['business_id'];
            Yii::app()->session['signup_branch_id'] = $r['results']['business_branch'];
            Yii::app()->session['signup_user_id'] = $r['results']['user_id'];
            Yii::app()->session['signup_username'] = $r['results']['user'];

            $this->redirect(Yii::app()->createUrl('merchant/signup2'));
        } else {
            Yii::app()->user->setFlash('error', Yii::t('backend', 'UNKNOWN_ERROR'));
            $this->redirect(Yii::app()->createUrl('merchant/login'));
        }
    }

    /**
     * Step1a. Verify password sent by email
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.2.0
     * @since 4.2.0
     * <AUTHOR>
     * @access public
     * @return json
     */
    public function actionVerifyCode()
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $ip = (Yii::app()->request->userHostAddress == '127.0.0.1') ? Utils::getExternalIPV330($platformName)['ip'] : Yii::app()->request->userHostAddress;

        if (!Yii::app()->user->isGuest) {
            Yii::log('IsGuest='. Yii::app()->user->getID() .' IP='. $ip . ' POST='. CJSON::encode($_POST), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            Yii::app()->user->logout(false);
            // Yii::app()->user->setFlash('info', Yii::t('frontend', 'FRONTEND_SESSION_EXPIRED'));
            // $this->redirect('/merchant/signup');
        }

        $inputPassword = isset($_POST['verify_code']) ? $_POST['verify_code'] : null;
        $sessionPassword = Yii::app()->session['lead_verify_code'];

        if ($inputPassword && $sessionPassword && $inputPassword == $sessionPassword) {
            Yii::app()->session['lead_verified'] = true;

            $merchantLeadId = Yii::app()->session['lead_merchant_lead_id'];
            $merchantUsername = Yii::app()->session['lead_user_name'];

            $leadModel = MerchantAppLead::model()->findByPk($merchantLeadId);
            $timezoneId = Utils::getTimezoneSession();
            $timeZoneName = TimezoneMysql::model()->findByPk($timezoneId)->name;

            $reqInfo['apiUrl'] = '/api/api/MerchantSignup';

            $formData['appToken'] = APP_TOKEN_WEBSITE;
            $formData['business_name'] = $leadModel->name;
            $formData['timeZoneName'] = $timeZoneName;
            $formData['email'] = $leadModel->email;
            $formData['ip'] = $ip;
            $formData['random_psw'] = $sessionPassword;
            $formData['business_user_name'] = $merchantUsername;
            $formData['platformSolutionFk'] = Yii::app()->session['lead_platform_id'];
            $formData['lang'] = Yii::app()->language;
            $formData['device_id'] = null;
            $formData['coalition_id'] = Yii::app()->session['lead_coalition_id'];

            $resultJson = Utils::httpRequest($reqInfo, $formData, $platformName);
            $r = CJSON::decode($resultJson);

            if ($r['status'] == 'OK') {
                Yii::app()->session['signup_business_id'] = $r['results']['business_id'];
                Yii::app()->session['signup_branch_id'] = $r['results']['business_branch'];
                Yii::app()->session['signup_user_id'] = $r['results']['user_id'];
                Yii::app()->session['signup_username'] = $r['results']['user'];

                Utils::sendResponse(200, CJSON::encode([
                    'status' => 'OK',
                    'message' => '',
                    'next' => 'about_business',
                ]));
            } else {
                Utils::sendResponse(200, CJSON::encode($r));
            }
        } else {
            Yii::log('Verification code doesnt match=' . $ip . ' sessionPassword=' .$sessionPassword. ' POST='. CJSON::encode($_POST), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);

            Utils::sendResponse(200, CJSON::encode([
                'status' => 'NOT_OK',
                'message' => Yii::t('merchantapp', 'MERCHANTAPP_WRONG_PASSWORD'),
            ]));
        }
    }

    /**
     * VerifyCode redirect to integration install
     *
     * <AUTHOR>
     * @version V4.33
     * @throws Exception
     */
    public function actionVerifyCodeIntegration()
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $ip = (Yii::app()->request->userHostAddress == '127.0.0.1') ? Utils::getExternalIPV330($platformName)['ip'] : Yii::app()->request->userHostAddress;

        if (!Yii::app()->user->isGuest) {
            Yii::log('IsGuest=' . Yii::app()->user->getID() . ' IP=' . $ip . ' POST=' . CJSON::encode($_POST), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            Yii::app()->user->logout(false);
            // Yii::app()->user->setFlash('info', Yii::t('frontend', 'FRONTEND_SESSION_EXPIRED'));
            // $this->redirect('/merchant/signup');
        }

        $inputPassword = isset($_POST['verify_code']) ? $_POST['verify_code'] : null;
        $sessionPassword = Yii::app()->session['lead_verify_code'];

        if (isset($_POST['redirect'],$_POST['utm_source']) && $_POST['utm_source'] =='integration-page' && $inputPassword && $sessionPassword && $inputPassword == $sessionPassword) {
            Utils::sendResponse(200, CJSON::encode([
                'status' => 'redirect',
                'message' => '',
                'url' => Yii::app()->createUrl($_POST['redirect']),
            ]));
        } else {
            Yii::log('Verification code doesnt match=' . $ip . ' sessionPassword=' . $sessionPassword . ' POST=' . CJSON::encode($_POST), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);

            Utils::sendResponse(200, CJSON::encode([
                'status' => 'NOT_OK',
                'message' => Yii::t('merchantapp', 'MERCHANTAPP_WRONG_PASSWORD'),
            ]));
        }
    }
    /**
     * Step2. Create the business
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.2.0
     * @since 4.2.0
     * <AUTHOR>
     * @access public
     * @return json
     */
    public function actionSignup2()
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $ip = (Yii::app()->request->userHostAddress == '127.0.0.1') ? Utils::getExternalIPV330($platformName)['ip'] : Yii::app()->request->userHostAddress;

        if (!Yii::app()->user->isGuest) {
            Yii::log('IsGuest='. Yii::app()->user->getID() .' IP='. $ip . ' POST='. CJSON::encode($_POST), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            Yii::app()->user->logout(false);
        }

        //Checking if the email was verified in previous step
        if (!Yii::app()->session['lead_verified'] || Yii::app()->session['lead_verified'] == false) {
            Yii::log('Not verified redirect to signup page IP=' . Yii::app()->request->userHostAddress, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            $this->redirect('/merchant/signup');
        }

        Yii::app()->session['signup_wizard_saved'] = false;

        $model = new MerchantSignupForm('step2');

        if (isset($_POST['MerchantSignupForm'])) {

            $countryFk = null;
            $countryCode = $_POST['country_code'];

            $timezoneId = Utils::getTimezoneSession();
            $timeZoneName = TimezoneMysql::model()->findByPk($timezoneId)->name;

            if ($countryCode == '' || $countryCode == null) {
                Yii::log('Phone Country code is NULL IP=' . Yii::app()->request->userHostAddress . ' businessId=' . Yii::app()->session['businessId'], CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
                Utils::sendResponse(200, CJSON::encode(['status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR')]));
            }

            $reqInfo['apiUrl'] = '/api/api/MerchantSignupStep2';

            //standard form data
            $formData['appToken'] = APP_TOKEN_WEBSITE;
            $formData['businessId'] = Yii::app()->session['signup_business_id'];
            $formData['businessBranchId'] = Yii::app()->session['signup_branch_id'];
            $formData['business_logo'] = isset($_POST['MerchantSignupForm']['logo_image']) ? $_POST['MerchantSignupForm']['logo_image'] : null;
            $formData['business_logo_image_large'] = isset($_POST['MerchantSignupForm']['logo_image_large']) ? $_POST['MerchantSignupForm']['logo_image_large'] : null;
            $formData['business_logo_image_medium'] = isset($_POST['MerchantSignupForm']['logo_image_medium']) ? $_POST['MerchantSignupForm']['logo_image_medium'] : null;
            $formData['business_logo_image_thumbnail'] = isset($_POST['MerchantSignupForm']['logo_image_thumbnail']) ? $_POST['MerchantSignupForm']['logo_image_thumbnail'] : null;
            $formData['street_number'] = ($_POST['MerchantSignupForm']['street_number']) ? $_POST['MerchantSignupForm']['street_number'] .' '  : '';
            $formData['country'] = $_POST['MerchantSignupForm']['country'];
            $formData['country_short'] = $_POST['MerchantSignupForm']['country_short'];
            $formData['province'] = $_POST['MerchantSignupForm']['province'];
            $formData['city'] = $_POST['MerchantSignupForm']['city'];
            $formData['zipcode'] = $_POST['MerchantSignupForm']['zipcode'];
            $formData['route'] = $_POST['MerchantSignupForm']['route'];
            $formData['lat'] = $_POST['MerchantSignupForm']['lat'];
            $formData['lng'] = $_POST['MerchantSignupForm']['lng'];
            $formData['timeZoneName'] = $timeZoneName;
            $formData['contact_name'] = $_POST['MerchantSignupForm']['contact_name'];
            $formData['phone'] = preg_replace("/[^0-9]/", '', $_POST['MerchantSignupForm']['phone']??'');
            $formData['country_code'] = $countryCode;
            $formData['short_description'] = $_POST['MerchantSignupForm']['short_description'];
            $formData['ip'] = (Yii::app()->request->userHostAddress == '127.0.0.1') ? Utils::getExternalIPV330($platformName)['ip'] : Yii::app()->request->userHostAddress;
            $formData['platformSolutionFk'] = Yii::app()->session['lead_platform_id'];
            $formData['lang'] = Yii::app()->language;
            $formData['device_id'] = null;

            $resultJson = Utils::httpRequest($reqInfo, $formData, $platformName);
            // echo $resultJson; die;

            $r = CJSON::decode($resultJson);

            if ($r['status'] == 'OK') {
                Utils::sendResponse(200, CJSON::encode([
                    'status' => 'OK',
                    'message' => '', //$r['message']
                    'next' => 'choose_category',
                ]));
            } else {
                Utils::sendResponse(200, CJSON::encode($r));
            }
        }

        new JsTrans('frontend', Yii::app()->language);

        $countries = Countries::getAllCountries(); //for countries dropdown
        $categories = BusinessCategory::loadBusinessCategories();
        $timezoneId = Utils::getTimezoneSession();

        //for phone number formatting
        $ip = (Yii::app()->request->userHostAddress == '127.0.0.1') ? null : Yii::app()->request->userHostAddress;
        $allCounties = Utils::getEnabledCountriesWithDefaultSelected($platformName, $ip);
        $selectedCountry = $allCounties['selectedCountry']['code']; //country code: CA, US
        $allCounties = array_keys(CHtml::listData($allCounties['countryList'], 'code', 'name'));
        $allCounties = json_encode($allCounties);

        $this->render('signup2', array('model' => $model, 'countries' => $countries, 'categories' => $categories, 'allCounties' => $allCounties, 'selectedCountry' => $selectedCountry));
    }

    /**
     * Step2a Select Business Category
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.2.0
     * @since 4.2.0
     * <AUTHOR>
     * @access public
     * @return json/html
     */
    public function actionCategory()
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $ip = (Yii::app()->request->userHostAddress == '127.0.0.1') ? Utils::getExternalIPV330($platformName)['ip'] : Yii::app()->request->userHostAddress;

        if (!Yii::app()->user->isGuest) {
            Yii::log('IsGuest='. Yii::app()->user->getID() .' IP='. $ip . ' POST='. CJSON::encode($_POST), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            Yii::app()->user->logout(false);
            // Yii::app()->user->setFlash('info', Yii::t('frontend', 'FRONTEND_SESSION_EXPIRED'));
            // $this->redirect('/merchant/signup');
        }

        //Checking if the email was verified in previous step
        if (!Yii::app()->session['lead_verified'] || Yii::app()->session['lead_verified'] == false) {
            Yii::log('Not verified redirect to signup page IP=' . Yii::app()->request->userHostAddress, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            $this->redirect('/merchant/signup');
        }

        if (isset($_POST['category'])) {
            $category = (int) $_POST['category'];
            $businessId = Yii::app()->session['signup_business_id'];
            $businessBranchId = Yii::app()->session['signup_branch_id'];

            $businessModel = Business::model()->findByPk($businessId);

            if (!$businessModel) {
                Yii::log('businessModel not found IP=' . $ip, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
                $this->redirect('/merchant/signup');
            }

            $businessModel->category = $category == 0 ? 99 : $category;
            Utils::saveModel($businessModel);
            Utils::sendResponse(200, CJSON::encode([
                'status' => 'OK',
                'message' => '',
                'next' => 'wizard',
            ]));
        }

        $categories = BusinessCategory::model()->findAll();

        $this->render('categories', ['categories' => $categories]);
    }

    /**
     * Wizard - the view
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.2.0
     * @since 4.2.0
     * <AUTHOR>
     * @access public
     * @return html
     */
    public function actionWizard()
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $ip = (Yii::app()->request->userHostAddress == '127.0.0.1') ? Utils::getExternalIPV330($platformName)['ip'] : Yii::app()->request->userHostAddress;

        if (!Yii::app()->user->isGuest) {
            Yii::log('IsGuest='. Yii::app()->user->getID() .' IP='. $ip . ' POST='. CJSON::encode($_POST), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            Yii::app()->user->logout(false);
            // Yii::app()->user->setFlash('info', Yii::t('frontend', 'FRONTEND_SESSION_EXPIRED'));
            // $this->redirect('/merchant/signup');
        }

        //Checking if the email was verified in previous step
        if (!Yii::app()->session['lead_verified'] || Yii::app()->session['lead_verified'] == false) {
            Yii::log('Not verified redirect to signup page IP=' . $ip, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            $this->redirect('/merchant/signup');
        }

        $businessId = Yii::app()->session['signup_business_id'];
        $businessBranchId = Yii::app()->session['signup_branch_id'];

        if (isset(Yii::app()->session['signup_wizard_saved']) && Yii::app()->session['signup_wizard_saved']) {
            Yii::log('Wizard already saved IP=' . $ip . ' businessId='.$businessId, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            $this->redirect(Yii::app()->params['betaUrl']);
        }

        $businessModel = Business::model()->findByPk($businessId);
        $branchModel = BusinessBranch::model()->findByPk($businessBranchId);

        if (!$businessModel || !$branchModel) {
            Yii::log('businessModel not found IP=' . $ip, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            $this->redirect('/merchant/signup');
        }

        $currencySymbol = '';
        $currency = Currency::getCurrencySymbolByBranchId($businessBranchId, $platformName);

        if ($currency != null) {
            $currencySymbol = $currency['currencySymbol'];
        }

        $this->render('wizard', ['currency' => $currencySymbol]);
    }

    /**
     * Save Wizard
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.2.0
     * @since 4.2.0
     * <AUTHOR>
     * @access public
     * @return json
     */
    public function actionSaveWizard()
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $ip = (Yii::app()->request->userHostAddress == '127.0.0.1') ? Utils::getExternalIPV330($platformName)['ip'] : Yii::app()->request->userHostAddress;

        if (!Yii::app()->user->isGuest) {
            Yii::log('IsGuest=' . Yii::app()->user->getID() . ' IP=' . $ip . ' POST=' . CJSON::encode($_POST), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            Yii::app()->user->logout(false);
            // Yii::app()->user->setFlash('info', Yii::t('frontend', 'FRONTEND_SESSION_EXPIRED'));
            // $this->redirect('/merchant/signup');
        }

        //Checking if the email was verified in previous step
        if (!Yii::app()->session['lead_verified'] || Yii::app()->session['lead_verified'] == false) {
            Yii::log('Not verified redirect to signup page IP=' . $ip, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            $this->redirect('/merchant/signup');
        }

        $businessId = Yii::app()->session['signup_business_id'];
        $businessBranchId = Yii::app()->session['signup_branch_id'];

        $businessModel = Business::model()->findByPk($businessId);
        $branchModel = BusinessBranch::model()->findByPk($businessBranchId);

        if (!$businessModel || !$branchModel) {
            Yii::log('businessModel not found IP=' . $ip, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            $this->redirect('/merchant/signup');
        }

        $currencySymbol = '';
        $currency = Currency::getCurrencySymbolByBranchId($businessBranchId, $platformName);

        if ($currency != null) {
            $currencySymbol = $currency['currencySymbol'];
        }

        $discountAmount = $_POST['discount_amount'];
        $pointsRewardTitle = $discountAmount . $currencySymbol . ' Discount';
        $businessEntity = $_POST['entity_type_fk'];

        $reqInfo['apiUrl'] = '/api/api/MerchantWizardV420';

        $formData = $_POST;
        $formData['appToken'] = APP_TOKEN_WEBSITE;
        $formData['business_branch_pk'] = $businessBranchId;
        $formData['userId'] = Yii::app()->session['signup_user_id'];
        $formData['redemp_cat_title'] = ($businessEntity == 1) ? $pointsRewardTitle : $_POST['redemp_cat_title'];
        $formData['platformSolutionFk'] = Yii::app()->session['lead_platform_id'];
        $formData['business_industry_pk'] = $businessModel->category;
        $formData['lang'] = Yii::app()->language;
        $formData['device_id'] = null;

        $resultJson = Utils::httpRequest($reqInfo, $formData, $platformName);
        // echo $resultJson; die;
        $r = CJSON::decode($resultJson);

        if ($r['status'] === 'OK') {
            Yii::app()->session['signup_wizard_saved'] = true;
            $leadModel = MerchantAppLead::model()->findByAttributes(['business_fk' => $businessId]);
            $leadModel->temp_code = null;
            $leadModel->save();

            $businessModel->refresh();
            $businessModel->status_manager_fk = STATUS_MANAGER_VERIFIED;
            Utils::saveModel($businessModel);

            $prices = KPackagePrice::findPackagePrice(2);
            if (isset($prices[0]) && $prices[0]->price == 0) {
                BusinessRules::saveBusinessRulesFromPackage($prices[0]->package_fk, $businessId);
            }

            $userId = Yii::app()->session['signup_user_id'];
            $user = User::model()->findByPk($userId);
            $redirect = rtrim((string)Yii::app()->params['betaUrl'], '/');
            if ($user && isset($_POST['password'])) {
                try {
                    $identity = new UserBearerTokenIdentity($userId);
                    if ($identity->authenticate()) {
                        if (Yii::app()->user->login($identity, $duration = 0)) {
                            $this->getTokenForMerchant($user, $_POST['password']);
                            $redirect = rtrim((string)Yii::app()->params['serverUrl'], '/') . '/merchant/newPortal';
                        }
                    }
                } catch (\Exception $e) {
                    Yii::log($e . ' user id=' . $userId . ' password:' . $_POST['password'], CLogger::LEVEL_ERROR, __METHOD__);
                }
            }

            Utils::sendResponse(200, CJSON::encode([
                'status' => 'OK',
                'message' => '',
                'next' => 'merchant_login',
                'redirect' => $redirect,
            ]));
        } else {
            Utils::sendResponse(200, CJSON::encode($r));
        }
    }

    /**
     * Getting latitude and longitude by address from google maps
     *
     * Used on : Website(Merchant Signup)
     *
     * <AUTHOR>
     * @version Jul - Aug 2015 release V3.3.1
     * @since 3.2.0
     * @access private
     * @return array
     */

    private function getLatLongByAddress($postMerchantSignupForm)
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $stateName = '';
        $countryName = '';
        $cityName = '';
        $zipcode = '';

        if ($postMerchantSignupForm['country'] != '') {
            $countryName = Countries::model()->findByPk($postMerchantSignupForm['country'])->name;
        }

        if ($postMerchantSignupForm['province'] != '') {
            $stateName = Regions::model()->findByPk($postMerchantSignupForm['province'])->name;
        }

        if ($postMerchantSignupForm['city'] != '') {
            $cityName = Cities::model()->findByPk($postMerchantSignupForm['city'])->name;
        }

        if ($postMerchantSignupForm['zipcode'] != '') {
            $zipcode = ', ' . $postMerchantSignupForm['zipcode'];
        }

        $address = $postMerchantSignupForm['address'] . ', ' . $cityName . ', ' . $stateName . ', ' . $countryName . $zipcode;

        //Yii::log('Getting lat/long from address: '.$address, CLogger::LEVEL_INFO, $platformName.'_'.__METHOD__);
        //$prepAddr = str_replace(' ','+',$address);
        return Utils::getLongLatFromAddress($address, $platformName);
    }

    /**
     * TIMEZONE - Globalization implementation
     * Login with email and password
     * Added captcha after 3 login attempts
     * Used in : Website(Login)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return html
     */
    public function actionLogin()
    {
        $isUser = false;
        $redirectUrl = null;
        $jwtForPModule = null;
        $isBusinessDisabled = isset($_GET['disabled']) && (int) $_GET['disabled'] === 1;
        $loginForm = $this->captchaRequired() ? new LoginForm('captchaRequired') : new LoginForm('login');
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $socialLogin = '';

        $this->loginPageViewLog();

        if (isset($_POST['credential'], $_POST['g_csrf_token'])) {
            $loginForm = new LoginForm('googleLogin');
        }
        unset($_COOKIE['nbp_origin']);
        $urlParts = parse_url(Yii::app()->params['serverUrl']);
        setcookie('nbp_origin', '', -1, '/', $urlParts['host'], true, true);

        if (isset($_GET['username'])) {
            $loginForm->username = $_GET['username'];
        }

        if (isset($_GET['redirect']) && $_GET['redirect'] != '') {
            $redirectUrl = str_replace("redirect=", "", $_SERVER["QUERY_STRING"]);
        }

        if (isset($_POST['credential'], $_POST['g_csrf_token'])) {
            if(isset($_COOKIE['g_csrf_token'])) $googleSignIn = new GoogleSignIn($_POST['credential'], $_POST['g_csrf_token'], $_COOKIE['g_csrf_token']);
            else $googleSignIn = new GoogleSignIn($_POST['credential'], $_POST['g_csrf_token'], null);
            $cred = $googleSignIn->getUserCredentials();
            $_POST['LoginForm']['username'] = $cred['email'];
            $socialLogin = 'google';
        }

        if (isset($_GET['source'],$_GET['platform']) && $_GET['source'] === 'cim' && $_GET['platform'] === 'lightspeed') {
            $jwtForPModule = trim((string)$_GET['source']);
            $redirectUrl = '/pos/lightspeed/profile?' . $_SERVER["QUERY_STRING"];
        } elseif (isset($_GET['source'],$_GET['platform']) && $_GET['source'] === 'cim' && $_GET['platform'] === 'heartland') {
            $jwtForPModule = trim((string)$_GET['source']);
            $redirectUrl = '/pos/heartland/profile?' . $_SERVER["QUERY_STRING"];
        } elseif (isset($_GET['source'],$_GET['platform']) && $_GET['source'] === 'attachToSale' && $_GET['platform'] === 'lightspeed') {
            $jwtForPModule = trim((string)$_GET['source']);
            $redirectUrl = '/pos/lightspeed/register?' . $_SERVER["QUERY_STRING"];
        } elseif (isset($_GET['source'],$_GET['platform'],$_GET['redirectTo']) && $_GET['source'] ==='integration-redemption') {
            $this->source = $_GET['source'];
            $posSystem = PosSystem::model()->findByAttributes(['name' => $_GET['platform'], 'enabled' => 1]);
            if ($posSystem) {
                $jwtForPModule = trim((string)$_GET['source']);
                $redirectWithToken = true;
                $redirectUrl = $_GET['redirectTo'];
            }
        }
        //Lei start: Ls eCom install redirect to this page
        elseif (isset($_GET['source'],$_GET['platform_id']) && $_GET['source'] === 'integration' && in_array($_GET['platform_id'],  [13,14,15,16,18,23,30,31])) {
            $redirectUrl = $_GET['redirect'];
            $integrationInstall = ['email' => $_GET['email'] ?? null, 'redirectUrl' => $redirectUrl];
            if (isset($_GET['cid'])) {
                $integrationInstall['cid'] = $_GET['cid'];
            }
        }
        //Lei end

        if(isset($_GET['access_token']) && $_GET['access_token'] != '') {
            Yii::log('access_token in login action::' . $_GET['access_token'], CLogger::LEVEL_INFO, __METHOD__);

            // Set access token in cookie
            $urlParts = parse_url(Yii::app()->params['serverUrl']);
            $expiryDate = new DateTime('+1 month');
            $fp = strrpos((string)$urlParts['host'], '.');
            $sp = strrpos((string)substr((string)$urlParts['host'], 0, $fp), '.');
            if ($sp === false) {
                $domain = $urlParts['host'];
            } else {
                $domain = substr((string)$urlParts['host'], $sp);
            }
            setcookie('merchant_access_token', $_GET['access_token'] ?? '', $expiryDate->getTimeStamp(), '/', $domain, true, true);
            setcookie('merchant_refresh_token', $_GET['refresh_token'] ?? '', $expiryDate->getTimeStamp(), '/', $domain, true, true);
            Controller::authenticateViaBearerTokenFromNBP($_GET['access_token']);
            $redirectUrl = $_GET['redirect'];
            $this->redirect($redirectUrl);
        }

        if (isset($_GET['source'], $_GET['t']) && $_GET['source'] === 'AccessApproval') {
            try {
                $result = AccessRequest::decodeRequest($_GET['t']);

                /** @var AccessRequest $model */
                $model = $result['accessRequest'];

                $model->approved_at = date('Y-m-d H:i:s');
                $model->save();
            } catch (\Exception $e) {
                Yii::log($e, CLogger::LEVEL_ERROR, __METHOD__);
            }
        }

        if (isset($_POST['LoginForm'])) {
            try {
                if(isset($_POST['g-recaptcha-response'])) {
                    $loginForm->reCaptchaResponse = $_POST['g-recaptcha-response'];
                }
                $loginForm->attributes = $_POST['LoginForm'];

                $loginUsername = $loginForm->attributes['username'];

                Yii::log(json_encode([
                    'message' => 'Login attempt',
                    'username' => $loginUsername,
                    'ip' => Yii::app()->request->userHostAddress,
                    'session_id' => session_id(),
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                    'social_login' => $socialLogin,
                ]), CLogger::LEVEL_INFO, __METHOD__);

                if (Utils::isIpBlacklisted((string) Yii::app()->request->userHostAddress)) {
                    Utils::logMessage(json_encode([
                        'message' => 'Blacklisted IP attempt',
                        'ip' => Yii::app()->request->userHostAddress,
                        'credential' => $loginUsername,
                    ]), __METHOD__);
                    throw new \RuntimeException(Yii::t('landingpages', 'LANDINGPAGE_USERRESETPIN_FORM_WRONG_EMAIL'));
                }

                $this->checkCurrentUserStatus($loginUsername, $loginForm->attributes['password'], $socialLogin);

                // User doesn't have a facebook account
                if ($loginForm->validate() && $loginForm->login()) {
                    $loginUsername = $loginForm->attributes['username'];
                    $loginPassword = $loginForm->attributes['password'];

                    $businessId = $this->checkBusinessStatus($loginPassword, $socialLogin);

                    $user = User::model()->findByPk(Yii::app()->user->getID());

                    // Get Location for IP
                    $location = Utils::lookupLocation(Yii::app()->request->userHostAddress);
                    $forceMfa = $this->shouldForceMfa($user, $location);

                    // Track logins
                    try {
                        $auditLogin = new AuditUserLogin();
                        $auditLogin->ip = Yii::app()->request->userHostAddress;
                        $auditLogin->user_id = Yii::app()->user->getID();
                        $auditLogin->session_id = session_id();
                        $auditLogin->db_name = Utils::getDsnAttribute('dbname');
                        $auditLogin->platform_id = PLATFORM_WEBSITE;
                        $auditLogin->user_agent = (isset($_SERVER['HTTP_USER_AGENT'])) ? $_SERVER['HTTP_USER_AGENT'] : null;
                        $auditLogin->details = json_encode(['username' => $loginUsername, 'social_login' => $socialLogin, 'location' => $location]);
                        $auditLogin->save(false);

                        AuditUserLoginNotification::checkLocationAndLockUserAccount($auditLogin);
                    } catch (Exception $e) {
                        Yii::log(json_encode([
                            'message' => 'Error: ' . $e->getMessage(),
                            'ip' => Yii::app()->request->userHostAddress,
                            'user_id' => Yii::app()->user->getID(),
                            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                        ]), CLogger::LEVEL_INFO, __METHOD__);
                    }

                    // Retrieve token from API for user and store in DB and cookie
                    if ($jwtForPModule) {
                        $token = $this->getTokenForMerchant($user, $loginPassword, $socialLogin, $_POST['credential'] ?? '');
                        if (isset($redirectWithToken) && $redirectWithToken) {
                            if (strpos((string)$redirectUrl ?? '', '?') === false) {
                                $redirectUrl .= '?' . http_build_query(['kt' => $token]);
                            } else {
                                $redirectUrl .= '&' . http_build_query(['kt' => $token]);
                            }
                        }
                    }

                    if (isset($_GET['source'],$_GET['platform_id'],$_GET['cid']) && $_GET['source'] === 'integration' && $_GET['platform_id'] == PLATFORM_POS_BIGCOMMERCE) {
                        if (Yii::app()->user->checkAccess('business') && Yii::app()->user->checkAccess('admin')) {
                            //install bc integration after login
                            Yii::app()->getModule('pos');
                            $bc = new BigCommercePosProvider(null, POS_PROVIDER_TYPE_POS_BRANCH);
                            $response = $bc->install(['business_id' => $businessId, 'cid' => $_GET['cid'], 'channel_id' =>isset($_GET['channel'])?$_GET['channel']:null]);
                            if (isset($response['status']) && $response['status'] === 'OK') {
                                header("Location: " . rtrim((string)Yii::app()->params['integration_api_url'], '/') . "/bigCommerce/view?status=1&cid=".$_GET['cid']);
                                exit();
                            } else {
                                throw new \Exception($response['message']);
                            }
                        } else {
                            throw new \Exception('Please login as business admin to complete the installation.');
                        }
                    }

                    // Get business rules
                    $businessRules = BusinessRules::getBusinessRules($businessId);

                    // Get business profile
                    $businessProfile = BusinessProfile::model()->findByAttributes([
                        'business_fk' => $businessId
                    ]);

                    // Check MFA if it's active
                    $mfaEnabled = false;
                    if ($businessRules && $businessRules->enable_mfa_flag == 1 && $businessProfile && $businessProfile->mfa_enabled == 1) {
                        $mfaEnabled = true;
                    }

                    if ($forceMfa) {
                        Yii::app()->session->add('force_mfa', false);
                        if (Yii::app()->params['envParam'] === 'PROD'){
                            $mfaEnabled = true;
                        }
                    }
                    
                    if (Yii::app()->user->checkAccess('business') && Yii::app()->user->checkAccess('admin')) {
                        if (!isset($token)) {
                            $this->getTokenForMerchant($user, $loginPassword, $socialLogin, $_POST['credential'] ?? '');
                        }

                        if ($mfaEnabled) {
                            //Create verification and redirect to MFA page
                            $this->createMfaVerification();
                        } else {
                            if ($redirectUrl) {
                                $this->redirect($redirectUrl);
                            }
                            if ($businessRules->beta_portal_link) {
                                $this->redirect('/merchant/newPortal');
                            } else {
                                $this->redirect(array('businessAdmin/index'));
                            }
                        }

                    } elseif (Yii::app()->user->checkAccess('branch') && Yii::app()->user->checkAccess('admin')) {
                        if (!isset($token)) {
                            $this->getTokenForMerchant($user, $loginPassword, $socialLogin, $_POST['credential'] ?? '');
                        }
                        if ($mfaEnabled) {
                            //Create verification and redirect to MFA page
                            $this->createMfaVerification();
                        } else {
                            if ($businessRules->beta_portal_link) {
                                $this->redirect('/merchant/newPortal');
                            } else {
                                $this->redirect(array('branchAdmin/index'));
                            }
                        }
                    } elseif (Yii::app()->user->checkAccess('clerk')) { //for clerk
                        if ($mfaEnabled) {
                            $token = $this->getTokenForMerchant($user, $loginPassword, $socialLogin, $_POST['credential'] ?? '');
                            //Create verification and redirect to MFA page
                            $this->createMfaVerification();
                        } else {
                            if ($redirectUrl) {
                                $this->redirect($redirectUrl);
                            }

                            if ($businessRules->beta_portal_link) {
                                Yii::app()->user->logout(false);
                                Yii::app()->user->setFlash('error', Yii::t('backend', 'BACKEND_PORTAL_ACCESS_DENIED'));
                                $this->redirect(array('/merchant/login'));
                            } else {
                                $this->redirect(array('profile/personal'));
                            }
                        }
                    } elseif (Yii::app()->user->checkAccess('superadmin') || Yii::app()->user->checkAccess('merchantsetup')) { //for systemAdmin
                        if(!isset($token)) {
                            $this->getTokenForMerchant($user, $loginPassword, $socialLogin, $_POST['credential'] ?? '');
                        }
                        if (Yii::app()->params['envParam'] !== 'PROD') {
                            // Bypass MFA verification for non-prod environments
                            Yii::app()->session->add('user_verified', true);
                        } else {
                            $this->createMfaVerification();
                        }
                        $this->redirect(array('systemAdmin/main'));
                    } elseif (Yii::app()->user->checkAccess('businessgroupmanager')) { //for Business Group Manager
                        $this->redirect(array('businessGroupManager/business/impersonate'));
                    }

                } else {// validate
                    //if validation fails we count the attempts and save in session
                    $this->counter = Yii::app()->session->itemAt('captchaRequired') + 1;
                    Yii::app()->session->add('captchaRequired', $this->counter);

                    Utils::logFailedLoginAttempt($_POST['LoginForm']['username'] ?? null, PLATFORM_WEBSITE);
                }
            } catch (Exception $e) {
                Yii::log($e . json_encode([
                    'username' => $_POST['LoginForm']['username'] ?? null,
                    'credential' => $_POST['credential'] ?? null,
                    'ip' => Yii::app()->request->userHostAddress,
                ]),CLogger::LEVEL_ERROR, __METHOD__);
                Yii::app()->user->setFlash('error', $e->getMessage());
            }
        } // POST LoginForm

        $this->render('login', array(
            //'model' => $model,
            'loginForm' => $loginForm,
            'isUser' => $isUser,
            'platform_id' => $_GET['platform_id']??'',
            'isBusinessDisabled' => $isBusinessDisabled,
            'integrationInstall' => isset($integrationInstall)?(object)$integrationInstall:null,
        ));
    }

    /**
     * Create MFA verification code and redirect to MFA page
     * Used for both employee and system admin (no userEntities)
     *
     * @throws GuzzleException
     * @throws CHttpException
     * @throws IdentityProviderException
     */
    private function createMfaVerification(): void
    {
        $user = Yii::app()->user;

        //Get a user's access token
        $providerToken = OauthProviderToken::getAccessTokenForUser($user->id);

        if (Yii::app()->user->checkAccess('superadmin') || Yii::app()->user->checkAccess('merchantsetup')) {
            $merchantProfile = MerchantProfile::model()->findByAttributes(['user_fk' => $user->id]);
            $channel = $merchantProfile->mfa_preferred_channel ?? 'email';
            if (in_array($channel, ['sms', 'email'], true)) {
                $verification = $this->createMfaVerificationForSystemAdmin($providerToken->access_token, [
                    "channel" => $channel,
                ]);
            } else {
                $verification['status'] = true; // for TOTP no need to send verification code
            }
        } else {
            // Employee: Business Admin, Branch or Clerk
            $userEntities = UserEntityPermission::getUserEntitiesByRole(
                $user->id, __METHOD__, PlatformSolution::PLATFORM_NAME_WEBSITE
            );
            $branch = BusinessBranch::model()->findByPk($userEntities['mainBranchId']);
            $business = Business::model()->findByPk($userEntities['businessId']);

            // Initialize Kangaroo API Client
            $api = new KangarooApiClient($business, $branch, $providerToken->access_token);

            // Create verification
            $params = ["channel" => "email"];
            $verification = $api->createMfaVerification($params);
        }

        if (!$verification['status']) {
            throw new Exception(Yii::t('backend', 'MFA_CREATE_VERIFICATION_FAILED'));
        }

        //Add session to set user as not verified
        Yii::app()->session->add('user_verified', false);

        $this->redirect(array('/merchant/mfa'));
    }

    /**
     * Render Two-Factor authentication page after login
     *
     * @throws CHttpException
     */
    public function actionMfa()
    {
        $user = Yii::app()->user;

        if (Yii::app()->session->get('user_verified')) {
            throw new CHttpException(403, Yii::t('backend', 'BACKEND_CUSTOM_ROLE_ACCESS_DENIED'));
        }

        $primaryChannel = 'email';
        $channels = ['email'];
        if (Yii::app()->user->checkAccess('superadmin') || Yii::app()->user->checkAccess('merchantsetup')) {
            $merchantProfile = MerchantProfile::model()->findByAttributes(['user_fk' => $user->id]);
            if ($merchantProfile) {
                $primaryChannel = $merchantProfile->mfa_preferred_channel ?? 'email';
                $channels = (array) json_decode((string) $merchantProfile->mfa_channels, true);
            }
        } else {
            $userEntities = UserEntityPermission::getUserEntitiesByRole(
                $user->id, __METHOD__, PlatformSolution::PLATFORM_NAME_WEBSITE
            );

            // Get Business Profile
            $businessProfile = BusinessProfile::model()->findByAttributes([
                'business_fk' => $userEntities['businessId']
            ]);

            // Decode MFA channels
            if ($businessProfile && $businessProfile->mfa_channels) {
                $channels = (array) json_decode((string) $businessProfile->mfa_channels, true);
            }
        }

        $this->render('mfa', array(
            'isUser' => false,
            'channels' => $channels,
            'primaryChannel' => $primaryChannel
        ));
    }

    /**
     * Validate mfa
     *
     * @return void
     * @throws GuzzleException
     */
    public function actionValidateMfa()
    {
        try {
            $user = Yii::app()->user;

            //Get verification code
            $verificationCode = $_POST['verification_code'] ?? "";
            $channel = $_POST['channel'] ?? "email";

            if (empty($verificationCode)) {
                throw new Exception(Yii::t('landingpages', 'MFA_CODE_REQUIRED'));
            }

            $params = [
                'verification_code' => $verificationCode,
                'channel' => $channel,
                'action' => 'verify'
            ];

            //Get the user's access token
            $providerToken = OauthProviderToken::getAccessTokenForUser($user->id);

            if (Yii::app()->user->checkAccess('superadmin') || Yii::app()->user->checkAccess('merchantsetup')) {
                $redirectTo = '/systemAdmin/main';
                $verification = $this->checkMfaVerificationForSystemAdmin($providerToken->access_token, $params);
            } else {
                $redirectTo = '/businessAdmin/index';
                $userEntities = UserEntityPermission::getUserEntitiesByRole(
                    $user->id, __METHOD__, PlatformSolution::PLATFORM_NAME_WEBSITE
                );

                $branch = BusinessBranch::model()->findByPk($userEntities['mainBranchId']);
                $business = Business::model()->findByPk($userEntities['businessId']);

                //Initialize Kangaroo API Client
                $api = new KangarooApiClient($business, $branch, $providerToken->access_token);
                $verification = $api->checkMfaVerification($params);
            }

            if ($verification['status']) {
                //Set user as verified in the session
                Yii::app()->session['user_verified'] = true;

                Utils::sendResponse(200, CJSON::encode(['status' => 'OK', 'redirectUrl' => $redirectTo,]));
            } else {
                Utils::sendResponse(200, CJSON::encode([
                    'status' => 'NOT_OK',
                    'message' => Yii::t('backend', 'MFA_INVALID_VERIFICATION')
                ]));
            }
        } catch (Exception $e) {
            Utils::sendResponse(200, json_encode(['status' => 'NOT_OK', 'message' => $e->getMessage(),]));
        }
    }

    /**
     * Resend MFA verification
     *
     * @return void
     * @throws GuzzleException
     */
    public function actionResendVerification()
    {
        try {
            $user = Yii::app()->user;

            //Get channel
            $channel = "email"; //$_POST['channel']

            //Get user's access token
            $providerToken= OauthProviderToken::getAccessTokenForUser($user->id);

            if (Yii::app()->user->checkAccess('superadmin') || Yii::app()->user->checkAccess('merchantsetup')) {
                $verification = $this->createMfaVerificationForSystemAdmin(
                    $providerToken->access_token, ["channel" => $channel]
                );
            } else {
                $userEntities = UserEntityPermission::getUserEntitiesByRole(
                    $user->id, __METHOD__, PlatformSolution::PLATFORM_NAME_WEBSITE
                );

                $branch = BusinessBranch::model()->findByPk($userEntities['mainBranchId']);
                $business = Business::model()->findByPk($userEntities['businessId']);

                //Initialize Kangaroo API Client
                $api = new KangarooApiClient($business, $branch, $providerToken->access_token);

                // Create verification
                $verification = $api->createMfaVerification(["channel" => $channel]);
            }

            if ($verification['status']) {
                Utils::sendResponse(200, CJSON::encode([
                    'status' => 'OK',
                    'message' => '',
                    'hide_resend' => true
                ]));
            } else {
                Utils::sendResponse(200, CJSON::encode([
                    'status' => 'NOT_OK',
                    'message' => $verification['description']
                ]));
            }
        } catch (Exception $e) {
            Utils::sendResponse(200, json_encode([
                'status' => 'NOT_OK',
                'message' => $e->getMessage(),
            ]));
        }
    }

    public function actionNewPortal()
    {
        $betaUrl = Yii::app()->params['betaUrl'];
        $access_token_nbp = isset($_COOKIE['merchant_access_token']) ? $_COOKIE['merchant_access_token'] : '';
        $refresh_token_nbp = isset($_COOKIE['merchant_refresh_token']) ? $_COOKIE['merchant_refresh_token'] : '';
        if($betaUrl !== '' && $betaUrl !== null){
            header("Location: " . $betaUrl . "/server/auth/portal/?access_token=" . $access_token_nbp . "&refresh_token=" . $refresh_token_nbp);
        }else{
            $this->redirect(array('businessAdmin/index'));
            // header("Location: http://localhost:8080/server/auth/portal/?access_token=" . $access_token_nbp . "&refresh_token=" . $refresh_token_nbp);

            // header("Location: http://localhost:8080/#/advanced/integrations?access_token=" . $access_token_nbp . "&refresh_token=" . $refresh_token_nbp);
        }
        // <a class="h3-link k3" href="<?php echo Yii::app()->params['betaUrl'];>/server/auth/portal/?access_token=<=$_COOKIE['merchant_access_token']>&refresh_token=<=$_COOKIE['merchant_refresh_token']>" target="_top" data-pjax='false'>
    }

    private function getTokenForMerchant($user, $loginPassword, $socialLogin = '', $socialToken = '')
    {
        if (Yii::app()->user->checkAccess('superadmin') || Yii::app()->user->checkAccess('merchantsetup')) {
            $appKey = '';
        } else {
            $userEntities = UserEntityPermission::getUserEntitiesByRole(
                $user->id, __METHOD__, PlatformSolution::PLATFORM_NAME_WEBSITE
            );
            $branch = BusinessBranch::model()->findByPk($userEntities['mainBranchId']);
            $business = Business::model()->findByPk($userEntities['businessId']);

            //generate X-Application-Key token
            $appKey = Business::generateApplicationKey($business, $branch);
        }

        // Initialize Kangaroo API provider
        $kangarooApiProvider = new \KangarooRewards\OAuth2\Client\Provider\Kangaroo([
            'clientId' => Yii::app()->params['kangarooApiClientId'],
            'clientSecret' => Yii::app()->params['kangarooApiClientSecret'],
            'urlAccessToken' => Yii::app()->params['kangarooApiUri'] . '/oauth/token',
            'urlResourceOwnerDetails' => Yii::app()->params['kangarooApiUri'] . '/me',
            'redirectUri' => null,
        ]);

        $params = [
            'username' => $user->email ?: $user->username,
            'password' => $loginPassword,
            'scope' => 'admin',
        ];

        if ($socialLogin === 'google') {
            unset($params['password']);
            $params['google_token'] = $socialToken;
        }

        if (!empty($appKey)) {
            $params = array_merge($params, [
                'application_key' => $appKey,
            ]);
        }

        if ($socialLogin === 'google') {
            $token = $kangarooApiProvider->getAccessToken('google', $params);
        } else {
            // Retrieve Access Token using password grant
            $token = $kangarooApiProvider->getAccessToken('password', $params);
        }

        // Store the token in DB
        $providerToken = new OauthProviderToken();
        $providerToken->provider_id = OAUTH_PROVIDER_KANGAROO_API;
        $providerToken->token_hash = hash('sha512', $token->getToken());
        $providerToken->access_token = $token->getToken();
        $providerToken->expires = $token->getExpires();
        $providerToken->refresh_token = $token->getRefreshToken();
        $providerToken->user_fk = $user->id;
        $providerToken->save();

        // Set access token in cookie
        $domain = Utils::getCookieDomain();
        $expiryDate = new DateTime('+1 month');

        setcookie('merchant_access_token', (string)$token->getToken(), $expiryDate->getTimeStamp(), '/', $domain, true, true);
        setcookie('merchant_refresh_token', (string)$token->getRefreshToken(), $expiryDate->getTimeStamp(), '/', $domain, true, true);
        return $providerToken->token_hash;
    }

    /**
     * Check status of the business and access to merchant portal
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.1.0
     * @since 4.1.0
     * <AUTHOR>
     * @access public
     */
    private function checkBusinessStatus($loginPassword=null, $socialLogin = '')
    {
        $userId = Yii::app()->user->id;
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;

        if (Yii::app()->user->checkAccess('superadmin') ||
            Yii::app()->user->checkAccess('merchantsetup') ||
            Yii::app()->user->checkAccess('businessgroupmanager')
        ) {
            return true;
        } elseif (Yii::app()->user->checkAccess('business')) {
            $userEntities = UserEntityPermission::getUserEntitiesByRole($userId, __METHOD__, $platformName);
            $businessId = $userEntities['businessId'];
            $mainBranchId = $userEntities['mainBranchId'];
            $businessModel = Business::model()->findByPk($businessId);

            if ($businessModel && !$businessModel->enabled) {
                Yii::log('Business is DISABLED. Entities: ' . CJSON::encode($userEntities), CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
                Yii::app()->user->logout();
                $this->redirect(array('/merchant/login?disabled=1'));
            }

            // $userModel = User::model()->findByPk($userId);
            if ($businessModel->status_manager_fk == STATUS_MANAGER_VERIFIED) {
                return $businessId;
            } elseif ($businessModel->status_manager_fk == STATUS_MANAGER_SIGNUP) {
                Yii::log('STATUS_MANAGER_SIGNUP entities: ' . CJSON::encode($userEntities), CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
                Yii::app()->user->logout(false);//destroy only auth session variables

                Yii::app()->session['lead_verified'] = true;
                Yii::app()->session['lead_platform_id'] = PLATFORM_WEBSITE;
                Yii::app()->session['signup_business_id'] = $businessId;
                Yii::app()->session['signup_branch_id'] = $mainBranchId;
                Yii::app()->session['signup_user_id'] = $userId;

                $this->redirect(array('/merchant/signup2'));
            } elseif ($businessModel->status_manager_fk == STATUS_MANAGER_WIZARD) {
                Yii::log('STATUS_MANAGER_WIZARD entities: ' . CJSON::encode($userEntities), CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
                Yii::app()->user->logout(false);

                Yii::app()->session['lead_verified'] = true;
                Yii::app()->session['lead_platform_id'] = PLATFORM_WEBSITE;
                Yii::app()->session['signup_business_id'] = $businessId;
                Yii::app()->session['signup_branch_id'] = $mainBranchId;
                Yii::app()->session['signup_user_id'] = $userId;

                //to log in the user automatically
                // Yii::app()->session['lead_verify_code'] = $r['results']['lead_random'];
                // Yii::app()->session['lead_user_name'] = $r['results']['lead_user_name'];

                $this->redirect(array('/merchant/wizard'));
            } elseif ($businessModel->status_manager_fk == STATUS_MANAGER_MERCHANTAPPVERIFIED) {
                Yii::log('STATUS_MANAGER_MERCHANTAPPVERIFIED entities: ' . CJSON::encode($userEntities), CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
                Yii::app()->user->logout(false);
                Yii::app()->user->setFlash('error', Yii::t('backend', 'BACKEND_PORTAL_ACCESS_DENIED'));
                $this->redirect(array('/merchant/login'));
            } elseif ($businessModel->status_manager_fk == STATUS_MANAGER_INTEGRATION_SIGNUP_WAIT_FOR_SUBSCRIPTION
            || $businessModel->status_manager_fk == STATUS_MANAGER_WAIT_FOR_UPDATING_SUBSCRIPTION) {
                //process subscription
                $userModel = User::model()->findByPk($userId);
                $merchantAppLead = MerchantAppLead::model()->findByAttributes(['email' => $userModel->email], ['order' => 'enabled desc']);
                if ($merchantAppLead->platform_solution_fk == PLATFORM_POS_SHOPIFY) {
                    //From Shopify
                    $user = User::model()->findByPk(Yii::app()->user->getID());
                    $this->getTokenForMerchant($user, $loginPassword, $socialLogin);
                    $this->redirect('/package/shopify');
                } elseif ($merchantAppLead->platform_solution_fk == PLATFORM_POS_LIGHTSPEED_ECOM) {
                    if ($merchantAppLead->cluster_id == LIGHTSPEED_ECOM_CLUSTER_EU1) {
                        $currency = 'EUR';
                    } else {
                        $currency = null;
                    }
                    $trial_enabled = true;
                    if ($businessModel->status_manager_fk == STATUS_MANAGER_WAIT_FOR_UPDATING_SUBSCRIPTION) {
                        $trial_enabled = false;
                    }

                    if (!empty($_GET['redirect'])) {
                        $redirectUrl = $_GET['redirect'];
                        $successUrl = Yii::app()->createAbsoluteUrl('merchant/subscriptionSuccess?' . http_build_query(['redirect' => $redirectUrl]));
                        $cancelUrl = Yii::app()->createAbsoluteUrl('merchant/login?' . $redirectUrl);
                    } else {
                        $successUrl = Yii::app()->createAbsoluteUrl('merchant/subscriptionSuccess');
                        $cancelUrl = Yii::app()->createAbsoluteUrl('merchant/login');
                    }

                    $this->loadSubscription($userId, $businessId, $userModel->email, $currency, $successUrl, $cancelUrl, $trial_enabled, POS_SYSTEM_LIGHTSPEED_ECOM);
                }
            }else {
                Yii::log('Business Status is NOT Verified entities: ' . CJSON::encode($userEntities), CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
                Yii::app()->user->logout();
                $this->redirect(array('/merchant/login?disabled=1'));
            }
        } elseif (Yii::app()->user->checkAccess('branch')) {
            $userEntities = UserEntityPermission::getUserEntitiesByRole($userId, __METHOD__, $platformName);
            $businessId = $userEntities['businessId'];
            $businessModel = Business::model()->findByPk($businessId);

            if ($businessModel && !$businessModel->enabled) {
                Yii::log('Business is DISABLED. Entities: ' . CJSON::encode($userEntities), CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
                Yii::app()->user->logout();
                $this->redirect(array('/merchant/login?disabled=1'));
            }

            return $businessId;
        } elseif (Yii::app()->user->checkAccess('user')) {
            Yii::app()->user->logout();
            $this->render('login', array(
                'loginForm' => new LoginForm('login'),
                'isUser' => true,
                'isBusinessDisabled' => false,
            ));
            Yii::app()->end();
        } else {
            Yii::log('It should never go here userId='. $userId, CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
            Yii::app()->user->logout();
            $this->redirect(array('/merchant/login?disabled=1'));
        }
    }

    /**
     * Getting the current status from UserStatusManager
     * If new business admin logs in first time Redirect to create password page if the password is correct
     *
     * Used on : Website(Merchant->Login)
     *
     * @version Website June 2014 Release
     * @access private
     * @return redirect
     */

    private function checkCurrentUserStatus($usernameOrEmail, $inputPassword, $socialLogin = '')
    {
        $userId = User::searchUsernameOrEmailExistence($usernameOrEmail);

        if ($userId !== null) {
            //loading the user record
            $userModel = User::model()->findByPk($userId);
            //getting the current status
            $userCurrentStatus = (int) UserStatusManager::getCurrentUserStatus($userId);

            try {
                $timezoneId = Utils::getTimezoneSession();
            } catch (\Exception $e) {
                // IPv6 will throw exception
                $timezoneId = 165;
            }

            $passwordCorrect = User::passwordVerify($inputPassword, $userModel->password);
            if ($socialLogin === 'google') {
                $passwordCorrect = false;
            }

            if ($userCurrentStatus === 15 && (int) $userModel->enabled === 0 && $passwordCorrect) {
                // Business Signup and not Active
                $randomToken = User::generateRandomString(60);

                $userModel->utc_link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
                $userModel->passwordreset = $randomToken;
                $userModel->update('passwordreset, utc_link_expires');

                $this->redirect(array('/merchant/activate?q=' . $randomToken));

            } elseif ($userCurrentStatus === 27 && (int) $userModel->enabled === 0 && $passwordCorrect) {
                // Business Signup and not Active
                $userModel->enabled = 1;
                $userModel->update('enabled');

                $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 28, '', '', 1, $timezoneId); //ACTIVE_BUSINESS_SELFSIGNUP_TEMP_PSW

                return true;
            }
        }
    }

    /**
     * Processing the user activation
     * Displaying the create form password
     * Collecting the post data /username/password/password_repeat
     * Validating the post data
     * Saving the new password and username in database
     * Finally we authenticate the user and log in
     * Used in : Website(MerchantController)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public function actionActivate()
    {
        $user = null;
        $CreatePasswordForm = null;

        if (isset($_GET['q']) && $_GET['q'] != '') {
            $q = Yii::app()->format->text($_GET['q']);
            $businessId = (isset($_GET['b'])) ? (int) $_GET['b'] : null;
        } else {
            Yii::app()->user->setFlash('error', Yii::t('frontend', 'NO_ACTIV_LINK'));
            $this->render('createPassword', array('userModel' => $user, 'CreatePasswordForm' => $CreatePasswordForm));
            return;
            //$this->redirect(Yii::app()->createUrl('site/index'));
        }

        $user = User::model()->findByAttributes(array(
            'passwordreset' => $q,
        ));

        if (!$user) {
            Yii::app()->user->setFlash('error', Yii::t('frontend', 'NO_ACTIV_LINK'));
            $this->render('createPassword', array('userModel' => $user, 'CreatePasswordForm' => $CreatePasswordForm));
            return;
            //$this->redirect(Yii::app()->createUrl('site/index'));
        }

        $now = date("Y-m-d H:i:s");
        //Checking if the link has expired
        if ($now > $user->utc_link_expires) {
            Yii::app()->user->setFlash('error', Yii::t('frontend', 'LINK_EXPIRED'));
            $this->render('createPassword', array('userModel' => $user, 'CreatePasswordForm' => $CreatePasswordForm));
            return;
            //$this->redirect(Yii::app()->createUrl('site/index'));
        }

        // Getting CreatePasswordForm model for validation
        $CreatePasswordForm = new CreatePasswordForm();

        if (isset($_POST['CreatePasswordForm'])) {
            $timezoneId = Utils::getTimezoneSession();
            $CreatePasswordForm->attributes = $_POST['CreatePasswordForm'];
            // validating the data
            if ($CreatePasswordForm->validate()) {

                // Updating user's password, username from the form
                // $user->username = $CreatePasswordForm->username; // Getting the username from post form
                $user->password = User::cryptPassword($CreatePasswordForm->password);
                //$user->basic_profile_reminder = date('Y-m-d');
                $user->enabled = 1;
                $user->passwordreset = '';
                $user->timezone_mysql_fk = $timezoneId;
                $user->ip = Yii::app()->request->userHostAddress;

                $transaction = Yii::app()->db->beginTransaction();

                try
                {
                    Utils::saveModel($user);

                    //preparing the username
                    if (strpos((string)$user->username, "@")) {
                        $username = strstr($user->username, '@', true);
                    } else {
                        $username = $user->username;
                    }

                    //Getting the business logo to include in email
                    $businessLogo = '';
                    if ($businessId != null) {
                        $defaultBranch = BusinessBranch::getDefaultBranch($businessId);
                        $businessLogo = Yii::app()->getBaseUrl(true) . $defaultBranch['logo_image'];

                        //Enqueue for sending in background
                        MessagesQueue::create([
                            'send_to' => $user->email,
                            'cell_email' => MESSAGE_SEND_BY_EMAIL,
                            'user_fk' => $user->id,
                            'business_fk' => $businessId,
                            'params' => [
                                'emailView' => 'employeeWelcomeBilingual',
                                'layout' => 'bilingual',
                                'emailSubject' => Yii::t('frontend', 'USR_WELCOME'),
                                'emailData' => [
                                    'username' => '',
                                    'link' => '',
                                    'businessLogo' => $businessLogo,
                                ],
                                'withdraw_credit_flag' => true
                            ],
                        ]);
                    }

                    //Creating user status for the user
                    $userStatus = UserStatusManager::createUserStatusManagerRecordV330($user->id, $modifiedBy = null, $status_manager_fk = 8, '', '', 1, $timezoneId); //Active Regular User


                    // Authenticating the user with email and new password
                    $identity = new UserIdentity($user->email, $CreatePasswordForm->password);

                    if (!$identity->authenticate()) {
                        Yii::log('error', CLogger::LEVEL_ERROR, $identity->errorMessage);
                        $transaction->rollback();
                        Yii::app()->user->setFlash('error', $identity->errorMessage);
                        $this->render('createPassword', array('userModel' => $user, 'CreatePasswordForm' => $CreatePasswordForm));
                        return;
                    }

                    // commit transactions
                    $transaction->commit();

                    Yii::app()->user->login($identity); // log the user in
                    //Yii::app()->user->setFlash('success', Yii::t('frontend', 'PASS_CHANGED'));
                    if (Yii::app()->user->checkAccess('admin') && Yii::app()->user->checkAccess('business')) {
                        $this->redirect(array('/businessAdmin/index'));
                    } elseif (Yii::app()->user->checkAccess('admin') && Yii::app()->user->checkAccess('branch')) {
                        $this->redirect(array('/branchAdmin/index'));
                    } elseif (Yii::app()->user->checkAccess('clerk')) {
                        $this->redirect(array('/profile/personal'));
                    } else {
                        $this->redirect(array('/site/login'));
                    }

                } catch (CDbException $e) {
                    Yii::log('error', CLogger::LEVEL_ERROR, $e);
                    $transaction->rollback();
                    Yii::app()->user->setFlash('error', Yii::t('frontend', 'UNKNOWN_ERROR'));
                    $this->render('createPassword', array('userModel' => $user, 'CreatePasswordForm' => $CreatePasswordForm));
                    return;
                }
            } // validate
        } // post

        $this->render('createPassword', array(
            'userModel' => $user,
            'CreatePasswordForm' => $CreatePasswordForm,
        ));
    }

    // public function actionLocation()
    // {
    //     if (Util::isMobile()) {
    //         $lat = Yii::app()->getRequest()->getParam('lat');
    //         $long = Yii::app()->getRequest()->getParam('long');
    //         if (!(isset($lat) || isset($long))) {
    //             Yii::log("location called no params.", "info", "site:location");
    //             Yii::app()->end;
    //         }
    //         if (Yii::app()->request->isAjaxRequest) {
    //             if (isset($lat) && isset($long)) {
    //                 Yii::log("location called ajax:[$lat,$long]", "info", "site:location");
    //                 if (!Yii::app()->user->isGuest) {
    //                     Yii::app()->user->setState('geo_lat', $lat);
    //                     Yii::app()->user->setState('geo_long', $long);
    //                 }
    //                 echo "<div id='#loc'>$lat $long</div>";
    //             }
    //         } else {
    //             if (isset($lat) && isset($long)) {
    //                 $lat = Yii::app()->getRequest()->getParam('lat');
    //                 $long = Yii::app()->getRequest()->getParam('long');
    //                 Yii::log("location called ajax:[$lat,$long]", "info", "site:location");
    //                 if (!Yii::app()->user->isGuest) {
    //                     Yii::app()->user->setState('geo_lat', $lat);
    //                     Yii::app()->user->setState('geo_long', $long);
    //                 }
    //                 echo "<div id='#loc'>$lat $long</div>";
    //             }
    //         }
    //     }
    // }

    // This is the action to handle external exceptions.
    public function actionError()
    {

        if ($error = Yii::app()->errorHandler->error) {
            if (Yii::app()->request->isAjaxRequest) {
                echo $error['message'];
            } else {
                $this->render('error', $error);
            }

        }
    }

    // public function actionCity()
    // {
    //     $city = Yii::app()->params['city']; // default city in config file
    //     if (isset($_GET['city'])) {
    //         $city = $_GET['city'];
    //     }
    //     $this->render('index', array(
    //         'city' => $city,
    //     ));
    // }

    // public function actionUpdateusername()
    // {
    //     $model = new UpdateUserNameForm();
    //     if (isset($_POST['UpdateUserNameForm'])) {
    //         $model->attributes = $_POST['UpdateUserNameForm'];
    //         $model->email = Yii::app()->user->getState('email');
    //         if ($model->validate() && isset($model->email)) {
    //             $user = User::model()->findByAttributes(array(
    //                     'email' => $model->email
    //             ));
    //             $user->username = $model->username;
    //             $user->enabled = 1;
    //             $user->update(array(
    //                     'username',
    //                     'enabled'
    //             ));
    //             Yii::app()->user->setFlash('info', Yii::t('frontend', 'USER_UPDATED'));
    //             $this->redirect(Yii::app()->createUrl('site/index'));
    //         }
    //     }
    //     $this->render('updateusername', array(
    //             'model' => $model
    //     ));
    // }

    // Logs out the current user and redirect to homepage.
    public function actionLogout()
    {
        $langId = User::getUserLanguage();
        if($langId!==null) {
            $userLanguage = Language::loadLanguageById($langId);
            $preLogOutLang = $userLanguage['abreviation'];
        } else {
            $preLogOutLang = 'en';
        }
        Yii::app()->user->logout();

        unset($_COOKIE['merchant_access_token']);
        $urlParts = parse_url(Yii::app()->params['serverUrl']);
        setcookie('merchant_access_token', '', -1, '/', $urlParts['host'], true, true);

        if(isset($_COOKIE['nbp_origin'])){
            unset($_COOKIE['merchant_access_token']);
            $urlParts = parse_url(Yii::app()->params['serverUrl']);
            setcookie('merchant_access_token', '', -1, '/', $urlParts['host'], true, true);

            unset($_COOKIE['nbp_origin']);
            $urlParts = parse_url(Yii::app()->params['serverUrl']);
            setcookie('nbp_origin', '', -1, '/', $urlParts['host'], true, true);
        }

        // $accessToken = isset($_COOKIE['merchant_access_token']) ? $_COOKIE['merchant_access_token'] : 'invalid';
        // Yii::log('access_token = ' . $accessToken, CLogger::LEVEL_INFO, __METHOD__);
        // Yii::log(json_encode($_COOKIE), CLogger::LEVEL_INFO, __METHOD__);

            $this->redirect(Yii::app()->homeUrl . 'merchant/login?lang=' . $preLogOutLang);
    }

    // Change the language
    public function actionChangeLanguage()
    {
        Yii::app()->session->add('languageChanged', 'Yes');
        $language = strtolower((string)$_GET['lang']);

        if ($language == 'french') {
            Yii::app()->session->add('language', 'fr');
            Yii::app()->language = 'fr';
        }
        if ($language == 'english') {
            Yii::app()->session->add('language', 'en');
            Yii::app()->language = 'en';
        }
        $this->redirect(Yii::app()->getRequest()->getUrlReferrer());
    }

    /**
     * Forgot password page for Merchants
     *
     * Used in : Website(/merchant/lostpassword)
     *
     * @return void
     * @throws CException
     * @version 3.2.0
     * @access public
     */
    public function actionForgotPassword()
    {
        //$this->setappLanguage();
        $model = new LostPasswordForm();
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;

        if (isset($_POST['LostPasswordForm'])) {
            $model->attributes = $_POST['LostPasswordForm'];
            $model->reCaptchaResponse = $_POST['g-recaptcha-response'];

            if (Utils::isIpBlacklisted((string) Yii::app()->request->userHostAddress)) {
                Utils::logMessage(json_encode([
                    'message' => 'Blacklisted IP attempt',
                    'ip' => Yii::app()->request->userHostAddress,
                    'credential' => $model->email,
                ]), __METHOD__);
                Yii::app()->user->setFlash('error', Yii::t('landingpages', 'LANDINGPAGE_USERRESETPIN_FORM_WRONG_EMAIL'));
                $this->render('lostpassword', array('model' => $model));
                return;
            }

            // Grab the user data
            $user = User::model()->findByAttributes(['email' => $model->email, 'enabled' => 1],['condition' => 'business_fk IS NULL']);

            if ($model->validate() && $user != null) {
                if (Yii::app()->authManager->checkAccess('user', $user->id)) {
                    //a user cannot reset his password
                    Yii::app()->user->setFlash('error', Yii::t('landingpages', 'LANDINGPAGE_MERCHANTRESETPSW_USER_NOT_ALLOWED'));
                    $this->render('lostpassword', array('model' => $model));
                    return;
                }

                //sending activation code by sms or email
                $this->sendEmailPasswordReset($user/*Model*/);
            } elseif ($user == null) {
                Yii::app()->user->setFlash('error', Yii::t('landingpages', 'LANDINGPAGE_USERRESETPIN_FORM_WRONG_EMAIL'));
            }
        }

        $this->render('lostpassword', array('model' => $model));
    }

    /**
     * Prepare the email for reset password and send it
     * Handle timezone
     * Used in : Website(/merchant/ForgotPassword)
     * @version May - June 2015 V3.3.0
     * @since 3.2.0
     * @param object $user
     * @access private
     * @return array
     */
    private function sendEmailPasswordReset($user)
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        // Create secret reset link
        $random = User::generateRandomString(60);
        $link = $this->createAbsoluteUrl('merchant/resetPassword', array('q' => $random));
        //Loading all languages
        $language = Language::model()->findByPk($user->language);
        $strLang = strtolower((string)$language->abreviation);

        $emailData = ['user' => $user, 'link' => $link];
        $emailSubject = Yii::t('frontend', 'PSW_RESET_REQUEST', array(), null, $strLang);
        $sendTo = $user->email;

        $emailSent = Utils::sendEmailWithTemplate('passwordReset', 'bilingual', $emailSubject, $sendTo, $emailData, 'application.views.mail.' . $strLang);

        if ($emailSent['status'] == 'OK') {
            $timezoneId = Utils::getTimezoneSession();

            Yii::log('Merchant reset password email sent ID=' . $user->id . ' email=' . $user->email, CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);
            $user->passwordreset = $random;
            $user->utc_link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
            $user->timezone_mysql_fk = $timezoneId;
            $user->update();

            Yii::app()->user->setFlash('success', Yii::t('frontend', 'PASS_RESET'));
            $this->redirect(Yii::app()->createUrl('merchant/ForgotPassword'));
        } else {
            Yii::log('Merchant reset password email NOT SENT id=' . $user->id . ' email=' . $user->email, CLogger::LEVEL_WARNING, $platformName . '_' . __METHOD__);
            Yii::app()->user->setFlash('error', Yii::t('frontend', 'ERROR'));
            $this->redirect(Yii::app()->createUrl('merchant/ForgotPassword'));
        }
    }

    /**
     * Reset merchant password from the link
     *
     * Used in : Website(/merchant/ForgotPassword)
     *
     * @version 3.2.0
     * @param string $q
     * @access public
     * @return array
     */
    public function actionResetPassword()
    {
        if (isset($_GET['q']) && $_GET['q'] != '') {
            $q = Yii::app()->format->text($_GET['q']);
        } else {
            Yii::app()->user->setFlash('error', Yii::t('frontend', 'NO_ACTIV_LINK'));
            $this->render('resetpass', array('model' => null, 'userModel' => null));
            return;
        }

        $user = User::model()->findByAttributes(array(
            'passwordreset' => $q,
        ));

        if ($user == null) {
            Yii::app()->user->setFlash('error', Yii::t('frontend', 'LINK_EXPIRED'));
            $this->render('resetpass', array('model' => null, 'userModel' => null));
            return;
        }

        $model = new ResetPassword();
        if (!$user) {
            Yii::app()->user->setFlash('error', Yii::t('frontend', 'NO_ACTIV_LINK'));
            $this->render('resetpass', array('model' => $model));
            return;
        }
        $now = date("Y-m-d H:i:s");
        //Checking if the link has expired
        if ($now > $user->utc_link_expires) {
            Yii::app()->user->setFlash('error', Yii::t('frontend', 'LINK_EXPIRED'));
            $this->render('resetpass', array('model' => $model));
            return;
        }

        //set app language as user language
        $languageModel = Language::model()->findByPk($user->language);
        Yii::app()->session->add('language', $languageModel->abreviation);
        Yii::app()->language = $languageModel->abreviation;

        if (isset($_POST['ResetPassword'])) {
            $model->attributes = $_POST['ResetPassword'];
            if ($model->validate()) {
                $hashedPassword = User::cryptPassword($model->password);
                $user->password = $hashedPassword;
                $user->passwordreset = '';
                $user->saveAttributes(array(
                    'password',
                    'passwordreset',
                ));
                try {
                    $userEntities = UserEntityPermission::getUserEntitiesByRole($user->id, __METHOD__, PlatformSolution::PLATFORM_NAME_WEBSITE);
                    $businessId = $userEntities['businessId'];

                    $businessRule = BusinessRules::getBusinessRules($businessId);
                    if ($businessRule->beta_portal_link) {
                        $this->redirect(Yii::app()->params['betaUrl']);
                    } else {
                        Yii::app()->user->setFlash('success', Yii::t('frontend', 'PASS_CHANGED'));
                        $this->redirect(Yii::app()->createUrl('merchant/login'));
                    }
                } catch (\Exception $e) {
                    Yii::app()->user->setFlash('success', Yii::t('frontend', 'PASS_CHANGED'));
                    $this->redirect(Yii::app()->createUrl('merchant/login'));
                }
            }
        }
        $this->render('resetpass', array(
            'model' => $model,
            'userModel' => $user,
        ));
    }

    public function actionAbout()
    {
        //$this->setappLanguage();
        $this->render('about', array());
    }

    public function checkEmail($email)
    {
        $check = preg_match('/\s/', $email);
        if ($check == 1) {
            return false;
        } else {
            return preg_match("^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,3})$^", $email);
        }

    }

    private function generatePassword($length)
    {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789*&#$";
        return substr((string)str_shuffle($chars), 0, $length);
    }

    private function captchaRequired()
    {
        return Yii::app()->session->itemAt('captchaRequired') >= $this->attempts;
    }

    /**
     * Adding logs
     * Saving the uploaded image (original image), send the response with image path
     *
     * Used on : Website(Business Signup landing page)
     *
     * @version July - Aug 2015 release V3.3.1
     * <AUTHOR>
     * @access public
     * @return JSON
     */
    public function actionUploadAndSaveOriginalImage()
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        //getting business and branch id from session
//        $busBranchId = Yii::app()->session['businessBranchId'];
        $busBranchId = Yii::app()->session['signup_branch_id'];

        $nbOfImage = (isset($_GET['nbOfImage'])) ? (int)$_GET['nbOfImage'] : 1;

        $maxFileSize = UPLOAD_MAX_FILESIZE;
        $iniMaxUpload = 10 . 'MB'; //ini_get('upload_max_filesize');

        if (isset($_FILES['upload_image' . $nbOfImage]) && $_FILES['upload_image' . $nbOfImage]['size'] > $maxFileSize) {
            Yii::log("The image is too big: " . $_FILES['upload_image' . $nbOfImage]['size'], CLogger::LEVEL_WARNING, $platformName . '_' . __FUNCTION__);
            $result = ['status' => 'NOT_OK', 'message' => Yii::t('backend', 'BACKEND_IMAGE_SIZE_RESTRICTION', ['{max_size}' => $iniMaxUpload])];
            Utils::sendResponse(200, CJSON::encode($result));
        }

        try {
            $userEntities['mainBranchId'] = $busBranchId;
            $userEntities['businessId'] = BusinessBranch::model()->findByPk($busBranchId)->business_fk;
            $uploader = new ImageUploader($userEntities, $_FILES, $_POST, $_GET);
            $uploadedFilePath = $uploader->upload();
            $uploader->resize();

            echo CJSON::encode(array('status' => 'OK', 'imageUrl' => $uploadedFilePath, 'nbOfImage' => $nbOfImage));
            Yii::app()->end();
        } catch (Exception $e) {
            Utils::sendResponse(200, json_encode([
                'status' => 'NOT_OK',
                'message' => $e->getMessage(),
            ]));
            Yii::app()->end();
        }
    }

    /**
     * Getting the content for  Crop Image Modal Dialog
     *
     * Used on : Website (Business Admin[Punch reward, Ofers] )
     *
     * @version Website May Release
     * @param
     * @access public
     * @return View
     */

    public function actionCropImageModalView()
    {
        $dialogTitle = Yii::t('backend', 'CROP_IMAGE_TITLE');

        if (!isset($_GET['calledFrom'])) {
            throw new CHttpException(404, Yii::t('frontend', 'EX_NOT_FOUND'));
        } elseif (!isset($_GET['imagePath'])) {
            throw new CHttpException(404, Yii::t('frontend', 'EX_NOT_FOUND'));
        }

        $calledFrom = (int) $_GET['calledFrom'];
        $imagePath = $_GET['imagePath'];
        $nbOfImage = (isset($_GET['nbOfImage'])) ? (int) $_GET['nbOfImage'] : 1;

        $this->renderPartial('_cropImageModalView',
            array(
                'dialogTitle' => $dialogTitle,
                'imagePath' => $imagePath,
                'calledFrom' => $calledFrom,
                'nbOfImage' => $nbOfImage,
            ));
    }

    /**
     * Crop the image and save in 3 different sizes
     *
     * Used on : Website (Business Logo, landing page)
     *
     * @version Website June Release
     * @param
     * @access public
     * @return JSON
     */

    public function actionCropImage()
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['imagePath'])) {
            $busBranchId = Yii::app()->session['signup_branch_id'];
            $imageCropper = new ImageCropper($_POST, $busBranchId);
            $images = $imageCropper->crop();

            echo CJSON::encode(array(
                'status' => 'OK',
                'image_large' => $images['image_large'],
                'image_medium' => $images['image_medium'],
                'image_thumbnail' => $images['image_thumbnail']));
            Yii::app()->end();
        }
    }

    /**
     * Sending email when the user clicks on upgrade button
     *
     * Used on : Website(BusinessAdmin)
     *
     * @version Website June 2014 Release
     * @param
     * @access public
     * @return JSON
     */

    public function actionSendUpgradeEmail()
    {
        $upgradeFeature = Yii::app()->request->getParam('upgrade');
        $businessId = (int) Yii::app()->request->getParam('businessId');

        $model = BusinessBranch::model()->findByAttributes(array('business_fk' => $businessId, 'default_branch' => 1));

        if ($model == null) {
            echo CJSON::encode(array('status' => 'success', 'businessId' => $businessId, 'message' => ''));
            Yii::app()->end();
        }

        //sending the email
        $mail = new YiiMailer();
        $mail->IsSMTP(); // Set mailer to use SMTP

        $mail->setView('upgradeEmail');
        $mail->setLayout('feedbackMain');
        $mail->setData(array(
            'business_name' => $model->name,
            'contact_name' => $model->contact_name,
            'phone' => Utils::getFormattedPhoneNumber($model->phone, $model->country_phone_fk, 'INTERNATIONAL'),
            'email' => $model->email,
            'address' => $model->address,
            'upgradeFeature' => $upgradeFeature,
        ));
        $mail->setReplyTo($model->email);
        $mail->setFrom($model->email);
        $mail->setTo(array(
            Yii::app()->params['salesemail'],
        ));
        $mail->setSubject('Lead - Kangaroo portal upgrade / ' . $upgradeFeature);

        if ($mail->send()) {
            echo CJSON::encode(array('status' => 'success', 'businessId' => $businessId, 'message' => ''));
        } else {
            echo CJSON::encode(array('status' => 'error', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'), 'errors' => $mail->getError()));
        }
    }

    /**
     * Formtatting phone number
     * When Admin clicks on upgrade button it will show this form
     *
     * Used in : Website(Business Admin)
     *
     * @version May - June 2015 release V3.3.0
     * @since 3.1.1
     * <AUTHOR>
     * @access public
     * @return html/json
     */
    public function actionPortalUpgradeDialog()
    {
        $adminPortalUpgradeForm = new AdminPortalUpgradeForm('admin-99-upgrade');
        $dialogTitle = Yii::t('backend', 'BACKEND_BUSADMINMENU_POPUP_UPGRADE_TITLE');

        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $businessId = (int) Yii::app()->request->getParam('buid');

        $upgradeFeature = (isset($_GET['upgrade'])) ? $_GET['upgrade'] : '';

        $ip = (Yii::app()->request->userHostAddress == '127.0.0.1') ? null : Yii::app()->request->userHostAddress;
        $allCountries = Utils::getEnabledCountriesWithDefaultSelected($platformName, $ip);
        $selectedCountry = $allCountries['selectedCountry']['code']; //country code: CA, US
        $allCountries = array_keys(CHtml::listData($allCountries['countryList'], 'code', 'name'));
        $allCountries = json_encode($allCountries);

        if (isset($_POST['AdminPortalUpgradeForm'])) {
            $adminPortalUpgradeForm->attributes = $_POST['AdminPortalUpgradeForm'];

            $countryCode = 'CA';

            if (isset($_POST['country_code']) && $_POST['country_code'] != '') {
                $countryCode = trim((string)$_POST['country_code']);
                $countryCode = substr((string)$countryCode, 0, 2);
                $countryCode = strtoupper((string)$countryCode);
            }

            if (Utils::isValidPhone($adminPortalUpgradeForm->phone, $countryCode, $platformName) == false) {
                $adminPortalUpgradeForm->addCustomError('phone', Yii::t('commonlabels', 'COMMON_PHONENUMBER_INVALID_PHONE'));
            }

            // validating the data
            if ($adminPortalUpgradeForm->validate()) {
                $businessId = $_POST['AdminPortalUpgradeForm']['businessId'];

                $branchModel = BusinessBranch::model()->findByAttributes(array('business_fk' => $businessId, 'default_branch' => 1));
                if ($branchModel == null) {
                    $businessName = '';
                    $businessEmail = '';
                } else {
                    $businessName = $branchModel->name;
                    $businessEmail = $branchModel->email;
                }

                $formattedPhone = $adminPortalUpgradeForm->phone; //E164
                if (isset($_POST['country_code']) && $_POST['country_code'] != '') {
                    $countryCode = trim((string)$_POST['country_code']);
                    $countryCode = substr((string)$countryCode, 0, 2);

                    $countryFk = Countries::model()->findByAttributes(['code' => $countryCode])->country_pk;
                    $formattedPhone = Utils::getFormattedPhoneNumber($adminPortalUpgradeForm->phone, $countryFk, 'INTERNATIONAL');
                }

                $arrayContent = array(
                    'business_name' => $businessName,
                    'contact_name' => $adminPortalUpgradeForm->first_name . ' ' . $adminPortalUpgradeForm->last_name,
                    'email' => $businessEmail,
                    'subject' => $adminPortalUpgradeForm->subject,
                    'phone' => $formattedPhone,
                    'message_content' => $adminPortalUpgradeForm->message_content,
                    'upgradeFeature' => $adminPortalUpgradeForm->upgradeFeature,
                );

                $result = $this->sendContactEmailSales($arrayContent);

                echo CJSON::encode(array('status' => 'OK', 'message' => Yii::t('backend', 'BACKEND_BUSADMINMENU_POPUP_UPGRADE_SUCCESS')));

            } // validate
            else {
                echo CJSON::encode(array('status' => 'errors', 'errors' => $adminPortalUpgradeForm->getErrors()));
            }
            Yii::app()->end();
        } // post

        $this->renderPartial('_merchantPortalUpgradeForm', array('adminPortalUpgradeForm' => $adminPortalUpgradeForm, 'dialogTitle' => $dialogTitle, 'upgradeFeature' => $upgradeFeature, 'businessId' => $businessId, 'allCountries' => $allCountries, 'selectedCountry' => $selectedCountry));
    }

    /**
     * Ajax request to get currency by country id
     *
     * Used in : Website(Merchant Controller)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public function actionAjaxGetCurrency()
    {
        $country = (isset($_GET['country'])) ? $_GET['country'] : null;

        if (!$country) {
            Utils::sendResponse(200, CJSON::encode(['status' => 'NOT_OK', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR')]));
        }

        $country = Countries::model()->findByAttributes(['code' => $country]);
        $currency = Currency::model()->findByPk($country->currency_fk);

        if ($currency != null) {
            $currencySymbol = $currency->name . ' (' . $currency->symbol . ')';
        } else {
            $currencySymbol = '';
        }

        $currencyText = $currencySymbol ? Yii::t('commonlabels', 'COMMON_GENERIC_CURRENCY') . ' ' . $currencySymbol : '';
        Utils::sendResponse(200, CJSON::encode(['status' => 'OK', 'currency' => $currencyText]));
    }

    /**
     * Sending email to sales from cotnact form
     *
     * Used in : Website(Business Admin)
     *
     * @version July - August release V 3.1.1
     * @access public
     * @return string
     */

    private function sendContactEmailSales($arrayContent)
    {
        //sending the email
        $mail = new YiiMailer();
        $mail->IsSMTP(); // Set mailer to use SMTP

        $mail->setView('portalUpgrade');
        $mail->setLayout('feedbackMain');
        $mail->setData(
            $arrayContent
        );
        $mail->setReplyTo($arrayContent['email']);
        $mail->setFrom(Yii::app()->params['emailin'], Yii::app()->params['fullnamemailin']);
        $mail->setTo(array(
            Yii::app()->params['salesemail'],
        ));
        $mail->setSubject('Request/' . $arrayContent['subject']);

        if ($mail->send()) {
            return 'OK';
        } else {
            return 'NOT_OK';
        }
    } //private function

    // public function actionGeo()
    // {
    //     // $r = Cities::insertGetCityV330('Berlin', 71, 211);
    //     $r = $this->getLatLongByAddress(['country'=>197, 'province'=>62, 'city'=>29757, 'address'=>'74, La Rambla Alcorcón']);
    //     echo '<pre>';
    //     print_r($r);
    // }
    function actionSquareTest(){
        Yii::log('*********SquareTest************'.json_encode($_REQUEST), CLogger::LEVEL_INFO,  __FUNCTION__);
    }

    private function loadSubscription($userId, $businessId, $email, $currency, $successUrl, $cancelUrl, $trail_enabled, $posSystemPk)
    {
        $lang = Yii::app()->language;
        if ($lang === 'nl') {
            $lang = 'en';
        }

        try {
            $stripePayment = new \application\components\stripe\BusinessPackageStripePayment();
            $stripePayment->setUserId($userId);
            $stripePayment->setBusinessId($businessId);
            $session = $stripePayment->checkoutSubscriptionBuilder($lang, $successUrl, $cancelUrl, $currency, $trail_enabled, $posSystemPk);
            if(!$session){
                Yii::app()->user->setFlash('error', Yii::t('backend', 'BACKEND_PORTAL_ACCESS_DENIED'));
                $this->redirect(array('/merchant/login'));
            }
            $this->render('checkout', array(
                'checkout_session_id' => $session->id,
                'lang' => $lang,
                'cancelUrl' =>$cancelUrl,
            ));
        } catch (\Exception $e) {
            Yii::log(CJSON::encode([
                'user_id' => $userId,
                'email' => $email,
                'successUrl' => $successUrl,
                'cancelUrl' => $cancelUrl
            ]), CLogger::LEVEL_ERROR, __METHOD__);
            Yii::app()->user->setFlash('error', Yii::t('landingpages', 'UNKNOWN_ERROR'));
            $this->redirect($cancelUrl);
        }
        exit();
    }

    public function actionSubscriptionSuccess()
    {
        $this->checkBusinessStatus();

        //$model = new User();
        $user = User::model()->findByPk(Yii::app()->user->getID());
        $user->login_type_fk = 1; // 1=website
        $user->ip = Yii::app()->request->userHostAddress;
        $user->timezone_mysql_fk = $timezoneId = Utils::getTimezoneSession();
        $user->update();

        // Track logins
        try {
            $auditLogin = new AuditUserLogin();
            $auditLogin->ip = Yii::app()->request->userHostAddress;
            $auditLogin->user_id = Yii::app()->user->getID();
            $auditLogin->session_id = session_id();
            $auditLogin->db_name = Utils::getDsnAttribute('dbname');
            $auditLogin->platform_id = PLATFORM_WEBSITE;
            $auditLogin->user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            $auditLogin->save(false);
        } catch (Exception) {
            Yii::log(json_encode([
                'ip' => Yii::app()->request->userHostAddress,
                'user_id' => Yii::app()->user->getID(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            ]), CLogger::LEVEL_INFO, __METHOD__);
        }

        if (Yii::app()->user->checkAccess('business') && Yii::app()->user->checkAccess('admin')) {
            if (isset($_GET['redirect'])) {
                $this->redirect($_GET['redirect']);
            } else {
                $this->redirect(array('businessAdmin/index'));
            }
        } elseif (Yii::app()->user->checkAccess('branch') && Yii::app()->user->checkAccess('admin')) {
            $this->redirect(array('branchAdmin/index'));
        } elseif (Yii::app()->user->checkAccess('clerk')) { //for clerk
            $this->redirect(array('profile/personal'));
        } elseif (Yii::app()->user->checkAccess('superadmin') || Yii::app()->user->checkAccess('merchantsetup')) { //for systemAdmin
            $this->redirect(array('systemAdmin/main'));
        } elseif (Yii::app()->user->checkAccess('businessgroupmanager')) { //for Business Group Manager
            $this->redirect(array('businessGroupManager/business/impersonate'));
        }
    }

    public function actionAutoLogin()
    {
        Yii::log('actionAutoLogin GET=' . CJSON::encode($_GET) . ' POST=' . CJSON::encode($_POST), CLogger::LEVEL_INFO, __METHOD__);

        // header('Access-Control-Allow-Origin: ' . Yii::app()->params['selfSignUpUrl']);
        // Set auto_login_flag in cookie
        if (isset($_GET['token'])) {
            $urlParts = parse_url(Yii::app()->params['serverUrl']);
            $fp = strrpos((string)$urlParts['host'], '.');
            $sp = strrpos((string)substr((string)$urlParts['host'], 0, $fp), '.');
            if ($sp === false) {
                $domain = $urlParts['host'];
            } else {
                $domain = substr((string)$urlParts['host'], $sp);
            }
            setcookie('auto_login_flag', '1', 0, '/', $domain, true, true);
            Yii::log('actionAutoLogin After set cookie', CLogger::LEVEL_INFO, __METHOD__);
        }

        $redirectUrl = Yii::app()->params['serverUrl'] . '/businessAdmin';
        if (isset($_GET['redirect'])) {
            $redirectUrl = $_GET['redirect'];
        }

        if (!isset($_GET['token']) && !isset($_GET['hToken'])) {
            Yii::log('actionAutoLogin No token attached', CLogger::LEVEL_INFO, __METHOD__);
            $this->redirect($redirectUrl);
//            Utils::sendResponse(401, Yii::t('backend', 'BACKEND_WSSECURITY_AUTHORIZATION_ERROR'), 'application/text');
        }

        if (isset($_GET['token'])) {
            $token = $_GET['token'];
            $providerToken = OauthProviderToken::model()->findByAttributes([
                'access_token' => $token,
            ], ['order' => 'id desc']);
        } else {
            $token = $_GET['hToken'];
            $providerToken = OauthProviderToken::model()->findByAttributes([
                'token_hash' => $token,
            ], ['order' => 'id desc']);
        }

        if (!$providerToken || $providerToken->expires < time()) {
            Yii::log('actionAutoLogin No token found or token is expired', CLogger::LEVEL_INFO, __METHOD__);
            $this->redirect($redirectUrl);
//            Utils::sendResponse(401, Yii::t('backend', 'BACKEND_WSSECURITY_AUTHORIZATION_ERROR'));
        }

        try {
            $userEntities = UserEntityPermission::getUserEntitiesByRole($providerToken->user_fk, __METHOD__, PlatformSolution::PLATFORM_NAME_WEBSITE);
            $businessRules = BusinessRules::model()->findByAttributes(['business_fk' => $userEntities['businessId']]);
            // Set auto_login_flag in cache for new bp
            if (isset($_GET['token'])) {
                $cacheKey = 'auto_login_flag_' . $userEntities['businessId'];
                Yii::app()->cache->set($cacheKey, 1, 60);
                Yii::log('Set cache for 1 min, cacheKey: ' . $cacheKey, CLogger::LEVEL_INFO, __METHOD__);
            }
            if (isset($_GET['redirect'])) {
                if (strpos((string)$_GET['redirect'], rtrim((string)Yii::app()->params['betaUrl'], "/")) !== false) {
                    $redirectUrl .= "?access_token=" . $providerToken->access_token . "&refresh_token=" . $providerToken->refresh_token;
                }
            } elseif ($businessRules->beta_portal_link) {
                $redirectUrl = rtrim((string)Yii::app()->params['betaUrl'], "/") . "/server/auth/portal/?access_token=" . $providerToken->access_token . "&refresh_token=" . $providerToken->refresh_token;
            }
        } catch (\Exception $exception) {
            Yii::log('actionAutoLogin user=' . $providerToken->user_fk . ' error:' . $exception->getMessage(), CLogger::LEVEL_ERROR, __METHOD__);
        }

        Yii::log('actionAutoLogin redirectURL='. $redirectUrl, CLogger::LEVEL_INFO, __METHOD__);

        $identity = new UserBearerTokenIdentity($providerToken->user_fk);

        if (!$identity->authenticate()) {
            Yii::log('actionAutoLogin authenticate failed, user=' . $providerToken->user_fk, CLogger::LEVEL_INFO, __METHOD__);
            $this->redirect($redirectUrl);
//            Utils::sendResponse(401, Yii::t('backend', 'BACKEND_WSSECURITY_AUTHORIZATION_ERROR'));
        }

        if (!Yii::app()->user->login($identity, $duration = 0)) {
            Yii::log('actionAutoLogin login failed user=' . $providerToken->user_fk, CLogger::LEVEL_INFO, __METHOD__);
            $this->redirect($redirectUrl);
//            Utils::sendResponse(401, Yii::t('backend', 'BACKEND_WSSECURITY_AUTHORIZATION_ERROR'));
        }

        $this->redirect($redirectUrl);
//        Utils::sendResponse(200, json_encode(['redirect_url' => $redirectUrl]));
    }

    private function loginPageViewLog(): void
    {
        Yii::log(json_encode([
            'message' => 'Login Page Viewed',
            'post_credential' => $_POST['credential'] ?? null,
            'username' => $_POST['LoginForm']['username'] ?? null,
            'query_string' => $_SERVER["QUERY_STRING"] ?? null,
            'g_csrf_token' => $_POST['g_csrf_token'] ?? null,
            'ip' => Yii::app()->request->userHostAddress,
            'session_id' => session_id(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'access_token' => $_GET['access_token'] ?? null,
        ]), CLogger::LEVEL_INFO, __METHOD__);
    }

    private function createMfaVerificationForSystemAdmin(string $accessToken, array $paramsArr): array
    {
        return $this->executeMfaRequest($accessToken, '/verify/verifications', $paramsArr);
    }

    private function checkMfaVerificationForSystemAdmin(string $accessToken, array $paramsArr): array
    {
        return $this->executeMfaRequest($accessToken, '/verify/verification-checks', $paramsArr);
    }

    private function executeMfaRequest(string $accessToken, string $endpoint, array $paramsArr): array
    {
        try {
            $client = new \GuzzleHttp\Client([
                'headers' => [
                    'Authorization' => 'Bearer ' . $accessToken,
                    'Accept' => 'application/vnd.kangaroorewards.api.v1+json;',
                    'Content-Type' => 'application/json',
                ],
                'timeout' => 30,
            ]);

            $request = $client->request('POST', Yii::app()->params['kangarooApiUri'] . $endpoint, ['json' => $paramsArr]);
            $httpCode = $request->getStatusCode();
            $data = json_decode($request->getBody()->getContents(), true);

            if ($endpoint === '/verify/verifications') {
                $this->logMfaOperation('MFA Creation Info', $httpCode, $data);
            }

            return [
                'status' => ($httpCode === 200 || $httpCode === 204),
                'data' => $data['data'] ?? null,
            ];
        } catch (Exception $e) {
            Yii::log($e->getMessage(), CLogger::LEVEL_ERROR, __METHOD__);
            return [
                'status' => false,
                'data' => []
            ];
        }
    }

    private function logMfaOperation(string $operation, int $httpCode, array|null $data): void
    {
        Yii::log($operation . ' ' . CJSON::encode([
            'http_code' => $httpCode,
            'data' => $data
        ]), CLogger::LEVEL_INFO, __METHOD__);
    }

    private function shouldForceMfa($user, $location): bool
    {
        $forceMfa = false;
        $currentIp = Yii::app()->request->userHostAddress;
        $userAgent = ($_SERVER['HTTP_USER_AGENT'] ?? null);

        $previousLogin = AuditUserLogin::model()->findByAttributes([
            'user_id' => $user->id,
        ], [
            'condition' => 'platform_id IN (' . PLATFORM_WEBSITE . ',' . PLATFORM_MERCHANT_PORTAL . ')',
            'order' => 'id DESC',
            'limit' => 1,
        ]);

        if (!$previousLogin) {
            return true;
        }

        // Same IP, same device -> Allow
        if ($previousLogin->ip === $currentIp && $previousLogin->user_agent === $userAgent) {
            return false;
        }

        // New IP, same device -> Allow
        if ($previousLogin->ip !== $currentIp && $previousLogin->user_agent === $userAgent) {
            return false;
        }

        if ($previousLogin->details) {
            $detailsObj = json_decode($previousLogin->details);
            $previousLocation = $detailsObj->location ?? Utils::lookupLocation($previousLogin->ip);
            $differentCountry = $previousLocation->countryCode !== $location->countryCode;
            $differentCity = $previousLocation->city !== $location->city;
            $differentIp = $previousLogin->ip !== $currentIp;
            $differentUserAgent = $previousLogin->user_agent !== $userAgent;
            if ($differentCountry || $differentCity || $differentIp || $differentUserAgent) {
                $forceMfa = true;
            }
        }

        return $forceMfa;
    }
}
