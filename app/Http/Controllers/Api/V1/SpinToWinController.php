<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiHelper;
use App\Helpers\UserTransaction\TransactionFactory;
use App\Http\Controllers\Api\V1\ApiController;
use App\Http\Requests\V1\ParticipateSpinToWin;
use App\Http\Requests\V1\UserSpinWinPrize;
use App\Http\Resources\SpinToWinCollection;
use App\Http\Resources\SpinToWinResource;
use App\Http\Resources\UserPrizeWinCollection;
use App\Models\SpinToWin;
use App\Services\SpinToWinService;
use App\Traits\BusinessRulesTrait;
use App\Traits\SpinToWinTrait;
use App\Models\V1\User;
use App\Traits\UserPrizeWinTrait;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SpinToWinController extends ApiController
{
    use SpinToWinTrait;
    private $limit = 8;

    /**
     * List user prizes
     *
     * @param Request $request
     * 
     * <AUTHOR>
     * @return SpinToWinCollection|\Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            $business = $request->appBusiness();

            $locale = App::getLocale();

            $includes = explode(',', $request->include);

            $spinService = new SpinToWinService($request, spinToWin: null, businessId: $business->business_pk);
            
            if(!$spinService->showSpinToWinForBusiness()){
                return $this->errorNotFound();
            }

            if(in_array('all', $includes)){
                $enabled = null;
            }else{
                $enabled = 1;
            }

            $spinToWinList = $this->getSpinToWinList($business->business_pk, $enabled);

            return new SpinToWinCollection($spinToWinList);
        } catch (\LogicException $e) {
            Log::error(__METHOD__ . ' ' . $e, ['input' => '']);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, ['input' => '']);
            return $this->errorInternalError();
        }
    }
    
    /**
     * List user prizes
     *
     * @param Request $request
     * @param SpinToWin $spinToWin
     * 
     * <AUTHOR>
     * @return SpinToWinResource|\Illuminate\Http\JsonResponse
     */
    public function show(Request $request, SpinToWin $spinToWin)
    {
        try {
            $user = Auth::user();
            $business = $request->appBusiness();

            $locale = App::getLocale();

            $spinService = new SpinToWinService($request, spinToWin: $spinToWin, businessId: $business->business_pk);
            
            if(!$spinService->showSpinToWinForBusiness()){
                return $this->errorNotFound();
            }

            return new SpinToWinResource($spinToWin);
        } catch (\LogicException $e) {
            Log::error(__METHOD__ . ' ' . $e, ['input' => '']);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, ['input' => '']);
            return $this->errorInternalError();
        }
    }

    /**
     * Choose and assign user prize if applicable
     *
     * <AUTHOR>
     * @since 13/07/2022
     * @param ParticipateSpinToWin $request
     * @param SpinToWin $spinToWin
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function participate(ParticipateSpinToWin $request, SpinToWin $spinToWin)
    {
        try {
            $user = Auth::user();
            $business = $request->appBusiness();
            
            $spinService = new SpinToWinService($request, $spinToWin, businessId: $business->business_pk);

            $showSpinForCustomerStatus = $spinService->showSpinWinForCustomer();

            $response = null;
            switch ($showSpinForCustomerStatus) {
                case 'disabled':
                    $response = $this->errorWrongArgs('Bad Request', 'Spin To Win is disabled.');
                    break;
                case 'not_found':
                    $response =  $this->errorNotFound();
                    break;
                case 'user_limit_reached':
                    $response =  $this->errorWrongArgs('Bad Request', 'Customer max participation number reached.');
                    break;
                case 'prizes_limit_reached':
                    $response =  $this->errorWrongArgs('Bad Request', 'There are no prizes left.');
                    break;
                case 'enabled':
                    $response =  $spinService->getCustomerPrize();
                    break;
                default:
                    $response = $this->errorWrongArgs('Bad Request');
            }
            return $response;
        } catch (\LogicException $e) {
            Log::error(__METHOD__ . ' ' . $e, ['input' => '']);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, ['input' => '']);
            return $this->errorInternalError();
        }
    }
    
    /**
     * List user prizes
     *
     * @param Request $request
     * 
     * <AUTHOR>
     * @return SpinToWinCollection|\Illuminate\Http\JsonResponse
     */
    public function details(Request $request)
    {
        try {
            $user = Auth::user();
            $business = $request->appBusiness();

            $locale = App::getLocale();

            $spinService = new SpinToWinService($request, spinToWin: null, businessId: $business->business_pk);
            
            if(!$spinService->showSpinToWinForBusiness()){
                return $this->errorNotFound();
            }

            $activeSpinToWins = $this->getActiveSpinToWin($business->business_pk);

            return new SpinToWinCollection($activeSpinToWins);
        } catch (\LogicException $e) {
            Log::error(__METHOD__ . ' ' . $e, ['input' => '']);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, ['input' => '']);
            return $this->errorInternalError();
        }
    }

    /**
     * /spin-to-wins/{spin_to_win_Id}/participants-prizes?customer_id=eq|{customerUid}
     * @param Request $request
     * @param SpinToWin $spinToWin
     * @return \App\Http\Resources\UserPrizeWinCollection|\Illuminate\Http\JsonResponse
     * <AUTHOR>
     * @since 2024-03
     */
    public function spinToWinParticipantPrizes(Request $request, SpinToWin $spinToWin)
    {
        try {
            $business = $request->appBusiness();
            $spinService = new SpinToWinService($request, $spinToWin, businessId: $business->business_pk);

            if (!$spinService->showSpinToWinForBusiness()) {
                return $this->errorUnauthorized();
            }

            if ($request->customer_id) {
                # Filter Conditions
                $conditions = ApiHelper::buildQueryConditions(
                    $request->only(['customer_id'])
                );
            }
            $userPrizes = $spinService->getCustomerPrizeStatusPaginated($conditions ?? [])
                ->simplePaginate((int) $request->per_page);

            return new UserPrizeWinCollection($userPrizes);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e);
            return $this->errorInternalError();
        }
    }
}