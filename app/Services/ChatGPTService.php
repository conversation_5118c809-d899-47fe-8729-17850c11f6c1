<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\GptChat;
use App\Models\GptChatCompletion;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use JsonException;

class ChatGPTService implements GenAIServiceInterface
{
    private Client $client;

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => 'https://api.openai.com',
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . config('app.CHAT_GPT_APP_KEY')
            ],
            'timeout' => 120,
        ]);
    }

    /**
     * $validated[model] - chatgpt-4o-latest, gpt-4o, gpt-4o-mini
     * @param GptChat $gptChat
     * @param array $validated - ['model', 'prompt', 'context', 'temperature']
     * @param int $maxTokens
     * @return GptChatCompletion|null
     * @throws GuzzleException
     * @throws JsonException
     */
    public function chatCompletions(GptChat $gptChat, array $validated, int $maxTokens = 4000): ?GptChatCompletion
    {
        $messages = [["role" => "user", "content" => $validated['prompt']]];

        if (isset($validated['context']) && trim($validated['context'])) {
            array_unshift($messages, ["role" => "system", "content" => $validated['context']]);
        }

        $model = $this->getAIModel($validated);
        $requestData = [
            'messages' => $messages,
            'model' => $model,
            'n' => 1,
            'user' => 'B' . bin2hex($gptChat->business->business_uid),
        ];

        // not compatible with o-series models
        if (!in_array($model, ['o4-mini', 'o1', 'o1-mini', 'o3-mini'], true)) {
            $requestData['max_completion_tokens'] = $maxTokens;
            $requestData['temperature'] = $validated['temperature'] ?? 0.7;
        }

        try {
            $response = $this->client->post('/v1/chat/completions', [
                'json' => $requestData,
            ]);

            $body = json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);

            $reply = (string) $body['choices'][0]['message']['content'];

            $context = null;
            if (isset($validated['context']) && trim($validated['context'])) {
                $context = filter_var($validated['context'], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            }

            $message['gpt_chat_id'] = $gptChat->id;
            $message['prompt'] = filter_var($validated['prompt'], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            $message['context'] = $context;
            $message['message'] = $reply;
            $message['usage_prompt_tokens'] = $body['usage']['prompt_tokens'];
            $message['usage_completion_tokens'] = $body['usage']['completion_tokens'];
            $message['usage_total_tokens'] = $body['usage']['total_tokens'];
            $message['message_info'] = [
                'id' => $body['id'],
                'object' => $body['object'],
                'model' => $body['model'],
                'source' => $validated['source'] ?? 'campaign_builder',
                'provider' => 'openai',
            ];

            return GptChatCompletion::create($message);
        } catch (RequestException $e) {
            if (method_exists($e, 'getResponse') && $e->hasResponse()) {
                Log::error(__METHOD__ . ' ' . $e->getResponse()?->getBody(), [
                    'businessId' => $gptChat->business->business_pk,
                ]  + $validated);
            } else {
                Log::error($e, ['businessId' => $gptChat->business->business_pk] + $validated);
            }
        }

        throw new \RuntimeException('The server encountered an unexpected condition which prevented it from fulfilling the request');
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function imageGeneration(GptChat $gptChat, array $validated): ?array
    {
        $price = 0.020; // per image
        $size = $validated['size'] ?? '1024x1024';
        $n = $validated['n'] ?? 1;
        try {
            $response = $this->client->post('/v1/images/generations', [
                'json' => [
                    'prompt' => $validated['prompt'],
                    'size' => $size,
                    'n' => $n,
                    'user' => bin2hex($gptChat->business->business_uid),
                ],
            ]);

            $body = json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);

            GptChatCompletion::create([
                'gpt_chat_id' => $gptChat->id,
                'prompt' => $validated['prompt'],
                'message' => json_encode($body['data'], JSON_THROW_ON_ERROR),
                'message_info' => [
                    'model' => 'dall-e',
                    'object' => 'image.generation',
                    'source' => $validated['source'] ?? 'campaign_editor',
                    'cost' => count($body['data']) * $price,
                    'provider' => 'openai',
                ]
            ]);

            return $body['data'];
        } catch (RequestException $e) {
            if (method_exists($e, 'getResponse') && $e->hasResponse()) {
                Log::error(__METHOD__ . ' ' . $e->getResponse()?->getBody(), [
                    'businessId' => $gptChat->business->business_pk
                    ] + $validated);
            } else {
                Log::error($e, ['businessId' => $gptChat->business->business_pk] + $validated);
            }
        }

        throw new \RuntimeException('The server encountered an unexpected condition which prevented it from fulfilling the request');
    }

    private function getAIModel(array $validated): string
    {
        $allowedModels = ['chatgpt-4o-latest', 'gpt-4o', 'gpt-4o-mini', 'gpt-4.1', 'gpt-4.1-mini', 'o4-mini'];

        if (isset($validated['model']) && in_array(trim($validated['model']), $allowedModels, true)) {
            return trim($validated['model']);
        }

        return 'gpt-4o-mini';
    }

    private function costPerModelMap(): array
    {
        // model=chatgpt-4o-latest - Latest used in ChatGPT
        // model=gpt-4o - GPT 4o model
        // model=gpt-4o-mini - Mini GPT 4o model
        return [
            'chatgpt-4o-latest' => [
                'input_cost' => 5.00,
                'output_cost' => 15.00,
                'effective_date' => '2024-01-01'
            ],
            'gpt-4o' => [
                'input_cost' => 2.50,
                'output_cost' => 10.00,
                'effective_date' => '2024-01-01'
            ],
            'gpt-4o-mini' => [
                'input_cost' => 0.15,
                'output_cost' => 0.60,
                'effective_date' => '2024-01-01'
            ],
            'gpt-4.1' => [
                'input_cost' => 2.00,
                'output_cost' => 8.00,
                'effective_date' => '2025-04-14'
            ],
            'gpt-4.1-mini' => [
                'input_cost' => 0.40,
                'output_cost' => 1.60,
                'effective_date' => '2025-04-14'
            ],
            'o4-mini' => [
                'input_cost' => 1.10,
                'output_cost' => 4.40,
                'effective_date' => '2025-04-16'
            ],
            'dall-e-3' => [
                'standard' => [
                    '1024x1024' => 0.04,
                    '1024x1792' => 0.08,
                ],
                'hd' => [
                    '1024x1024' => 0.08,
                    '1024x1792' => 0.12,
                ],
                'effective_date' => '2024-01-01'
            ],
            'dall-e-2' => [
                '256x256' => 0.016,
                '512x512' => 0.018,
                '1024x1024' => 0.02,
                'effective_date' => '2024-01-01'
            ]
        ];
    }
}
