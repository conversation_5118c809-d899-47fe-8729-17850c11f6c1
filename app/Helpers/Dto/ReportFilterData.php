<?php

namespace App\Helpers\Dto;

use App\Helpers\DataTransferObject\DataTransferObject;

class ReportFilterData extends DataTransferObject
{
    /** @var string */
    public string $field = '';

    /** @var string */
    public string $operator = '';

    /** @var string */
    public string $value = '';

    /** @var string */
    public string $type = '';

    /**
     * @param array $data
     * @return static
     */
    public static function fromRequestData(array $data): self
    {
        return new self($data);
    }
}
