<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used on API
    |
     */

    'REQUEST_WRONG_ARGS' => 'Bad Request',
    'REQUEST_NOT_FOUND' => 'Not found',

    'API_GIFTCARD_PURCHASE_SMS' => 'You have just purchased a :currency_cymbol:item_price Gift Card @:business_name',
    'ACTIVATION_OF_YOUR_ACCOUNT' => ':business_name: Activation of your Loyalty Program Account',
    'FRONTEND_RESET_PIN_SMS' => ':business_name. Click this link to reset your PIN :short_link',
    'FRONTEND_RESET_PIN_SMS_BROADNET' => ':business_name. Click this link to reset :short_link',
    'FRONTEND_RESET_PIN_SMS_VERIF_CODE' => ':business_name. Verification code :verif_code to reset your PIN',
    'FRONTEND_RESET_PIN_SMS_VERIF_CODE_BROADNET' => ':business_name. Reset with :verif_code',
    'FRONTEND_CHANGE_PHONE_SMS' => ':business_name. Click this verification link to confirm your cell# :short_link',
    'API_TPR_REDEMPTION_SMS' => 'You have just redeemed :reward for :points points @:business_name',
    'FRONTEND_USERSIGNUP_VERIFY_SMS_STORE' => 'Welcome to :business_name Loyalty Program. Your PIN code is :pin_code. Tap the link to verify your account :short_link',
    'FRONTEND_USERSIGNUP_VERIFY_SMS_STORE_WITHOUT_PIN' => 'Welcome to :business_name Loyalty Program. Tap the link to verify your account :short_link',
    'FRONTEND_USERSIGNUP_WELCOME' => 'Welcome to :business_name Loyalty Program. Your PIN is :pin_code.',
    'FRONTEND_USERSIGNUP_WELCOME_BROADNET' => 'Welcome to :business_name Loyalty Program: :pin_code.',
    'FRONTEND_USERSIGNUP_WELCOME_WITHOUT_PIN' => 'Welcome to :business_name Loyalty Program.',
    'MERCHANTAPP_PUNCHPOINTSREWARD_EXACT_AMOUNT_CLAIM_ERROR_MESSAGE' => 'When Exact Amount of the purchase is selected, the amount is required.',
    'MERCHANTAPP_PUNCHPOINTSREWARD_FRECUENCY_ERROR_MESSAGE' => 'Ask the cashier when you can claim next!',
    'MERCHANTAPP_CLERKPINCODE_INCORRECT_PIN' => 'Invalid Employee ID (PIN Code)',
    'MERCHANTAPP_CUSTOMER_INCORRECT_PIN' => 'Invalid Customer PIN Code',
    'MERCHANTAPP_POINTREDEEM_MERCHANT_NOT_ALLOW' => 'This Merchant is not allow to Redeem on behalf of the Consumer any more',

    'CUSTOMER_NO_ELIGIBLE_TO_REDEEM_THIS' => 'This customer is not eligible to redeem this',

    'TRX_SUBTYPE_COUPON_CLAIMED' => 'Coupon claimed',
    'TRX_SUBTYPE_OFFER_CREATED_BASIC' => 'Offer Created',
    'TRX_SUBTYPE_TAGGED_BASIC' => 'Offer Tagged',
    'TRX_SUBTYPE_UNTAGGED_BASIC' => 'Offer Untagged',
    'TRX_SUBTYPE_FACEBOOK_SHARED_BASIC' => 'Offer Shared on FB',
    'TRX_SUBTYPE_TWITTER_SHARED_BASIC' => 'Offer Shared on TW',
    'TRX_SUBTYPE_TARGETED_BASIC' => 'Offer Created - Targetted',
    'TRX_SUBTYPE_REFER_BUSINESS' => 'Referred Business',
    'TRX_SUBTYPE_EXTERNAL_TX' => 'External TX',
    'TRX_SUBTYPE_NEW_BUSINESS' => 'New Business Created',
    'TRX_SUBTYPE_CHECKIN' => 'Check-in',
    'TRX_SUBTYPE_GIFTCARD_POINTS_CREATED' => 'Giftcard Created - Points',
    'TRX_SUBTYPE_FACEBOOK_SHARED_GIFTCARD' => 'Giftcard Shared on FB',
    'TRX_SUBTYPE_TWITTER_SHARED_GIFTCARD' => 'Giftcard Shared on TW',
    'TRX_SUBTYPE_TARGETED_GIFTCARD' => 'Giftcard Created - Targetted',
    'TRX_SUBTYPE_GIFTCARD_POINTS_PURCHASED' => 'Giftcard Purchased - Points',
    'TRX_SUBTYPE_GIFTCARD_POINTS_PAYPAL_REFUND' => 'Giftcard Refunded - PayPal',
    'TRX_SUBTYPE_GIFTCARD_POINTS_PURCHASED_MERCHANT' => 'Giftcard Purchase - At Store',
    'TRX_SUBTYPE_TAGGED_POINTS' => 'Offer Tagged',
    'TRX_SUBTYPE_CLAIMED_POINTS' => 'Offer Claimed',
    'TRX_SUBTYPE_SHARED' => 'Offer Shared',
    'TRX_SUBTYPE_ACCEPTED' => 'Offer Accepted',
    'TRX_SUBTYPE_UNTAGGED_POINTS' => 'Offer Untagged',
    'TRX_SUBTYPE_FACEBOOK_SHARED_POINTS' => 'Offer Shared on FB',
    'TRX_SUBTYPE_TWITTER_SHARED_POINTS' => 'Offer Shared on TW',
    'TRX_SUBTYPE_OFFER_CREATED_POINTS' => 'Offer Created',
    'TRX_SUBTYPE_CLERK_CANCELATION' => 'Offer Canceled -  Clerk',
    'TRX_SUBTYPE_TARGETED_POINTS' => 'Offer Created - Targetted',
    'TRX_SUBTYPE_OFFER_CREATED_PUNCH' => 'Offer Created',
    'TRX_SUBTYPE_TAGGED_PUNCH' => 'Offer Tagged',
    'TRX_SUBTYPE_UNTAGGED_PUNCH' => 'Offer Untagged',
    'TRX_SUBTYPE_CLAIMED_PUNCH' => 'Offer Claimed',
    'TRX_SUBTYPE_FACEBOOK_SHARED_PUNCH' => 'Offer Shared on FB',
    'TRX_SUBTYPE_TWITTER_SHARED_PUNCH' => 'Offer Shared on TW',
    'TRX_SUBTYPE_TARGETED_PUNCH' => 'Offer Created - Targetted',
    'TRX_SUBTYPE_REWARD_POINTS' => 'Reward',
    'TRX_SUBTYPE_REDEMPTION' => 'Redemption',
    'TRX_SUBTYPE_CALLBACK' => 'Callback',
    'TRX_SUBTYPE_DONATION' => 'Donate',
    'TRX_SUBTYPE_SIGNUP_REWARD_POINTS' => 'Signup Bonus',
    'TRX_SUBTYPE_PRODUCT_REDEMPTION_POINTS' => 'Redemption - Catalog',
    'TRX_SUBTYPE_CLERK_TRX_CANCELATION' => 'Transaction Canceled -  Clerk',
    'TRX_SUBTYPE_CLERK_REDEMPTION' => 'Redemption on behalf of Client - Custom',
    'TRX_SUBTYPE_CLERK_PRODUCT_REDEMPTION_POINTS' => 'Redemption on behalf of Client - Catalog',
    'TRX_SUBTYPE_POINTS_REFUND' => 'Refund - Points',
    'TRX_SUBTYPE_CLAIMED_POINTS_REWARD' => 'AutoClaim - Points',
    'TRX_SUBTYPE_AGGREGATED_CLAIMED_POINTS_REWARD' => 'MultiAutoClaim - Points',
    'TRX_SUBTYPE_TRANSACTION_SUBTYPE_NAME_FOLLOW_BUSINESS' => 'Follow-My-Store Bonus',
    'TRX_SUBTYPE_TRANSFER_POINTS_SENDER_RECALL' => 'Points Transfer Recall',
    'TRX_SUBTYPE_PRODUCT_CREATED' => 'Product Created',
    'TRX_SUBTYPE_REWARD_PUNCH' => 'Reward',
    'TRX_SUBTYPE_PRODUCT_REDEMPTION_PUNCH' => 'Redemption',
    'TRX_SUBTYPE_SIGNUP_REWARD_PUNCH' => 'Signup Bonus',
    'TRX_SUBTYPE_MULTI_PUNCHES' => 'MultiPunch Reward',
    'TRX_SUBTYPE_CLERK_PRODUCT_REDEMPTION_PUNCH' => 'Redemption on behalf of client',
    'TRX_SUBTYPE_POINTS_REWARD_CREATED' => 'Redemption Offer Created',
    'TRX_SUBTYPE_FACEBOOK_SHARED_RED_CATALOG_POINTS' => 'Offer Shared on FB',
    'TRX_SUBTYPE_TWITTER_SHARED_RED_CATALOG_POINTS' => 'Offer Shared on TW',
    'TRX_SUBTYPE_TARGETED_RED_CATALOG_POINTS' => 'Offer Created - Targetted',
    'TRX_SUBTYPE_PUNCH_REWARD_CREATED' => 'Redemption Offer Created',
    'TRX_SUBTYPE_FACEBOOK_SHARED_RED_CATALOG_PUNCH' => 'Offer Shared on FB',
    'TRX_SUBTYPE_TWITTER_SHARED_RED_CATALOG_PUNCH' => 'Offer Shared on TW',
    'TRX_SUBTYPE_TARGETED_RED_CATALOG_PUNCH' => 'Offer Created - Targetted',
    'TRX_SUBTYPE_TRANSFER_POINTS_SENDER' => 'Points Transfer - Sender',
    'TRX_SUBTYPE_TRANSFER_POINTS_RECEIVER' => 'Points Transfer - Receiver',
    'TRX_SUBTYPE_TRANSFER_PUNCHES_SENDER' => 'Punch Transfer - Sender',
    'TRX_SUBTYPE_TRANSFER_PUNCHES_RECEIVER' => 'Punch Transfer - Receiver',
    'TRX_SUBTYPE_TROK_ACCEPTED' => 'Trok Accepted',
    'TRX_SUBTYPE_BUNDLE_ACCEPTED' => 'Bundle Accepted',
    'TRX_SUBTYPE_DELETED_TROK' => 'Trok Deleted',
    'TRX_SUBTYPE_TROK_CREATED' => 'Trok Created',
    'TRX_SUBTYPE_TROK_UPDATED' => 'Trok Updated',
    'TRX_SUBTYPE_FOLLOW_BUSINESS' => 'Follow-My-Store Bonus',
    'TRX_SUBTYPE_ADJUST_BALANCE_POINTS_INCREASE' => 'Adjust Increase Points',
    'TRX_SUBTYPE_ADJUST_BALANCE_PUNCH_INCREASE' => 'Adjust Increase Punches',
    'TRX_SUBTYPE_ADJUST_BALANCE_POINTS_DECREASE' => 'Adjust Decrease Points',
    'TRX_SUBTYPE_ADJUST_BALANCE_PUNCH_DECREASE' => 'Adjust Decrease Punches',
    'TRX_SUBTYPE_GIFTCARD_AMOUNT_PURCHASED' => 'Giftcard Purchased - Amount',
    'TRX_SUBTYPE_GIFTCARD_AMOUNT_PURCHASED_MERCHANT' => 'Giftcard Purchase - At Store',
    'TRX_SUBTYPE_TRANSFER_AMOUNT_SENDER' => 'Amount Transfer - Sender',
    'TRX_SUBTYPE_TRANSFER_AMOUNT_RECEIVER' => 'Amount Transfer - Receiver',
    'TRX_SUBTYPE_GIFTCARD_AMOUNT_PAYMENT' => 'Giftcard Payment',
    'TRX_SUBTYPE_COUPON_GET_COUPON' => 'Coupon Tagged',
    'TRX_SUBTYPE_COUPON_CONVERTIBLE_CREATED' => 'Convertible Coupon created',
    'TRX_SUBTYPE_COUPON_CONVERTIBLE_CLAIMED' => 'Convertible Coupon redeemed',
    'TRX_SUBTYPE_GIFTCARD_AMOUNT_STORE_CREDIT' => 'Cash-back',
    'TRX_SUBTYPE_GIFTCARD_AMOUNT_REFUND_STORE_CREDIT' => 'Refund Cash-back',
    'TRX_SUBTYPE_REFERRAL_REFEREE_REWARD_POINTS' => 'Referred rewarded',
    'TRX_SUBTYPE_REFERRAL_REFERER_REWARD_POINTS' => 'Referrer rewarded',
    'TRX_SUBTYPE_REFERRAL_REFEREE_REWARD_PUNCH' => 'Referred rewarded',
    'TRX_SUBTYPE_REFERRAL_REFERER_REWARD_PUNCH' => 'Referrer rewarded',
    'TRX_SUBTYPE_OFFER_REFUND_POINTS' => 'Offer Refunded (Irregular)',
    'TRX_SUBTYPE_TRANSFER_AMOUNT_SENDER_RECALL' => 'Transfer Recalled',
    'TRX_SUBTYPE_GIFTCARD_AMOUNT_PAYMENT_REFUND' => 'Gift Card Refund',
    'TRX_SUBTYPE_REWARD_REDEEM_BUNDLE' => 'Reward Redeem Bundle',
    'TRX_SUBTYPE_LEAVE_MEMBERSHIP' => 'Leave Membership',
    'TRX_SUBTYPE_COMPLETE_PROFILE' => 'Complete Profile',

    //** START - Notifications - THE VAR NAME CAN NOT CHANGE (THESE ARE USED IN SOME TABLES)
    'NOTIF_USER_TAGGED_OFFER' => 'Tagged an Offer from :{business_name} ',
    'NOTIF_FRIEND_REQUEST' => 'sent you a friend request.',
    'NOTIF_FRIEND_ACCEPTED' => ':{username} accepted your friend request.',
    'NOTIF_REFER_BUSINESS' => 'has referred :{business_name}  to you.',
    'NOTIF_IS_FOLLOWING' => 'for referring :{business_name} to :{username}.',
    'NOTIF_TITLE_YOU_EARNED' => 'You earned :{units_exchange} Points',
    'NOTIF_TITLE_TROK_ACCEPTED_POINT' => 'Exchange Accepted!',
    'NOTIF_TEXT_TROK_ACCEPTED_POINT' => 'You just got :{units_exchange} Pt from :{business_one} in exchange for your  :{units_exchange} Pt from  :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_POINTS' => 'You just got :{units_exchange} pts from :{business_one} in exchange for your  :{units_exchange} pts from  :{business_two}',
    'NOTIF_TITLE_YOU_EARNED_O_POINTS' => 'Thank you',
    'NOTIF_TITLE_YOU_EARNED_POINT' => 'You earned :{units_exchange} point',
    'NOTIF_OFFER_CREATED' => ':{offer_title} from :{business_name}',
    'NOTIF_PUNCH_CREATED' => ':{offer_title} from :{business_name}',
    'NOTIF_TITLE_NEW_OFFER' => 'New Offer', //changed
    'NOTIF_TITLE_NEW_PUNCH' => 'New Reward', //changed
    'NOTIF_TITLE_FRIEND_ACCEPTED' => 'Friend Request Accepted',
    'NOTIF_TITLE_STORE_REFERAL_ACCEPTED' => 'Store Referral Accepted',
    'NOTIF_TEXT_THANKS_FOR_REFERRING' => 'Thank you for referring :{business_name} to :{username}',
    'NOTIF_TEXT_YOU_EARNED_X_PT' => 'You earned :{units_exchange} point for referring :{business_name} to :{username}',
    'NOTIF_TEXT_YOU_EARNED_X_PTS' => 'You earned :{units_exchange} points for referring :{business_name} to :{username}',
    'NOTIF_TITLE_OFFER_TAGGED' => 'Offer Tagged',
    'NOTIF_USER_TAGGED_OFFER' => ':{username} Tagged an Offer from :{business_name}',
    'NOTIF_TEXT_TROK_ACCEPTED_POINT_YOU' => 'You just accepted :{username_created}\'s Exchange and got :{units_exchange} Pt from :{business_one} in exchange for your  :{units_exchange} Pt from  :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_POINTS_YOU' => 'You just accepted :{username_created}\'s Exchange and got :{units_exchange} pts from :{business_one} in exchange for your  :{units_exchange} pts from  :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_POINT_USER' => ':{username_accepted} just accepted your Exchange; you got :{units_exchange} Pt from :{business_one} in exchange for your :{units_exchange} Pt from :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_POINTS_USER' => ':{username_accepted} just accepted your Exchange; you got :{units_exchange} pts from :{business_one} in exchange for your :{units_exchange} pts from :{business_two}',
    'NOTIF_TITLE_PUNCH_SHARED' => 'Reward shared successfully',
    'NOTIF_PUNCH_SHARED_FACEBOOK' => 'You earned :{units_exchange} Punches for sharing :{offer_title} on Facebook',
    'NOTIF_PUNCH_SHARED_TWITTER' => 'You earned :{units_exchange} Punches for sharing :{offer_title} on Twitter',
    'NOTIF_PUNCH_SHARED_TWITTER1' => 'You earned :{units_exchange} Punch for sharing :{offer_title} on Twitter',
    'NOTIF_PUNCH_SHARED_FACEBOOK1' => 'You earned :{units_exchange} Punch for sharing :{offer_title} on Facebook',
    'NOTIF_TITLE_WELCOME_POINT_PUNCH' => 'Welcome to :{business_name}\'s loyalty program',
    'NOTIF_TEXT_WELCOME_PUNCH_SINGULAR' => 'As a thank you for your loyalty, :{business_name} is offering you :{units_exchange} Punch',
    'NOTIF_TEXT_WELCOME_PUNCH_PLURAL' => 'As a thank you for your loyalty, :{business_name} is offering you :{units_exchange} Punches',
    'NOTIF_TEXT_WELCOME_POINT_SINGULAR' => 'As a thank you for your loyalty, :{business_name} is offering you :{units_exchange} :{points_label}',
    'NOTIF_TEXT_WELCOME_POINT_PLURAL' => 'As a thank you for your loyalty, :{business_name} is offering you :{units_exchange} :{points_label}',
    'NOTIF_TITLE_OFFER_SHARED' => 'Offer shared successfully',
    'NOTIF_OFFER_SHARED_FACEBOOK_PLURAL' => 'You earned :{units_exchange} Points for sharing :{offer_title} on Facebook',
    'NOTIF_OFFER_SHARED_TWITTER_PLURAL' => 'You earned :{units_exchange} Points for sharing :{offer_title} on Twitter',
    'NOTIF_OFFER_SHARED_FACEBOOK_SINGULAR' => 'You earned :{units_exchange} Point for sharing :{offer_title} on Facebook',
    'NOTIF_OFFER_SHARED_TWITTER_SINGULAR' => 'You earned :{units_exchange} Point for sharing :{offer_title} on Twitter',
    'NOTIF_TEXT_TROK_ACCEPTED_PUNCH_YOU' => 'You just accepted :{username_created}\'s Exchange and got :{units_exchange} Punch from :{business_one} in exchange for your  :{units_exchange} Punch from  :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_PUNCHES_YOU' => 'You just accepted :{username_created}\'s Exchange and got :{units_exchange} Punches from :{business_one} in exchange for your  :{units_exchange} Punches from  :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_PUNCH_USER' => ':{username_accepted} just accepted your Exchange; you got :{units_exchange} Punch from :{business_one} in exchange for your :{units_exchange} Punch from :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_PUNCHES_USER' => ':{username_accepted} just accepted your Exchange; you got :{units_exchange} Punches from :{business_one} in exchange for your :{units_exchange} Punches from :{business_two}',
    'NOTIF_TEXT_YOU_EARNED_X_PUNCH' => 'You earned :{units_exchange} punch for referring :{business_name} to :{username}',
    'NOTIF_TEXT_YOU_EARNED_X_PUNCHES' => 'You earned :{units_exchange} punches for referring :{business_name} to :{username}',
    'NOTIF_PROMOTION_SHARED_FACEBOOK_PLURAL' => 'You earned :{units_exchange} Punches for sharing :{offer_title} on Facebook',
    'NOTIF_PROMOTION_SHARED_TWITTER_PLURAL' => 'You earned :{units_exchange} Punches for sharing :{offer_title} on Twitter',
    'NOTIF_PROMOTION_SHARED_FACEBOOK_SINGULAR' => 'You earned :{units_exchange} Punch for sharing :{offer_title} on Facebook',
    'NOTIF_PROMOTION_SHARED_TWITTER_SINGULAR' => 'You earned :{units_exchange} Punch for sharing :{offer_title} on Twitter',
    'FRONTEND_NOTIF_POINTREWARD_CREATED' => ':{offer_title} from :{business_name}',
    'FRONTEND_NOTIF_POINTREWARD_SHARED_FB_PLURAL' => 'You earned :{units_exchange} :{points_label} for sharing :{offer_title} on Facebook',
    'FRONTEND_NOTIF_POINTREWARD_SHARED_TW_PLURAL' => 'You earned :{units_exchange} :{points_label} for sharing :{offer_title} on Twitter',
    'FRONTEND_NOTIF_POINTREWARD_SHARED_FB_SINGULAR' => 'You earned :{units_exchange} :{points_label} for sharing :{offer_title} on Facebook',
    'FRONTEND_NOTIF_POINTREWARD_SHARED_TW_SINGULAR' => 'You earned :{units_exchange} :{points_label} for sharing :{offer_title} on Twitter',
    'FRONTEND_NOTIF_PUNCHREWARD_SHARED_FB_PLURAL' => 'You earned :{units_exchange} Punches for sharing :{offer_title} on Facebook',
    'FRONTEND_NOTIF_PUNCHREWARD_SHARED_TW_PLURAL' => 'You earned :{units_exchange} Punches for sharing :{offer_title} on Twitter',
    'FRONTEND_NOTIF_PUNCHREWARD_SHARED_FB_SINGULAR' => 'You earned :{units_exchange} Punch for sharing :{offer_title} on Facebook',
    'FRONTEND_NOTIF_PUNCHREWARD_SHARED_TW_SINGULAR' => 'You earned :{units_exchange} Punch for sharing :{offer_title} on Twitter',

    'FRONTEND_USER_PIN' => 'Your PIN is :{pin_code}',
    'FRONTEND_USER_PIN_BROADNET' => 'Use :{pin_code} for login',

    //V325
    'NOTIF_REQ_NEW_OFFER_TITLE' => 'New Offer',
    'NOTIF_REQ_NEW_POINT_REDEMPTION_TITLE' => 'New Product to Redeem',
    'NOTIF_REQ_NEW_PUNCH_REDEMPTION_TITLE' => 'New Product to Redeem',
    'NOTIF_REQ_NEW_MKTCAMPAIGN_OFFER_TITLE' => 'A special Offer only for you',
    'NOTIF_REQ_NEW_MKTCAMPAIGN_POINT_REDEMPTION_TITLE' => 'New point product to redeem, exclusive for you',
    'NOTIF_REQ_NEW_MKTCAMPAIGN_PUNCH_REDEMPTION_TITLE' => 'New punch product to redeem, exclusive for you',
    'NOTIF_TEXT_OFFER_CREATED' => ':{offer_title} from :{business_name}',
    'NOTIF_TEXT_POINT_REDEMPTION_CREATED' => ':{offer_title} from :{business_name}',
    'NOTIF_TEXT_PUNCH_REDEMPTION_CREATED' => ':{offer_title} from :{business_name}',
    'NOTIF_TEXT_MKTCAMPAIGN_GENERIC' => ':{offer_title} from :{business_name}',
    'FRONTEND_NOTIF_REWARD_SHARED_FB_ZERO' => 'Thank you for sharing :{offer_title} on Facebook',
    'FRONTEND_NOTIF_REWARD_SHARED_TW_ZERO' => 'Thank you for sharing :{offer_title} on Twitter',
    'NOTIF_REQ_NEW_GIFTCARD_TITLE' => 'New Offer', //A new Gift Card was just created - TITLE
    'NOTIF_TEXT_NEW_GIFTCARD' => ':{offer_title} from :{business_name}', //A new Gift Card was just created - CONTENT

    'NOTIF_REQ_GIFTCARD_PURCHASE_TITLE' => 'Gift Card Purchase was successfully completed', //A Gift Card was just purchased by consumer - TITLE
    'NOTIF_TEXT_GIFTCARD_PURCHASE' => 'You bought :{currency}:{units_exchange} gift card from :{business_name}', //A new Gift Card was just purchased by consumer - CONTENT

    'NOTIF_REQ_POINT_GIFTCARD_SHARED_FACEBOOK_TITLE' => 'Gift Card Shared in Facebook', //A Gift Card was just shared - TITLE
    'FRONTEND_NOTIF_POINT_GIFTCARD_SHARED_FB_SINGULAR' => 'You earned 1 Point for sharing :{offer_title} on Facebook', //A Gift Card was just shared - CONTENT
    'FRONTEND_NOTIF_POINT_GIFTCARD_SHARED_FB_PLURAL' => 'You earned :{units_exchange} :{points_label} for sharing :{offer_title} on Facebook', //A Gift Card was just shared - CONTENT
    'FRONTEND_NOTIF_POINT_GIFTCARD_SHARED_FB_ZERO' => 'Thank you for sharing :{offer_title} on Facebook',

    'NOTIF_REQ_POINT_GIFTCARD_SHARED_TWITTER_TITLE' => 'Gift Card Shared in Twitter', //A Gift Card was just shared - TITLE
    'FRONTEND_NOTIF_POINT_GIFTCARD_SHARED_TW_SINGULAR' => 'You earned 1 Point for sharing :{offer_title} on Twitter', //A Gift Card was just shared - CONTENT
    'FRONTEND_NOTIF_POINT_GIFTCARD_SHARED_TW_PLURAL' => 'You earned :{units_exchange} :{points_label} for sharing :{offer_title} on Twitter', //A Gift Card was just shared - CONTENT
    'FRONTEND_NOTIF_POINT_GIFTCARD_SHARED_TW_ZERO' => 'Thank you for sharing :{offer_title} on Twitter',

    'NOTIF_REQ_TRANSFER_POINTS_RECEIVER_TITLE' => 'You received a Gift Card', //Receiver notif: received points that were transferred to him - TITLE
    'FRONTEND_NOTIF_TRANSFER_POINTS_RECEIVER' => ':{sender_username} gifted you :{currency}:{units_exchange} from :{business_name}', //Receiver notif: received points that were transferred to him - CONTENT
    'NOTIF_REQ_GIFTCARD_TRANSFER_POINTS_RECEIVER_TITLE' => 'You received a gift card',
    'FRONTEND_NOTIF_GIFTCARD_TRANSFER_POINTS_RECEIVER' => ':{sender_username} gifted :{currency}:{amount} from :{business_name}',
    'ACTIVITY_TRANSFER_POINTS_BOTH' => 'Gifted points from :{business} to :{receiver_username}',

    'NOTIF_REQ_POINT_PURCHASE_REWARD_TITLE' => 'Earned :{points_label}',
    'FRONTEND_NOTIF_POINT_PURCHASE_REWARD' => 'You Purchased and earned :{units_exchange} :{points_label} at :{business_name}',
    'NOTIF_REQ_PUNCH_PURCHASE_REWARD_TITLE' => 'Earned Punches',
    'FRONTEND_NOTIF_PUNCH_PURCHASE_REWARD' => 'You Purchased and earned :{units_exchange} Punches :{business_name}',
    'NOTIF_REQ_POINT_PURCHASE_REDEMPTION_TITLE' => 'You Redeemed :{points_label}',
    'FRONTEND_NOTIF_POINT_PURCHASE_REDEMPTION' => 'You have Redeemed :{units_exchange} :{points_label} at :{business_name}',
    'NOTIF_REQ_PUNCH_PURCHASE_REDEMPTION_TITLE' => 'You Redeemed Punches',
    'FRONTEND_NOTIF_PUNCH_PURCHASE_REDEMPTION' => 'You have Redeemed :{units_exchange} Punches at :{business_name}',
    'NOTIF_TEXT_NEW_PRODUCT' => ':{offer_title} from :{business_name}',
    'NOTIF_REQ_NEW_PRODUCT_TITLE' => 'New Product',
    'NOTIF_REQ_ACCEPT_GIFTCARD_TITLE' => 'Gift Card Accepted!',

    'NOTIF_REQ_REFERRAL_REFEREE_REWARD' => 'Referral Bonus',
    'NOTIF_REQ_REFERRAL_REFERER_REWARD' => 'Referral Bonus',
    'NOTIF_TEXT_REFERRER_EARNED' => ':{business_name} offered you :{units_exchange} for referring :{sender}',
    'NOTIF_TEXT_REFERREE_EARNED' => ':{business_name} offered you :{units_exchange} to thank you for your visit',
    'NOTIF_TEXT_REFERER_EARNED_OFFER' => ':{business_name} offered you :{offer_title} for referring :{sender}',
    'NOTIF_TEXT_REFEREE_EARNED_OFFER' => ':{business_name} offered you :{offer_title} to thank you for your visit',
    'NOTIF_TEXT_COUPON_CLAIM' => 'You have redeemed :{offer} from :{business_name} :{date}',
    'NOTIF_REQ_COUPON_CLAIM' => 'Coupon Redeemed',
    'NOTIF_REQ_GIFTCARD_AMOUNT_PURCHASE_TITLE' => 'Gift Card Purchase was successfully completed',
    'NOTIF_REQ_GIFTCARD_AMOUNT_TRANSFER_RECEIVER_TITLE' => 'You received a gift card',
    'FRONTEND_PAYPAL_PAYMENT_SUCCESS4_AMOUNT' => 'Your new :{business_name} balance is now :{currency}:{amount}',
    'FRONTEND_NOTIF_GIFTCARD_PAYMENT' => 'You have Paid :{currency}:{amount} at :{business_name}',
    'NOTIF_REQ_GIFTCARD_AMOUNT_PAYMENT_TITLE' => 'You Paid with Gift Card',
    'NOTIF_REQ_WELCOME_POINTS_TITLE' => 'Signup Bonus',
    'NOTIF_REQ_WELCOME_PUNCH_TITLE' => 'Signup Bonus',

    'NOTIF_REQ_CAMPAIGN_STATS_COMPLETED' => 'CAMPAIGN_STATS_COMPLETED',
    'NOTIF_TEXT_CAMPAIGN_STATS_COMPLETED' => 'Campaign complete',
    'NOTIF_REQ_TRENDING_OFFER' => 'Your offer is trending now!',
    'NOTIF_REQ_TRENDING_REWARD' => 'Your reward is trending now!',
    'NOTIF_REQ_COMPLETE_PROFILE' => 'Complete Profile',

    //** END - Notifications - THE VAR NAME CAN NOT CHANGE (THESE ARE USED IN SOME TABLES)

    'FRONTEND_SEND_GIFT_CARD_TO_FRIEND' => 'You sent a gift card of value :{value} to :{receiver}. Recall?',
    'FRONTEND_RECEIVE_GIFT_CARD_FROM_FRIEND' => 'You received a gift card of value :{value} from :{sender}. Accept?',
    'FRONTEND_GIFT_CARD_CLAIM_BACK_SUCCESS' => 'Gift Card Recalled!',
    'FRONTEND_GIFT_CARD_CLAIM_GIFT_SUCCESS' => 'Gift Card Accepted!',
    'FRONTEND_GIFTCARD_MESSAGE_ACCEPT_BTN' => 'Accept',
    'FRONTEND_GIFTCARD_MESSAGE_RECALL_BTN' => 'Recall',
    'FRONTEND_GIFTCARD_SENDER_EMPTY' => 'Please enter sender name',
    'FRONTEND_GIFTCARD_RECIPIENT_EMPTY' => 'Please enter recipient name',
    'FRONTEND_GIFTCARD_RECIPIENT_ACCEPT_GIFT_SENDER_MESSAGE' => ':{receiver_username} has accepted your :{businessName} Gift Card of :{amount}:{currency}',

    'FRONTEND_TRANSFER_NOT_ENOUGH_BALANCE' => 'You do not have enough :{pts_punches} from :{business_name}',
    'FRONTEND_TRANSFER_PTS_SMS' => 'A friend has gifted you a :{currency}:{amount} value of :{business_name} Points; Tap here to claim :{link}',
    'FRONTEND_TRANSFER_POINTS_SMS' => 'A friend transferred you :{points} points from :{business_name}; Tap here to claim :{link}',
    'FRONTEND_TRANSFER_PTS_SMS_NEW_USER' => 'A friend has gifted you a :{currency}:{amount} value of :{business_name} Points; Tap here to claim :{link}',
    'FRONTEND_TRANSFER_PTS_EMAIL_SUBJECT'=>  ':{sender} offered you a Gift Card of :{currency}:{amount} from «:{business_name}»',
    'FRONTEND_TRANSFER_POINTS_EMAIL_SUBJECT'=>  ':{sender} transferred you :{points} points from «:{business_name}»',
    'FRONTEND_TRANSFER_NOT_ALLOW_TO_YOURSELF' => 'Sounds thoughtful, but you cannot gift yourself',
    'NOTIF_REQ_POINT_OFFER_SHARED_FACEBOOK' => 'Offer shared on Facebook',
    'NOTIF_REQ_BASIC_OFFER_SHARED_FACEBOOK' => 'Offer shared on Facebook',
    'NOTIF_REQ_POINT_REWARD_SHARED_FACEBOOK' => 'Reward shared on Facebook',
    'PT'=>'pt',
    'PTS' => 'pts',
    'POINT' => 'Point',
    'POINTS' => 'Points',
    'PUNCH'=>'punch',
    'PUNCHES' => 'punches',

    'BACKEND_GENERIC_DAYS' => 'days',
    'BACKEND_GENERIC_DAY' => 'day',
    'BACKEND_REFERRAL_REFEREE_SMS_EARN_EXPIRE' => 'I was just at :{business_name}, go check it out! You will earn :{points}. Valid for :{valid} :{referrer}',
    'BACKEND_REFERRAL_REFEREE_SMS_EARN' => 'I was just at :{business_name}, go check it out! You will earn :{points}; :{referrer}',
    'BACKEND_REFERRAL_REFEREE_SMS_ZERO' => 'I was just at :{business_name}, go check it out, you will love it; :{referrer}',
    'FRONTEND_USER_REFERER_EARN_SUBJECT' => 'Earned :{pts} from a referred friend - :{business_name}',
    'FRONTEND_USER_REFERER_EARN_SMS' => 'Your Balance @:{business_name} is: :{balance}. You earned :{pts} from a referred friend.',

    'FRONTEND_GENERIC_MAIL_DOWNLOADKNGAPP'=> 'Download the Kangaroo Rewards App on your Smartphone, it\'s FREE!',
    'FRONTEND_GENERIC_MAIL_SIGNATURE'=> 'Thanks,<br>The Kangaroo Team!',
    'FRONTEND_GENERIC_MAIL_ASK_FOR_SUPPORT_1'=> 'If you\'re having problems, please contact ',
    'FRONTEND_GENERIC_MAIL_ASK_FOR_SUPPORT_2'=> ' for further assistance',
    'FRONTEND_THANKS'=>'Thanks',



    'TRX_SUBTYPE_INITIAL_POINTS_IMPORT' => 'Initial points import',
    'TRX_SUBTYPE_INITIAL_PUNCHES_IMPORT' => 'Initial punches import',
    'TRX_SUBTYPE_CONVERTIBLE_PRODUCT_REDEMPTION_POINTS' => 'Redemption converted to Gift Card',
    'TRX_SUBTYPE_CONVERTIBLE_GIFTCARD_AMOUNT_PURCHASED' => 'Converted to Gift Card Amount',
    'TRX_SUBTYPE_SPIN_DRAW_FREE_POINTS' => 'Spin Draw Free :{points_label}',
    'TRX_SUBTYPE_SPIN_DRAW_FREE_PRODUCT' => 'Spin Draw Free Product',
    'TRX_SUBTYPE_SPIN_DRAW_PORCENTAGE_OFF' => 'Spin Draw Percentage Off',
    'TRX_SUBTYPE_SPIN_DRAW_OFFER' => 'Spin Draw Offer',
    'TRX_SUBTYPE_BONUS_POINTS_ASSIGNED' => 'Bonus :{points_label} assigned',
    'TRX_SUBTYPE_SURVEY_COMPLETED' => 'Survey Completed',

    // refund the redeemed points
    'NOTIF_REQ_REFUND_REDEEMED_POINTS_TITLE' => 'Refund :{points_label}',
    'FRONTEND_ACTIVITY_REDEEM_POINT_REFUND' => 'Refund :{units_exchange} :{points_label} from expired coupon',

    //refund
    'NOTIF_REQ_POINT_PURCHASE_REFUND_TITLE' => 'Refund :{points_label}',
    'FRONTEND_ACTIVITY_POINT_REFUND' => 'Refund :{units_exchange} :{points_label}',

    'FRONTEND_NOTIF_TITLE_YOU_EARNED' => 'You earned :{units_exchange} :{points_label}',
    'FRONTEND_ACTIVITY_POINT_REDEEM' => 'You redeemed :{units_exchange} :{points_label}',
    'NOTIF_REQ_PRODUCT_REVIEW_TITLE' => 'Product review bonus',
    'FRONTEND_NOTIF_PRODUCT_REVIEW_REWARD_POINTS' => 'You earned :{units_exchange} :{points_label} for review a product on the website.',

    'FRONTEND_BUS_CAT_RESTAURANT' => 'Cafes & Restaurants',
    'FRONTEND_BUS_CAT_SERVICES' => 'Services',
    'FRONTEND_BUS_CAT_BAR' => 'Bars',
    'FRONTEND_BUS_CAT_CONVEN_STORE' => 'Groceries',
    'FRONTEND_BUS_CAT_DEPT_STORE' => 'Boutiques',
    'FRONTEND_BUS_CAT_LIFESTYLE' => 'Beauty & Lifestyle',
    'FRONTEND_BUS_CAT_ELECTRO' => 'Electronics',
    'FRONTEND_BUS_CAT_ENTERT' => 'Entertainment',
    'FRONTEND_BUS_CAT_OTHER' => 'Other',
    'FRONTEND_BUS_CAT_FINANCE' => 'Finance',
    'FRONTEND_BUS_CAT_E_WALLET' => 'E-Wallet',
    'FRONTEND_BUS_CAT_E_COM' => 'E-Com',
    'FRONTEND_BUS_CAT_TRANSPORT' => 'Transport',
    'FRONTEND_BUS_CAT_TRAVEL' => 'Travel',
    'FRONTEND_BUS_CAT_SHOPPING' => 'Shopping',
    'FRONTEND_BUS_CAT_CHARITY' => 'Charity',

    'FRONTEND_BUS_CAT_PRACTITIONER' => 'General Practitioner',
    'FRONTEND_BUS_CAT_HEALTH' => 'Health & Wellness',
    'FRONTEND_BUS_CAT_HOSPITALS' => 'Hospitals, Medical Centers, Medical Labs',
    'FRONTEND_BUS_CAT_MEDICAL' => 'Medical Equipment',
    'FRONTEND_BUS_CAT_OBSTETRICIAN' => 'Obstetrician & Gynecologist',
    'FRONTEND_BUS_CAT_OPTICAL' => 'Optical',
    'FRONTEND_BUS_CAT_PHARMA' => 'Pharmacies',
    'FRONTEND_BUS_CAT_PHYSIOTHERAPIST' => 'Physiotherapist',
    'FRONTEND_BUS_CAT_SURGEON' => 'Surgeon & Specialists',
    'FRONTEND_BUS_CAT_AUTOMOTIVE' => 'Automotive, Equipment & Transportation',
    'FRONTEND_BUS_CAT_BROKERS' => 'Custom Brokers',
    'FRONTEND_BUS_CAT_EDUCATIONAL' => 'Educational',
    'FRONTEND_BUS_CAT_FASHION' => 'Fashion & Retail',
    'FRONTEND_BUS_CAT_FINANCIAL' => 'Financial Services',
    'FRONTEND_BUS_CAT_MARKETING' => 'Marketing & Advertising',
    'FRONTEND_BUS_CAT_CORPORATE' => 'Corporate Services',

    'FRONTEND_BUS_CAT_E_COM_APPAREL_ACCESS' => 'E-Com - Apparel & Accessories',
    'FRONTEND_BUS_CAT_E_COM_AUTOMOTIVE' => 'E-Com - Automotive',
    'FRONTEND_BUS_CAT_E_COM_ELECTRONICS' => 'E-Com - Electronics',
    'FRONTEND_BUS_CAT_E_COM_FOOD_BEVERAGE' => 'E-Com - Food & Beverages',
    'FRONTEND_BUS_CAT_E_COM_JEWELRY' => 'E-Com - Jewelry',
    'FRONTEND_BUS_CAT_E_COM_HOME_GARDEN' => 'E-Com - Home & Garden',
    'FRONTEND_BUS_CAT_E_COM_HARDWARE_HOME_IMPROVE' => 'E-Com - Hardware & Home Improvement',
    'FRONTEND_BUS_CAT_E_COM_HEALTH_BEAUTY' => 'E-Com - Health & Beauty',
    'FRONTEND_BUS_CAT_E_COM_MASS_MARKET_RETAILER' => 'E-Com - Mass Market Retailer',
    'FRONTEND_BUS_CAT_E_COM_OFFICE_SUPPLIES' => 'E-Com - Office Supplies',
    'FRONTEND_BUS_CAT_E_COM_SPECIALTY' => 'E-Com - Specialty',
    'FRONTEND_BUS_CAT_E_COM_SPORTING_GOODS' => 'E-Com - Sporting Goods',
    'FRONTEND_BUS_CAT_E_COM_TOYS_HOBBIES' => 'E-Com - Toys & Hobbies',

    'NOTIF_REQ_CHECKIN' => 'Check-in',
    'NOTIF_REQ_CHECKIN_POINTS_TITLE' => 'Check-in',
    'NOTIF_TEXT_CHECK_IN' => 'You checked-in at :{business_name} and earned :{units_exchange} :{points_label}',
    'FRONTEND_NOTIF_POINT_PURCHASE_REFUND' => 'Your :{business_name} balance has been updated by -:{units_exchange} :{points_label}',

    'BATCH_EXPORT_FIRST_NAME' => 'First',
    'BATCH_EXPORT_LAST_NAME' => 'Last',
    'BATCH_EXPORT_TYPE' => 'Type',
    'BATCH_EXPORT_AMOUNT' => 'Amount',
    'BATCH_EXPORT_POINTS' => 'Points',
    'BATCH_EXPORT_PHONE' => 'Phone',
    'BATCH_EXPORT_EMAIL' => 'Email',
    'BATCH_EXPORT_TAGS' => 'Tags',
    'BATCH_EXPORT_DOB' => 'Date of Birth',
    'BATCH_EXPORT_SMS_CONSENT' => 'SMS Consent',
    'BATCH_EXPORT_EMAIL_CONSENT' => 'Email Consent',
    'BATCH_EXPORT_PUSH_CONSENT' => 'Push Consent',
    'BATCH_EXPORT_STATUS' => 'Status',
    'BATCH_EXPORT_TRAN_TIME' => 'Trans. Time',
    'BATCH_EXPORT_CREATED_TIME' => 'Created',
    'BATCH_EXPORT_CARD_NUMBER' => 'Card Number',
    'BATCH_EXPORT_LANGUAGE' => 'Language',
    'BATCH_EXPORT_ERRORS' => 'Errors',
    "BATCH_EXPORT_CUSTOM_FIELD_1" => 'Custom Field 1',
    "BATCH_EXPORT_CUSTOM_FIELD_2" => 'Custom Field 2',
    "BATCH_EXPORT_CUSTOM_FIELD_3" => 'Custom Field 3',
    "BATCH_EXPORT_CUSTOM_FIELD_4" => 'Custom Field 4',
    "BATCH_EXPORT_CUSTOM_FIELD_5" => 'Custom Field 5',


    'DOWNLOAD_FILE_IS_READY' => 'Your CSV is available',
    'TRX_SUBTYPE_REFUND_REDEEMED_POINTS_EXCLUDE_TIERS' => 'Refund - Redeemed Points',
    'TRX_SUBTYPE_REFUND' => 'Refund',
    'TRX_SUBTYPE_REWARD_REDEEM' => 'Reward/Redeem',
    'TRX_SUBTYPE_PRODUCT_REVIEW_REWARD_POINTS' => 'Product review bonus',
    'TRX_SUBTYPE_REDEMPTION_PTS_REFUND' => 'Refund from expired coupon or unused redemption',

    'EXPORT_ID' => 'ID',
    'EXPORT_GENERIC_DATE_TIME' => 'Date/Time',
    'EXPORT_GENERIC_TRANSACTION' => 'Transaction',
    'EXPORT_TRX_LIST_HEADER_REWARDED_PUNCHES' => 'Rewarded points',
    'EXPORT_TRX_LIST_HEADER_REDEEMED_PUNCHES' => 'Redeemed points',
    'EXPORT_PUNCHES' => 'punches',
    'EXPORT_GENERIC_USERNAME' => 'Username',
    'EXPORT_TRX_LIST_HEADER_USER_BALANCE' => 'User balance',
    'EXPORT_GENERIC_BUSINESS_NAME' => 'Business Name',
    'EXPORT_GENERIC_CLERK_NAME' => 'Clerk Name',
    'EXPORT_A_LA_CARTE' => 'A La Carte',
    'EXPORT_TRX_LIST_HEADER_REWARDED_POINTS' => 'Rewarded points',
    'EXPORT_TRX_LIST_HEADER_REDEEMED_POINTS' => 'Redeemed points',
    'EXPORT_GENERIC_RECEIPT_NUMBER' => 'Receipt Number',
    'EXPORT_GENERIC_AMOUNT' => 'Amount',
    'EXPORT_TRX_LIST_HEADER_GIFTCARD_BALANCE' => 'Giftcard / Cashback',

    'ACTIVITY_CONVERTIBLE_GIFTCARD_AMOUNT' => 'Converted to gift card from {business}',
    'NOTIF_REQ_CONVERTIBLE_GIFT_CARD_POINTS_TITLE' => 'You Redeemed :{points_label} to Gift Card',
    'FRONTEND_NOTIF_CONVERTIBLE_GIFT_CARD_POINTS' => 'You have Redeemed :{units_exchange} :{points_label} at :{business_name}',
    'NOTIF_REQ_CONVERTIBLE_GIFT_CARD_AMOUNT_TITLE' => 'You got Gift Card',
    'FRONTEND_NOTIF_CONVERTIBLE_GIFT_CARD_AMOUNT' => 'You have got :{currency}:{amount} at :{business_name}',
    'FRONTEND_MAIL_SUBJECT_WELCOME_TO_MERCHANT'=> 'Welcome to Kangaroo Rewards',
    'AUDIT_ACTIVITY' => 'Activity',
    'IP_ADDRESS' => 'IP Address',
    'AUDIT_EMPLOYEE' => 'Employee',

    'AUDIT_EVENT_RULES_VIEWED' => 'Rules viewed',
    'AUDIT_EVENT_RULES_CREATED' => '',
    'AUDIT_EVENT_RULES_UPDATED' => 'Rules updated',
    'AUDIT_EVENT_RULES_DELETED' => '',
    'AUDIT_EVENT_CONSENT_VIEWED' => '',
    'AUDIT_EVENT_CONSENT_CREATED' => '',
    'AUDIT_EVENT_CONSENT_UPDATED' => 'Consent changed',
    'AUDIT_EVENT_CONSENT_DELETED' => '',
    'AUDIT_EVENT_OFFER_VIEWED' => 'Offer viewed',
    'AUDIT_EVENT_OFFER_CREATED' => 'Offer created',
    'AUDIT_EVENT_OFFER_UPDATED' => 'Offer updated',
    'AUDIT_EVENT_OFFER_DELETED' => 'Offer deleted',
    'AUDIT_EVENT_REDEMPTION_VIEWED' => 'Redemption viewed',
    'AUDIT_EVENT_REDEMPTION_CREATED' => 'Redemption created',
    'AUDIT_EVENT_REDEMPTION_UPDATED' => 'Redemption updated',
    'AUDIT_EVENT_REDEMPTION_DELETED' => 'Redemption deleted',
    'AUDIT_EVENT_GIFT_CARD_VIEWED' => 'Gift card viewed',
    'AUDIT_EVENT_GIFT_CARD_CREATED' => 'Gift card created',
    'AUDIT_EVENT_GIFT_CARD_UPDATED' => 'Gift card updated',
    'AUDIT_EVENT_GIFT_CARD_DELETED' => 'Gift card deleted',
    'AUDIT_EVENT_MARKETING_CAMPAIGN_VIEWED' => 'Marketing campaign viewed',
    'AUDIT_EVENT_MARKETING_CAMPAIGN_CREATED' => 'Marketing campaign created',
    'AUDIT_EVENT_MARKETING_CAMPAIGN_UPDATED' => 'Marketing campaign updated',
    'AUDIT_EVENT_MARKETING_CAMPAIGN_DELETED' => 'Marketing campaign canceled',
    'AUDIT_EVENT_DRAW_VIEWED' => 'Draw viewed',
    'AUDIT_EVENT_DRAW_CREATED' => 'Draw created',
    'AUDIT_EVENT_DRAW_UPDATED' => 'Draw updated',
    'AUDIT_EVENT_DRAW_DELETED' => 'Draw deleted',
    'AUDIT_EVENT_PRODUCT_VIEWED' => 'Product viewed',
    'AUDIT_EVENT_PRODUCT_CREATED' => 'Product created',
    'AUDIT_EVENT_PRODUCT_UPDATED' => 'Product updated',
    'AUDIT_EVENT_PRODUCT_DELETED' => 'Product deleted',
    'AUDIT_EVENT_PRODUCT_REWARD_VIEWED' => 'A la Carte viewed',
    'AUDIT_EVENT_PRODUCT_REWARD_CREATED' => 'A la Carte created',
    'AUDIT_EVENT_PRODUCT_REWARD_UPDATED' => 'A la Carte updated',
    'AUDIT_EVENT_PRODUCT_REWARD_DELETED' => 'A la Carte deleted',
    'AUDIT_EVENT_SLIDESHOW_VIEWED' => 'Slideshow viewed',
    'AUDIT_EVENT_SLIDESHOW_CREATED' => 'Slideshow created',
    'AUDIT_EVENT_SLIDESHOW_UPDATED' => 'Slideshow updated',
    'AUDIT_EVENT_SLIDESHOW_DELETED' => 'Slideshow deleted',
    'AUDIT_EVENT_REFERRAL_PROGRAM_VIEWED' => 'Referral program viewed',
    'AUDIT_EVENT_REFERRAL_PROGRAM_CREATED' => 'Referral program created',
    'AUDIT_EVENT_REFERRAL_PROGRAM_UPDATED' => 'Referral program updated',
    'AUDIT_EVENT_REFERRAL_PROGRAM_DELETED' => 'Referral program deleted',
    'AUDIT_EVENT_BRANCH_VIEWED' => 'Branch viewed',
    'AUDIT_EVENT_BRANCH_CREATED' => 'Branch created',
    'AUDIT_EVENT_BRANCH_UPDATED' => 'Branch updated',
    'AUDIT_EVENT_BRANCH_DELETED' => 'Branch deleted',
    'AUDIT_EVENT_CUSTOMER_VIEWED' => 'Customer profile viewed',
    'AUDIT_EVENT_CUSTOMER_CREATED' => 'Customer created',
    'AUDIT_EVENT_CUSTOMER_UPDATED' => 'Customer profile updated',
    'AUDIT_EVENT_CUSTOMER_DELETED' => 'Customer deleted',
    'AUDIT_EVENT_SURVEY_VIEWED' => 'Survey viewed',
    'AUDIT_EVENT_SURVEY_CREATED' => 'Survey created',
    'AUDIT_EVENT_SURVEY_UPDATED' => 'Survey updated',
    'AUDIT_EVENT_SURVEY_DELETED' => 'Survey deleted',
    'AUDIT_EVENT_SURVEY_SEND_VIEWED' => 'Survey send viewed',
    'AUDIT_EVENT_SURVEY_SEND_CREATED' => 'Survey send created',
    'AUDIT_EVENT_SURVEY_SEND_UPDATED' => 'Survey send updated',
    'AUDIT_EVENT_SURVEY_SEND_DELETED' => 'Survey send canceled',
    'AUDIT_EVENT_BANNER_VIEWED' => 'Banner viewed',
    'AUDIT_EVENT_BANNER_CREATED' => 'Banner created',
    'AUDIT_EVENT_BANNER_UPDATED' => 'Banner updated',
    'AUDIT_EVENT_BANNER_DELETED' => 'Banner deleted',
    'AUDIT_EVENT_INTEGRATION_VIEWED' => 'Integration viewed',
    'AUDIT_EVENT_INTEGRATION_CREATED' => 'Integration created',
    'AUDIT_EVENT_INTEGRATION_UPDATED' => 'Integration updated',
    'AUDIT_EVENT_INTEGRATION_DELETED' => '',
    'AUDIT_EVENT_SOCIAL_MEDIA_VIEWED' => 'Social media viewed',
    'AUDIT_EVENT_SOCIAL_MEDIA_CREATED' => 'Social media created',
    'AUDIT_EVENT_SOCIAL_MEDIA_UPDATED' => 'Social media updated',
    'AUDIT_EVENT_SOCIAL_MEDIA_DELETED' => '',
    'AUDIT_EVENT_IMPORT_TRANSACTION_VIEWED' => '',
    'AUDIT_EVENT_IMPORT_TRANSACTION_CREATED' => 'Import transaction created',
    'AUDIT_EVENT_IMPORT_TRANSACTION_UPDATED' => 'Import transaction updated',
    'AUDIT_EVENT_IMPORT_TRANSACTION_DELETED' => '',
    'AUDIT_EVENT_UPLOAD_CLIENTS_VIEWED' => 'Contacts list viewed',
    'AUDIT_EVENT_UPLOAD_CLIENTS_CREATED' => 'Contacts list uploaded',
    'AUDIT_EVENT_UPLOAD_CLIENTS_UPDATED' => '',
    'AUDIT_EVENT_UPLOAD_CLIENTS_DELETED' => '',
    'AUDIT_EVENT_BUSINESS_PROFILE_VIEWED' => 'App colors viewed',
    'AUDIT_EVENT_BUSINESS_PROFILE_CREATED' => 'App colors created',
    'AUDIT_EVENT_BUSINESS_PROFILE_UPDATED' => 'App colors updated',
    'AUDIT_EVENT_BUSINESS_PROFILE_DELETED' => 'App colors deleted',
    'AUDIT_EVENT_FREQUENT_BUYER_VIEWED' => 'Frequent buyer viewed',
    'AUDIT_EVENT_FREQUENT_BUYER_CREATED' => 'Frequent buyer created',
    'AUDIT_EVENT_FREQUENT_BUYER_UPDATED' => 'Frequent buyer updated',
    'AUDIT_EVENT_FREQUENT_BUYER_DELETED' => 'Frequent buyer deleted',
    'AUDIT_EVENT_REVIEW_RULE_VIEWED' => 'Review program viewed',
    'AUDIT_EVENT_REVIEW_RULE_CREATED' => 'Review program created',
    'AUDIT_EVENT_REVIEW_RULE_UPDATED' => 'Review program updated',
    'AUDIT_EVENT_REVIEW_RULE_DELETED' => 'Review program deactivated',
    'AUDIT_EVENT_EMPLOYEE_VIEWED' => 'Employee viewed',
    'AUDIT_EVENT_EMPLOYEE_CREATED' => 'Employee added',
    'AUDIT_EVENT_EMPLOYEE_UPDATED' => 'Employee updated',
    'AUDIT_EVENT_EMPLOYEE_DELETED' => 'Employee deleted',
    'AUDIT_EVENT_ROLE_VIEWED' => 'Role viewed',
    'AUDIT_EVENT_ROLE_CREATED' => 'Role created',
    'AUDIT_EVENT_ROLE_UPDATED' => 'Role updated',
    'AUDIT_EVENT_ROLE_DELETED' => 'Role deleted',
    'AUDIT_EVENT_EMPLOYEE_ROLE_VIEWED' => '',
    'AUDIT_EVENT_EMPLOYEE_ROLE_CREATED' => 'Employee role assigned',
    'AUDIT_EVENT_EMPLOYEE_ROLE_UPDATED' => 'Employee role updated',
    'AUDIT_EVENT_EMPLOYEE_ROLE_DELETED' => '',
    'AUDIT_EVENT_BANK_ACCOUNT_VIEWED' => 'Payment settings viewed',
    'AUDIT_EVENT_BANK_ACCOUNT_CREATED' => 'Payment settings created',
    'AUDIT_EVENT_BANK_ACCOUNT_UPDATED' => 'Payment settings updated',
    'AUDIT_EVENT_BANK_ACCOUNT_DELETED' => 'Payment settings deleted',
    'AUDIT_EVENT_DASHBOARD_VIEWED' => 'Dashboard viewed',
    'AUDIT_EVENT_AUDIT_LOG_VIEWED' => 'Audit logs viewed',
    'AUDIT_EVENT_TIERS_PROGRAM_VIEWED' => 'Tiers program viewed',
    'AUDIT_EVENT_TIERS_PROGRAM_CREATED' => 'Tiers program created',
    'AUDIT_EVENT_TIERS_PROGRAM_UPDATED' => 'Tiers program updated',
    'AUDIT_EVENT_TIERS_PROGRAM_DELETED' => 'Tiers program deleted',
    'AUDIT_EVENT_CUSTOMER_SEGMENT_VIEWED' => 'Customer segments viewed',
    'AUDIT_EVENT_CUSTOMER_SEGMENT_UPDATED' => 'Customer segments updated',
    'AUDIT_EVENT_TAG_VIEWED' => 'Tags viewed',
    'AUDIT_EVENT_TAG_CREATED' => 'Tag created',
    'AUDIT_EVENT_TAG_UPDATED' => 'Tag updated',
    'AUDIT_EVENT_TAG_DELETED' => 'Tag deleted',

    'BACKEND_RULES_BONUSPTS_TITLE_SIGN_UP_AT_STORE' => 'Sign up at my store',
    'BACKEND_RULES_BONUSPTS_TITLE_FOLLOW_STORE' => 'Join my Loyalty Program via the Kangaroo Apps',
    'BACKEND_RULES_CHECK_IN_POINTS' => 'Customers who check-in at my store will earn',


    'BACKEND_OFFERS' => 'offers',
    'BACKEND_OFFER' => 'an offer',
    'BACKEND_AND' => 'and',

    'TRX_SUBTYPE_REWARD_RECEIPT_SCAN_QR_CODE' => 'Scan QR Code Receipt Reward',
    'BACKEND_MULTIPLIER_OFFER_DEFAULT_DESCRIPTION' => 'You will earn :{multip_factor} times the points on your purchase.',

    'CRM_FIRST_NAME' => 'First Name',
    'CRM_LAST_NAME' => 'Last Name',
    'CRM_PHONE_NUMBER' => 'Phone',
    'CRM_EMAIL_ADDRESS' => 'Email',
    'CRM_GENDER' => 'Gender',
    'CRM_BIRTHDATE' => 'Date of Birth',
    'CRM_NOTES' => 'Notes',
    'CRM_ADDRESS' => 'Address',
    'CRM_BALANCE' => 'Points',
    'CRM_BRANCH' => 'Branch',
    'CRM_POSTAL_CODE' => 'Postal Code',
    'CRM_COUNTRY' => 'Country',
    'CRM_PROMO_MAIL' => 'Promotions by Mail',
    'CRM_ALLOW_EMAIL' => 'Allow Email',
    'CRM_ALLOW_SMS' => 'Allow SMS',
    'CRM_ALLOW_PUSH' => 'Allow Push',
    'CRM_TAGS' => 'Tags',
    'CRM_TERMS_CONDITIONS' => 'Terms & Conditions',
    'CRM_ACCOUNT_NUMBER' => 'Account Number',
    'CRM_CUSTOM_FIELD_1' => 'Custom Field 1',
    'CRM_CUSTOM_FIELD_2' => 'Custom Field 2',
    'CRM_CUSTOM_FIELD_3' => 'Custom Field 3',
    'CRM_CUSTOM_FIELD_4' => 'Custom Field 4',
    'CRM_CUSTOM_FIELD_5' => 'Custom Field 5',

    'NOTIF_TEXT_POINTS_EXPIRATION' => 'Points expired: :{units_exchange} :{points_label}',
    'ACTIVITY_POINTS_EXPIRATION' => 'Points expired',
    'NOTIF_REQ_POINTS_EXPIRATION' => 'Points expired',
    'TRX_SUBTYPE_POINTS_EXPIRATION' => 'Points Expiration',
    'CUSTOMER_EMAIL' => 'Customer Email',
    'EXPIRED_POINTS' => 'Points expired',

    'BACKEND_SPIN_WIN_TRY_AGAIN' => 'Try Again',

    'CUSTOMER_FIRST_VISIT' => 'First Visit',
    'CUSTOMER_LAST_VISIT' => 'Last Visit',
    'CUSTOMER_TIER_STATUS' => 'Tier Status',
    'CUSTOMER_LIFETIME_POINTS' => 'Lifetime Points',
    'CUSTOMER_E_WALLET' => 'E Wallet',
    'CUSTOMER_GIFT_CARD' => 'Gift Card',
    'CUSTOMER_STORE_CREDIT' => 'Store Credit',
    'CUSTOMER_NOTES' => 'Notes',
    'CUSTOMER_CASL_CONSENT' => 'Consent',

    'CUSTOMER_TOTAL_EARNS' => 'Earned Points',
    'CUSTOMER_TOTAL_REDEEMED' => 'Redeemed Points',
    'CUSTOMER_POINTS' => 'Current Points',
    'UPCOMING_EXPIRED_POINTS' => 'Points expiring soon',
    'UPCOMING_EXPIRED_DATE' => 'Date expiring',

    'TWILIO_SEND_VERIFICATION_FAILED' => 'Failed to send verification code',
    'TWILIO_CREATE_TOTP_FAILED' => 'Failed to create a TOTP',
    'TWILIO_VERIFICATION_FAILED' => 'Invalid verification code',
    'TWILIO_AUTH_CODE_REQUIRED' => 'Security code is required',
    'TWILIO_VERIFICATION_NOT_FOUND' => 'Verification not found',

    'VERIFY_CHANNEL_REQUIRED' => 'Channel field is required',

    'BACKEND_RULES_FOLLOW_FACEBOOK' => 'Follow on Facebook',
    'BACKEND_RULES_FOLLOW_TWITTER' => 'Follow on Twitter',
    'BACKEND_RULES_FOLLOW_INSTAGRAM' => 'Follow on Instagram',
    'BACKEND_RULES_FOLLOW_TIKTOK' => 'Follow on Tiktok',
    'EXPORT_GENERIC_COUPON' => 'Coupon',

    'REPORTS_FIELD_ID' => 'ID',
    'REPORTS_FIELD_FIRST_NAME' => 'First Name',
    'REPORTS_FIELD_LAST_NAME' => 'Last Name',
    'REPORTS_FIELD_EMAIL' => 'Email',
    'REPORTS_FIELD_PHONE' => 'Phone Number',
    'REPORTS_FIELD_TAGS' => 'Tags',
    'REPORTS_FIELD_POINTS' => 'Points',
    'REPORTS_FIELD_GIFTCARD' => 'Gift Card Balance',
    'REPORTS_FIELD_LIFETIME' => 'Lifetime Balance',
    'REPORTS_FIELD_BRANCH_NAME' => 'Branch Name',
    'REPORTS_FIELD_TIER_LEVEL' => 'Tier Level',
    'REPORTS_FIELD_CREATED_AT' => 'Created On',
    'REPORTS_FIELD_ACCOUNT_NUMBER' => 'Account Number',

    // Loyalty Generated Revenue
    'REPORTS_FIELD_DATE' => 'Date',
    'REPORTS_FIELD_TOTAL_AMOUNT' => 'Total Amount',
    'REPORTS_FIELD_CAMPAIGNS_AMOUNT' => 'Campaigns Amount',
    'REPORTS_FIELD_CUSTOMERS_COUNT' => 'Customers Count',
    'REPORTS_FIELD_TRANSACTIONS_COUNT' => 'Transactions Count',

    // Average Customer Spend
    'REPORTS_FIELD_AVG_AMOUNT' => 'Average Amount',

    // Customers Unsubscribed
    'REPORTS_FIELD_EMAIL_ACTION' => 'Email Action',
    'REPORTS_FIELD_SMS_ACTION' => 'SMS Action',
    'REPORTS_FIELD_PUSH_ACTION' => 'Push Action',
    'REPORTS_FIELD_UPDATED_AT' => 'Updated On',
    'REPORTS_FIELD_CAMPAIGN_NAME' => 'Campaign Name',
    
    // Campaign Engagement
    'REPORTS_FIELD_REDEEMED_AT' => 'Redeemed On',
    'REPORTS_FIELD_TARGETED_AT' => 'Targeted On',
    'REPORTS_FIELD_TRX_NUM' => 'Transactions',
    'REPORTS_FIELD_OFFER_NAME' => 'Offer',
    'REPORTS_FIELD_REDEMPTION_NAME' => 'Redemption',
    'REPORTS_FIELD_AMOUNT_SPENT' => 'Amount Spent',

    //Reviews
    'REPORTS_FIELD_REVIEW_PK' => 'Id',
    'REPORTS_FIELD_REVIEWER_EMAIL' => 'Email',
    'REPORTS_FIELD_REVIEWER_NAME' => 'Name',
    'REPORTS_FIELD_RATING' => 'Rating',
    'REPORTS_FIELD_TITLE' => 'Title',
    'REPORTS_FIELD_COMMENT' => 'Review',
    'REPORTS_FIELD_PRODUCT_TITLE' => 'Product',
    'REPORTS_FIELD_PHOTOS' => 'Photos',
    'REPORTS_FIELD_VIDEOS' => 'Videos',
    'REPORTS_FIELD_PUBLISHED' => 'Published',
    'REPORTS_FIELD_VERIFIED_BUYER' => 'Verified buyer',
    'REPORTS_FIELD_REVIEW_TYPE_PRODUCT' => 'Product',
    'REPORTS_FIELD_REVIEW_TYPE_WEBSITE' => 'Website',
    'REPORTS_FIELD_YES' => 'Yes',
    'REPORTS_FIELD_NO' => 'No',

    'DOUBLE_OPT_IN_TEXT_MESSAGE' => 'Reply Y or YES to confirm that you want to receive SMS messages from :{business_name}',
    'RE_OPT_IN_TEXT_MESSAGE' => ':{business_name}. To continue receiving our exclusive offers, click on the link to re-opt-in and confirm your consent: ',
    'RE_OPT_IN_NO_TRX__TEXT_MESSAGE' => ':{business_name}. To continue receiving our exclusive offers, click on the link to re-opt-in and confirm your consent: ',
    'CUSTOMER_NOT_MATCH_ERROR' => "Customer doesn't match",
    'CUSTOMER_EMAIL_NOT_MATCH_ERROR' => "Customer email doesn't match",
    'CUSTOMER_PHONE_NOT_MATCH_ERROR' => "Customer phone number doesn't match",
    'REPORTS_FIELD_VISITS' => 'Visits',
    'REPORTS_FIELD_POINTS_EARNED' => 'Points earned',
    'REPORTS_FIELD_POINTS_REDEEMED' => 'Points redeemed',
    'BACKEND_DRAW_WINNER_EMAIL_SUBJECT' => ':{business_name} Draw Results - YOU HAVE WON :{prize}!',
    'BACKEND_DRAW_WINNER_SMS_CONTENT' => ':{business_name} Draw Results - YOU HAVE WON :{prize}! Come to the store to claim your prize by :{expiry_date}!',
    'BACKEND_DRAW_RESULTS_ADMIN_EMAIL_SUBJECT' => 'Draw results are in for ":{draw_name}"',
    'FRONTEND_GENERIC_MAIL_SIGNATURE2'=> 'Sincerely,<br>The Kangaroo Team',
    'CHARGE_FAILED_EMAIL_SUBJECT' => 'Attention required - payment unsuccess',
    'AMAZON_GIFT_CARD_REDEMPTION_EMAIL_SUBJECT' => 'Redeeming Your Amazon Coupon Code!',
    'AMAZON_GIFT_CARD_REDEMPTION_SMS' => 'We are thrilled to inform you that you have successfully redeemed an exciting Amazon coupon code :code. :businessName.',
    'ADMIN_INSUFFICIENT_BUSINESS_WALLET_AMOUNT_EMAIL_SUBJECT' => 'Attention required - Vouchers are not available',
    'ADMIN_FRAUDULENT_ACTIVITY_EMAIL_SUBJECT' => 'Important Notice: Redemption Vouchers Temporarily Blocked',

    'REPORTS_FIELD_7D' => '7 days',
    'REPORTS_FIELD_14D' => '14 days',
    'REPORTS_FIELD_30D' => '30 days',
    'REPORTS_FIELD_60D' => '60 days',
    'REPORTS_FIELD_90D' => '90 days',
    'REPORTS_FIELD_180D' => '180 days',
    'REPORTS_FIELD_360D' => '360 days',
    'REPORTS_FIELD_TYPE' => 'Type',
    'REPORTS_FIELD_BRANCH' => 'Branch',
    'REPORTS_FIELD_TOTAL_PURCHASES' => 'Purchases',
    'REPORTS_FIELD_TOTAL_CUSTOMERS' => 'Customers',
    'REPORTS_FIELD_AVERAGE_CLV' => 'Average CLV',
    'REPORTS_FIELD_TIME_INTERVAL' => 'Time Interval',
    'REPORTS_FIELD_PERCENTAGE' => 'Percentage',
    'REPORTS_FIELD_LIFETIME_AVG_PURCHASE' => 'Lifetime Average CLV',
    'REPORTS_FIELD_CAMPAIGNS' => 'Campaigns',
    'REPORTS_FIELD_CHURNED' => 'Churned',
    'REPORTS_FIELD_FIRST_ENGAGEMENT' => 'First engagement',
    'REPORTS_FIELD_LAST_ENGAGEMENT' => 'Last engagement',
    'REPORTS_FIELD_BOUNCED' => 'Bounced',
    'REPORTS_FIELD_PREDICTED_CHURNED' => 'Predicted to churn',
    'REPORTS_FIELD_PREDICTED_CHURNED_PROB' => 'Probability of churn (%)',
    'TRX_SUBTYPE_REFERRAL_REFEREE_REWARD_POINTS_BY_PURCHASE_RATIO' => 'Referred rewarded',
    'TRX_SUBTYPE_REFERRAL_REFERER_REWARD_POINTS_BY_PURCHASE_RATIO' => 'Referrer rewarded',
    'RULE_SELECTED_BRANCH' => 'The selected branch is',
    'RULE_SELECTED_BRANCHES' => 'The selected branches are',
    'VALIDATION_ERROR_BRANCH_NOT_VALID' => 'The selected branch is invalid or not enabled for this business.',
    'VALIDATION_ERROR_PRODUCT_NOT_VALID' => 'The selected product id is invalid or not enabled for this business.',
    'VALIDATION_ERROR_OFFER_NOT_VALID' => 'The selected offer id is invalid or not enabled for this business.',
    'VALIDATION_ERROR_REDEMPTION_NOT_VALID' => 'The selected redemption id is invalid or not enabled for this business.',
    'VALIDATION_ERROR_TIER_NOT_VALID' => 'The selected tier status id is invalid.',
    'VALIDATION_ERROR_CAMPAIGN_NOT_VALID' => 'The selected campaign id is invalid.',
    'VALIDATION_ERROR_INTEGRATION_ACCOUNT_NOT_VALID' => 'The selected account is invalid.',

    'BACKEND_SEGMENTS_FIRST_TIME' => 'New Customers',
    'BACKEND_SEGMENTS_REPEAT' => 'Repeat',
    'BACKEND_SEGMENTS_LOYAL' => 'Loyal',
    'BACKEND_SEGMENTS_REGULAR' => 'Regular',
    'BACKEND_SEGMENTS_ATRISK' => 'At-Risk',

    // A2P10DLC Status
    // Missing
    'BACKEND_A2P10DLC_STATUS_MISSING_TITLE' => 'Missing 10DLC registration',
    'BACKEND_A2P10DLC_STATUS_MISSING_LONG_MESSAGE' => 'Before you can send <b>SMS marketing campaigns</b>, you must first submit a <b>10DLC compliance form</b> on behalf of your business',
    'BACKEND_A2P10DLC_STATUS_MISSING_BUTTON' => 'Submit 10DLC registration',
    // Pending
    'BACKEND_A2P10DLC_STATUS_REVIEW_TITLE' => '10DLC registration :review_status',
    'BACKEND_A2P10DLC_STATUS_REVIEW_LONG_MESSAGE' => 'Before you can send <b>SMS marketing campaigns</b>, your <b>10DLC compliance form</b> must be approved. Currently, it is :review_status',
    'BACKEND_A2P10DLC_STATUS_REVIEW_BUTTON' => '10DLC registration :review_status',
    'BACKEND_A2P10DLC_STATUS_PENDING_REVIEW_TEXT' => 'pending review',
    'BACKEND_A2P10DLC_STATUS_IN_REVIEW_TEXT' => 'in review',
    // Errors
    'BACKEND_A2P10DLC_STATUS_FAIL_TITLE' => 'Attention Required for 10 DLC registration',
    'BACKEND_A2P10DLC_STATUS_FAIL_LONG_MESSAGE' => 'Before you can send <b>SMS marketing campaigns</b>, you must first complete your <b>10DLC compliance form</b>',
    'BACKEND_A2P10DLC_STATUS_FAIL_BUTTON' => 'Complete 10DLC registration',
    // Approved
    'BACKEND_A2P10DLC_STATUS_IN_PROGRESS_TITLE' => '10 DLC registration in progress',
    'BACKEND_A2P10DLC_STATUS_IN_PROGRESS_LONG_MESSAGE' => 'Your <b>10DLC compliance form</b> finalization is currently in progress',
    'BACKEND_A2P10DLC_STATUS_IN_PROGRESS_BUTTON' => '10DLC registration in progress',
    // Approved
    'BACKEND_A2P10DLC_STATUS_APPROVED_TITLE' => '10 DLC registration approved',
    'BACKEND_A2P10DLC_STATUS_APPROVED_LONG_MESSAGE' => 'You can now send <b>SMS marketing campaigns</b>',
    'BACKEND_A2P10DLC_STATUS_APPROVED_BUTTON' => '10DLC registration approved',

    'PIN_CODE_RESET_EMAIL_SUBJECT' => ':business_name: PIN Code reset request',
    'KR_PARTNER_REWARD_EMAIL_SUBJECT' => ':business_name: Partner Reward Redemption',
    'ACCOUNT_NUMBER' => 'Account Number',
    'EXTERNAL_ORDER_ID' => 'Order Id',

    // Transaction request & response
    'INVALID_TRANSACTION_DATE_PAST_OR_TODAY' => 'The created_at must be a date in the past or today.',

    /**
     * 
     * CUSTOMIZABLE MESSAGES DEFAULT VARIABLES
     * 
     */
    
     /**
      * WELCOME SMS
      */
    'CUSTOMIZED_MESSAGE_WELCOME_SMS_PIN_CODE' => '. Your PIN is {{$pinCode}}.',
    'CUSTOMIZED_MESSAGE_WELCOME_SMS_PIN_CODE_BROADNET' => ': {{$pinCode}}.',
    'CUSTOMIZED_MESSAGE_WELCOME_SMS_WITHOUT_PIN_CODE' => '.',
    
    /**
     * Tier Upgrade Email
     */

    /**
     * Offer/reward
     */
    'BACKEND_OFFERS_VALIDATION_FREE_PRODUCT_BRANCH' => 'Please choose a free product for :{$branchName}.',
    'BACKEND_OFFERS_VALIDATION_FREE_PRODUCT_BRANCH_ONLY_ONE' => 'Only one free product is allowed for branch :{$branchName}.',
    'BACKEND_OFFERS_VALIDATION_POS_PRODUCT' => 'Please select products for :{$branchName}.',
    'BACKEND_OFFERS_VALIDATION_POS_PRODUCT_BRANCH' => 'The selected product(:{$name}) is unavailable in any of the chosen branches.',
    'BACKEND_OFFERS_VALIDATION_CATEGORY_PRODUCT' => 'Selecting both categories and products is not allowed.',
    'BACKEND_OFFERS_VALIDATION_POS_COLLECTION' => 'Please select collections for :{$branchName}.',
    'BACKEND_OFFERS_VALIDATION_POS_COLLECTION_BRANCH' => 'The selected collection :{$name} is unavailable in any of chosen branches.',

    'ACCOUNT_LOCKED_EMAIL_SUBJECT' => 'Your account has been locked',
];
