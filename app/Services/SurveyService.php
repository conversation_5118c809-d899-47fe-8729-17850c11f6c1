<?php

namespace App\Services;

use App\Http\Requests\V1\SurveyAnswersRequest;
use App\Models\SurveyStatistic;
use App\Models\V1\Activity;
use App\Models\V1\Business;
use App\Models\V1\BusinessBranch;
use App\Models\V1\BusinessRules;
use App\Models\V1\Notification;
use App\Models\V1\Transaction;
use App\Models\V1\User;
use App\Models\V1\UserEntityPoints;
use App\Traits\SurveyTrait;
use Exception;
use KangarooRewards\Common\Models\SurveyAnonymous;
use KangarooRewards\Common\Models\SurveyAnonynous;
use KangarooRewards\Common\Models\SurveyComment;
use KangarooRewards\Common\Models\SurveyResponse;

class SurveyService
{
    private $userId;
    private $businessId;
    private $surveyId;
    private $clerkId;
    private $business;
    private $businessRules;
    private $branchId;
    private $silo_flag;
    private $branch_timezone;
    private $survey;
    private $businessTypeFk;
    private $branchTypeFk;
    private $transactionId;
    private $results;
    private $event;
    private $broadcastId;

    /**
     * @param SurveyAnswersRequest $request
     * @throws Exception
     */
    public function __construct(SurveyAnswersRequest $request)
    {
        $this->initFromJwtToken($request);

        $this->results = $request->answers;

        $this->business = Business::findByPk($this->businessId);
        $this->businessRules = BusinessRules::forBusinessWithTaxes($this->businessId);
        $this->branchId = $this->business->business_branch_pk;
        $this->silo_flag = $this->businessRules->silo_flag;
        $this->branch_timezone = $this->business->branch_timezone_fk;

        $this->survey = SurveyTrait::findByPkForBusiness($this->surveyId, $this->businessId);

        $this->businessTypeFk = $this->business->entity_type_fk; //1, 7, 99
        $this->branchTypeFk = BusinessBranch::getBranchEntityTypeByBusinessEntityType($this->businessTypeFk);

        $this->event = (object) $request->event;
    }

    /**
     * @param SurveyAnswersRequest $request
     * @throws Exception
     */
    private function initFromJwtToken(SurveyAnswersRequest $request)
    {
        $jwt = $request->getJWTData();

        $this->userId = $jwt->userId;
        $this->businessId = $jwt->businessId;
        $this->surveyId = $jwt->surveyId;
        $this->clerkId = $jwt->clerkId;
        $this->broadcastId = $jwt->broadcastId;
    }

    /**
     * @throws Exception
     */
    public function saveAnswers()
    {
        $anonymousId = null;
        if (!$this->userId || $this->survey->survey_type == SURVEY_TYPE_SATISFACTION) {
            $model = new SurveyAnonymous();
            $model->save();
            $anonymousId = $model->id;
        }

        foreach ($this->results as $key => $q) {
            $q = (object) $q;

            if (empty($q->child) || empty($q->parent)) {
                throw new \RuntimeException('Can not find the question: ' . json_encode($this->results));
            }

            $deviceLocation = $authDeviceLocationModel->authdevice_location_pk ?? null;

            $response = new SurveyResponse();
            $response->survey_question_answer_relation_child_fk = $q->child;
            $response->survey_question_answer_relation_parent_fk = $q->parent;
            $response->user_fk = $this->userId;
            $response->employee_fk = $this->clerkId;
            $response->business_branch_fk = $this->branchId;
            $response->platform_solution_fk = config('app.platform_id', 10);
            $response->authdevice_location_fk = $deviceLocation;
            $response->survey_anonymous_fk = $anonymousId;
            $response->survey_broadcast_fk = $this->broadcastId;
            $response->save();

            if (isset($q->answer_free_text) && !empty($q->answer_free_text)) {
                $comment = new SurveyComment();
                $comment->comment = filter_var($q->answer_free_text, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
                $comment->survey_response_fk = $response->survey_response_pk;
                $comment->save();
            }
        }

        if (!$this->userId) {
            return;
        }

        if (!$this->survey->points_earned) {
            // Do not reward the user if no points for survey
            return;
        }

        if (!(isset($q->not_interested) && $q->not_interested)) {
            $this->saveTransaction();
            $this->saveAcivity();
            $this->saveNotification();
            $this->updateUserPoints();
        }
    }

    public function saveTransaction()
    {
        $trxModel = new Transaction();
        $trxModel->utc_created = date('Y-m-d H:i:s');
        $trxModel->transaction_type_fk = TRX_TYPE_SURVEY; //4;
        $trxModel->transaction_sub_type_fk = TRX_SUBTYPE_SURVEY_COMPLETED;
        $trxModel->user_fk = $this->userId;
        $trxModel->authusertoken_fk = null;
        $trxModel->entity_fk = $this->branchId;
        $trxModel->entity_type_fk = $this->branchTypeFk;
        $trxModel->timezone_mysql_fk = $this->branch_timezone;
        $trxModel->save();

        $this->transactionId = $trxModel->transaction_pk;
    }

    /**
     *
     */
    public function saveAcivity()
    {
        $activityModel = new Activity();
        $activityModel->description = 'Referee {username}, has been rewarded {pts} from {business_name}';
        $activityModel->utc_publish = date('Y-m-d H:i:s');
        $activityModel->transaction_fk = $this->transactionId;
        $activityModel->units_exchange = $this->survey->points_earned;
        $activityModel->punch_offer_fk = null;
        $activityModel->hidden = $this->silo_flag;
        $activityModel->timezone_mysql_fk = $this->branch_timezone;
        $activityModel->save();
    }

    public function saveNotification()
    {
        //SAVE NOTIFICATION
        Notification::create([
            'notificationRequester' => NOTIF_REQ_REFERRAL_REFEREE_REWARD,
            'notification_text' => ' points have been earned after completed the Suvey',
            'notificationType' => 2,
            'user_receiver_fk' => $this->userId,
            'user_sender_fk' => $this->userId,
            'business_fk' => $this->businessId,
            'business_branch_fk' => $this->branchId,
            'timezone_mysql_fk' => $this->branch_timezone,
            'units_exchange' => $this->survey->points_earned,
        ]);
    }

    /**
     * @throws Exception
     */
    public function updateUserPoints()
    {
        $userModel = User::find($this->userId);
        $businessModel = Business::find($this->businessId);

        UserEntityPoints::increaseBalance($userModel, $businessModel, $this->survey->points_earned);
    }

    public function saveEvent()
    {
        $event = new SurveyStatistic();

        $event->event_type = $this->event->event_type;
        $event->platform_id = $this->event->platform_id;
        $event->survey_fk = $this->surveyId;
        $event->user_fk = $this->userId ?? null;

        $event->save();
    }
}
