<?php

namespace KangarooRewards\Workflows\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class WorkflowActionExecutionCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return (new WorkflowActionExecutionResource($item))->response()->getData()->data;
            }),
        ];
    }
}