<?php

namespace application\components\proxy;

use <PERSON><PERSON>;
use OauthProviderToken;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

/**
 * A simplified version of KangarooApiClient that only requires a user ID
 */
class SimpleKangarooApiClient
{
    /**
     * @var Client
     */
    private $client;

    /**
     * @var string
     */
    private string $accessToken;

    /**
     * Constructor that only requires a user ID
     *
     * @param int $userId The user ID to create the client for
     * @throws \League\OAuth2\Client\Provider\Exception\IdentityProviderException
     */
    public function __construct(int $userId)
    {
        // Get the access token for the user
        $oauthProviderToken = OauthProviderToken::getAccessTokenForUser($userId);
        $this->accessToken = $oauthProviderToken->access_token;

        // Create the client with headers
        $clientObject = [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept' => 'application/vnd.kangaroorewards.api.v1+json;',
                'Content-Type' => 'application/json',
            ]
        ];

        $this->client = new Client($clientObject);
    }

    /**
     * Generic method to make GET requests
     *
     * @param string $endpoint API endpoint
     * @param array $paramsArr Query parameters
     * @return array|string Response body
     * @throws GuzzleException
     */
    public function get(string $endpoint, array $paramsArr = [], bool $raw = false): array|string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . $endpoint;

        if (!empty($paramsArr)) {
            $requestUrl .= '?' . http_build_query($paramsArr);
        }

        $request = $this->client->request("GET", $requestUrl);
        if ($raw) {
            return $request->getBody()->getContents();
        }

        return json_decode($request->getBody()->getContents(), true);
    }

    /**
     * Generic method to make POST requests
     *
     * @param string $endpoint API endpoint
     * @param array $paramsArr Request body
     * @return array Response body
     * @throws GuzzleException
     */
    public function post(string $endpoint, array $paramsArr = []): array
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . $endpoint;

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return json_decode($request->getBody()->getContents(), true);
    }

    /**
     * Generic method to make PATCH requests
     *
     * @param string $endpoint API endpoint
     * @param array $paramsArr Request body
     * @return array Response body
     * @throws GuzzleException
     */
    public function patch(string $endpoint, array $paramsArr = []): array
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . $endpoint;

        $request = $this->client->request("PATCH", $requestUrl, ['json' => $paramsArr]);

        return json_decode($request->getBody()->getContents(), true);
    }

    /**
     * Generic method to make DELETE requests
     *
     * @param string $endpoint API endpoint
     * @param array $paramsArr Query parameters
     * @return array Response body
     * @throws GuzzleException
     */
    public function delete(string $endpoint, array $paramsArr = []): array
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . $endpoint;

        if (!empty($paramsArr)) {
            $requestUrl .= '?' . http_build_query($paramsArr);
        }

        $request = $this->client->request("DELETE", $requestUrl);

        return json_decode($request->getBody()->getContents(), true);
    }
}
