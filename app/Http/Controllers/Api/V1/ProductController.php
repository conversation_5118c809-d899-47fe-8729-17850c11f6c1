<?php

namespace App\Http\Controllers\Api\V1;

use App\Exceptions\ResourceNotFoundException;
use App\Helpers\DataTransferObject\DataTransferObjectError;
use App\Helpers\Dto\ProductData;
use App\Helpers\Product\ProductFactory;
use App\Helpers\Product\ProductUpdateFactory;
use App\Http\Requests\V1\CreateProduct;
use App\Http\Resources\ProductResource;
use App\Models\V1\ProductBranch;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Models\V1\Business;
use App\Models\V1\Product;
use App\Helpers\V1\Utils;
use App\Http\Requests\V1\DeleteProduct;
use App\Http\Requests\V1\UpdateProduct;
use App\Http\Requests\V1\ViewProduct;
use App\Http\Resources\ProductCollection;
use Illuminate\Support\Facades\Log;
use App\Traits\LogContextTrait;
use App\Traits\ProductTrait;

/**
 * Products resource representation.
 *
 * @Resource("Products", uri="/products")
 */
class ProductController extends ApiController
{
    use LogContextTrait, ProductTrait;

    /**
     * List Products
     * Get a JSON representation of all products for a business
     *
     * GET /products
     *
     * @Get("/products{?page}")
     * @Versions({"v1"})
     * @Response(200, body={"data":[{"id":12345678,"first":"Sara","last":"Silverman","email":"<EMAIL>"},{"id":12345679,"first":"victoria","last":"Leblanc","email":"<EMAIL>"}],"cursor": {"next": 2,"next_uri": "/products?page=2"}})
     * @Parameters({
     *      @Parameter("page", type="integer", required=false description="The page of results to view.", default=1)
     * })
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user(); //merchant user id
            $business = $request->appBusiness();

            $this->validate($request, [
                'per_page' => 'sometimes|integer|min:1|max:' . config('app.pagination_limit'),
                'page' => 'sometimes|integer|min:1',
                'all_products' => 'sometimes|integer|in:0,1',
                'sort.name' => 'sometimes|string|in:asc,desc',
                'sort.price' => 'sometimes|string|in:asc,desc',
                'sort.published' => 'sometimes|string|in:asc,desc',
            ]);

            $allProducts = $request->all_products === 1 ?? false;
            $sortConditions = $request->sort ?? [];

            if (isset($request->page)) {
                $products = Product::forBusiness($business->business_pk, !$allProducts, $sortConditions)->paginate((int)$request->per_page);
                return new ProductCollection($products);
            } else {
                $products = Product::forBusiness($business->business_pk)->get();
                $_products = Product::transformCollection($products, $langId = 1);

                $cursor = [
                    'count' => count($_products),
                ];

                return $this->respondWithArray(['data' => $_products, 'cursor' => $cursor]);
            }
        } catch (\InvalidArgumentException $e) {
            \Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\LogicException $e) {
            \Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \Log::error($e);
            return $this->errorInternalError();
        }
    }

    public function show(ViewProduct $request, Product $product)
    {
        try {
            return new ProductResource($product);
        } catch (\InvalidArgumentException $e) {
            \Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\LogicException $e) {
            \Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \Log::error($e);
            return $this->errorInternalError();
        }
    }

    /**
     * Create product for a business
     * <AUTHOR>
     * @since 07-02-2022
     * @param CreateProduct $request
     * @return \Illuminate\Http\Response
     */
    public function store(CreateProduct $request)
    {
        $this->addLogContext(['data' => $request->all()]);
        try {
            $user = $request->user(); //merchant user id
            $this->addLogContext(['userId' => $request->user()->id ?? null]);
            $business = Business::forUser($user);
            $this->addLogContext(['business' => $business->business_pk ?? null]);

            $factory = new ProductFactory($request, $business);

            $factory->validate();
        } catch (\InvalidArgumentException $e) {
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (DataTransferObjectError $e) {
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (ResourceNotFoundException $e) {
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorNotFound('Not found', $e->getMessage());
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorInternalError();
        }

        \DB::connection('tenant')->beginTransaction();
        try {
            $product = $factory->createProduct();
            \DB::connection('tenant')->commit();
        } catch (\InvalidArgumentException $e) {
            \DB::connection('tenant')->rollBack();
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \DB::connection('tenant')->rollBack();
            Log::error(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorInternalError();
        }

        return new ProductResource($product);
    }

    /**
     * Create product for a business
     * <AUTHOR>
     * @since 07-02-2022
     * @param UpdateProduct $request
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateProduct $request, Product $product)
    {
        $this->addLogContext(['data' => $request->all()]);
        try {
            $user = $request->user(); //merchant user id
            $this->addLogContext(['userId' => $request->user()->id ?? null]);
            $business = Business::forUser($user);
            $this->addLogContext(['business' => $business->business_pk ?? null]);

            $factory = new ProductUpdateFactory($request, $business, $product);

            $factory->validate();
        } catch (\InvalidArgumentException $e) {
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (DataTransferObjectError $e) {
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (ResourceNotFoundException $e) {
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorNotFound('Not found', $e->getMessage());
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorInternalError();
        }

        \DB::connection('tenant')->beginTransaction();
        try {
            // TODO Implement handling for uploading using laravel Factory / Builder pattern and implementing DTOs
            // TODO Update available branches with it
            // TODO Create / Update Product Rewards (A La Carte) when updated
            $product = $factory->updateProduct($product);
            \DB::connection('tenant')->commit();
        } catch (\InvalidArgumentException $e) {
            \DB::connection('tenant')->rollBack();
            Log::warning(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \DB::connection('tenant')->rollBack();
            Log::error(__METHOD__ . ' ' . $e, $this->getLogContext());
            return $this->errorInternalError();
        }

        return new ProductResource($product);
    }

    /**
     * Delete Template
     * Get a JSON response after deleting Template
     *
     * @DELETE("/products/{product}")
     *
     * @param DeleteProduct $request
     * @param Product $product
     * @return ProductResource|\Illuminate\Http\JsonResponse
     * @throws \Exception
     *
     */
    public function destroy(DeleteProduct $request, Product $product)
    {
        \DB::connection('tenant')->beginTransaction();
        try {
            $this->deleteProduct($product);
            \DB::connection('tenant')->commit();
        } catch (\InvalidArgumentException $e) {
            \DB::connection('tenant')->rollBack();
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \DB::connection('tenant')->rollBack();
            return $this->errorInternalError();
        }

        return $this->respondNoContent();
    }
}
