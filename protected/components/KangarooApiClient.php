<?php

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class KangarooApiClient
{
    private $user;

    /**
     * @var array
     */
    private $business;

    /**
     * @var
     */
    private $appKey;

    /**
     * @var Client
     */
    private $client;

    /**
     * @var BusinessBranch
     */
    private $branch;

    public function __construct(Business $business, BusinessBranch $branch, $accessToken, User $user = null, $lang = null)
    {
        $this->user = $user;
        $this->business = $business;
        $this->branch = $branch;

        $this->generateAppKey();
        $clientObject = [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'X-Application-Key' => $this->appKey,
                'Accept' => 'application/vnd.kangaroorewards.api.v1+json;',
                'Content-Type' => 'application/json',
                // 'Accept-Encoding' => 'gzip',
            ]
        ];
        
        if (isset($lang)) {
            $clientObject['headers']['Accept-Language'] = $lang;
        }

        $this->client = new Client($clientObject);
    }

    /**
     * @throws GuzzleException
     */
    public function getAllA2pProfiles(array $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/a2p-10dlc/twilio/customer-profiles/?'
            . http_build_query($paramsArr);

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function getA2pProfile(int $id): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/a2p-10dlc/twilio/customer-profiles/' . $id;

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function updateA2pProfile(int $id, array $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/a2p-10dlc/twilio/customer-profiles/' . $id;

        $request = $this->client->request("PATCH", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function createA2pProfile($paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/a2p-10dlc/twilio/customer-profiles/';

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function createA2pBrand($paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/a2p-10dlc/twilio/a2p-brands/';

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function getA2pBrand(int $id): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/a2p-10dlc/twilio/a2p-brands/' . $id;

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function getA2pCampaignUseCase(int $id, array $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/a2p-10dlc/twilio/a2p-profiles/campaigns/' . $id;

        if ($paramsArr) {
            $requestUrl .= '?' .  http_build_query($paramsArr);
        }

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function createA2pMessagingService($paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/a2p-10dlc/twilio/messaging-services/';

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function deleteTwilioSubAccount(array $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/a2p-10dlc/twilio/jobs/';

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function getAllGiftCards(): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/gift-cards/';

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function getTierProgress(): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/customers/' . $this->getUserUid();
        $requestUrl .= '?' . http_build_query([
            'include' => 'tier_level,tier_progress,tiers',
            'relationships' => 'tier_levels',
        ]);

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function getPointsExpirationReport($page, $perPage, string $startDate, string $endDate, $groupBy = '', $include = null): string
    {
        $payload = [
            'per_page' => $perPage,
            'page' => $page,
            'date_from' => $startDate,
            'date_to' => $endDate,
            'group_by' => $groupBy,
        ];

        if (!empty($include)) {
            $payload['include'] = $include;
        }

        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/reports/points-expiration/?' . http_build_query($payload);

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function getAllAuditLogs($page, $perPage, string $startDate, string $endDate): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/audit-logs/?' . http_build_query([
            'per_page' => $perPage,
            'page' => $page,
            'date_from' => $startDate,
            'date_to' => $endDate,
        ]);

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function getAllRoles($paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/roles/?' . http_build_query($paramsArr);

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function getAllEmployees($paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/employees/?' . http_build_query($paramsArr);

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function getAllCallToActions($paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/call-to-actions/?' . http_build_query($paramsArr);

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function createCallToAction($paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/call-to-actions';

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function updateCallToAction($id, $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/call-to-actions/' . $id;

        $request = $this->client->request("PATCH", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function deleteCallToAction($id): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/call-to-actions/' . $id;

        $request = $this->client->request("DELETE", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function internalGetMarketingPointsExpiryFilter($paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/internal/marketing/points-expiry-filter/?'
            . http_build_query($paramsArr);

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * Create mfa verification
     * 
     * @param $paramsArr
     * @return array
     * @throws GuzzleException
     */
    public function createMfaVerification($paramsArr = []): array
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/verify/verifications';

        try {
            $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);
            $httpCode = $request->getStatusCode();
            $data = (array) json_decode($request->getBody()->getContents(), true);
        } catch (Exception $e) {
            Yii::log($e->getMessage(), CLogger::LEVEL_ERROR,  __METHOD__);
            $httpCode = $e->getCode();
            $data = [];
        }

        Yii::log('MFA Creation Info ' . CJSON::encode([
            'http_code' => $httpCode,
            'data' => $data
        ]), CLogger::LEVEL_INFO, __METHOD__);

        return [
            'status' => $this->getStatusFromHttpCode($httpCode),
            'data' => $data['data'] ?? [],
        ];
    }

    /**
     * Validate mfa code
     *
     * @param $paramsArr
     * @return array
     * @throws GuzzleException
     */
    public function checkMfaVerification($paramsArr = []): array
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/verify/verification-checks';

        try {
            $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);
            $httpCode = $request->getStatusCode();
            $data = (array) json_decode($request->getBody()->getContents(), true);
        } catch (Exception $e) {
            Yii::log($e->getMessage(), CLogger::LEVEL_ERROR,  __METHOD__);
            $httpCode = $e->getCode();
            $data = [];
        }

        return [
            'status' => $this->getStatusFromHttpCode($httpCode),
            'data' => $data['data'] ?? [],
        ];
    }

    private function getUserUid(): string
    {
        return bin2hex($this->user->user_uid);
    }

    private function generateAppKey()
    {
        $this->appKey = Business::generateApplicationKey($this->business, $this->branch);
    }

    /**
     * Check HTTP code and return boolean
     * 
     * @param $httpCode
     * @return bool
     */
    private function getStatusFromHttpCode($httpCode)
    {
        if ($httpCode === 200 || $httpCode === 204) {
            return true;
        }
        return false;
    }

    /**
     * @throws GuzzleException
     */
    public function internalAnonymizeUser($user, $paramsArr = []): string
    {
        $uId = bin2hex($user->user_uid);
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/internal/users/' . $uId . '?'
            . http_build_query($paramsArr);

        $request = $this->client->request("DELETE", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function internalSendUserMessage($paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . "/internal/users/{$this->user->id}/messages";

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function internalSmsProviderActions($id, $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/internal/sms-providers/' . $id . '/actions';

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function createChat(array $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/gpt/chats';

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function sendChatGptMessage(int $id, array $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . "/gpt/chats/{$id}/completions";

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function internalCreateChat(array $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/internal/gpt/chats';

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function internalSendChatGptMessage(int $id, array $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . "/internal/gpt/chats/{$id}/completions";

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function qrCodeHelper(array $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . "/helpers/qr-codes";

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * Create and process the sale from the QR code
     * <AUTHOR>
     * @since 5/18/2023
     * @throws GuzzleException
     */
    public function processSaleFromQrCode($paramsArr = [])
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/transactions';

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return json_decode($request->getBody()->getContents(), true);
    }

    /**
     * Upload products list with CSV data
     * <AUTHOR>
     * @since 31/05/2023
     * @throws GuzzleException
     */
    public function uploadProductsList(array $paramsArr = []): array
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/products/imports/batches';

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return json_decode($request->getBody()->getContents(), true);
    }

    /**
     * Get imported products list
     * <AUTHOR>
     * @since 31/05/2023
     * @throws GuzzleException
     */
    public function getImportedProductsList(array $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/products/imports?' . http_build_query($paramsArr);

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

     /**
      * get translated strings
      * <AUTHOR>
      * @since 5/31/2023
      * @throws GuzzleException
      */
    public function getPublicAppStrings($paramsArr = [])
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/public/app-strings';

        $request = $this->client->request("GET", $requestUrl);

        return json_decode($request->getBody()->getContents(), true);
    }

    /**
     * Delete non imported product
     * <AUTHOR>
     * @since 06/06/2023
     * @throws GuzzleException
     */
    public function deleteNonImportedProduct($id): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/products/imports/' . $id;

        $request = $this->client->request("DELETE", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * Delete all nont imported products
     * <AUTHOR>
     * @since 06/06/2023
     * @throws GuzzleException
     */
    public function deleteAllNonImportedProduct($batchId): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/products/imports/batches/' . $batchId . '/inactive';

        $request = $this->client->request("DELETE", $requestUrl);

        return $request->getBody()->getContents();
    }

    /**
     * Update batch import
     * @param $id
     * @param $payload
     * @return string
     * @throws GuzzleException
     */
    public function updateProductBatchImport($id, $payload): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/products/imports/batches/' . $id;

        $request = $this->client->request("PATCH", $requestUrl, ['json' => $payload]);

        return $request->getBody()->getContents();
    }

    public function getThirdPartyCouponsCards()
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/third-party-coupons/cards';

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    public function updateThirdPartyCouponsCard($id, $params)
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/third-party-coupons/cards/' . $id;

        $request = $this->client->request("PATCH", $requestUrl, ['json' => $params]);

        return $request->getBody()->getContents();
    }
    
    public function deleteThirdPartyCouponsCard($id)
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/third-party-coupons/cards/'.$id;

        $request = $this->client->request("DELETE", $requestUrl);

        return $request->getBody()->getContents();
    }

    public function topUpBusinessWallet($cardId, $amount, $currency)
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/third-party-coupons/cards/' . $cardId . "/payment";

        $request = $this->client->request("POST", $requestUrl, ['json' => ['amount' => (int)$amount * 100, 'currency' => $currency]]);

        return $request->getBody()->getContents();
    }

    public function getBusinessWallets()
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/business-wallets';

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    public function createBusinessWallets($params)
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/business-wallets';

        $request = $this->client->request("POST", $requestUrl, ['json' =>$params]);

        return $request->getBody()->getContents();
    }

    public function updateBusinessWallets($id, $params)
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/business-wallets/'.$id;

        $request = $this->client->request("PATCH", $requestUrl, ['json' =>$params]);

        return $request->getBody()->getContents();
    }

    public function getFraudRules()
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/fraud-rules';

        $request = $this->client->request("GET", $requestUrl);

        return $request->getBody()->getContents();
    }

    public function createFraudRules($params)
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/fraud-rules';

        $request = $this->client->request("POST", $requestUrl, ['json' =>$params]);

        return $request->getBody()->getContents();
    }

    public function updateFraudRules($id, $params)
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/fraud-rules/'.$id;

        $request = $this->client->request("PATCH", $requestUrl, ['json' =>$params]);

        return $request->getBody()->getContents();
    }

    public function internalSendPushNotification(array $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/internal/notifications/push';

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    /**
     * Send batch of customers list to FCM push notification
     * Used in: Campaigns
     * @param array $paramsArr
     * @return string
     * @throws GuzzleException
     */
    public function internalSendBatchPushNotification(array $paramsArr = []): string
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/internal/notifications/campaign/push';

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }

    public function internalExportReviewers($paramsArr)
    {
        $requestUrl = Yii::app()->params['kangarooApiUri'] . '/internal/reviews/export-reviewers';

        $request = $this->client->request("POST", $requestUrl, ['json' => $paramsArr]);

        return $request->getBody()->getContents();
    }
}
