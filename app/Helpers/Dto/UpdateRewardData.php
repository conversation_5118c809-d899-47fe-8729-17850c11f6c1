<?php

namespace App\Helpers\Dto;

use App\Helpers\DataTransferObject\DataTransferObject;
use Carbon\Carbon;

class UpdateRewardData extends DataTransferObject
{
    /** @var integer|null */
    public ?int $targeted_flag = null;

    /** @var string|Carbon|null */
    public string|Carbon|null $publish_at = null;

    /** @var string|Carbon|null */
    public string|Carbon|null $expires_at = null;

    /** @var integer|null */
    public ?int $never_expires_flag = null;

    /** @var float|int|null */
    public int|null|float $discount_value = null;

    /** @var float|int|null */
    public int|null|float $real_value = null;

    /** @var float|int|null */
    public int|null|float $amount = null;

    /** @var int|null */
    public ?int $points = null;

    /** @var int|null */
    public ?int $shipping_option = null;

    /** @var bool|null */
    public ?bool $partner_reward = null;

    /** @var integer|string|null */
    public int|string|null $partner_reward_type = null;

    /** @var bool|null */
    public ?bool $redeem_for_gift_card = null;

    /** @var TierData[]|null  */
    public ?array $redemption_user_tiers;

    /** @var string|null */
    public ?string $external_id = null;

    /** @var string|null */
    public ?string $external_category_id = null;

    /** @var \App\Helpers\Dto\RewardLanguageData[]|null */
    public ?array $reward_languages;

    /** @var \App\Helpers\Dto\OfferImageData[]|null */
    public ?array $images;

    /** @var \App\Helpers\Dto\OfferBranchData[]|null */
    public ?array $available_at_branches;

    /**
     * @var int[]
     */
    public $products = [];

    /**
     * @var \App\Helpers\Dto\PosCategoryData[]
     */
    public $categories = [];
}