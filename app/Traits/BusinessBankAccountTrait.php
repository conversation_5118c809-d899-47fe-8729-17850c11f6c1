<?php

namespace App\Traits;

use App\Helpers\V1\PaymentHelper;
use App\Http\Resources\ThirdPartyCouponCardCollection;
use App\Models\BusinessBankAccount;
use App\Models\V1\BusinessBranch;
use Illuminate\Support\Facades\DB;

trait BusinessBankAccountTrait
{
    use TrdCouponsStripeTrait;

    public function getBankAccounts($businessId, $providerId)
    {
        $query = BusinessBankAccount::where('business_fk', $businessId)
            ->where('payment_provider_id', $providerId);

        if ($providerId == PaymentHelper::PROVIDER_STRIPE_3RD_COUPONS) {
            $query->whereNotNull('stripe_payment_method_id');
        }

        return $query->get();
    }

    public function fetchLastCreatedBankAccount($businessId, $userId, $providerId)
    {
        return BusinessBankAccount::withoutGlobalScope('enabled')
            ->where('business_fk', $businessId)
            ->where('payment_provider_id', $providerId)
            ->where('modifiedby_fk', $userId)
            ->orderBy('utc_created', 'DESC')
            ->first();
    }

    public function updateDefaultBankAccount($businessBankAccount, $default)
    {

        DB::connection('tenant')->beginTransaction();
        try {

            if ((int)$default == 1) {
                $businessAccounts = $this->getBankAccounts($businessBankAccount->business_fk, $businessBankAccount->payment_provider_id);
                foreach ($businessAccounts as $account) {
                    if ($account->default == 1) {
                        $account->update(['default' => 0]);
                    }
                }
            }
            $businessBankAccount->update(['default' => $default]);
            DB::connection('tenant')->commit();
            return $businessBankAccount;
        } catch (\Exception $e) {
            DB::connection('tenant')->rollBack();
            throw $e;
        }
    }

    public function getBankAccount($businessId, $bankAccountId)
    {
        return BusinessBankAccount::where('business_bankaccount_pk', $bankAccountId)
            ->where('business_fk', $businessId)
            ->first();
    }

    public function createOrGetBusinessBankAccountCustomer($user, $lastCreatedBankAccount)
    {
        $customer = null;
        if (isset($lastCreatedBankAccount, $lastCreatedBankAccount->stripe_customer_id)) {
            $customer = $this->getTrdCouponsStripeCustomer($lastCreatedBankAccount->stripe_customer_id);
        }

        if (!$customer) {
            $params = [];
            if (!empty($user->email)) {
                $params['email'] = $user->email;
            }

            if (!empty($user->first)) {
                $params['name'] = $user->first;
            }

            if (!empty($user->last)) {
                if (isset($params['name'])) {
                    $params['name'] = $params['name'] . ' ' . $user->last;
                } else {
                    $params['name'] = $user->last;
                }
            }

            $customer = $this->createTrdCouponsStripeCustomer($params);
        }

        return $customer;
    }

    public function getBusinessAccounts($businessId, $user, $providerId)
    {
        if ($providerId != PaymentHelper::PROVIDER_STRIPE_3RD_COUPONS) {
            throw new \RuntimeException('Currently not implemented');
        }

        $bankAccounts = $this->getBankAccounts($businessId, $providerId);
        $lastCreatedBankAccount = $this->fetchLastCreatedBankAccount($businessId, $user->id, $providerId);
        $customer = $this->createOrGetBusinessBankAccountCustomer($user, $lastCreatedBankAccount);
        if (isset($lastCreatedBankAccount, $lastCreatedBankAccount->stripe_customer_id)) {
            $setupIntentId = $lastCreatedBankAccount->stripe_setup_intent_id;
        }
        $setupIntent = $this->getTrdCouponsStripeSetupIntent($setupIntentId ?? null);

        info(__METHOD__, ['setupIntentId' => $setupIntentId ?? null, 'status' => $setupIntent->status ?? null]);
        if (!isset($setupIntent) || $setupIntent->status === 'succeeded' || $setupIntent->status === 'canceled') {
            $setupIntent = $this->createTrdCouponsStripeSetupIntent([
                'customer' => $customer->id,
                'payment_method_types' => ['card'],
            ]);
            $branch = BusinessBranch::getDefault($businessId);
            BusinessBankAccount::create(['stripe_customer_id' => $customer->id,
                'stripe_setup_intent_id' => $setupIntent->id,
                'business_fk' => $businessId,
                'payment_provider_id' => $providerId,
                'modifiedby_fk' => $user->id,
                'timezone_mysql_fk' => $branch->branch_timezone_fk,
            ]);
            if (!$lastCreatedBankAccount->stripe_payment_method_id && $lastCreatedBankAccount->enabled == 1) {
                $lastCreatedBankAccount->update(['enabled' => 0]);
            }
        }

        $cardCollection = new ThirdPartyCouponCardCollection($bankAccounts);

        return [
            'stripe_api_publishable_key' => config('app.trd_stripe_api_publishable_key'),
            'setup_client_secret' => $setupIntent->client_secret,
            'user' => [
                'first' => $user->first,
                'last' => $user->last,
                'email' => $user->email
            ],
            'cards' => $cardCollection->response()->getData()->data,
        ];
    }

    public function deleteBusinessBankAccount(BusinessBankAccount $businessBankAccount)
    {
        if ($businessBankAccount->stripe_payment_method_id) {
            $this->deleteTrdCouponsStripePaymentMethod($businessBankAccount->stripe_payment_method_id);
        }
        $businessBankAccount->update(['enabled' => 0]);
    }
}