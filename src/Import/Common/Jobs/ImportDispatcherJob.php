<?php

namespace KangarooRewards\Import\Common\Jobs;

use Exception;
use App\Jobs\Job;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use KangarooRewards\Import\Common\Models\BatchImport;
use KangarooRewards\Import\Common\Repositories\BatchImportRepository;
use KangarooRewards\Import\Common\Traits\ImportTypeTrait;

class ImportDispatcherJob extends Job
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use ImportTypeTrait;

    protected BatchImportRepository $batchImportRepository;
    private BatchImport $batchImport;

    /**
     * @param BatchImport $batchImport
     * @param BatchImportRepository $batchImportRepository
     */
    public function __construct(BatchImport $batchImport, BatchImportRepository $batchImportRepository)
    {
        $this->batchImport = $batchImport;
        $this->batchImportRepository = $batchImportRepository;
    }

    /**
     * @return void
     */
    public function handle(): void
    {
        ini_set('memory_limit', '-1');

        try {
            // Get the related model by import type
            $model = $this->getTempModelByImportType($this->batchImport->type);

            // Process imported entries in chunks
            $chunkCounter = 1;

            do {
                // Get and update the process time for the imported entries
                $importedEntriesIds = $this->batchImportRepository->updateRecordsProcessTimeByType($model, $this->batchImport->id);

                Log::info(__METHOD__, ["message" => "Dispatching imported records started with chunks", "chunkNb" => $chunkCounter, "totalEntries" => count($importedEntriesIds), "batchId" => $this->batchImport->id]);

                if (!empty($importedEntriesIds)) {
                    $this->batchImportRepository->updateImportLogAndStatus($this->batchImport, null, [
                        'message' => 'Dispatching Started - '. $chunkCounter,
                        'count' => count($importedEntriesIds)
                    ]);

                    ImportProcessorJob::dispatch($this->batchImport, $this->batchImportRepository, $importedEntriesIds);

                    $this->batchImportRepository->updateImportLogAndStatus($this->batchImport, null, [
                        'message' => 'Dispatching Finished - '. $chunkCounter
                    ]);

                    $chunkCounter++;
                }
            } while (!empty($importedEntriesIds));
        } catch (Exception $e) {
            Log::error(__METHOD__, ['batchId' => $this->batchImport->id, 'message' => $e->getMessage()]);

            $this->batchImportRepository->updateImportLogAndStatus($this->batchImport, BATCH_IMPORT_STATUS_FAILED, [
                'message' => 'Error in Dispatching',
                'error' => $e->getMessage()
            ]);
        }

        $logMessage = "Peak memory usage: " . (memory_get_peak_usage(true) / 1024 / 1024) . " MB";
        info(__METHOD__, ['message' => $logMessage]);
    }
}