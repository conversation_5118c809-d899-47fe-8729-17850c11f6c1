<?php

namespace App\Traits;

// use App\Models\V1\BusinessTier;
// use App\Models\V1\BusinessTierSetup;

use App\Http\Resources\TemplateCollection;
use App\Http\Resources\TemplateResource;
use App\Models\V1\BusinessBranch;
use App\Models\Template;
use App\Models\V1\Business;
use App\Models\V1\BusinessProfile;
use App\Models\V1\BusinessRules;
use App\Models\V1\EmailProvider;
use App\Models\V1\Language;
use App\Models\V1\Translation;

// use App\Models\V1\Transaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Lang;
use Carbon\Carbon;
use RuntimeException;

trait TemplateTrait
{
    use CustomMessagesTrait;

    /**
     * @param $businessId
     * @param $sourceModule
     * @param $systemMessageType
     * @return mixed
     */
    public static function forBusiness($businessId, $sourceModule = null, $systemMessageType = null, $attributes = [])
    {
        $defaultBranch = BusinessBranch::getDefault($businessId);

        if (in_array($sourceModule, self::$customMsgTypes)) {
            $template = self::getCustomizedTemplateForBusiness($sourceModule, $defaultBranch->business_branch_pk);
            if (empty($template)) {
                return [];
            }
            return [$template];
        }

        $fields = [
            't.email_template_pk',
            't.name',
            't.email_subject',
            't.thumbnail',
            't.source_module',
            't.system_message_type',
            't.channel',
            't.is_default',
            't.enabled',
            't.business_branch_fk',
            't.utc_created',
            't.utc_updated'
        ];

        $enabled = 1;

        $query = Template::from('email_templates as t')
            ->select($fields)
            ->where('t.business_branch_fk', $defaultBranch->business_branch_pk)
            ->where('t.enabled', $enabled);

        if (!empty($sourceModule)) {
            $query->where('t.source_module', $sourceModule);
        }

        if (!empty($systemMessageType)) {
            $query->where('t.system_message_type', $systemMessageType);
        }

        if (!empty($attributes['channel'])) {
            $query->where('t.channel', $attributes['channel']);
        }

        return $query->get();
    }

    /**
     * @param integer|null $templateId
     * @param integer|null $sourceModule
     * @param integer|null $businessId
     * @return mixed
     */
    public static function specificTemplate($templateId, $businessId = null, $sourceModule = null)
    {
        if (in_array($sourceModule, self::$customMsgTypes) && !empty($businessId)) {
            $defaultBranch = BusinessBranch::getDefault($businessId);
            $template = self::getCustomizedTemplateForBusiness($sourceModule, $defaultBranch->business_branch_pk);
            if (empty($template)) {
                return null;
            }
            return $template;
        }

        $fields = [
            't.email_template_pk',
            't.name',
            't.template',
            't.thumbnail',
            't.email_subject',
            't.general_styles',
            't.template_data',
            't.source_module',
            't.system_message_type',
            't.channel',
            't.is_default',
            't.enabled',
            't.business_branch_fk',
            't.utc_created',
            't.utc_updated'
        ];

        $enabled = 1;

        $query = Template::from('email_templates as t')
            ->select($fields)
            ->where('t.email_template_pk', $templateId)
            ->where('t.enabled', $enabled);
        return $query->first();
    }

    /**
     * @param $sourceModules
     * @return mixed
     */
    public static function getCustomizedTemplateForBusiness($sourceModules, $businessBranchId)
    {
        $template = self::getBusinessCustomizedTemplate($businessBranchId, $sourceModules);

        if (empty($template)) {
            $template = self::getDefaultCustomizedTemplate($sourceModules);
        }

        return $template;
    }

    /**
     * @param $sourceModules
     * @return mixed
     */
    public static function getDefaultCustomizedTemplate($sourceModules)
    {
        $fields = [
            't.email_template_pk',
            't.name',
            't.template',
            't.thumbnail',
            't.general_styles',
            't.template_data',
            't.source_module',
            't.system_message_type',
            't.channel',
            't.is_default',
            't.enabled',
            't.business_branch_fk',
            't.utc_created',
            't.utc_updated',
            't.email_subject'
        ];

        $enabled = 1;

        $query = Template::from('email_templates as t')
            ->select($fields)
            ->where('t.is_default', 1)
            ->where('t.enabled', $enabled);

        if (is_array($sourceModules)) {
            $query = $query->whereIn('t.source_module', $sourceModules);
        } else {
            $query = $query->where('t.source_module', $sourceModules);
        }

        if (is_array($sourceModules)) {
            return $query->get()->toArray();
        }

        return $query->first();
    }

    /**
     * @param $templateId
     * @return mixed
     */
    public static function getBusinessCustomizedTemplate($businessBranchId, $sourceModules = [])
    {
        $fields = [
            't.email_template_pk',
            't.name',
            't.template',
            't.thumbnail',
            't.general_styles',
            't.template_data',
            't.source_module',
            't.system_message_type',
            't.channel',
            't.is_default',
            't.enabled',
            't.business_branch_fk',
            't.utc_created',
            't.utc_updated',
            't.email_subject'
        ];

        $enabled = 1;

        $query = Template::from('email_templates as t')
            ->select($fields)
            ->where('t.business_branch_fk', $businessBranchId)
            ->where('t.enabled', $enabled);

        if (is_array($sourceModules)) {
            $query = $query->whereIn('t.source_module', $sourceModules);
        } else {
            $query = $query->where('t.source_module', $sourceModules);
        }

        if (is_array($sourceModules)) {
            return $query->get()->toArray();
        }

        return $query->first();
    }


    /**
     * @param $sourceModules
     * @param $businessBranchId
     * @return mixed
     */
    public function getMultipleCustomizedTemplatesForBusiness($sourceModules, $businessBranchId)
    {
        if (!is_array($sourceModules)) {
            $sourceModulesArray = [$sourceModules];
        } else {
            $sourceModulesArray = $sourceModules;
        }

        if (count($sourceModulesArray) <= 1) {
            $fields = [
                't.email_template_pk',
                't.name',
                't.email_subject',
                't.template',
                't.thumbnail',
                't.general_styles',
                't.template_data',
                't.source_module',
                't.system_message_type',
                't.channel',
                't.is_default',
                't.enabled',
                't.business_branch_fk',
                't.utc_created',
                't.utc_updated'
            ];
        } else {
            $fields = [
                't.email_template_pk',
                't.name',
                't.email_subject',
                't.thumbnail',
                't.source_module',
                't.system_message_type',
                't.channel',
                't.is_default',
                't.enabled',
                't.business_branch_fk',
                't.utc_created',
                't.utc_updated'
            ];
        }

        $enabled = 1;

        $query = Template::from('email_templates as t')
            ->select($fields)
            ->where('t.business_branch_fk', $businessBranchId)
            ->whereIn('t.source_module', $sourceModulesArray)
            ->where('t.enabled', $enabled);

        foreach ($sourceModulesArray as $sourceModule) {
            if (!in_array($sourceModule, self::$customMsgTypes)) {
                throw new \Exception("Template of module: " . $sourceModule . " cannot be customized");
            }
            $query = $query->union(self::buildDefaultCustomTemplatesQuery($sourceModule, $businessBranchId, $fields));
        }

        if (!is_array($sourceModules)) {
            return $query->first();
        }

        return $query->get();
    }

    private static function buildDefaultCustomTemplatesQuery($sourceModule, $businessBranchId, $fields)
    {
        $enabled = 1;

        $query = Template::from('email_templates as t')
            ->select($fields)
            ->where('t.is_default', 1)
            ->where('t.enabled', 1)
            ->where('source_module', $sourceModule)
            ->whereNotExists(function ($query) use ($enabled, $sourceModule, $businessBranchId) {
                $query->select(DB::raw(1))
                    ->from('email_templates')
                    ->where('business_branch_fk', $businessBranchId)
                    ->where('source_module', $sourceModule)
                    ->where('enabled', $enabled);
            });

        return $query;
    }

    public static function createTemplate($business, array $data): Template
    {
        $defaultBranch = BusinessBranch::getDefault($business->business_pk);

        $data['name'] = isset($data['name']) ? htmlspecialchars($data['name']) : null;
        $data['email_subject'] = isset($data['email_subject']) ? htmlspecialchars($data['email_subject']) : null;
        $data['template'] = htmlspecialchars(trim(stripslashes($data['template'])));
        $data['general_styles'] = isset($data['general_styles']) ? json_encode($data['general_styles']) : null;
        $data['template_data'] = isset($data['template_data']) ? json_encode($data['template_data']) : null;
        $data['source_module'] = isset($data['source_module']) ? htmlspecialchars($data['source_module']) : 'marketing';
        $data['thumbnail'] = isset($data['thumbnail']) ? htmlspecialchars($data['thumbnail']) : null;
        $data['system_message_type'] = isset($data['system_message_type']) ? (int)$data['system_message_type'] : 2;
        $data['default'] = isset($data['default']) ? (int)$data['default'] : 0;

        $template = new Template();

        $template->name = $data['name'];
        $template->email_subject = $data['email_subject'];
        $template->template = $data['template'];
        $template->general_styles = $data['general_styles'];
        $template->template_data = $data['template_data'];
        $template->thumbnail = $data['thumbnail'];
        $template->business_branch_fk = $defaultBranch->business_branch_pk;
        $template->enabled = 1;
        $template->source_module = $data['source_module'];
        $template->system_message_type = $data['system_message_type'];
        $template->channel = $data['channel'] ?? 1;
        $template->is_default = $data['default'];

        $template->utc_created = Carbon::now();

        $template->save();

        return $template;
    }

    public static function updateTemplate(Template $template, $business, array $data): Template
    {
        $data['name'] = isset($data['name']) ? e($data['name']) : $template->name;
        $data['email_subject'] = isset($data['email_subject']) ? e($data['email_subject']) : $template->email_subject;
        $data['template'] = isset($data['template']) ? e(trim(stripslashes($data['template']))) : $template->template;
        $data['general_styles'] = isset($data['general_styles']) ? json_encode(
            $data['general_styles']
        ) : $template->general_styles;
        $data['template_data'] = isset($data['template_data']) ? json_encode(
            $data['template_data']
        ) : $template->template_data;
        $data['thumbnail'] = isset($data['thumbnail']) ? e($data['thumbnail']) : $template->thumbnail;

        $template->name = $data['name'];
        $template->email_subject = $data['email_subject'];
        $template->template = $data['template'];
        $template->general_styles = $data['general_styles'];
        $template->template_data = $data['template_data'];
        $template->thumbnail = $data['thumbnail'];

        $template->save();

        return $template;
    }

    public static function createOrUpdateCustomTemplate($template, $business, array $data): Template
    {
        $defaultBranch = BusinessBranch::getDefault($business->business_pk);

        $data['source_module'] = $template->source_module;
        $data['system_message_type'] = $template->system_message_type;
        $data['channel'] = $template->channel;
        $data['default'] = 0;
        $data['business_branch_fk'] = $defaultBranch->business_branch_pk;

        if ($template->business_branch_fk != $defaultBranch->business_branch_pk) {
            $template = self::createTemplate($business, $data);
        } else {
            $template = self::updateTemplate($template, $business, $data);
        }

        return $template;
    }

    public static function deleteTemplate(Template $template): void
    {
        $template->enabled = 0;
        $template->save();
    }

    public function updateTemplateTranslations($request, $translations, $templateId, $businessId)
    {
        $translationsArray = [];
        $langId = $request->lang_id;
        $fields = $request->fields;

        $sanitizationArray = ['template', 'email_subject', 'thumbnail'];

        $translationsArray[$langId] = [];
        foreach ($fields as $field_key => $field_value) {
            if(!empty($field_value) && in_array($field_key, $sanitizationArray)){
                $field_value = htmlspecialchars($field_value);
            }

            $translation = TranslationTrait::saveTranslation(
                $langId,
                'email_templates',
                $field_key,
                $templateId,
                $businessId,
                $field_value
            );

            $translationsArray[$langId][$translation->field_name] = $translation->value;
        }

        return $translationsArray;
    }

    private static function createTemplateTranslation($templateId, $businessId, $langId, $field_key, $field_value)
    {
        $templateTranslation = new Translation();
        $templateTranslation->resource_id = $templateId;
        $templateTranslation->resource_name = 'email_templates';
        $templateTranslation->field_name = $field_key;
        $templateTranslation->value = trim(stripslashes($field_value));
        $templateTranslation->language_fk = $langId;
        $templateTranslation->business_fk = $businessId;

        $templateTranslation->save();

        return $templateTranslation;
    }

    private static function updateTemplateTranslation($templateId, $businessId, $langId, $field_key, $field_value)
    {
        $templateTranslation = Translation::getTranslationField(
            $langId,
            'email_templates',
            $field_key,
            $templateId,
            $businessId
        );

        if ($businessId !== $templateTranslation->business_fk) {
            throw new RuntimeException('Translation could not be found');
        }

        $templateTranslation->value = !empty($field_value) ? trim(
            stripslashes($field_value)
        ) : $templateTranslation->value;

        $templateTranslation->save();

        return $templateTranslation;
    }

    public static function getModuleVariables($template, $business)
    {
        if(in_array($template->source_module, self::$customMsgTypes)){
            return self::getCustomModuleVariables($template, $business, false);
        }
        
        return [];
    }

    /**
     * Get template languages (language info)
     *
     * @param $template
     * @return array
     * <AUTHOR>
     */
    public static function getTemplateLanguages($templateModel, $business): array
    {
        $templateTranslations = Translation::getAllTemplateTranslations($templateModel->email_template_pk);

        if(empty($templateTranslations)){
            return [];
        }

        $defaultLangId = 1;

        $templateLanguages[] = self::transformTemplateLanguage(
            $defaultLangId,
            $templateModel,
            $business,
            $templateModel
        );

        //Append the other translations for the tier
        foreach ($templateTranslations as $langId => $translation) {
            $templateLanguages[] = self::transformTemplateLanguage($langId, $translation, $business, $templateModel);
        }

        return $templateLanguages;
    }

    /**
     * Transform Template language
     *
     * @param int $langId
     * @param mixed $template
     * @return array
     * <AUTHOR>
     */
    private static function transformTemplateLanguage(
        $langId,
        $template,
        $business,
        $templateModel,
        $defaultTemplate = null
    ): array {
        $languages = Language::getAllLanguages();
        
        return [
            'language_id' => $langId,
            'template' => self::replaceBlocks($templateModel, $business, $template['template'], $langId),
            'template_data' => $template['template_data'] ?? null,
            'thumbnail' => isset($template['thumbnail']) ? (string) htmlspecialchars_decode($template['thumbnail']) : null,
            'email_subject' => isset($template['email_subject']) ? (string) htmlspecialchars_decode($template['email_subject']) : null,
            'language' => [
                'id' => $langId,
                'abbreviation' => $languages[$langId]['abreviation'],
                'name' => $languages[$langId]['name']
            ]
        ];
    }

    public function getBusinessTemplateForPagination($businessId, $conditions)
    {
        $defaultBranch = BusinessBranch::getDefault($businessId);
        $defaultBranchId = $defaultBranch->business_branch_pk;

        $fields = [
            'email_template_pk',
            'name',
            'email_subject',
            'template',
            'thumbnail',
            'general_styles',
            'template_data',
            'source_module',
            'system_message_type',
            'channel',
            'is_default',
            'enabled',
            'business_branch_fk',
            'utc_created',
            'utc_updated'
        ];

        // Default templates with no source_module
        $defaultNoSource = Template::select($fields)
            ->where('is_default', 1)
            ->whereNull('source_module')
            ->where('enabled', 1);
        $this->appendBusinessTemplateForPaginationConditions($conditions, $defaultNoSource);

        // Custom templates with no source_module
        $customNoSource = Template::select($fields)
            ->where('is_default', 0)
            ->whereNull('source_module')
            ->where('business_branch_fk', $defaultBranchId)
            ->where('enabled', 1);
        $this->appendBusinessTemplateForPaginationConditions($conditions, $customNoSource);

        // Custom templates with source_module module, grouped by source_module and returning the latest one
        $customWithSourceIds = Template::select(DB::raw('MAX(email_template_pk) as id'))
            ->where('is_default', 0)
            ->whereNotNull('source_module')
            ->where('business_branch_fk', $defaultBranchId)
            ->where('enabled', 1)
            ->groupBy('source_module');
        $this->appendBusinessTemplateForPaginationConditions($conditions, $customWithSourceIds);

        $customWithSource = Template::select($fields)
            ->whereIn('email_template_pk', $customWithSourceIds);

        // custom source_module
        $customSourceModules = Template::select('source_module')
            ->where('is_default', 0)
            ->whereNotNull('source_module')
            ->where('business_branch_fk', $defaultBranchId)
            ->where('enabled', 1)
            ->distinct();
        $this->appendBusinessTemplateForPaginationConditions($conditions, $customSourceModules);

        // Default templates with source_module, excluding those with the same source_module in custom templates
        $defaultWithSourceIds = Template::select(DB::raw('MAX(email_template_pk) as id'))
            ->where('is_default', 1)
            ->whereNotNull('source_module')
            ->where('enabled', 1)
            ->whereNotIn('source_module', $customSourceModules)
            ->groupBy('source_module');
        $this->appendBusinessTemplateForPaginationConditions($conditions, $defaultWithSourceIds);

        $defaultWithSource = Template::select($fields)
            ->whereIn('email_template_pk', $defaultWithSourceIds);


        // Union all queries
        $query = $defaultNoSource
            ->unionAll($defaultWithSource)
            ->unionAll($customNoSource)
            ->unionAll($customWithSource);


        foreach ($conditions as $condition) {
            if ($condition['type'] === 'WHERE') {
                $query->where($condition['column'], $condition['op'], $condition['value']);
            } elseif ($condition['type'] === 'IN') {
                $query->whereIn($condition['column'], $condition['value']);
            }
        }

        return $query;
    }

    private function appendBusinessTemplateForPaginationConditions($conditions, &$query):void
    {
        foreach ($conditions as $condition) {
            if ($condition['type'] === 'WHERE') {
                $query->where($condition['column'], $condition['op'], $condition['value']);
            } elseif ($condition['type'] === 'IN') {
                $query->whereIn($condition['column'], $condition['value']);
            }
        }
    }
}
