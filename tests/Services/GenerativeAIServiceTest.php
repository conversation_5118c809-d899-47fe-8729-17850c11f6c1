<?php

namespace Tests\Services;

use App\Models\V1\Business;
use App\Services\GenAIServiceFactory;
use App\Services\GenAIServiceInterface;
use App\Services\GenerativeAIService;
use Mockery;
use ReflectionClass;
use Tests\TestCase;

/**
 * php ./vendor/bin/phpunit tests/Services/GenerativeAIServiceTest.php
 */
class GenerativeAIServiceTest extends TestCase
{
    private $business;

    public function setUp(): void
    {
        parent::setUp();
        $this->business = Business::findByPk(15);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testGenerateWithSubjectLineTypeBasicInput(): void
    {
        $input = [
            'type' => 'subject_line',
            'title' => 'Summer Sale',
            'description' => 'Get 50% off',
            'n' => 3
        ];

        // Mock the AI response
        $service = $this->createServiceWithMockedAI($input, [
            '1. Test Subject 1',
            '2. Test Subject 2',
            '3. Test Subject 3',
        ]);

        //$service->generate();
        //$results = $service->getResults();

        // Use reflection to check the final prompt
        $reflection = new \ReflectionClass($service);
        $inputProperty = $reflection->getProperty('input');
        $inputProperty->setAccessible(true);
        $finalInput = $inputProperty->getValue($service);

        $this->assertEquals('Summer Sale. Get 50% off', $finalInput['prompt']);
        $this->assertArrayNotHasKey('description', $finalInput);
    }

    /**
     * @dataProvider inputProvider
     */
    public function testRemoveNumbersAndQuotes($input, $expectedOutput): void
    {
        $data = ['title' => 'Test title', 'description' => 'Test description'];

        $instance = new GenerativeAIService($this->business, $data);

        // Use reflection to access the private method
        $reflectionClass = new ReflectionClass(GenerativeAIService::class);
        $method = $reflectionClass->getMethod('removeNumbersAndQuotes');
        $method->setAccessible(true);

        // Invoke the private method with the provided input
        $result = $method->invoke($instance, $input);

        // Assert the result matches the expected output
        $this->assertEquals($expectedOutput, $result);
    }
    
    

    /*public function testExtractMessagesWithPreAndPostText(): void
    {
        $business = Business::findByPk(15);
        $data = ['title' => 'Test title', 'description' => 'Test description'];

        $instance = new GenerativeAIService($business, $data);

        // Use reflection to access the private method
        $reflectionClass = new ReflectionClass(GenerativeAIService::class);
        $method = $reflectionClass->getMethod('extractMessages');
        $method->setAccessible(true);

        $input = 'Sure! Here are five catchy email subject lines for your store-wide sale featuring a 30% off promotion on blouses:

1. "👗 Unwrap 30% Off All Blouses – Your Wardrobe Awaits!"
2. "Limited Time Only: 30% Off Every Blouse in Store!"
3. "Blouse Bonanza: Grab 30% Off Storewide Today!"
4. "Refresh Your Style with 30% Off All Blouses – Shop Now!"
5. "Don\'t Miss Out: 30% Off Every Blouse Just for You!"

Feel free to mix and match or tweak them to better suit your brand voice!';

        $expectedOutput = [];

        // Invoke the private method with the provided input
        $result = $method->invoke($instance, $input);

        dd($result);
        // Assert the result matches the expected output
        //$this->assertEquals($expectedOutput, $result);
    }*/

    private function createServiceWithMockedAI(array $input, array $responses, bool $shouldFail = false)
    {
        // Create the service first
        $service = new GenerativeAIService($this->business, $input);

        // Create mock AI service
        $mockAIService = Mockery::mock(GenAIServiceInterface::class);

        if ($shouldFail) {
            $mockAIService->shouldReceive('chatCompletions')
                ->once()
                ->withAnyArgs() // Accept any number of arguments
                ->andReturn(null);
        } else {
            foreach ($responses as $response) {
                $mockChat = (object) ['message' => $response];
                $mockAIService->shouldReceive('chatCompletions')
                    ->once()
                    ->withAnyArgs() // Accept any number of arguments
                    ->andReturn($mockChat);
            }
        }

        // Use reflection to inject the mocked service
        $reflection = new \ReflectionClass($service);
        $genAIServiceProperty = $reflection->getProperty('genAIService');
        $genAIServiceProperty->setAccessible(true);
        $genAIServiceProperty->setValue($service, $mockAIService);

        return $service;
    }


    private function mockGenAIService(array $responses)
    {
        $mockService = $this->createMock(GenAIServiceInterface::class);

        foreach ($responses as $index => $response) {
            $mockChat = (object) ['message' => $response];
            $mockService->expects($this->at($index))
                ->method('chatCompletions')
                ->willReturn($mockChat);
        }

        // Mock the factory to return our mock service
        $this->app->bind(GenAIServiceInterface::class, function () use ($mockService) {
            return $mockService;
        });

        // Override the GenAIServiceFactory
        GenAIServiceFactory::shouldReceive('createService')
            ->andReturn($mockService);
    }

    private function mockGenAIServiceFailure()
    {
        $mockService = $this->createMock(GenAIServiceInterface::class);
        $mockService->expects($this->once())
            ->method('chatCompletions')
            ->willReturn(null);

        $this->app->bind(GenAIServiceInterface::class, function () use ($mockService) {
            return $mockService;
        });

        GenAIServiceFactory::shouldReceive('createService')
            ->andReturn($mockService);
    }

    public function inputProvider(): array
    {
        return [
            ['"This is a string with quotes"', 'This is a string with quotes'],
            ['"This is a string with quotes and a "word" in quotes"', 'This is a string with quotes and a word in quotes'],
            ['1) Remove numbers at the beginning', 'Remove numbers at the beginning'],
            ['2) Remove numbers at the beginning', 'Remove numbers at the beginning'],
            ['123) Remove numbers at the beginning', 'Remove numbers at the beginning'],
            ['1. Another string with numbers', 'Another string with numbers'],
            ['2. Another string with numbers', 'Another string with numbers'],
            ['15. Another string with numbers', 'Another string with numbers'],
            ['A string without numbers', 'A string without numbers'],
            ['A string without quotes', 'A string without quotes'],
            ['Special characters !@#$%^&*()_+{}[]|;:,.<>?/', 'Special characters !@#$%^&*()_+{}[]|;:,.<>?/'],
            ['Multiline string with numbers and quotes' . PHP_EOL . '12345', 'Multiline string with numbers and quotes' . PHP_EOL . '12345'],
            ['String with mixed special characters: @#12^&*', 'String with mixed special characters: @#12^&*'],
            [' String with space at the beginning and end ', 'String with space at the beginning and end'],
            ['Mixed case string WITH nUmBeRs AnD QuOtEs', 'Mixed case string WITH nUmBeRs AnD QuOtEs'],
            ['Unicode characters: 漢字 Русский αβγ', 'Unicode characters: 漢字 Русский αβγ'],
            ['', ''], // empty string
            ['5) Sh', 'Sh'], // Short string
            ['Sh', 'Sh'], // Short string
            ['A', 'A'], // Short string
            ['1)', ''],
            ['1) ', ''],
            ['1. ', ''],
        ];
    }
}
