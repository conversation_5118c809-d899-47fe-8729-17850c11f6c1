<?php

namespace App\Traits;

use App\Models\GptChatCompletion;
use App\Models\MarketingCreditHistory;
use App\Models\V1\Campaign;
use App\Models\V1\EmailSmsQueue;
use App\Models\V1\EntityMktgPackageCredit;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

trait CampaignTrait
{
    use MarketingCreditHistoryTrait;
    use EntityMktgPackageCreditTrait;

    public function listCampaignAnalysisReportsPaginated(int $campaignId): EloquentBuilder | QueryBuilder
    {
        return GptChatCompletion::select(['id', 'message', 'created_at', 'entity_campaign_fk'])
            ->where('entity_campaign_fk', $campaignId)
            ->orderBy('id', 'desc');
    }

    public function systemListAllCampaignAnalysisReports(array $include = []): EloquentBuilder | QueryBuilder
    {
        $query = GptChatCompletion::select(['id', 'message', 'created_at', 'entity_campaign_fk'])
            ->where('entity_campaign_fk', '>', 0)
            ->orderBy('id', 'desc');

        if (in_array('campaign', $include, true)) {
            $query->with('campaign');
        }

        return $query;
    }

    public function canGenerateAnalysisReport(Campaign $campaign): bool
    {
        if (
            $campaign->enabled === 0 && $campaign->auto === 0 && $campaign->canceled === 0
            && $campaign->draft === 0 && $campaign->silent === 0 && $campaign->status == 'QUEUE_COMPLETED'
            && $campaign->utc_end < now()->subDays(5)
        ) {
            return true;
        }
        return false;
    }

    public function getCampaignsToAnalyze()
    {
        $finishedCampaigns = $this->getFinishedCampaigns();

        // Exclude campaigns that have already been analyzed
        $analyzedCampaignIds = GptChatCompletion::select(['entity_campaign_fk'])
            ->whereIn('entity_campaign_fk', $finishedCampaigns->pluck('entity_campaign_pk'))
            ->pluck('entity_campaign_fk');

        return $finishedCampaigns->whereNotIn('entity_campaign_pk', $analyzedCampaignIds);
    }

    /**
     * Retrieve a list of campaigns that finished more than 5 days ago
     * <AUTHOR>
     * @return array|Collection
     */
    public function getFinishedCampaigns(): array|Collection
    {
        // fetch all campaigns finished more than 5 days ago and generate analysis reports for each
        return Campaign::select(['entity_campaign_pk', 'business_fk', 'campaign_name'])
            ->where('enabled', 0) // finished
            ->where('auto', 0) // not auto
            ->where('canceled', 0) // not canceled
            ->where('draft', 0) // not draft
            ->where('silent', 0) // not silent
            ->whereNotNull('utc_end')
            ->where('utc_created', '>=', now()->subDays(30))
            ->where('utc_end', '<', now()->subDays(5)) // Finished more than 5 days ago
            ->where('status', 'QUEUE_COMPLETED')
            ->get();
    }

    /**
     * list business campaigns
     * @param $businessId
     * @param $perPage
     * @param $page
     * @param null $calendarDates
     * @return mixed
     */
    public function listCampaignsForBusiness($businessId, $perPage, $page, $calendarDates = null, $conditions = [], $sorting = []): mixed
    {
        $query = Campaign::where('business_fk', $businessId)
            ->where('silent', 0);

        if (!empty($calendarDates)) {
            $query->where('utc_start', '<=', $calendarDates['endDate'])
                ->whereNull('utc_end')
                ->where('canceled', 0)
                ->where('draft', 0)
                ->where(function ($query) use ($calendarDates) {
                    return $query->where(function ($query) use ($calendarDates) {
                        return $query->whereNull('expiry_date')
                        ->where('utc_start', '>=', $calendarDates['startDate'])
                        ->where('auto', 0);
                    })->orWhere(function ($query) use ($calendarDates) {
                        return $query->where(function ($query) use ($calendarDates) {
                            return $query->where('expiry_date', '>=', $calendarDates['startDate'])
                            ->orWhereNull('expiry_date');
                        })->where('auto', 1);
                    });
                });
        }

        foreach ($conditions as $condition) {
            $condition['value'] = is_string($condition['value']) ? e($condition['value']) : $condition['value'];
            if ($condition['type'] === 'WHERE') {
                $query->where($condition['column'], $condition['op'], $condition['value']);
            } elseif ($condition['type'] === 'IN') {
                $query->whereIn($condition['column'], $condition['value']);
            } elseif ($condition['type'] === 'BETWEEN') {
                $query->whereBetween($condition['column'], $condition['value']);
            }
        }


        if (!empty($sorting)) {
            foreach ($sorting as $sort) {
                if ($sort['field'] === 'created_at') {
                    $sort['field'] = 'utc_created';
                } elseif ($sort['field'] === 'end_at') {
                    $sort['field'] = 'utc_end';
                }

                $query->orderBy($sort['field'], $sort['order']);
            }
        } else {
            $query->orderBy('utc_created', 'desc');
        }
//        info(__METHOD__, ['sort' => $sorting, 'query' => $query->toSql(), 'bindings' => $query->getBindings()]);
        if (empty($calendarDates)) {
            $campaigns = $query->paginate($perPage, ['*'], 'page', $page);
        } else {
            $campaigns = $query->get();
        }

        return $campaigns;
    }

        /**
     * list business campaigns
     * @param $businessId
     * @param $perPage
     * @param $page
     * @return mixed
     */
    public function listCampaignsForBusinessAll($businessId): mixed
    {
        return Campaign::where('business_fk', $businessId)
            ->where('silent', 0);
    }

    /**
     * Cancel campaign
     * @param Campaign $campaign
     * @return bool
     * @throws \Throwable
     */
    public function cancelCampaign(Campaign $campaign): bool
    {
        DB::connection('tenant')->beginTransaction();
        try {
            if ($campaign->canceled == 0 && $campaign->auto == 0) {
                // Get campaign package credit
                $credit = EntityMktgPackageCredit::find($campaign->entity_mktg_package_credit_fk);

                // Return back the email, sms, push credits
                $this->refundCampaignCredits($credit, $campaign);

                // Store the campaign credits
                $this->recordCreditsHistory($credit, $campaign);
            }

            // Cancel and disable the campaign
            $campaign->enabled = 0;
            $campaign->canceled = 1;
            $campaign->save();

            DB::connection('tenant')->commit();

            return true;
        } catch (\Exception $e) {
            DB::connection('tenant')->rollBack();

            Log::error(__METHOD__, [
                'campaignId' => $campaign->entity_campaign_pk,
                'business' => $campaign->business_fk,
                'e' => $e
            ]);

            return false;
        }
    }

    /**
     * Cancel the campaign linked to the offer / reward
     * @param $campaignId
     * @return bool
     * @throws \Throwable
     */
    public function cancelCampaignByOfferOrReward($campaignId): bool
    {
        if (!is_null($campaignId)) {
            $campaign = Campaign::find($campaignId);

            if ($campaign) {
                return $this->cancelCampaign($campaign);
            }
        }

        return true;
    }

    /**
     * Get campaign by ID and business ID
     * @param $campaignId
     * @param $businessId
     * @return mixed
     */
    public function getCampaignByBusiness($campaignId, $businessId): mixed
    {
        return Campaign::where([
            'entity_campaign_pk' => $campaignId,
            'business_fk' => $businessId
        ])->first();
    }

    /**
     * Retrieve a list of auto campaigns grouped by their group_id, returning the most recently created campaign for each group
     * @param $businessId
     * @param $page
     * @param $perPage
     * @return LengthAwarePaginator
     */
    public function fetchGroupedAutoCampaigns($businessId, $page, $perPage): LengthAwarePaginator
    {
        $subQuery = DB::table('entity_campaign')
            ->select(DB::raw('group_id, MAX(entity_campaign_pk) as max_entity_campaign_pk'))
            ->where('business_fk', $businessId)
            ->where('auto', 1)
            ->where('draft', 0)
            ->groupBy('group_id');

        // Main query to fetch the latest campaigns using the sub-query
        return  Campaign::from('entity_campaign as c1')
            ->joinSub($subQuery, 'subquery', function ($join) {
                $join->on('c1.entity_campaign_pk', '=', 'subquery.max_entity_campaign_pk');
            })
            ->where('c1.business_fk', $businessId)
            ->where('c1.auto', 1)
            ->where('c1.draft', 0)
            ->orderBy('c1.entity_campaign_pk', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * Get list of campaign IDs by group ID
     * @param $groupId
     * @param $businessId
     * @return mixed
     */
    public function getCampaignIdsByGroup($groupId, $businessId): mixed
    {
        return Campaign::select('entity_campaign_pk')
            ->where([
                'group_id' => $groupId,
                'business_fk' => $businessId
            ])->pluck('entity_campaign_pk')
            ->toArray();
    }

    /**
     *  Retrieves a paginated list of campaign customers with their user details and email/SMS queue statistics, filtering by date range.
     * @param array $campaignIds
     * @param string $startDate
     * @param string $endDate
     * @param int $perPage
     * @param int $page
     * @param array|null $sort
     * @return mixed
     */
    public function getGroupedCampaignCustomers(array $campaignIds, string $startDate, string $endDate, int $perPage, int $page, ?array $sort = []): mixed
    {
        // Parse date into proper format
        $startDateTime = Carbon::parse($startDate)->format('Y-m-d H:i:s');
        $endDateTime = Carbon::parse($endDate)->format('Y-m-d H:i:s');

        $query = EmailSmsQueue::select([
                'user.user_uid',
                'user.username',
                'user.first',
                'user.last',
                'user.phone',
                'user.email',
                'email_sms_queue.utc_created',
                'email_sms_queue.utc_sent',
                'email_sms_queue.utc_updated',
                'email_sms_queue.send_to',
                'email_sms_queue.cell_email',
                'email_sms_queue.email_sms_status_fk',
                'stats_campaign_queues.processed',
                'stats_campaign_queues.dropped',
                'stats_campaign_queues.delivered',
                'stats_campaign_queues.deferred',
                'stats_campaign_queues.open',
                'stats_campaign_queues.click',
                'stats_campaign_queues.spam',
                'stats_campaign_queues.sms_unsubscribed',
                'stats_campaign_queues.email_unsubscribed',
            ])
            ->join('user', 'user.id', '=', 'email_sms_queue.user_fk')
            ->leftJoin('stats_campaign_queues', 'email_sms_queue.email_sms_queue_pk', '=', 'stats_campaign_queues.email_sms_queue_fk')
            ->whereIn('email_sms_queue.entity_campaign_fk', $campaignIds)
            ->whereDate('email_sms_queue.utc_created', '>=', $startDateTime)
            ->whereDate('email_sms_queue.utc_created', '<=', $endDateTime);

        if (!empty($sort)) {
            $query->orderBy($sort['field'], strtolower($sort['direction']));
        } else {
            $query->orderBy('email_sms_queue.email_sms_queue_pk', 'DESC');
        }

        return $query->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * @param array $campaignIds
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public function getCampaignGroupSummary(array $campaignIds, string $startDate, string $endDate): array
    {
        // Convert start_date and end_date to datetime for proper comparison
        $startDateTime = Carbon::parse($startDate)->format('Y-m-d H:i:s');
        $endDateTime = Carbon::parse($endDate)->format('Y-m-d H:i:s');

        // Now get the aggregated stats for all campaigns in this group
        $stats = DB::table('entity_campaign as ec')
            ->join('entity_mktg_package_credit as empc', 'empc.entity_mktg_package_credit_pk', '=', 'ec.entity_mktg_package_credit_fk')
            ->join('entity_mktg_package as emp', 'emp.entity_mktg_package_pk', '=', 'empc.entity_mktg_package_fk')
            ->leftJoin('stats_campaigns as stats', 'stats.entity_campaign_fk', '=', 'ec.entity_campaign_pk')
            ->whereIn('ec.entity_campaign_pk', $campaignIds)
            ->where('ec.enabled', 0)
            ->whereBetween('ec.utc_start', [$startDateTime, $endDateTime])
            ->select([
                DB::raw('GROUP_CONCAT(DISTINCT ec.entity_campaign_pk) as campaign_ids'),
                DB::raw('GROUP_CONCAT(DISTINCT ec.campaign_name) as campaign_names'),
                DB::raw('SUM(ec.sent_sms) as sent_sms'),
                DB::raw('SUM(ec.sent_email) as sent_email'),
                DB::raw('SUM(ec.sent_ios_push_notif) as sent_ios_push_notif'),
                DB::raw('SUM(ec.sent_android_push_notif) as sent_android_push_notif'),
                DB::raw('MIN(ec.utc_start) as start_date'),
                DB::raw('MAX(ec.utc_end) as end_date'),
                DB::raw('MAX(ec.utc_updated) as last_updated'),
                DB::raw('SUM(ec.sent_sms + ec.sent_email + ec.sent_ios_push_notif + ec.sent_android_push_notif) as total_targeted'),
                DB::raw('SUM(stats.email_processed) as email_processed'),
                DB::raw('SUM(stats.email_drops) as email_drops'),
                DB::raw('SUM(stats.email_delivered) as email_delivered'),
                DB::raw('SUM(stats.email_deferred) as email_deferred'),
                DB::raw('SUM(stats.email_opens) as email_opens'),
                DB::raw('SUM(stats.email_clicks) as email_clicks'),
                DB::raw('SUM(stats.email_spam_reports) as email_spam_reports'),
                DB::raw('SUM(stats.email_failed) as email_failed'),
                DB::raw('SUM(stats.sms_delivered) as sms_delivered'),
                DB::raw('SUM(stats.sms_clicks) as sms_clicks'),
                DB::raw('SUM(stats.sms_failed) as sms_failed'),
                DB::raw('SUM(stats.users_purchase) as users_purchase'),
                DB::raw('SUM(stats.amount_users_purchase) as amount_users_purchase'),
                DB::raw('SUM(stats.amount_users_purchase_email) as amount_users_purchase_email'),
                DB::raw('SUM(stats.total_purchase_amount) as total_purchase_amount'),
                DB::raw('SUM(stats.total_visits) as total_visits'),
                DB::raw('SUM(stats.users_coupon_redeem) as users_coupon_redeem'),
                DB::raw('SUM(stats.email_delivered + stats.sms_delivered) as total_delivered'),
                DB::raw('SUM(stats.email_opens + stats.sms_clicks) as total_engaged'),
                DB::raw('SUM(stats.email_failed + stats.sms_failed) as total_failed'),
                DB::raw('SUM(ec.sent_ios_push_notif + ec.sent_android_push_notif) as total_push'),
                DB::raw('0 as push_clicks'),
                DB::raw('0 as push_opens'),
                DB::raw('0 as push_failed'),
                DB::raw('SUM(stats.email_unsubscribed) as email_unsubscribed'),
                DB::raw('SUM(stats.sms_unsubscribed) as sms_unsubscribed'),
                DB::raw('SUM(stats.email_unsubscribed + stats.sms_unsubscribed) as total_unsubscribed'),
                'ec.group_id',
            ])
            ->first();

        return $stats ? (array)$stats : [];
    }

    /**
     * @param $businessId
     * @param $page
     * @param $perPage
     * @return LengthAwarePaginator
     */
    public function getLiveGeofencedCampaigns($businessId, $page, $perPage): LengthAwarePaginator
    {
        return Campaign::from('entity_campaign as c')
            ->where('c.business_fk', $businessId)
            ->where('c.silent', 1)
            ->where('c.audience_type', CAMPAIGN_AUDIENCE_GEOFENCE_CUSTOMERS_TYPE)
            ->where('c.draft', 0)
            ->orderBy('c.entity_campaign_pk', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);
    }
}
