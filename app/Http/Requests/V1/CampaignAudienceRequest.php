<?php

namespace App\Http\Requests\V1;

use App\Models\V1\Business;
use App\Models\V1\Campaign;
use App\Models\V1\PosBranch;
use App\Rules\CommaValuesValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\App;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class CampaignAudienceRequest extends FormRequest
{
    private $lang;
    private $business;

    public function authorize(): bool
    {
        $whoWillReceive = (array)($this->input('whoWillReceive') ?? []);
        $business = $this->getBusiness();

        if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_CAMPAIGNED_OFFERS_REDEMPTIONS, $whoWillReceive)) {
            if (empty($this->input('campaignedOffersRedemptionsCampaignIds'))) {
                return true;
            }

            $campaignIds = (array)$this->input('campaignedOffersRedemptionsCampaignIds');
            $campaignModels = Campaign::select(['entity_campaign_pk', 'business_fk'])
                ->whereIn('entity_campaign_pk', $campaignIds)
                ->get();
            foreach ($campaignModels as $campaign) {
                if ($campaign->business_fk !== (int)$business->business_pk) {
                    return false;
                }
            }
        }
        return true;
    }

    private function getBusiness()
    {
        if (!$this->business) {
            $user = $this->user();
            $this->business = Business::forUser($user);
        }

        return $this->business;
    }

    public function setBusiness($business)
    {
        $this->business = $business;
    }

    public function rules(): array
    {
        $business = $this->getBusiness();

        return [
            'sendPromoBy' => 'required|string|in:email_sms,sms_push,email,sms,push',
            'sendEmailSms' => [
                'required',
                'string',
                new CommaValuesValidation('in:email,sms|sms,email|email,sms,push|sms,email,push|email,push,sms|sms,push,email|push,email,sms|push,sms,email|sms,push|push,sms|email|sms|push')
            ],
            'messageSMS' => 'string',
            'applyConditionsAnd' => 'sometimes|boolean',
            'excludeDormants' => 'sometimes|boolean',
            'excludeCustPrevRegCamp' => 'sometimes|boolean',
            'excCustPrevRegCampDays' => 'sometimes|integer|min:1',
            'excCustPrevRegCampUnit' => 'sometimes|string|in:DAYS,HOURS',
            'excludeCustPrevRegCampIds' => 'sometimes|string',
            'excludeCustPrevRegCriteria' => 'sometimes|integer|in:0,1',
            'excludeCustPrevAutoCamp' => 'sometimes|boolean',
            'excCustPrevAutoCampDays' => 'sometimes|integer|min:1',
            'excCustPrevAutoCampUnit' => 'sometimes|string|in:DAYS,HOURS',
            'excludeCustPrevAutoCampIds' => 'sometimes|string',
            'excludeCustPrevAutoCriteria' => 'sometimes|integer|in:0,1',
            'whoWillReceive' => 'required|array',
            'whoWillReceive.*' => 'required|integer',
            'topSpending' => 'sometimes|integer',
            'specificCustomer' => 'sometimes|string',
            'specificCustomersList' => 'sometimes|array',
            'specificCustomersList.*' => 'required_with:specificCustomersList|string|min:1',
            'productRewardListId' => 'sometimes|array',
            'havePtsOperator' => 'sometimes|integer|in:1,2,3',
            'havePts' => 'sometimes|integer',
            'haveEndPts' => 'sometimes|integer',
            'punchItemIds' => 'sometimes|array',
            'punchItemIds.*' => 'required_with:punchItemIds|integer',
            'daysOp51' => 'sometimes|integer',
            'branchIdOp51' => 'sometimes|array',
            'branchIdOp51.*' => 'required_with:branchIdOp51|integer|min:1',
            'offerIds' => 'sometimes|array',
            'offerIds.*' => 'required_with:punchItemIds|integer',
            'daysOp50' => 'sometimes|integer',
            'branchIdOp50' => 'sometimes|array',
            'branchIdOp50.*' => 'required_with:branchIdOp50|integer|min:1',
            'customerTypesId' => 'sometimes|array',
            'customerTypesId.*' => [
                'required_with:customerTypesId',
                'integer',
                Rule::exists('tenant.business_tiers', 'business_tiers_pk')->where(function ($query) use ($business) {
                    return $query->where([
                        'business_fk' => $business->business_pk,
                        'enabled' => 1
                    ]);
                })
            ],
            'tagsId' => 'sometimes|array',
            'tagsId.*' => [
                'required_with:tagsId',
                'integer',
                Rule::exists('tenant.tags', 'tag_pk')->where(function ($query) use ($business) {
                    return $query->where([
                        'business_fk' => $business->business_pk,
                        'enabled' => 1
                    ]);
                })
            ],
            'referredBetwFirst' => [
                'sometimes',
                'date',
                'date_format:Y-m-d',
                'before_or_equal:referredBetwLast'
            ],
            'referredBetwLast' => [
                'sometimes',
                'date',
                'date_format:Y-m-d'
            ],
            'referredCustomersNumber' => 'sometimes|integer',
            'referredCustomersStatus' => 'sometimes|array',
            'referredCustomersStatus.*' => 'required_with:referredCustomersStatus|integer|in:1,2,3',
            'referralStatus' => 'sometimes|array',
            'referralStatus.*' => 'required_with:referralStatus|integer|in:1,2,3',
            'haveLifetime' => 'sometimes|integer|min:1',
            'haveLifetimeOperator' => 'sometimes|integer|in:1,2',
            'spentWeeksAmount' => 'sometimes|integer|min:1',
            'spentWeeksNumber' => 'sometimes|integer|min:1',
            'spentWeeksOperator' => 'sometimes|integer|in:1,2',
            'segmentsId' => 'sometimes|array',
            'segmentsId.*' => 'required_with:segmentsId|integer|in:1,2,3,4,5,6',
            'noRegTrxDays' => 'sometimes|integer|min:1',
            'noRegTrxDaysRangeEnd' => 'sometimes|integer|min:1',
            'noRegTrxDaysRangeOperator' => 'sometimes|integer|in:1,2',
            'noRegTrxFlag' => 'sometimes|integer|in:1,2',
            'noRegTrxBranchIds' => 'sometimes|required|array',
            'noRegTrxBranchIds.*' => 'required_with:noRegTrxBranchIds|integer',
            'atLeastOneTrxIn' => 'sometimes|integer|min:1',
            'atLeastOneTrxNum' => 'sometimes|integer|min:0',
            'weeksOperator' => 'sometimes|integer|in:1,2',
            'firstVisitBetwFirst' => [
                'sometimes',
                'date',
                'date_format:Y-m-d',
                'before_or_equal:firstVisitBetwLast'
            ],
            'firstVisitBetwLast' => [
                'sometimes',
                'date',
                'date_format:Y-m-d'
            ],
            'branchIdOp18' => 'sometimes|array',
            'branchIdOp18.*' => 'required_with:branchIdOp18|integer',
            'branchId' => 'sometimes|array',
            'branchId.*' => 'required_with:branchId|integer',
            'customerLanguagesId' => 'sometimes|array',
            'customerLanguagesId.*' => [
                'required_with:customerLanguagesId',
                'integer',
                Rule::exists('tenant.language', 'language_pk')->where(function ($query) {
                    return $query->where([
                        'enabled' => 1
                    ]);
                })
            ],
            'postalCode' => 'sometimes|string',
            'bookedAppointmentBetwFirst' => [
                'sometimes',
                'date',
                'date_format:Y-m-d',
                'before_or_equal:bookedAppointmentBetwLast'
            ],
            'bookedAppointmentBetwLast' => [
                'sometimes',
                'date',
                'date_format:Y-m-d'
            ],
            'bookingServiceId' => 'sometimes|array',
            'bookingServiceId.*' => [
                'required_with:bookingServiceId',
                'integer',
                Rule::exists('tenant.products', 'product_pk')->where(function ($query) {
                    return $query->where([
                        'pos_system_fk' => POS_SYSTEM_BOOKING,
                        'enabled' => 1
                    ]);
                })
            ],
            'bookingServiceOperator' => 'sometimes|integer|in:1,2',
            'bookingLastAppointmentsDays' => 'sometimes|integer|min:1',
            'reviewOperator' => 'sometimes|integer|in:1,2',
            'reviewDays' => 'sometimes|integer|min:1',
            'productTags' => 'sometimes|array',
            'productTags.*.tagId' => 'int',
            'productTags.*.tagName' => 'string|min:1',
            'productTagsDays' => 'sometimes|integer|min:1',
            'lsRetailBrands' => 'sometimes|array',
            'lsRetailBrands.*.itemId' => 'int',
            'lsRetailBrands.*.itemName' => 'min:1',
            'lsRetailBrandsDays' => 'integer|min:1',
            'lsRetailProducts' => 'sometimes|string',
            'lsRetailProductsBranches' => 'array',
            'lsRetailProductsDays' => 'integer|min:1',
            'lsRetailCategories' => 'sometimes|string',
            'lsRetailCategoriesDays' => 'integer|min:1',
            'shopifyAccount' => [
                'sometimes',
                'string',
                Rule::exists('tenant.pos_branch', 'account_id')->where(function ($query) use ($business) {
                    return $query->where([
                        'business_fk' => $business->business_pk,
                        'enabled' => 1,
                        'pos_system_fk' => POS_SYSTEM_SHOPIFY
                    ]);
                })
            ],
            'shopifyCategories' => 'sometimes|string',
            'shopifyCategoriesDays' => 'integer|min:1',
            'shopifyProducts' => 'sometimes|string',
            'shopifyProductsDays' => 'integer|min:1',
            'shopifyVendors' => 'sometimes|string',
            'shopifyVendorsDays' => 'integer|min:1',
            'shopifyTags' => 'sometimes|string',
            'shopifyTagsDays' => 'integer|min:1',
            'lsEcomAccount' => [
                'sometimes',
                'string',
                Rule::exists('tenant.pos_branch', 'account_id')->where(function ($query) use ($business) {
                    return $query->where([
                        'business_fk' => $business->business_pk,
                        'enabled' => 1,
                        'pos_system_fk' => POS_SYSTEM_LIGHTSPEED_ECOM
                    ]);
                })
            ],
            'lsEcomCategories' => 'sometimes|string',
            'lsEcomCategoriesDays' => 'integer|min:1',
            'lsEcomProducts' => 'sometimes|string',
            'lsEcomProductsDays' => 'integer|min:1',
            'lsEcomVendors' => 'sometimes|string',
            'lsEcomVendorsDays' => 'integer|min:1',
            'lsEcomTags' => 'sometimes|string',
            'lsEcomTagsDays' => 'integer|min:1',
            'lsEcomBrands' => 'sometimes|string',
            'lsEcomBrandsDays' => 'integer|min:1',
            'ecomzProducts' => 'sometimes|string',
            'ecomzProductsDays' => 'integer|min:1',
            'ecomzTags' => 'sometimes|string',
            'ecomzTagsDays' => 'integer|min:1',
            'ecomzBrands' => 'sometimes|string',
            'ecomzBrandsDays' => 'integer|min:1',
            'ecomzAccount' => [
                'sometimes',
                'string',
                Rule::exists('tenant.pos_branch', 'account_id')->where(function ($query) use ($business) {
                    return $query->where([
                        'business_fk' => $business->business_pk,
                        'enabled' => 1,
                        'pos_system_fk' => POS_SYSTEM_ECOMZ
                    ]);
                })
            ],
            'ecomzCategories' => 'sometimes|string',
            'ecomzCategoriesDays' => 'integer|min:1',
            'wooProducts' => 'sometimes|string',
            'wooProductsDays' => 'integer|min:1',
            'wooTags' => 'sometimes|string',
            'wooTagsDays' => 'integer|min:1',
            'wooAccount' => [
                'sometimes',
                'string',
                Rule::exists('tenant.pos_branch', 'account_id')->where(function ($query) use ($business) {
                    return $query->where([
                        'business_fk' => $business->business_pk,
                        'enabled' => 1,
                        'pos_system_fk' => POS_SYSTEM_WOOCOMMERCE_ECOM
                    ]);
                })
            ],
            'wooCategories' => 'sometimes|string',
            'wooCategoriesDays' => 'integer|min:1',
            'bcProducts' => 'sometimes|string',
            'bcProductsDays' => 'integer|min:1',
            'bcBrands' => 'sometimes|string',
            'bcBrandsDays' => 'integer|min:1',
            'bcAccount' => [
                'sometimes',
                'string',
                Rule::exists('tenant.pos_branch', 'account_id')->where(function ($query) use ($business) {
                    return $query->where([
                        'business_fk' => $business->business_pk,
                        'enabled' => 1,
                        'pos_system_fk' => POS_SYSTEM_BIGCOMMERCE
                    ]);
                })
            ],
            'bcCategories' => 'sometimes|string',
            'bcCategoriesDays' => 'integer|min:1',
            'magentoAccount' => [
                'sometimes',
                'string',
                Rule::exists('tenant.pos_branch', 'account_id')->where(function ($query) use ($business) {
                    return $query->where([
                        'business_fk' => $business->business_pk,
                        'enabled' => 1,
                        'pos_system_fk' => POS_SYSTEM_MAGENTO
                    ]);
                })
            ],
            'magentoCategories' => 'sometimes|string',
            'magentoCategoriesDays' => 'integer|min:1',
            'magentoProducts' => 'sometimes|string',
            'magentoProductsDays' => 'integer|min:1',
            'vendProducts' => 'sometimes|string',
            'vendProductsDays' => 'integer|min:1',
            'vendVendors' => 'sometimes|string',
            'vendVendorsDays' => 'integer|min:1',
            'vendTags' => 'sometimes|string',
            'vendTagsDays' => 'integer|min:1',
            'vendBrands' => 'sometimes|string',
            'vendBrandsDays' => 'integer|min:1',
            'heartlandAccount' => 'sometimes|string',
            'heartlandProducts' => 'sometimes|string',
            'heartlandProductsDays' => 'integer|min:1',
            'heartlandCategories' => 'sometimes|string',
            'heartlandCategoriesDays' => 'integer|min:1',
            'heartlandVendors' => 'sometimes|string',
            'heartlandVendorsDays' => 'integer|min:1',
            'lskProducts' => 'sometimes|string',
            'lskProductsDays' => 'integer|min:1',
            'lskProductsQuantity' => 'integer|min:1',
            'lskAccountingGroups' => 'sometimes|string',
            'lskAccountingGroupsDays' => 'integer|min:1',
            'qrCodeAccount' => 'sometimes|string',
            'qrCodeProducts' => 'sometimes|string',
            'qrCodeProductsDays' => 'integer|min:1',
            'campaignedOffersRedemptionsOperator' => 'sometimes|string|in:1,2',
            'campaignedOffersRedemptionsDays' => 'sometimes|integer|min:1',
            'campaignedOffersRedemptionsCampaignIds' => 'sometimes|array',
            'campaignedOffersRedemptionsCampaignIds.*' => 'sometimes|string',
            'campaignedOffersRedemptionsBranches' => 'sometimes|array',
            'campaignedOffersRedemptionsBranches.*' => 'sometimes|string',
            'campaignedOffersRedemptionsOffersList' => 'sometimes|array',
            'campaignedOffersRedemptionsOffersList.*' => 'sometimes|string',
            'campaignedOffersRedemptionsRedemptionsList' => 'sometimes|array',
            'campaignedOffersRedemptionsRedemptionsList.*' => 'sometimes|string',
            'branchIdOp75' => 'sometimes|array',
            'branchIdOp75.*' => 'sometimes|integer',
            'branchIdOp76' => 'sometimes|array',
            'branchIdOp76.*' => 'sometimes|integer',
            'customerCriteriaOp9' => 'sometimes|integer|in:1,2,3',
            'customerTypesOp9' => 'sometimes|array',
            'customerTypesOp9.*' => [
                'required_with:customerTypesOp9',
                'integer',
                Rule::exists('tenant.business_tiers', 'business_tiers_pk')->where(function ($query) use ($business) {
                    return $query->where([
                        'business_fk' => $business->business_pk,
                        'enabled' => 1
                    ]);
                })
            ],
            'daysBeforeBirthday' => 'required_with:customerCriteriaOp9|integer',
            'ptsExpireInactiveDays' => 'integer|min:1',
            'ptsExpireDays' => 'integer|min:1',
            'afterFirstVisit' => 'sometimes|integer|min:1',
            'branchIdOp13' => 'sometimes|integer',
//            'branchIdOp13.*' => 'required_with:branchIdOp13|integer',
            'nbRewardRedemp' => 'integer|min:1',
            'optRewardRedemp' => 'integer|min:1',
            'noTrxDays' => 'sometimes|integer|min:1',
            'noTrxDaysRangeEnd' => 'sometimes|integer|min:1',
            'noTrxDaysRangeOperator' => 'sometimes|integer|in:1,2',
            'noTrxBranchIds' => 'sometimes|required|array',
            'noTrxBranchIds.*' => 'required_with:noTrxBranchIds|integer',
            'regLastTrxDays' => 'sometimes|integer|min:1',
            'regLastTrxDaysRangeEnd' => 'sometimes|integer|min:1',
            'regLastTrxDaysRangeOperator' => 'sometimes|integer|in:1,2',
            'regLastTrxBranchIds' => 'sometimes|required|array',
            'regLastTrxBranchIds.*' => 'required_with:regLastTrxBranchIds|integer',
            'regNoTrxDays' => 'integer|min:1',
            'registeredCustomersBetwFirst' => [
                'sometimes',
                'date',
                'date_format:Y-m-d',
                'before_or_equal:registeredCustomersBetwLast'
            ],
            'registeredCustomersBetwLast' => [
                'sometimes',
                'date',
                'date_format:Y-m-d'
            ],
            'movedFromCustomerTypeId' => 'sometimes|array',
            'movedFromCustomerTypeId.*' => [
                'required_with:movedFromCustomerTypeId',
                'integer',
                Rule::exists('tenant.business_tiers', 'business_tiers_pk')->where(function ($query) use ($business) {
                    return $query->where([
                        'business_fk' => $business->business_pk,
                        'enabled' => 1
                    ]);
                })
            ],
            'movedToCustomerTypeId' => 'sometimes|array',
            'movedToCustomerTypeId.*' => [
                'required_with:movedToCustomerTypeId',
                'integer',
                Rule::exists('tenant.business_tiers', 'business_tiers_pk')->where(function ($query) use ($business) {
                    return $query->where([
                        'business_fk' => $business->business_pk,
                        'enabled' => 1
                    ]);
                })
            ],
            'haveGiftcard' => 'sometimes|integer|min:0',
            'haveGiftcardOperator' => 'required_with:haveGiftcard|integer|in:1,2,3',
            'haveEndGiftcard' => 'required_if:haveGiftcardOperator,3|integer|min:' . (int)$this->haveGiftcard,
            'isCustomerExport' => 'sometimes|integer',
            'ignore_consent' => 'sometimes|integer',
            'send_to_invalid_sms' => 'string',
            'ecwidProducts' => 'sometimes|string',
            'ecwidProductsDays' => 'sometimes|integer|min:1',
            'ecwidBrands' => 'sometimes|string',
            'ecwidBrandsDays' => 'sometimes|integer|min:1',
            'ecwidCategories' => 'sometimes|string',
            'ecwidCategoriesDays' => 'sometimes|integer|min:1',
            'summary_storage' => 'sometimes|string|in:mysql,clickhouse,memory',
            'reviewOrdersDateRange' => 'sometimes|array',
            'reviewOrdersDateRange.account_id' => [
                'required_with:reviewOrdersDateRange',
                'string',
                Rule::exists('pos_branch', 'account_id')->where(function ($query) use ($business) {
                    return $query->where('business_fk', $business->business_pk)
                        ->where('pos_system_fk', POS_SYSTEM_KANGAROO_REVIEWS)
                        ->where('enabled', 1);
                })
            ],
            'reviewOrdersDateRange.start_date' => 'required_with:reviewOrdersDateRange|date|date_format:Y-m-d',
            'reviewOrdersDateRange.end_date' => 'required_with:reviewOrdersDateRange|date|date_format:Y-m-d',
            'reviewProducts' => 'sometimes|string',
            'reviewProductsDays' => 'sometimes|integer|min:1',
            'reviewLastOrders' => 'sometimes|array',
            'reviewLastOrders.account_id' => [
                'required_with:reviewLastOrders',
                'string',
                Rule::exists('pos_branch', 'account_id')->where(function ($query) use ($business) {
                    return $query->where('business_fk', $business->business_pk)
                        ->where('pos_system_fk', POS_SYSTEM_KANGAROO_REVIEWS)
                        ->where('enabled', 1);
                })
            ],
            'reviewLastOrders.last_orders' => 'required_with:reviewLastOrders|integer|min:1',
        ];
    }

    /**
     * @throws ValidationException
     */
    /*public function prepareForValidation()
    {

    }*/

    /**
     * Configure the validator instance.
     *
     * @param \Illuminate\Validation\Validator $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $lang = $this->getLang();
            $business = $this->getBusiness();
            $whoWillReceive = $this->input('whoWillReceive');
            if (empty($whoWillReceive)) {
                return;
            }

            if ($this->input('excludeCustPrevRegCamp') && !empty($this->input('excludeCustPrevRegCampIds'))) {
                $campaignIds = json_decode($this->input('excludeCustPrevRegCampIds'), true);
                $count = Campaign::whereIn('entity_campaign_pk', $campaignIds)->where('business_fk', $business->business_pk)->count();
                if($count !== count($campaignIds)) {
                    $validator->errors()->add('excludeCustPrevRegCampIds', 'The selected campaigns are invalid');
                }
            }

            if ($this->input('excludeCustPrevAutoCamp') && !empty($this->input('excludeCustPrevAutoCampIds'))) {
                $campaignIds = json_decode($this->input('excludeCustPrevAutoCampIds'), true);
                $count = Campaign::whereIn('entity_campaign_pk', $campaignIds)->where('business_fk', $business->business_pk)->count();
                if($count !== count($campaignIds)) {
                    $validator->errors()->add('excludeCustPrevAutoCampIds', 'The selected campaigns are invalid');
                }
            }

            //Top X highest spending customers filter
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_TOP_HIGHEST_CUSTOMERS, $whoWillReceive) && empty($this->input('topSpending'))) {
                $validator->errors()->add('topSpending', 'The top spending field is required.');
            }

            //Send to specific customer (email/phone)
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_SPECIFIC_CUSTOMER, $whoWillReceive) && empty($this->input('specificCustomer'))) {
                $validator->errors()->add('specificCustomer', 'The specific email/phone field is required.');
            }

            //Send to specific customers list (email/phone)
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_SPECIFIC_CUSTOMERS_LIST, $whoWillReceive) && (empty($this->input('specificCustomersList')) || $this->input(
                        'specificCustomersList'
                    ) == '[]' || $this->input('specificCustomersList') == '{}')) {
                $validator->errors()->add('specificCustomersList', 'The specific email/phone field is required.');
            }

            //Send to specific customers list (email/phone)
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_REFERRED_CUSTOMERS_NUMBER, $whoWillReceive)) {
                if (empty($this->input('referredCustomersNumber'))) {
                    $validator->errors()->add('referredCustomersNumber', 'The number of referred customers is required.');
                }
                if (empty($this->input('referredCustomersNumber')) || $this->input('referredCustomersStatus') == '[]' || $this->input('referredCustomersStatus') == '{}') {
                    $validator->errors()->add('referredCustomersStatus', 'The referred customers\' status field is required.');
                }
            }

            //Product bought from À La Carte Rewards
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_BOUGHT_FROM_A_LA_CARTE, $whoWillReceive) && empty($this->input('productRewardListId'))) {
                $validator->errors()->add('productRewardListId', 'The product reward list field is required.');
            }

            //Customers who have less/more/between X points
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_POINTS, $whoWillReceive)) {
                if (empty($this->input('havePtsOperator'))) {
                    $validator->errors()->add('havePtsOperator', 'The balance points operator field is required.');
                }

                if (empty($this->input('havePts'))) {
                    $validator->errors()->add('havePts', 'The customer balance points field is required.');
                }

                if ($this->input('havePtsOperator') == 3) {
                    if (empty($this->input('haveEndPts'))) {
                        $validator->errors()->add('haveEndPts', 'The customer end balance points field is required.');
                    } elseif ($this->input('havePts') > $this->input('haveEndPts')) {
                        $validator->errors()->add('havePts', 'The start balance points cannot be greater than the end balance points.');
                    }
                }
            }

            //Customers who redeemed a specific redemption in the X days at specific branches
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REDEEMED_REDEMPTION, $whoWillReceive)) {
                if (empty($this->input('punchItemIds'))) {
                    $validator->errors()->add('punchItemIds', 'The customers redemption list field is required.');
                }

                if (empty($this->input('daysOp51'))) {
                    $validator->errors()->add('daysOp51', 'The customers redemption days field is required.');
                }
            }

            //Customers who redeemed a specific offer in the X days at specific branches
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REDEEMED_OFFERS, $whoWillReceive)) {
                if (empty($this->input('offerIds'))) {
                    $validator->errors()->add('offerIds', 'The customers offers list field is required.');
                }

                if (empty($this->input('daysOp50'))) {
                    $validator->errors()->add('daysOp50', 'The customers offers days field is required.');
                }
            }

            //Customer tiers status
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_TIERS, $whoWillReceive)) {
                if (empty($this->input('customerTypesId'))) {
                    $validator->errors()->add('customerTypesId', 'The customer types list field is required.');
                }
            }

            //Customer tags
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_TAGS, $whoWillReceive)) {
                if (empty($this->input('tagsId'))) {
                    $validator->errors()->add('tagsId', 'The customer tags list field is required.');
                }
            }

            //Customers who have referred between X and Y dates
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REFERRED, $whoWillReceive)) {
                if (empty($this->input('referredBetwFirst'))) {
                    $validator->errors()->add('referredBetwFirst', 'The customer referred start date is required.');
                }

                if (empty($this->input('referredBetwLast'))) {
                    $validator->errors()->add('referredBetwLast', 'The customer referred end date is required.');
                }
            }

            //Customers signed up through referrals and status
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REFERRALS_SIGNED_UP, $whoWillReceive)) {
                if (empty($this->input('referralStatus'))) {
                    $validator->errors()->add('referralStatus', 'The referrals status field is required.');
                }
            }

            //Customers with lifetime balance more than / less than X pts
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_LIFETIME_BALANCE, $whoWillReceive)) {
                if (empty($this->input('haveLifetime'))) {
                    $validator->errors()->add('haveLifetime', 'The lifetime balance field is required.');
                }

                if (empty($this->input('haveLifetimeOperator'))) {
                    $validator->errors()->add('haveLifetimeOperator', 'The lifetime balance operator field is required.');
                }
            }

            //Customers who spent more than / less than X $ in the last X weeks
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WEEKS_SPENDING, $whoWillReceive)) {
                if (empty($this->input('spentWeeksAmount'))) {
                    $validator->errors()->add('spentWeeksAmount', 'The amount spent field is required.');
                }

                if (empty($this->input('spentWeeksNumber'))) {
                    $validator->errors()->add('spentWeeksNumber', 'The spending weeks number field is required.');
                }

                if (empty($this->input('spentWeeksOperator'))) {
                    $validator->errors()->add('spentWeeksOperator', 'The spending weeks operator field is required.');
                }
            }

            //Customers segments
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_SEGMENTS, $whoWillReceive)) {
                if (empty($this->input('segmentsId'))) {
                    $validator->errors()->add('segmentsId', 'The segments list field is required.');
                }
            }

            //Customers who haven't made purchases in the last X days
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_NOT_MADE_PURCHASES, $whoWillReceive)) {
                if (empty($this->input('noRegTrxDays'))) {
                    if(!empty($this->input('noRegTrxDaysRangeOperator')) && $this->input('noRegTrxDaysRangeOperator') == 2){
                        $validator->errors()->add('noRegTrxDays', 'The purchases range start days field is required.');
                    }else{
                        $validator->errors()->add('noRegTrxDays', 'The purchases days field is required.');
                    }
                }
                
                if (!empty($this->input('noRegTrxDaysRangeOperator')) && $this->input('noRegTrxDaysRangeOperator') == 2) {
                    if(empty($this->input('noRegTrxDaysRangeEnd'))){
                        $validator->errors()->add('noRegTrxDaysRangeEnd', 'The purchases range end days field is required.');
                    } elseif($this->input('noRegTrxDaysRangeEnd') > $this->input('noRegTrxDays')){
                        $validator->errors()->add('noRegTrxDaysRangeError', __('marketing.BACKEND_MKTCAMPAIGN_LAST_PURCHASE_DAYS_RANGE_ERROR', [], $lang));
                    }
                }
            }


            //Customers who have made more/less than or equal X purchases in the last X weeks
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WEEKS_PURCHASES, $whoWillReceive)) {
                if (empty($this->input('atLeastOneTrxIn'))) {
                    $validator->errors()->add('atLeastOneTrxIn', 'The purchase weeks number field is required.');
                }

                if (empty($this->input('atLeastOneTrxNum'))) {
                    $validator->errors()->add('atLeastOneTrxNum', 'The purchase number field is required.');
                }

                if (empty($this->input('weeksOperator'))) {
                    $validator->errors()->add('weeksOperator', 'The purchase operator field is required.');
                }
            }

            //Customers with first visit between X and Y dates at specific branches
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WITH_FIRST_VISIT, $whoWillReceive)) {
                if (empty($this->input('firstVisitBetwFirst'))) {
                    $validator->errors()->add('firstVisitBetwFirst', 'The first visit start date field is required.');
                }

                if (empty($this->input('firstVisitBetwLast'))) {
                    $validator->errors()->add('firstVisitBetwLast', 'The first visit end date field is required.');
                }
            }

            //Customers whose preferred language is {X,Y,Z}
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_PREFERRED_LANGUAGE, $whoWillReceive)) {
                if (empty($this->input('customerLanguagesId'))) {
                    $validator->errors()->add('customerLanguagesId', 'The customer languages list field is required.');
                }
            }

            //Customers who have Postal / Zip Code
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_ALL_CUSTOMERS_HAVING_POSTAL_CODE, $whoWillReceive)) {
                if (empty($this->input('postalCode'))) {
                    $validator->errors()->add('postalCode', 'The customer postal / zip code field is required.');
                }
            }

            //Customers who have booked appointment types between X & Y dates
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_BOOKED_APPOINTMENTS_BETWEEN_DATES, $whoWillReceive)) {
                if (empty($this->input('bookedAppointmentBetwFirst'))) {
                    $validator->errors()->add('bookedAppointmentBetwFirst', 'The appointment start date field is required.');
                }

                if (empty($this->input('bookedAppointmentBetwLast'))) {
                    $validator->errors()->add('bookedAppointmentBetwLast', 'The appointment end date field is required.');
                }
            }

            //Customers who have/not booked the following appointment types in the last X days
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_BOOKED_SPECIFIC_APPOINTMENTS_PER_DAYS, $whoWillReceive)) {
                if (empty($this->input('bookingServiceId'))) {
                    $validator->errors()->add('bookingServiceId', 'The appointment types list field is required.');
                }

                if (empty($this->input('bookingServiceOperator'))) {
                    $validator->errors()->add('bookingServiceOperator', 'The appointment operator field is required.');
                }

                if (empty($this->input('bookingLastAppointmentsDays'))) {
                    $validator->errors()->add('bookingLastAppointmentsDays', 'The appointment days field is required.');
                }
            }

            //Customers who have/not left a product review on the website in the last X days
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_PRODUCT_REVIEWS, $whoWillReceive)) {
                if (empty($this->input('reviewOperator'))) {
                    $validator->errors()->add('reviewOperator', 'The product reviews operator field is required.');
                }

                if (empty($this->input('reviewDays'))) {
                    $validator->errors()->add('reviewDays', 'The product reviews days field is required.');
                }
            }

            // Customers who have purchased specific products by tags
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LS_PRODUCT_TAGS, $whoWillReceive)) {
                if (empty($this->input('productTags'))) {
                    $validator->errors()->add('productTags', __('marketing.BACKEND_MKTCAMPAIGN_PURCHASE_PRODUCT_TAG_REQUIRED', [], $lang));
                }
            }

            // Customers who have purchased specific products by brands
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LS_PRODUCT_BRANDS, $whoWillReceive)) {
                if (empty($this->input('lsRetailBrands')) || empty($this->input('lsRetailBrandsDays'))) {
                    $validator->errors()->add('lsRetailBrands', __('marketing.BACKEND_MKTCAMPAIGN_LS_RETAIL_BRANDS_REQUIRED', [], $lang));
                }
            }

            // Customers who have purchased specific products
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LS_PRODUCTS, $whoWillReceive)) {
                if (empty($this->input('lsRetailProductsBranches')) ||
                    empty($this->input('lsRetailProductsDays')) ||
                    empty($this->input('lsRetailProducts'))) {
                    $validator->errors()->add('lsRetailProducts', __('marketing.BACKEND_MKTCAMPAIGN_LS_RETAIL_PRODUCTS_REQUIRED', [], $lang));
                }
            }

            // Customers who have purchased specific products
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LS_PRODUCT_CATEGORIES, $whoWillReceive)) {
                if (empty($this->input('lsRetailCategories')) || empty($this->input('lsRetailCategoriesDays'))) {
                    $validator->errors()->add('lsRetailCategories', __('marketing.BACKEND_MKTCAMPAIGN_LS_RETAIL_CATEGORY_REQUIRED', [], $lang));
                }
            }

            // Shopify filters
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_CATEGORIES, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCTS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_VENDORS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_TAGS, $whoWillReceive)
            ) {
                if (empty($this->input('shopifyAccount'))) {
                    $validator->errors()->add('shopifyAccount', __('marketing.BACKEND_MKTCAMPAIGN_INTEGRATION_ACCOUNT_REQUIRED', [], $lang));
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_CATEGORIES, $whoWillReceive)) {
                    if (empty($this->input('shopifyCategories')) || empty($this->input('shopifyCategoriesDays'))) {
                        $validator->errors()->add('shopifyCategories', __('marketing.BACKEND_MKTCAMPAIGN_SHOPIFY_CATEGORY_REQUIRED', [], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCTS, $whoWillReceive)) {
                    if (empty($this->input('shopifyProducts')) || empty($this->input('shopifyProductsDays'))) {
                        $validator->errors()->add('shopifyProducts', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_PRODUCTS_REQUIRED', ['{posName}' => 'Shopify'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_VENDORS, $whoWillReceive)) {
                    if (empty($this->input('shopifyVendors')) || empty($this->input('shopifyVendorsDays'))) {
                        $validator->errors()->add('shopifyVendors', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_VENDOR_REQUIRED', ['{posName}' => 'Shopify'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_TAGS, $whoWillReceive)) {
                    if (empty($this->input('shopifyTags')) || empty($this->input('shopifyTagsDays'))) {
                        $validator->errors()->add('shopifyTags', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_TAG_REQUIRED', ['{posName}' => 'Shopify'], $lang));
                    }
                }
            }

            // LS ecom filters
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_CATEGORIES, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCTS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_VENDORS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_BRANDS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_TAGS, $whoWillReceive)
            ) {
                if (empty($this->input('lsEcomAccount'))) {
                    $validator->errors()->add('lsEcomAccount', __('marketing.BACKEND_MKTCAMPAIGN_INTEGRATION_ACCOUNT_REQUIRED', [], $lang));
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_CATEGORIES, $whoWillReceive)) {
                    if (empty($this->input('lsEcomCategories')) || empty($this->input('lsEcomCategoriesDays'))) {
                        $validator->errors()->add('lsEcomCategories', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_CATEGORY_REQUIRED', [], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCTS, $whoWillReceive)) {
                    if (empty($this->input('lsEcomProducts')) || empty($this->input('lsEcomProductsDays'))) {
                        $validator->errors()->add('lsEcomProducts', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_PRODUCTS_REQUIRED', ['{posName}' => 'Lightspeed ECOM'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_VENDORS, $whoWillReceive)) {
                    if (empty($this->input('lsEcomVendors')) || empty($this->input('lsEcomVendorsDays'))) {
                        $validator->errors()->add('lsEcomVendors', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_VENDOR_REQUIRED', ['{posName}' => 'Lightspeed ECOM'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_TAGS, $whoWillReceive)) {
                    if (empty($this->input('lsEcomTags')) || empty($this->input('lsEcomTagsDays'))) {
                        $validator->errors()->add('lsEcomTags', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_TAG_REQUIRED', ['{posName}' => 'Lightspeed ECOM'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_BRANDS, $whoWillReceive)) {
                    if (empty($this->input('lsEcomBrands')) || empty($this->input('lsEcomBrandsDays'))) {
                        $validator->errors()->add('lsEcomBrands', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_BRAND_REQUIRED', ['{posName}' => 'Lightspeed ECOM'], $lang));
                    }
                }
            }

            // ecomz filters
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCT_BRANDS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCTS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCT_CATEGORIES, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCT_TAGS, $whoWillReceive)
            ) {
                if (empty($this->input('ecomzAccount'))) {
                    $validator->errors()->add('ecomzAccount', __('marketing.BACKEND_MKTCAMPAIGN_INTEGRATION_ACCOUNT_REQUIRED', [], $lang));
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCT_CATEGORIES, $whoWillReceive)) {
                    if (empty($this->input('ecomzCategories')) || empty($this->input('ecomzCategoriesDays'))) {
                        $validator->errors()->add('ecomzCategories', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_CATEGORY_REQUIRED', ['{posName}' => 'ECOMZ'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCTS, $whoWillReceive)) {
                    if (empty($this->input('ecomzProducts')) || empty($this->input('ecomzProductsDays'))) {
                        $validator->errors()->add('ecomzProducts', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_PRODUCTS_REQUIRED', ['{posName}' => 'ECOMZ'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCT_TAGS, $whoWillReceive)) {
                    if (empty($this->input('ecomzTags')) || empty($this->input('ecomzTagsDays'))) {
                        $validator->errors()->add('ecomzTags', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_TAG_REQUIRED', ['{posName}' => 'ECOMZ'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCT_BRANDS, $whoWillReceive)) {
                    if (empty($this->input('ecomzBrands')) || empty($this->input('ecomzBrandsDays'))) {
                        $validator->errors()->add('ecomzBrands', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_BRAND_REQUIRED', ['{posName}' => 'ECOMZ'], $lang));
                    }
                }
            }

            // woo filters
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_WOO_PRODUCTS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_WOO_PRODUCT_CATEGORIES, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_WOO_PRODUCT_TAGS, $whoWillReceive)
            ) {
                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_WOO_PRODUCT_CATEGORIES, $whoWillReceive)) {
                    if (empty($this->input('wooCategories')) || empty($this->input('wooCategoriesDays'))) {
                        $validator->errors()->add('wooCategories', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_CATEGORY_REQUIRED', ['{posName}' => 'Woocommerce'], $lang));
                    }
                    if (empty($this->input('wooAccount'))) {
                        $wooCategories = json_decode($this->input('wooCategories'), true);
                        foreach ($wooCategories as $wooCat) {
                            if (!isset($wooCat['account_id'])) {
                                $validator->errors()->add('wooAccount', __('marketing.BACKEND_MKTCAMPAIGN_INTEGRATION_ACCOUNT_REQUIRED', [], $lang));
                                break;
                            }
                        }
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_WOO_PRODUCTS, $whoWillReceive)) {
                    if (empty($this->input('wooProducts')) || empty($this->input('wooProductsDays'))) {
                        $validator->errors()->add('wooProducts', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_PRODUCTS_REQUIRED', ['{posName}' => 'Woocommerce'], $lang));
                    }

                    if (empty($this->input('wooAccount'))) {
                        $wooProducts = json_decode($this->input('wooProducts'), true);
                        foreach ($wooProducts as $item) {
                            if (!isset($item['account_id'])) {
                                $validator->errors()->add('wooAccount', __('marketing.BACKEND_MKTCAMPAIGN_INTEGRATION_ACCOUNT_REQUIRED', [], $lang));
                                break;
                            }
                        }
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_WOO_PRODUCT_TAGS, $whoWillReceive)) {
                    if (empty($this->input('wooTags')) || empty($this->input('wooTagsDays'))) {
                        $validator->errors()->add('wooTags', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_TAG_REQUIRED', ['{posName}' => 'Woocommerce'], $lang));
                    }

                    if (empty($this->input('wooAccount'))) {
                        $wooTags = json_decode($this->input('wooTags'), true);
                        foreach ($wooTags as $item) {
                            if (!isset($item['account_id'])) {
                                $validator->errors()->add('wooAccount', __('marketing.BACKEND_MKTCAMPAIGN_INTEGRATION_ACCOUNT_REQUIRED', [], $lang));
                                break;
                            }
                        }
                    }
                }
            }

            // bc filters
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_BC_PRODUCTS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_BC_PRODUCT_CATEGORIES, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_BC_PRODUCT_BRANDS, $whoWillReceive)
            ) {
                if (empty($this->input('bcAccount'))) {
                    $validator->errors()->add('bcAccount', __('marketing.BACKEND_MKTCAMPAIGN_INTEGRATION_ACCOUNT_REQUIRED', [], $lang));
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_BC_PRODUCT_CATEGORIES, $whoWillReceive)) {
                    if (empty($this->input('bcCategories')) || empty($this->input('bcCategoriesDays'))) {
                        $validator->errors()->add('bcCategories', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_CATEGORY_REQUIRED', ['{posName}' => 'Bigcommerce'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_BC_PRODUCTS, $whoWillReceive)) {
                    if (empty($this->input('bcProducts')) || empty($this->input('bcProductsDays'))) {
                        $validator->errors()->add('bcProducts', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_PRODUCTS_REQUIRED', ['{posName}' => 'Bigcommerce'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_BC_PRODUCT_BRANDS, $whoWillReceive)) {
                    if (empty($this->input('bcBrands')) || empty($this->input('bcBrandsDays'))) {
                        $validator->errors()->add('bcBrands', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_TAG_REQUIRED', ['{posName}' => 'Bigcommerce'], $lang));
                    }
                }
            }

            // magento filters
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_MAGENTO_PRODUCT_CATEGORIES, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_MAGENTO_PRODUCTS, $whoWillReceive)
            ) {
                if (empty($this->input('magentoAccount'))) {
                    $validator->errors()->add('magentoAccount', __('marketing.BACKEND_MKTCAMPAIGN_INTEGRATION_ACCOUNT_REQUIRED', [], $lang));
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_MAGENTO_PRODUCT_CATEGORIES, $whoWillReceive)) {
                    if (empty($this->input('magentoCategories')) || empty($this->input('magentoCategoriesDays'))) {
                        $validator->errors()->add('magentoCategories', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_CATEGORY_REQUIRED', ['{posName}' => 'Magento'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_MAGENTO_PRODUCTS, $whoWillReceive)) {
                    if (empty($this->input('magentoProducts')) || empty($this->input('magentoProductsDays'))) {
                        $validator->errors()->add('magentoProducts', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_PRODUCTS_REQUIRED', ['{posName}' => 'Magento'], $lang));
                    }
                }
            }

            // Vend filters
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCTS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCT_VENDORS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCT_BRANDS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCT_TAGS, $whoWillReceive)
            ) {
                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCTS, $whoWillReceive)) {
                    if (empty($this->input('vendProducts')) || empty($this->input('vendProductsDays'))) {
                        $validator->errors()->add('vendProducts', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_PRODUCTS_REQUIRED', ['{posName}' => 'Vend'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCT_VENDORS, $whoWillReceive)) {
                    if (empty($this->input('vendVendors')) || empty($this->input('vendVendorsDays'))) {
                        $validator->errors()->add('vendVendors', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_VENDOR_REQUIRED', ['{posName}' => 'Vend'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCT_TAGS, $whoWillReceive)) {
                    if (empty($this->input('vendTags')) || empty($this->input('vendTagsDays'))) {
                        $validator->errors()->add('vendTags', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_TAG_REQUIRED', ['{posName}' => 'Vend'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCT_BRANDS, $whoWillReceive)) {
                    if (empty($this->input('vendBrands')) || empty($this->input('vendBrandsDays'))) {
                        $validator->errors()->add('vendBrands', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_BRAND_REQUIRED', ['{posName}' => 'Vend'], $lang));
                    }
                }
            }

            //Heartland filters
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_HEARTLAND_PRODUCTS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_HEARTLAND_CATEGORIES, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_HEARTLAND_VENDORS, $whoWillReceive)) {
                if (empty($this->input('heartlandAccount'))) {
                    $validator->errors()->add('heartlandAccount', __('marketing.BACKEND_MKTCAMPAIGN_INTEGRATION_ACCOUNT_REQUIRED', [], $lang));
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_HEARTLAND_PRODUCTS, $whoWillReceive)) {
                    if (empty($this->input('heartlandProducts')) || empty($this->input('heartlandProductsDays'))) {
                        $validator->errors()->add('heartlandProducts', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_PRODUCTS_REQUIRED', ['{posName}' => 'Heartland'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_HEARTLAND_CATEGORIES, $whoWillReceive)) {
                    if (empty($this->input('heartlandCategories')) || empty($this->input('heartlandCategoriesDays'))) {
                        $validator->errors()->add('heartlandCategories', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_CATEGORY_REQUIRED', ['{posName}' => 'Heartland'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_HEARTLAND_VENDORS, $whoWillReceive)) {
                    if (empty($this->input('heartlandVendors')) || empty($this->input('heartlandVendorsDays'))) {
                        $validator->errors()->add('heartlandVendors', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_VENDOR_REQUIRED', ['{posName}' => 'Heartland'], $lang));
                    }
                }
            }

            //Upcoming birthdays
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_UPCOMING_BIRTHDAYS, $whoWillReceive)) {
                if ($this->input('customerCriteriaOp9') == 3 && empty($this->input('customerTypesOp9'))) {
                    $validator->errors()->add('customerTypesOp9', __('marketing.BACKEND_MKTCAMPAIGN_CUSTOMER_TYPES_REQUIRED', [], $lang));
                }
            }

            // Customers who have made X earning/redemptions
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_MADE_EARN_REWARD, $whoWillReceive)) {
                if (empty($this->input('nbRewardRedemp'))) {
                    $validator->errors()->add('nbRewardRedemp', __('marketing.BACKEND_MKTCAMPAIGN_NUM_REWARDS_REDEMPTIONS', [], $lang));
                }
            }

            // Remind customers their points will expire for inactivity
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_POINT_EXPIRE, $whoWillReceive)) {
                if (empty($this->input('ptsExpireDays'))) {
                    $validator->errors()->add('ptsExpireDays', __('marketing.BACKEND_MKTCAMPAIGN_DAYS_BEFORE_EXPIRATION_ERROR', [], $lang));
                }
            }

            // Remind customers their points will expire for inactivity
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_POINT_EXPIRE_FOR_INACTIVITY, $whoWillReceive)) {
                if (empty($this->input('ptsExpireInactiveDays'))) {
                    $validator->errors()->add('ptsExpireInactiveDays', __('marketing.BACKEND_MKTCAMPAIGN_DAYS_BEFORE_EXPIRATION_ERROR', [], $lang));
                }
            }

            // New clients, X days after the First visit at Y branch
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_NEW_CLIENTS_AFTER_FIRST_VISIT, $whoWillReceive)) {
                if (empty($this->input('afterFirstVisit'))) {
                    $validator->errors()->add('afterFirstVisit', __('marketing.BACKEND_MKTCAMPAIGN_CLIENTS_DAYS_FIRST_VISIT_REQUIRED', [], $lang));
                }
            }

            // Customers who have made their last purchase exactly X days ago
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REGULAR_LAST_PURCHASE, $whoWillReceive)) {
                if (empty($this->input('regLastTrxDays'))) {
                    $validator->errors()->add('regLastTrxDays', __('marketing.BACKEND_MKTCAMPAIGN_LAST_PURCHASE_DAYS_REQUIRED', [], $lang));
                }
                
                if (!empty($this->input('regLastTrxRangeOperator')) && $this->input('regLastTrxRangeOperator') == 2) {
                    if(empty($this->input('regLastTrxRangeEnd'))){
                        $validator->errors()->add('regLastTrxRangeEnd', __('marketing.BACKEND_MKTCAMPAIGN_LAST_PURCHASE_DAYS_RANGE_END_REQUIRED', [], $lang));
                    }elseif($this->input('regLastTrx') > $this->input('regLastTrxRangeEnd')){
                        $validator->errors()->add('regLastTrxRangeError', __('marketing.BACKEND_MKTCAMPAIGN_LAST_PURCHASE_DAYS_RANGE_ERROR', [], $lang));
                    }
                }
            }
            
            // Customers who have made their last purchase exactly X days ago
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_LAST_PURCHASE, $whoWillReceive)) {
                if (empty($this->input('noTrxDays'))) {
                    $validator->errors()->add('noTrxDays', __('marketing.BACKEND_MKTCAMPAIGN_LAST_PURCHASE_DAYS_REQUIRED', [], $lang));
                }
                
                if (!empty($this->input('noTrxDaysRangeOperator')) && $this->input('noTrxDaysRangeOperator') == 2) {
                    if(empty($this->input('noTrxDaysRangeEnd'))){
                        $validator->errors()->add('noTrxDaysRangeEnd', __('marketing.BACKEND_MKTCAMPAIGN_LAST_PURCHASE_DAYS_RANGE_END_REQUIRED', [], $lang));
                    }elseif($this->input('noTrxDays') > $this->input('noTrxDaysRangeEnd')){
                        $validator->errors()->add('noTrxDaysRangeError', __('marketing.BACKEND_MKTCAMPAIGN_LAST_PURCHASE_DAYS_RANGE_ERROR', [], $lang));
                    }
                }
            }

            // customers who registered but didn't make a purchase in the last x days
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REGISTER_NO_TRANSACTION, $whoWillReceive)) {
                if (empty($this->input('regNoTrxDays'))) {
                    $validator->errors()->add('regNoTrxDays', __('marketing.BACKEND_MKTCAMPAIGN_REGISTER_NO_TRANSACTION_DAYS_REQUIRED', [], $lang));
                }
            }

            // Customers who registered between dates
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REGISTERED_BETWEEN_DATES, $whoWillReceive)) {
                if (empty($this->input('registeredCustomersBetwFirst'))) {
                    $validator->errors()->add('registeredCustomersBetwFirst', __('marketing.BACKEND_MKTCAMPAIGN_REGISTERED_START_DATE_REQUIRED', [], $lang));
                }

                if (empty($this->input('registeredCustomersBetwLast'))) {
                    $validator->errors()->add('registeredCustomersBetwLast', __('marketing.BACKEND_MKTCAMPAIGN_REGISTERED_END_DATE_REQUIRED', [], $lang));
                }
            }

            // Customers who have reached a specific tier
            if (in_array(CAMPAIGN_WHO_WILL_CUSTOMERS_REACHED_SPECIFIC_TIER, $whoWillReceive)) {
                if (empty($this->input('movedFromCustomerTypeId'))) {
                    $validator->errors()->add('movedFromCustomerTypeId', __('marketing.BACKEND_MKTCAMPAIGN_TIER_CHANGED_FROM_REQUIRED', [], $lang));
                }

                if (empty($this->input('movedToCustomerTypeId'))) {
                    $validator->errors()->add('movedToCustomerTypeId', __('marketing.BACKEND_MKTCAMPAIGN_TIER_CHANGED_TO_REQUIRED', [], $lang));
                }
            }

            // Customers who have more than/less than/between X store credit
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_STORE_CREDIT, $whoWillReceive)) {
                if (is_null($this->input('haveGiftcard'))) {
                    $validator->errors()->add('haveGiftcard', __('marketing.BACKEND_MKTCAMPAIGN_STORE_CREDIT_REQUIRED', [], $lang));
                }
            }

            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSK_PRODUCTS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSK_PRODUCT_ACCOUNTING_GROUPS, $whoWillReceive)
            ) {
                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSK_PRODUCTS, $whoWillReceive)) {
                    if (empty($this->input('lskProducts')) || empty($this->input('lskProductsDays') || empty($this->input('lskProductsQuantity')))) {
                        $validator->errors()->add('bcCategories', __('marketing.BACKEND_MKTCAMPAIGN_POS_PRODUCTS_REQUIRED', ['{posName}' => 'Lightspeed K-Series'], $lang));
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSK_PRODUCT_ACCOUNTING_GROUPS, $whoWillReceive)) {
                    if (empty($this->input('lskAccountingGroups')) || empty($this->input('lskAccountingGroupsDays'))) {
                        $validator->errors()->add('bcProducts', __('marketing.BACKEND_MKTCAMPAIGN_POS_ACCOUNTING_GROUPS_REQUIRED', ['{posName}' => 'Lightspeed K-Series'], $lang));
                    }
                }
            }

            // QR Code Filters
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_QRCODE_PRODUCTS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_QRCODE_BRANDS, $whoWillReceive)) {
                // Checking QR Code Products
                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_QRCODE_PRODUCTS, $whoWillReceive)) {
                    if (empty($this->input('qrCodeProducts')) || empty($this->input('qrCodeProductsDays'))) {
                        $validator->errors()->add('qrCodeProducts', __('marketing.BACKEND_MKTCAMPAIGN_INTEGRATION_PRODUCTS_REQUIRED', ['{posName}' => 'QRCODE'], $lang));
                    }
                }
            }

            // Campaigned Offers/Redemptions
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_CAMPAIGNED_OFFERS_REDEMPTIONS, $whoWillReceive)) {
                if (empty($this->input('campaignedOffersRedemptionsDays'))) {
                    $validator->errors()->add(
                        'campaignedOffersRedemptionsDays',
                        __('marketing.BACKEND_MKTCAMPAIGN_CAMPAIGNED_OFFERS_REDEMPTIONS_DAYS', ['{posName}' => 'QRCODE'], $lang)
                    );
                }
                if (empty($this->input('campaignedOffersRedemptionsCampaignIds'))) {
                    $validator->errors()->add(
                        'campaignedOffersRedemptionsCampaignIds',
                        __('marketing.BACKEND_MKTCAMPAIGN_CAMPAIGNED_OFFERS_REDEMPTIONS_CAMPAIGN_IDS', ['{posName}' => 'QRCODE'], $lang)
                    );
                }
                if (empty($this->input('campaignedOffersRedemptionsBranches'))) {
                    $validator->errors()->add(
                        'campaignedOffersRedemptionsBranches',
                        __('marketing.BACKEND_MKTCAMPAIGN_CAMPAIGNED_OFFERS_REDEMPTIONS_BRANCHES', ['{posName}' => 'QRCODE'], $lang)
                    );
                }
                if (empty($this->input('campaignedOffersRedemptionsOffersList')) && empty($this->input('campaignedOffersRedemptionsRedemptionsList'))) {
                    $validator->errors()->add(
                        'campaignedOffersRedemptionsOffersList',
                        __('marketing.BACKEND_MKTCAMPAIGN_CAMPAIGNED_OFFERS_REDEMPTIONS_LISTS', ['{posName}' => 'QRCODE'], $lang)
                    );
                    $validator->errors()->add(
                        'campaignedOffersRedemptionsRedemptionsList',
                        __('marketing.BACKEND_MKTCAMPAIGN_CAMPAIGNED_OFFERS_REDEMPTIONS_LISTS', ['{posName}' => 'QRCODE'], $lang)
                    );
                }
            }

            // Ecwid filters
            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECWID_CATEGORIES, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECWID_BRANDS, $whoWillReceive) ||
                in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECWID_PRODUCTS, $whoWillReceive)
            ) {
                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECWID_CATEGORIES, $whoWillReceive)) {
                    if (empty($this->input('ecwidCategories')) || empty($this->input('ecwidCategoriesDays'))) {
                        $validator->errors()->add('ecwidCategories', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_CATEGORY_REQUIRED', ['{posName}' => 'Ecwid'], $lang));
                    } else {
                        $ecwidCats = json_decode($this->input('ecwidCategories'), true);
                        foreach ($ecwidCats as $ecwidCat) {
                            if (!isset($ecwidCat['account_id'])) {
                                $validator->errors()->add('ecwidCategories', 'ecwidCategories.*.account_id is required');
                            }
                            if (!isset($ecwidCat['categoryId'])) {
                                $validator->errors()->add('ecwidCategories', 'ecwidCategories.*.categoryId is required');
                            }
                        }

                        $ecwidAccounts = array_unique(array_column($ecwidCats, 'account_id'));
                        foreach ($ecwidAccounts as $account) {
                            if (!$this->isIntegrationAccountIdValid($business->business_pk, POS_SYSTEM_ECWID, $account)) {
                                $validator->errors()->add('ecwidCategories', 'ecwidCategories.*.account_id is invalid');
                            }
                        }
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECWID_PRODUCTS, $whoWillReceive)) {
                    if (empty($this->input('ecwidProducts')) || empty($this->input('ecwidProductsDays'))) {
                        $validator->errors()->add('ecwidProducts', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_PRODUCTS_REQUIRED', ['{posName}' => 'Ecwid'], $lang));
                    } else {
                        $ecwidProducts = json_decode($this->input('ecwidProducts'), true);
                        foreach ($ecwidProducts as $ecwidProduct) {
                            if (!isset($ecwidProduct['account_id'])) {
                                $validator->errors()->add('ecwidProducts', 'ecwidProducts.*.account_id is required');
                            }
                            if (!isset($ecwidProduct['itemId'])) {
                                $validator->errors()->add('ecwidProducts', 'ecwidProducts.*.itemId is required');
                            }
                        }

                        $ecwidAccounts = array_unique(array_column($ecwidProducts, 'account_id'));
                        foreach ($ecwidAccounts as $account) {
                            if (!$this->isIntegrationAccountIdValid($business->business_pk, POS_SYSTEM_ECWID, $account)) {
                                $validator->errors()->add('ecwidProducts', 'ecwidProducts.*.account_id is invalid');
                            }
                        }
                    }
                }

                if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECWID_BRANDS, $whoWillReceive)) {
                    if (empty($this->input('ecwidBrands')) || empty($this->input('ecwidBrandsDays'))) {
                        $validator->errors()->add('ecwidBrands', __('marketing.BACKEND_MKTCAMPAIGN_ECOM_BRAND_REQUIRED', ['{posName}' => 'Ecwid'], $lang));
                    } else {
                        $ecwidBrands = json_decode($this->input('ecwidBrands'), true);
                        foreach ($ecwidBrands as $ecwidBrand) {
                            if (!isset($ecwidBrand['account_id'])) {
                                $validator->errors()->add('ecwidBrands', 'ecwidBrands.*.account_id is required');
                            }
                            if (!isset($ecwidBrand['itemId'])) {
                                $validator->errors()->add('ecwidBrands', 'ecwidBrands.*.itemId is required');
                            }
                        }
                        $ecwidAccounts = array_unique(array_column($ecwidBrands, 'account_id'));
                        foreach ($ecwidAccounts as $account) {
                            if (!$this->isIntegrationAccountIdValid($business->business_pk, POS_SYSTEM_ECWID, $account)) {
                                $validator->errors()->add('ecwidBrands', 'ecwidBrands.*.account_id is invalid');
                            }
                        }
                    }
                }
            }

            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_REVIEW_CUSTOMERS_HAVE_PURCHASES, $whoWillReceive) && empty($this->input('reviewOrdersDateRange'))) {
                $validator->errors()->add('reviewOrdersDateRange', __('marketing.BACKEND_MKTCAMPAIGN_REVIEW_ORDER_RANGE_REQUIRED', [], $lang));
            }

            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_REVIEW_CUSTOMERS_LAST_PURCHASES, $whoWillReceive) && empty($this->input('reviewLastOrders'))) {
                $validator->errors()->add('reviewLastOrders', __('marketing.BACKEND_MKTCAMPAIGN_REVIEW_LAST_X_ORDER_REQUIRED', [], $lang));
            }

            if (in_array(CAMPAIGN_WHO_WILL_RECEIVE_REVIEW_CUSTOMERS_HAVE_PURCHASES_PRODUCTS, $whoWillReceive)) {
                if (empty($this->input('reviewProducts')) || empty($this->input('reviewProductsDays'))) {
                    $validator->errors()->add('reviewProducts', __('marketing.BACKEND_MKTCAMPAIGN_PRODUCTS_REQUIRED', [], $lang));
                } else {
                    $products = json_decode($this->input('reviewProducts'), true);
                    foreach ($products as $product) {
                        if (!isset($product['account_id'])) {
                            $validator->errors()->add('reviewProducts', 'reviewProducts.*.account_id is required');
                        }
                    }
                    $accounts = array_unique(array_column($products, 'account_id'));
                    foreach ($accounts as $account) {
                        if (!$this->isIntegrationAccountIdValid($business->business_pk, POS_SYSTEM_KANGAROO_REVIEWS, $account)) {
                            $validator->errors()->add('reviewProducts', 'reviewProducts.*.account_id is invalid');
                        }
                    }
                }
            }

        });
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages()
    {
        $lang = $this->getLang();

        return [
            "whoWillReceive.required" => __('marketing.BACKEND_MKTCAMPAIGN_RECEIVE_NOT_SELECTED', [], $lang),
            "ptsExpireInactiveDays.min" => __('marketing.BACKEND_MKTCAMPAIGN_DAYS_BEFORE_EXPIRATION_ERROR', [], $lang),
            "ptsExpireDays.min" => __('marketing.BACKEND_MKTCAMPAIGN_DAYS_BEFORE_EXPIRATION_ERROR', [], $lang),
            "excCustPrevRegCampDays.min" => __('marketing.BACKEND_MKTCAMPAIGN_PREV_REG_CAMP', [], $lang),
            "excCustPrevAutoCampDays.min" => __('marketing.BACKEND_MKTCAMPAIGN_PREV_AUTO_CAMP', [], $lang),
            'noTrxBranchIds.required' => __('marketing.BACKEND_MKTCAMPAIGN_LAST_PURCHASES_BRANCHES_ARRAY_REQUIRED', [], $lang),
            'noTrxBranchIds.array' => __('marketing.BACKEND_MKTCAMPAIGN_LAST_PURCHASES_BRANCHES_ARRAY_REQUIRED', [], $lang),
            'noTrxBranchIds.*.required_with' => __('marketing.BACKEND_MKTCAMPAIGN_LAST_PURCHASES_BRANCHES_ID_REQUIRED', [], $lang),
            'noTrxBranchIds.*.integer' => __('marketing.BACKEND_MKTCAMPAIGN_LAST_PURCHASES_BRANCHES_ID_INTEGER_REQUIRED', [], $lang)
        ];
    }

    private function getLang()
    {
        if (!$this->lang) {
            $this->lang = App::getLocale();
        }

        return $this->lang;
    }

    private function isIntegrationAccountIdValid($businessId, $posSystemId, $account)
    {
        $posBranch = PosBranch::where([
            'business_fk' => $businessId,
            'enabled' => 1,
            'account_id' => $account,
            'pos_system_fk' => $posSystemId,
        ])
            ->first();
        if ($posBranch) {
            return true;
        }

        return false;
    }
}
