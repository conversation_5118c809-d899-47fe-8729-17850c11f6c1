<?php

namespace Tests\Services;

use Tests\TestCase;
use Mockery;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Config;
use Carbon\Carbon;
use App\Services\LoginMonitoringService;
use App\Notifications\SystemLoginAlert;
use Illuminate\Database\Query\Expression;

class LoginMonitoringServiceTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        Config::set('app.MAIL_TECH_SUPPORT', '<EMAIL>');
        Carbon::setTestNow(Carbon::create(2023, 1, 1, 12, 0, 0));
        DB::shouldReceive('raw')->andReturnUsing(fn ($value) => new Expression($value));
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow();
        Mockery::close();
        parent::tearDown();
    }

    public function testVolumeSpikeDetected(): void
    {
        $db = Mockery::mock();
        DB::shouldReceive('connection')->with('audit_external')->andReturn($db);

        $loginQ = Mockery::mock();
        $attemptQ = Mockery::mock();
        $db->shouldReceive('table')->with('user_logins')->andReturn($loginQ);
        $db->shouldReceive('table')->with('user_login_attempts')->andReturn($attemptQ);

        // Volume spike: successful = 1, failed = 0
        $loginQ->shouldReceive('where')->andReturnSelf();
        $loginQ->shouldReceive('whereIn')->andReturnSelf();
        $loginQ->shouldReceive('count')->andReturn(1);
        $attemptQ->shouldReceive('where')->andReturnSelf();
        $attemptQ->shouldReceive('whereIn')->andReturnSelf();
        $attemptQ->shouldReceive('count')->andReturn(0);

        // Baseline calculation
        $loginQ->shouldReceive('selectRaw')->andReturnSelf();
        $loginQ->shouldReceive('whereBetween')->andReturnSelf();
        $loginQ->shouldReceive('groupByRaw')->andReturnSelf();
        $loginQ->shouldReceive('pluck')->andReturn(collect([0]));
        $attemptQ->shouldReceive('selectRaw')->andReturnSelf();
        $attemptQ->shouldReceive('whereBetween')->andReturnSelf();
        $attemptQ->shouldReceive('groupByRaw')->andReturnSelf();
        $attemptQ->shouldReceive('pluck')->andReturn(collect([0]));

        // No per-user failures
        $attemptQ->shouldReceive('select')->andReturnSelf();
        $attemptQ->shouldReceive('groupBy')->andReturnSelf();
        $attemptQ->shouldReceive('having')->andReturnSelf();
        $attemptQ->shouldReceive('get')->andReturn(collect([]));

        // No location anomalies
        $loginQ->shouldReceive('select')->andReturnSelf();
        $loginQ->shouldReceive('orderBy')->twice()->andReturnSelf();
        $loginQ->shouldReceive('get')->andReturn(collect([]));

        $service = new LoginMonitoringService();
        $service->monitorLoginPatterns();

        $alerts = $service->getAlerts();
        $this->assertCount(1, $alerts);
        $this->assertStringContainsString(
            'High volume of successful logins (1 in last 15 min, threshold: 1)',
            $alerts[0]
        );
    }

    public function testPerUserFailureDetected(): void
    {
        $db = Mockery::mock();
        DB::shouldReceive('connection')->with('audit_external')->andReturn($db);

        $loginQ = Mockery::mock();
        $attemptQ = Mockery::mock();
        $db->shouldReceive('table')->with('user_logins')->andReturn($loginQ);
        $db->shouldReceive('table')->with('user_login_attempts')->andReturn($attemptQ);

        // No volume spikes
        $loginQ->shouldReceive('where')->andReturnSelf();
        $loginQ->shouldReceive('whereIn')->andReturnSelf();
        $loginQ->shouldReceive('count')->andReturn(0);
        $attemptQ->shouldReceive('where')->andReturnSelf();
        $attemptQ->shouldReceive('whereIn')->andReturnSelf();
        $attemptQ->shouldReceive('count')->andReturn(0);

        // Baseline calculation
        $loginQ->shouldReceive('selectRaw')->andReturnSelf();
        $loginQ->shouldReceive('whereBetween')->andReturnSelf();
        $loginQ->shouldReceive('groupByRaw')->andReturnSelf();
        $loginQ->shouldReceive('pluck')->andReturn(collect([0]));
        $attemptQ->shouldReceive('selectRaw')->andReturnSelf();
        $attemptQ->shouldReceive('whereBetween')->andReturnSelf();
        $attemptQ->shouldReceive('groupByRaw')->andReturnSelf();
        $attemptQ->shouldReceive('pluck')->andReturn(collect([0]));

        // Per-user failures
        $failure = (object)['username_hash' => 'hash1', 'cnt' => 5];
        $attemptQ->shouldReceive('select')->andReturnSelf();
        $attemptQ->shouldReceive('groupBy')->andReturnSelf();
        $attemptQ->shouldReceive('having')->andReturnSelf();
        $attemptQ->shouldReceive('get')->andReturn(collect([$failure]));

        // No location anomalies
        $loginQ->shouldReceive('select')->andReturnSelf();
        $loginQ->shouldReceive('orderBy')->twice()->andReturnSelf();
        $loginQ->shouldReceive('get')->andReturn(collect([]));

        $service = new LoginMonitoringService();
        $service->monitorLoginPatterns();

        $alerts = $service->getAlerts();
        $this->assertCount(1, $alerts);
        $this->assertStringContainsString(
            'Username hash hash1 had 5 failed attempts in last 15 min',
            $alerts[0]
        );
    }

    public function testLocationAnomalyDetected(): void
    {
        $db = Mockery::mock();
        DB::shouldReceive('connection')->with('audit_external')->andReturn($db);

        $loginQ = Mockery::mock();
        $attemptQ = Mockery::mock();
        $db->shouldReceive('table')->with('user_logins')->andReturn($loginQ);
        $db->shouldReceive('table')->with('user_login_attempts')->andReturn($attemptQ);

        // No volume spikes
        $loginQ->shouldReceive('where')->andReturnSelf();
        $loginQ->shouldReceive('whereIn')->andReturnSelf();
        $loginQ->shouldReceive('count')->andReturn(0);
        $attemptQ->shouldReceive('where')->andReturnSelf();
        $attemptQ->shouldReceive('whereIn')->andReturnSelf();
        $attemptQ->shouldReceive('count')->andReturn(0);

        // Baseline calculation
        $loginQ->shouldReceive('selectRaw')->andReturnSelf();
        $loginQ->shouldReceive('whereBetween')->andReturnSelf();
        $loginQ->shouldReceive('groupByRaw')->andReturnSelf();
        $loginQ->shouldReceive('pluck')->andReturn(collect([0]));
        $attemptQ->shouldReceive('selectRaw')->andReturnSelf();
        $attemptQ->shouldReceive('whereBetween')->andReturnSelf();
        $attemptQ->shouldReceive('groupByRaw')->andReturnSelf();
        $attemptQ->shouldReceive('pluck')->andReturn(collect([0]));

        // No per-user failures
        $attemptQ->shouldReceive('select')->andReturnSelf();
        $attemptQ->shouldReceive('groupBy')->andReturnSelf();
        $attemptQ->shouldReceive('having')->andReturnSelf();
        $attemptQ->shouldReceive('get')->andReturn(collect([]));

        // Recent login from a new location
        $recent = (object)[
            'id' => 1,
            'user_id' => 42,
            'country' => 'US',
            'region' => 'CA',
            'created_at' => '2023-01-01 12:00:00',
            'ip' => '***********',
            'details' => null
        ];
        
        // First call for recent logins
        $loginQ->shouldReceive('select')->andReturnSelf();
        $loginQ->shouldReceive('orderBy')->twice()->andReturnSelf();
        $loginQ->shouldReceive('get')->andReturn(collect([$recent]), collect([]));

        $service = $this->getMockBuilder(LoginMonitoringService::class)
            ->onlyMethods(['processLocationData', 'getHistoricalLocations'])
            ->getMock();
            
        $service->expects($this->once())
            ->method('processLocationData')
            ->willReturn([$recent]);
            
        $service->expects($this->once())
            ->method('getHistoricalLocations')
            ->willReturn([]);

        $service->monitorLoginPatterns();

        $alerts = $service->getAlerts();
        $this->assertCount(1, $alerts);
        $this->assertStringContainsString(
            'User 42 logged in from a new location US/CA at 2023-01-01 12:00:00',
            $alerts[0]
        );
    }

    public function testSendAlertNotificationsWithAndWithoutAlerts(): void
    {
        Notification::fake();

        $service = new LoginMonitoringService();
        // no alerts => nothing sent
        $this->assertFalse($service->sendAlertNotifications());
        Notification::assertNothingSent();

        // with alerts => sent
        $service->setRecipients(['<EMAIL>']);
        $ref = new \ReflectionClass($service);
        $alertsProp = $ref->getProperty('alerts');
        $alertsProp->setAccessible(true);
        $alertsProp->setValue($service, ['test alert']);
        $timeProp = $ref->getProperty('monitoringTime');
        $timeProp->setAccessible(true);
        $timeProp->setValue($service, Carbon::now());

        $this->assertTrue($service->sendAlertNotifications());
        Notification::assertSentOnDemand(SystemLoginAlert::class);
    }
}