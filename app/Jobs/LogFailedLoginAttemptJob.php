<?php

namespace App\Jobs;

use Google\Cloud\BigQuery\BigQueryClient;
use Illuminate\Support\Facades\Log;

class LogFailedLoginAttemptJob extends Job
{
    /**
     * The request info
     *
     * @var array
     */
    private array $data = [];

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        if (config('app.env') !== 'production') {
            return;
        }

        try {
            $bigQuery = new BigQueryClient([
                'projectId' => config('app.GOOGLE_CLOUD_PROJECT'),
                'keyFilePath' => base_path() . '/' . config('app.GOOGLE_APPLICATION_CREDENTIALS'),
            ]);

            $dataset = $bigQuery->dataset('mc_kng_audit');
            $table = $dataset->table('user_failed_logins');

            $data = ['data' => $this->data];
            $r = $table->insertRows([$data]);

            if (!$r->isSuccessful()) {
                $insertErrors = $r->info()['insertErrors'] ?? [];
                foreach ($insertErrors as $error) {
                    Log::warning(__METHOD__, ['error' => $error, 'data' => $this->data]);
                }
            }
        } catch (\Exception $e) {
            Log::warning($e, [$this->data]);
        }
    }
}
