<?php

namespace App\Http\Resources;

use App\Traits\EmployeePermissionTrait;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class BusinessRoleResource extends JsonResource
{
    

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $business = $request->appBusiness();

        $includeArr = $request->include ? array_map('trim', explode(',', $request->include)) : [];

        $includeEmployees = in_array('employees', $includeArr, true);

        return [
            $this::$wrap => [
                'id' => $this->id,
                'name' => $this->name,
                'description' => $this->description,
                'created_at' => (new Carbon($this->created_at))->toIso8601String(),
                'permission_id' => $this->permission_id,
                $this->mergeWhen($includeEmployees, [
                    'employees' => $this->getEmployeesForRole(),
                ]),
            ]
        ];
    }

    private function getEmployeesForRole()
    {
        $employees = EmployeePermissionTrait::getEmployeesForRole($this);

        if (!$employees) {
            return [];
        }

        $r = [];

        foreach ($employees as $employee) {
            $r[] = $this->transformEmployee($employee);
        }
        return $r;
    }

    private function transformEmployee($employee)
    {
        return [
            'id' => $employee->id,
            'email' => trim($employee->email),
            'first_name' => $employee->first,
            'last_name' => $employee->last,
        ];
    }
}
