<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ImportTransaction extends Model
{
    protected $table = 'import_transactions';
    protected $primaryKey = 'import_transactions_pk';
    protected $connection = 'tenant';

    const CREATED_AT = 'utc_created';
    const UPDATED_AT = 'utc_updated';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        //
    ];
}
