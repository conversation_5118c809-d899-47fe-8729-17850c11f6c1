<?php

namespace App\Helpers\V1;

use App\Models\V1\Activity;
use App\Models\V1\BusinessRules;
use App\Models\V1\Notification;
use App\Models\V1\Offer;
use App\Models\V1\UserOffer;
use App\Models\V1\UserOfferStory;

class GetCouponTransaction extends TransactionHelper
{
    public function validate()
    {
        $this->prepareForValidation();

        //For REDEEM the customer must exist
        if (!$this->customer) {
            throw new \LogicException('Customer not found. The customer MUST exist when redeeming.');
        }

        $this->offer = Offer::getDetails($this->data['offer']['id']);

        if (!$this->offer) {
            throw new \LogicException('Offer ' . $this->data['offer']['id'] . ' not found');
        }
    }

    /**
     * Create Transaction, Purchase and Activity
     * In specified order
     *
     * @return self
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @version 1.0
     */
    public function process(): self
    {
        /** @var GetCouponTransaction $this */
        $this->createTransaction()
            ->createActivity()
            ->createNotification()
            ->createUserOfferStory()
            ->createOrUpdateCustomizableTransactionDetails();

        return $this;
    }

    /**
     * @return $this
     * <AUTHOR>
     * @access protected
     * @version 1.0
     * @since 1.0
     */
    protected function createPurchase(): self
    {
        return $this;
    }

    protected function preparePurchaseParams()
    {
        //
    }

    protected function getTrxSubType()
    {
        //
    }

    /**
     * @return $this
     * <AUTHOR>
     * @access protected
     * @version 1.0
     * @since 1.0
     */
    protected function createUserOfferStory(): self
    {
        $userOffer = UserOffer::where('user_fk', $this->customer->id)
            ->where('offer_fk', $this->offer->offer_pk)
            ->first();

        if (!$userOffer) {
            $userOffer = new UserOffer;
            $userOffer->user_fk = $this->customer->id;
            $userOffer->offer_fk = $this->offer->offer_pk;
            $userOffer->current_offer_status = TRX_SUBTYPE_COUPON_GET_COUPON;
            $userOffer->timezone_mysql_fk = $this->branch->branch_timezone_fk;
            $userOffer->save();
        }

        $couponUserOfferStory = new UserOfferStory;
        $couponUserOfferStory->utc_created = date('Y-m-d H:i:s');
        $couponUserOfferStory->user_offer_fk = $userOffer->user_offer_pk;
        $couponUserOfferStory->transaction_fk = $this->transaction->transaction_pk;
        $couponUserOfferStory->timezone_mysql_fk = $this->branch->branch_timezone_fk;
        $couponUserOfferStory->qrcode = Offer::generateQRcode();
        $couponUserOfferStory->units_awarded = null;
        $couponUserOfferStory->save();

        $this->userOfferStory = $couponUserOfferStory;
        return $this;
    }

    /**
     * Create Activity
     * When coalition this will contain only activity record for coalition business
     * not coalition members
     *
     * @return $this
     * @since 1.0
     * <AUTHOR>
     * @access protected
     * @version 1.0
     */
    protected function createActivity(): self
    {
        $this->businessRules = BusinessRules::forBusinessWithTaxes($this->business->business_pk);

        Activity::create([
            'description' => 'ACTIVITY_COUPON_CLAIM',
            'utc_publish' => date('Y-m-d H:i:s'),
            'units_exchange' => 0,
            'amount' => 0,
            'transaction_fk' => $this->transaction->transaction_pk,
            'hidden' => $this->businessRules->silo_flag,
            'timezone_mysql_fk' => $this->branch->branch_timezone_fk,
        ]);

        return $this;
    }

    /**
     * Create Notification
     * When coalition this will contain only notification record for coalition business
     * not coalition members
     *
     * @return $this
     * @since 1.0
     * <AUTHOR>
     * @access protected
     * @version 1.0
     */
    protected function createNotification(): self
    {
        $notification = Notification::create([
            'notificationRequester' => 41,
            'notification_text' => 'NOTIF_TEXT_COUPON_CLAIM',
            'notificationType' => NOTIFICATION_TYPE_NOTIFICATION,
            'units_exchange' => 0,
            'amount' => 0,
            'user_receiver_fk' => $this->customer->id,
            'user_sender_fk' => $this->customer->id,
            'business_fk' => $this->business->business_pk,
            'business_branch_fk' => $this->branch->business_branch_pk,
            'timezone_mysql_fk' => $this->branch->branch_timezone_fk,
            'offer_fk' => $this->offer->offer_pk,
            'utc_publish' => date('Y-m-d H:i:s'),
        ]);

        $this->notification = $notification;
        return $this;
    }

    /**
     * @return self
     * <AUTHOR>
     * @access protected
     * @version 1.0
     * @since 1.0
     */
    protected function updateBalance(): self
    {
        return $this;
    }

    /**
     * Prepare Attributes for Transaction Model to be created
     *
     * @return mixed - attributes for Transaction model
     * @since 1.0
     * <AUTHOR>
     * @access protected
     * @version 1.0
     */
    protected function prepareTransactionParams(): array
    {
        $t = [];
        $t['entity_fk'] = $this->branch->business_branch_pk;
        $t['entity_type_fk'] = BRANCH_POINTS;
        $t['transaction_type_fk'] = TRX_TYPE_POINTS_PURCHASE;
        $t['transaction_sub_type_fk'] = TRX_SUBTYPE_COUPON_GET_COUPON;
        $t['user_fk'] = $this->customer->id;
        $t['timezone_mysql_fk'] = $this->branch->branch_timezone_fk;

        return $t;
    }
}
