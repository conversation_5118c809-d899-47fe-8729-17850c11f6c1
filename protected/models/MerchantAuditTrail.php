<?php

/**
 * This is the model class for table "merchant_audit_trail".
 *
 * The followings are the available columns in table 'merchant_audit_trail':
 * @property string $id
 * @property string $user_id
 * @property string $business_id
 * @property string $branch_id
 * @property string $db_name
 * @property string $class_name
 * @property string $session_id
 * @property string $ip
 * @property string $created_at
 * @property string $attributes
 */
class MerchantAuditTrail extends CActiveRecord
{
    public static $db = null;

    /**
     * Overides the model getDbConnection to use logging database
     * Returns the database connection used by active record.
     * By default, the "db" application component is used as the database connection.
     * You may override this method if you want to use a different database connection.
     * @return CDbConnection the database connection used by active record.
     * @throws CDbException if "db" application component is not defined
     * @throws CException
     */
    public function getDbConnection()
    {
        if (self::$db !== null) {
            return self::$db;
        } else {
            self::$db = Yii::app()->db_audit;
            if (self::$db instanceof CDbConnection) {
                self::$db->setActive(true);
                return self::$db;
            } else {
                throw new CDbException(Yii::t('yii','Active Record requires a "logging" CDbConnection application component.'));
            }
        }
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'mc_kng_audit.merchant_audit_trail';
//        return 'merchant_audit_trail';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('db_name, class_name, model_primary_key, model_attributes', 'required'),
            array('user_id, business_id, branch_id', 'length', 'max' => 11),
            array('db_name, session_id', 'length', 'max' => 100),
            array('class_name', 'length', 'max' => 50),
            array('ip', 'length', 'max' => 15),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('id, user_id, business_id, branch_id, db_name, class_name, session_id, request_id, ip, created_at, model_primary_key, model_attributes, system_user_id, comments', 'safe'),
            array('id, user_id, business_id, branch_id, db_name, class_name, session_id, request_id, ip, created_at, model_primary_key, model_attributes, system_user_id, comments', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'id' => 'ID',
            'user_id' => 'User',
            'business_id' => 'Business',
            'branch_id' => 'Branch',
            'db_name' => 'Db Name',
            'class_name' => 'Class Name',
            'session_id' => 'Session',
            'ip' => 'Ip',
            'created_at' => 'Created At',
            'model_attributes' => 'Attributes',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id, true);
        $criteria->compare('user_id', $this->user_id, true);
        $criteria->compare('business_id', $this->business_id, true);
        $criteria->compare('branch_id', $this->branch_id, true);
        $criteria->compare('db_name', $this->db_name, true);
        $criteria->compare('class_name', $this->class_name, true);
        $criteria->compare('session_id', $this->session_id, true);
        $criteria->compare('ip', $this->ip, true);
        $criteria->compare('created_at', $this->created_at, true);
        $criteria->compare('model_attributes', $this->model_attributes, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    /**
    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return MerchantAuditTrail the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    // public function beforeSave()
    // {
    //     if ($this->isNewRecord) {
    //         $this->utc_created = new CDbExpression('NOW()');
    //     }

    //     return parent::beforeSave();

    // }

    public static function handle($model, $traceAsString = '')
    {
        try {
            $isWebApplication = Yii::app() instanceof CWebApplication;
            $className = get_class($model);

            if ($isWebApplication) {
                // Allow all from web
            } elseif (in_array($className, ['FrequentBuyerDetails', 'UserEntityPoints', 'EntityCampaign'])) {
                // Allow only specific models when in console mode
            } else {
                // Do not handle from console, cron jobs
                return;
            }

            // Do not save the audit trail for non-production environments
            if (Yii::app()->params['envParam'] !== 'PROD' && in_array($className, ['FrequentBuyerDetails', 'UserEntityPoints'])) {
                return;
            }

            $userId =  $isWebApplication ? Yii::app()->user->id : null;
            $primaryKey = $model->getPrimaryKey();
            $dbName = Utils::getDsnAttribute('dbname');

            $systemUserId = null;
            if ($isWebApplication && null != Yii::app()->session->get('loginas_user_id')) {
                $systemUserId = $userId;
                $userId = Yii::app()->session->get('loginas_user_id');
            }

            //start a background job for each account separately
            // $jobId = Yii::app()->resque->createJob('audit_trail', 'Worker_AuditTrail', [
            //     'userId' => $userId,
            //     'className' => $className,
            //     'primaryKey' => $primaryKey,
            //     'systemUserId' => $systemUserId,
            //     'db' => $dbName,
                // todo add request ID and session ID in params
            // ], true);

            // start the job that will save;
            self::create($userId, $className, $primaryKey, $dbName, $systemUserId, $model, $traceAsString);
        } catch (Exception $e) {
            Yii::log($e, CLogger::LEVEL_ERROR, Yii::app()->name . '_' . __METHOD__);
        }
    }

    public static function create($userId, $className, $primaryKey, $dbName, $systemUserId = null, $model = null, $traceAsString = '')
    {
        $requestId = Yii::app()->request->getRequestId();
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;

        try {
            $isWebApplication = Yii::app() instanceof CWebApplication;

            if (Yii::app()->authManager->checkAccess('superadmin', $userId)) {
                $systemUserId = $userId;
            } elseif (Yii::app()->authManager->checkAccess('merchantsetup', $userId)) {
                $systemUserId = $userId;
            }

            $userEntities = UserEntityPermission::model()->findByAttributes(array('user_fk' => $userId));

            if (!$userId || !$userEntities) {
                // Yii::log('No userEntities found for userId=' . $userId . ' systemUserId=' . $systemUserId, CLogger::LEVEL_WARNING, $platformName . '_' . __METHOD__);
                // return;
                $userId = null;
            }

            if (Yii::app()->authManager->checkAccess('business', $userId)) {
                $businessId = $userEntities->entity_fk; //business Id
                $entityTypeFk = $userEntities->entity_type_fk; //1,7,99
                $defaultBranch = BusinessBranch::getDefaultBranch($businessId);
                $branchId = $defaultBranch['business_branch_pk']; //main Branch Id
            } elseif (Yii::app()->authManager->checkAccess('branch', $userId)) {
                $branchId = $userEntities->entity_fk; //his branch Id
                $entityTypeFk = $userEntities->entity_type_fk; //2,8,100
                $branchModel = BusinessBranch::model()->findByPk($branchId);
                $businessId = $branchModel->business_fk;
            } else {
                // Yii::log('authManager->checkAccess User does not have Business or Branch role userId=' . $userId, CLogger::LEVEL_WARNING, $platformName . '_' . __METHOD__);
                $businessId = null;
                $branchId = null;
                // return;
            }

            $context = [
                'application' => get_class(Yii::app()),
                'appId' => Yii::app()->getId(),
                'traceAsString' => $traceAsString,
            ];

            if (!$model) {
                $model = $className::model()->findByPk($primaryKey);
            }

            $modelAttributes = $model->attributes;
            if ($className === 'Business' && isset($model->business_uid)) {
                unset($modelAttributes['business_uid']);
            }

            $trail = new MerchantAuditTrail;
            $trail->user_id = $userId;
            $trail->business_id = $businessId;
            $trail->branch_id = $branchId;
            $trail->db_name = $dbName;
            $trail->class_name = $className;
            $trail->model_primary_key = $primaryKey;
            $trail->model_attributes = json_encode($modelAttributes);
            $trail->session_id = $isWebApplication ? session_id() : null;
            $trail->request_id = $requestId;
            $trail->system_user_id = $systemUserId;
            $trail->ip = Yii::app()->request->userHostAddress;
            $trail->comments = json_encode($context);

            Utils::saveModel($trail);
        } catch (Exception $e) {
            Yii::log($e, CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
        }
    }
}
