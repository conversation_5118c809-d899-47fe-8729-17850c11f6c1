## 1. Project purpose & coding guidelines

The project is named Members API, and its goals and style rules are captured in the API‑specific README. In particular, it
establishes a uniform, resource‑oriented REST API with clear separation of concerns (controllers only handle HTTP, business logic
lives in Services/Traits/Jobs, validation in FormRequests, etc.):

```markdown
    # Members API

    ## Purpose
    The goal of these guidelines is to create uniform coding habits among software
    personnel in the engineering department so that reading, checking, and maintaining
    code written by different persons becomes easier…

readme-api.md
```
--------------------------------------------------------------------------------------------------------------------------------

## 2. Core framework & dependencies

This is a Laravel project (Laravel 9.x on PHP 8.1+), wired up with Passport/Sanctum for API auth and a slew of first‑ and
third‑party packages:

```json
        "require": {
            "php": "^8.1.0",
            …
            "laravel/framework": "^9.52",
            "laravel/passport": "^11.8",
            "laravel/sanctum": "^3.3",
            …
        },
```
composer.json

You’ll also find dev tools for static analysis, code style, and testing under require-dev (PHPStan, Psalm, PHPCS, Rector,
PHPUnit, etc.).

--------------------------------------------------------------------------------------------------------------------------------

## 3. In‑repo PSR‑4 packages

Beyond the core Laravel app (app/), common or module‑specific logic is factored into in‑repo PSR‑4 “packages” under src/, each
namespaced KangarooRewards\…. For example:

```
    "autoload": {
      "psr-4": {
        "App\\": "app/",
        …
        "KangarooRewards\\Survey\\": "src/Survey/",
        "KangarooRewards\\Common\\": "src/Common/",
        "KangarooRewards\\Reports\\": "src/Reports",
        …
      }
    },

composer.json
```
--------------------------------------------------------------------------------------------------------------------------------

## 4. Directory structure

At the top level you’ll see the familiar Laravel layout plus some extras:

```
    .
    ├── app/               # HTTP layer & business logic (Controllers, Models, Services, Traits, Jobs…)
    ├── config/            # Laravel and package configuration
    ├── database/          # Migrations, factories, seeders
    ├── docs/              # Feature‑level documentation (e.g. Campaign Audience, Reports, Message Queue…)
    ├── public/            # Web root (index.php), assets
    ├── resources/         # Views, translations, frontend assets
    ├── routes/            # Route definitions (apiV1, apiPublic, web.php, etc.)
    ├── src/               # In‑repo PSR‑4 packages (KangarooRewards/*)
    ├── storage/           # Logs, cache, uploaded files
    ├── tests/             # PHPUnit & Testbench tests
    ├── tools/             # Dev‑helper scripts
    ├── vendor/            # Composer dependencies
    ├── artisan            # Laravel CLI
    ├── composer.json      # PHP dependencies & autoloading
    ├── phpcs.xml          # PHP_CodeSniffer rules
    ├── phpstan.neon       # PHPStan config
    ├── psalm.xml          # Psalm config
    ├── rector.php         # Rector config
    ├── qodana.yaml        # Qodana (JetBrains) config
    ├── readme-api.md      # API guidelines (see above)
    ├── readme-other.md    # Misc tips (e.g. CORS setup)
    ├── setup.md           # Supervisor/queue/docs setup
    └── …                  # and other standard Laravel files (package.json, webpack.mix.js, server.php, etc.)
```
--------------------------------------------------------------------------------------------------------------------------------

## 5. Routes & HTTP layer

All your endpoint definitions live under routes/:
```
    routes/
    ├── apiV1.php      # v1 authenticated API routes for business employees
    ├── apiV2.php      # v2 API routes (future, if any)
    ├── apiPublic.php  # public (unauthenticated) API
    ├── apiUsers.php   # user‑scoped API
    ├── web.php        # web‑facing routes
    …

routes/apiV1.php
```
Controllers are strictly resource‑oriented; validation uses FormRequest classes in app/Http/Requests/V1, and business logic is
delegated to Services/Jobs/Traits under app/Services, app/Jobs, etc.

--------------------------------------------------------------------------------------------------------------------------------

## 6. Feature documentation

The docs/ folder contains detailed write‑ups on key features and domain logic. For example:

```
    # Campaign Audience

    The target audience refers to a specific group of consumers…

docs/Campagin_Audience.md

    # Reports

    ## Authentication

    All the endpoints require OAuth2 resource owner authentication. An employee must be authenticated…

docs/Reports.md 
```

You’ll also find docs on Message Queue patterns, Google Sign‑in, ChatGPT integration, etc.

--------------------------------------------------------------------------------------------------------------------------------

## 7. Coding standards & static analysis

A suite of config files is provided so you can run linting, formatting, and static checks:

┌────────────────────┬──────────────┬───────────────────────────────────────────┐
│ Tool               │ Config File  │ Command Example                           │
├────────────────────┼──────────────┼───────────────────────────────────────────┤
│ PHP_CodeSniffer    │ phpcs.xml    │ ./vendor/bin/phpcs                        │
├────────────────────┼──────────────┼───────────────────────────────────────────┤
│ PHP CS Fixer       │ —            │ ./vendor/bin/php-cs-fixer fix             │
├────────────────────┼──────────────┼───────────────────────────────────────────┤
│ PHPStan            │ phpstan.neon │ ./vendor/bin/phpstan analyse -l 1 app src │
├────────────────────┼──────────────┼───────────────────────────────────────────┤
│ Psalm              │ psalm.xml    │ ./vendor/bin/psalm                        │
├────────────────────┼──────────────┼───────────────────────────────────────────┤
│ Rector             │ rector.php   │ vendor/bin/rector process app --dry-run   │
├────────────────────┼──────────────┼───────────────────────────────────────────┤
│ Qodana (JetBrains) │ qodana.yaml  │ docker run … jetbrains/qodana-php         │
├────────────────────┼──────────────┼───────────────────────────────────────────┤
│ PHPUnit            │ phpunit.xml  │ ./vendor/bin/phpunit                      │
└────────────────────┴──────────────┴───────────────────────────────────────────┘

Specs for API style (ISO 8601 dates, error handling, resource wrapping, middleware groups, etc.) are scattered through
readme-api.md.

--------------------------------------------------------------------------------------------------------------------------------

## 8. Setup & Dev workflows

See setup.md for instructions on Supervisor/queues and how to generate API docs, and readme-other.md for CORS middleware snippets
and other odds‑and‑ends.

--------------------------------------------------------------------------------------------------------------------------------

### In summary

    * **Laravel‑based** API service (v9.x, PHP 8.1)
    * **Strict MVC + service/job/form‑request separation** following the guidelines in `readme-api.md`
    * **In‑repo shared libraries** under `src/` for reusable KangarooRewards business logic
    * **Rich docs folder** for feature‑level deep dives
    * **Static analysis & CI configs** prewired for quality gates