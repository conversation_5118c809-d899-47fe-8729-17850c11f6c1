<?php

namespace KangarooRewards\Import\Customers\Repositories;

use KangarooRewards\Import\Common\Models\TempImportCustomer;

class TempCustomersRepository
{
    /**
     * Check if there is any duplicate email that has been processed but shares the same email value.
     * This method searches for any record with the specified email that has already been processed.
     *
     * @param string $email
     * @param int $currentBatchId
     * @return mixed
     */
    public static function findDuplicateProcessedEmail(string $email, int $currentBatchId): mixed
    {
        return TempImportCustomer::where('email', $email)
            ->where('batch_import_fk', $currentBatchId)
            ->where('import_status_fk', '!=', IMPORT_STATUS_READY_ID)
            ->exists();
    }

    /**
     * Check if there is any duplicate phone that has been processed but shares the same phone value.
     * This method searches for any record with the specified phone that has already been processed.
     *
     * @param $phone
     * @param int $currentBatchId
     * @return mixed
     */
    public static function findDuplicateProcessedPhone($phone, int $currentBatchId): mixed
    {
        return TempImportCustomer::where('phone', $phone)
            ->where('batch_import_fk', $currentBatchId)
            ->where('import_status_fk', '!=', IMPORT_STATUS_READY_ID)
            ->exists();
    }
}