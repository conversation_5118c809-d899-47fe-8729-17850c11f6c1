<?php

namespace App\Helpers\UserTransaction;

use App\Helpers\V1\Utils;
use App\Jobs\TransactionalMailMessage;
use App\Jobs\TransactionalTextMessage;
use App\Mail\Email;
use App\Models\GiftCardQueue;
use App\Models\V1\ActivationToken;
use App\Models\V1\Activity;
use App\Models\V1\Business;
use App\Models\V1\Offer;
use App\Models\V1\BusinessBranch;
use App\Models\V1\BusinessRules;
use App\Models\V1\Country;
use App\Models\V1\Currency;
use App\Models\V1\Customer;
use App\Models\V1\EmailProvider;
use App\Models\V1\Language;
use App\Models\V1\Notification;
use App\Models\V1\SmsProvider;
use App\Models\V1\Transaction;
use App\Models\V1\BusinessProfile;
use App\Models\V1\UserEntityPoints;
use App\Traits\RelationTrait;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Traits\HasFeatureFlag;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use KangarooRewards\Common\Traits\SmsVendorTrait;

/**
 *
 */
class TransferPointsTransaction extends TransactionBuilder
{
    use SmsVendorTrait;

    /**
     * @var \App\Models\V1\GiftCardQueue
     */
    protected $giftCardQueue;

    /**
     * @var \App\Models\V1\Customer
     */
    protected $receiver;

    protected $isNewReceiver = false;

    protected $receiverPin = null;

    /**
     *  $this->data - from input
     *
     *  {
     *      "intent": "transfer_points",
     *      "from": {
     *           "name" : "Val"
     *       },
     *       "to": {
     *           "name": "John Doe",
     *           "email": "<EMAIL>",
     *           "phone": "(*************", # email or phone required
     *           "country_code": "CA" # required with phone
     *       },
     *       "points": 100,
     *       "language": "en",
     *       "business_id": "11e6960e8bdda1e2bb02089e01cf89b5",
     *       "delivery_date": 1521644622, # optional - Unix Timestamp
     *  }
     */
    public function init()
    {
        $this->subTenant = Business::findUidWithBranch($this->data['business_id']);

        // Sub tenant is required for Transfer
        if (!$this->subTenant) {
            throw new ModelNotFoundException(
                "Business {$this->data['business_id']} not found"
            );
        }

        // By default the branch for tenant
        // It might be overriden when subTenant is set
        $this->finalBranch = BusinessBranch::getInfo($this->subTenant->business_branch_pk);

        $this->customer = RelationTrait::getUserByAppUserForBusiness($this->customer, $this->subTenant);
    }

    /**
     * Build transaction. The order is important
     *
     * @return mixed
     * @throws \Exception
     */
    public function build()
    {
        $this->createGiftCardQueue();

        $this->createTransaction();

        $this->transaction = $this->getTransaction();

        $this->updateBalance();

        $this->createActivity();

        $this->createNotification();

        $this->findOrcreateReceiverUser();

        $this->createNotificationForReceiver();

        $this->sendConfirmation();

        return $this->getTransaction();
    }

    public function validate()
    {
        $this->validatePhone();
        $this->validateSelfTransfer();
        $userBalance = UserEntityPoints::forBusiness(
            $this->customer->id,
            $this->subTenant->business_pk,
            $this->subTenant->entity_type_fk
        );

        if (!$userBalance) {
            throw new \LogicException(
                __('api.FRONTEND_TRANSFER_NOT_ENOUGH_BALANCE', [
                    '{pts_punches}' => 'points',
                    '{business_name}' => $this->subTenant->name,
                ]),
                1
            );
        }

        $userBalanceValue = $this->subTenant->entity_type_fk == BUSINESS_POINTS ? $userBalance->current_active_points : $userBalance->current_punches;

        $transferValue = (int) $this->data['points'];

        if ($userBalanceValue == 0 || $transferValue > $userBalanceValue) {
            throw new \LogicException(
                __('api.FRONTEND_TRANSFER_NOT_ENOUGH_BALANCE', [
                    '{pts_punches}' => 'points',
                    '{business_name}' => $this->subTenant->name,
                ]),
                1
            );
        }
    }

    protected function validatePhone()
    {
        if (!$this->customer) {
            throw new \InvalidArgumentException("Customer not found. The customer MUST exist when transfering.");
        }

        if (!empty($this->data['to']['phone'])) {
            $phone = $this->data['to']['phone'];

            if (!Utils::isValidPhone($phone, $this->data['to']['country_code'])) {
                throw new \InvalidArgumentException("Invalid phone number: {$phone}", 1);
            }
        }
    }

    protected function validateSelfTransfer()
    {
        if (!$this->customer) {
            throw new \InvalidArgumentException("Customer not found. The customer MUST exist when transfering.");
        }

        if (!empty($this->data['to']['phone']) && $this->customer->phone == $this->data['to']['phone']) {
            throw new \LogicException(__('api.FRONTEND_TRANSFER_NOT_ALLOW_TO_YOURSELF'), 1);
        }

        if (!empty($this->data['to']['email']) && $this->customer->email == $this->data['to']['email']) {
            throw new \LogicException(__('api.FRONTEND_TRANSFER_NOT_ALLOW_TO_YOURSELF'), 1);
        }
    }

    public function getResult()
    {
        $trx = $this->transaction;

        $_transaction = Transaction::findAndTransform($trx->transaction_pk);

        $_customer = Customer::transformItem($this->getCustomer());

        $_balance = UserEntityPoints::transformItem($this->getBalance());

        $_customer = array_merge($_customer, ['balance' => $_balance]);

        return array_merge($_transaction, ['customer' => $_customer]);
    }

    /**
     * Create Gift Card Queue record
     * When coalition this will contain only activity record for coalition business
     * not coalition members
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access protected
     * @return App\Models\V1\GiftCardQueue $giftCardQueue
     */
    protected function createGiftCardQueue()
    {
        $business = $this->getFinalBusiness();
        $businessType = $business->entity_type_fk;

        $language = Language::where('abreviation', $this->data['language'])->firstOrFail();

        $countryFk = null;
        if (isset($this->data['to']['country_code'], $this->data['to']['phone'])) {
            $country = Country::where('code', $this->data['to']['country_code'])->firstOrFail();
            $countryFk = $country->country_pk;
        }

        $queue = GiftCardQueue::create([
            'email_sms_status_fk' => EMAIL_SMS_STATUS_SCHEDULED,
            'enabled' => ENABLED,
            'max_attempts' => GIFTCARD_QUEUE_MAX_ATTEMPTS,
            'utc_schedule_date' => date('Y-m-d H:i:s'),
            'utc_created' => date('Y-m-d H:i:s'),
            'recipient_lang_fk' => $language->language_pk,
            'timezone_mysql_fk' => $this->subTenant->branch_timezone_fk,
            'sender_fk' => $this->customer->id,
            'receiver_fk' => null,
            'country_phone_fk' => $countryFk,
            'gift_card_value' => (int) $this->data['points'],
            'amount' => 0, // For Transfer Pts no amount required
            'business_branch_fk' => $this->finalBranch->business_branch_pk,
            'email_phone' => isset($this->data['to']['email']) ? $this->data['to']['email'] : $this->data['to']['phone'],
            'receiver_is_cell' => is_null($countryFk) ? 0 : 1, // 0 - email, 1 - cell
            'sender_name' => $this->data['from']['name'],
            'recipient_name' => $this->data['to']['name'],
            'offer_fk' => isset($this->data['giftcard_id']) ? $this->data['giftcard_id'] : null,
            'platform_solution_fk' => config('app.platform_id'),
            'notification_requester_fk' => NOTIF_REQ_TRANSFER_POINTS_RECEIVER_TITLE, // 29;
        ]);

        $this->giftCardQueue = $queue;
        return $queue;
    }

    /**
     * Create Acivity
     * When coalition this will contain only activity record for coalition business
     * not coalition members
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access protected
     * @return $this
     * @throws \Exception
     */
    protected function createActivity()
    {
        $business = $this->getFinalBusiness();
        $businessType = $business->entity_type_fk;

        if (!$this->businessRules) {
            throw new \Exception("Business Rules not found for business: " . $business->business_pk, 1);
        }

        if ($businessType == BUSINESS_POINTS) {
            $activityDescription = 'ACTIVITY_TRANSFER_POINTS_BOTH';
        } elseif ($businessType == BUSINESS_PUNCH) {
            $activityDescription = 'ACTIVITY_TRANSFER_POINTS_BOTH';
        }

        $activity = Activity::create([
            'description' => $activityDescription,
            'units_exchange' => (int) $this->data['points'],
            'amount' => 0,
            'transaction_fk' => $this->transaction->transaction_pk,
            'hidden' => $this->businessRules->silo_flag,
            'timezone_mysql_fk' => $this->finalBranch->branch_timezone_fk,
        ]);

        return $activity;
    }

    /**
     * Create Notification
     * When coalition this will contain only notification record for coalition business
     * not coalition members
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access protected
     * @return $this
     * @throws \Exception
     */
    protected function createNotification()
    {
        $business = $this->getFinalBusiness();
        $businessType = $business->entity_type_fk;

        if (!$this->giftCardQueue) {
            throw new \Exception("Gift Card Queue must be created first", 1);
        }

        if ($businessType == BUSINESS_POINTS) {
            $requester = NOTIF_REQ_TRANSFER_POINTS_RECEIVER_TITLE; // 29;
            $text = 'FRONTEND_SEND_GIFT_CARD_TO_FRIEND';
        } elseif ($businessType == BUSINESS_PUNCH) {
            $requester = NOTIF_REQ_TRANSFER_POINTS_RECEIVER_TITLE; // 29;
            $text = 'FRONTEND_SEND_GIFT_CARD_TO_FRIEND';
        }

        $notification = Notification::create([
            'notificationRequester' => $requester,
            'notification_text' => $text,
            'notificationType' => NOTIFICATION_TYPE_MESSAGE,
            'units_exchange' => $this->data['points'],
            'amount' => 0, // no amount for transfer points
            'user_receiver_fk' => $this->customer->id,
            'user_sender_fk' => $this->customer->id,
            'business_fk' => $business->business_pk,
            'business_branch_fk' => $this->finalBranch->business_branch_pk,
            'timezone_mysql_fk' => $this->finalBranch->branch_timezone_fk,
            'gift_card_queue_fk' => $this->giftCardQueue->gift_card_queue_pk,
            'offer_fk' => $this->giftCardQueue->offer_fk,
            'utc_publish' => date('Y-m-d H:i:s'),
        ]);

        $this->notification = $notification;
        return $this->notification;
    }

    /**
     * Create Notification for Receiver
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access protected
     * @return void
     * @throws \Exception
     */
    private function createNotificationForReceiver()
    {
        $business = $this->getFinalBusiness();
        $businessType = $business->entity_type_fk;

        if (!$this->giftCardQueue) {
            throw new \Exception("Gift Card Queue must be created first", 1);
        }

        $requester = $this->giftCardQueue->notification_requester_fk; // 29;
        $text = 'FRONTEND_RECEIVE_GIFT_CARD_FROM_FRIEND';

        $notification = Notification::create([
            'notificationRequester' => $requester,
            'notification_text' => $text,
            'notificationType' => NOTIFICATION_TYPE_MESSAGE,
            'units_exchange' => $this->data['points'],
            'amount' => 0, // no amount for transfer points
            'user_receiver_fk' => $this->receiver->id,
            'user_sender_fk' => $this->giftCardQueue->sender_fk,
            'business_fk' => $business->business_pk,
            'business_branch_fk' => $this->finalBranch->business_branch_pk,
            'timezone_mysql_fk' => $this->finalBranch->branch_timezone_fk,
            'gift_card_queue_fk' => $this->giftCardQueue->gift_card_queue_pk,
            'offer_fk' => $this->giftCardQueue->offer_fk,
            'utc_publish' => date('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Updates user balance for a specific business when a transaction takes place
     * In case of a regular business, the balance will be simple deducted
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access protected
     * @return void
     * @throws \Exception
     */
    protected function updateBalance()
    {
        if (!$this->transaction) {
            throw new \Exception("A Transaction MUST be created first", 1);
        }

        // It will throw an error if not enough points
        if ($this->subTenant) {
            $balance = UserEntityPoints::decreaseBalance($this->customer, $this->subTenant, $this->data['points']);
        } else {
            $balance = UserEntityPoints::decreaseBalance($this->customer, $this->business, $this->data['points']);
        }
    }

    /**
     * Prepare Attributes for Transaction Model to be created
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access protected
     * @return mixed - attributes for Transaction model
     */
    protected function prepareTransactionParams()
    {
        $business = $this->getFinalBusiness();
        $businessType = $business->entity_type_fk;

        $t = [];
        if ($businessType == BUSINESS_POINTS) {
            $t['entity_fk'] = $this->finalBranch->business_branch_pk;
            $t['entity_type_fk'] = BRANCH_POINTS;
            $t['transaction_type_fk'] = TRX_TYPE_TRANSFER_POINTS; // 13
            $t['transaction_sub_type_fk'] = TRX_SUBTYPE_TRANSFER_POINTS_SENDER; // 58
        } elseif ($businessType == BUSINESS_PUNCH) {
            $t['entity_fk'] = $this->finalBranch->business_branch_pk;
            $t['entity_type_fk'] = BRANCH_PUNCH;
            $t['transaction_type_fk'] = TRX_TYPE_TRANSFER_PUNCHES; // 14
            $t['transaction_sub_type_fk'] = TRX_SUBTYPE_TRANSFER_PUNCHES_SENDER; // 60
        } else {
            throw new \LogicException("Error Preparing Transaction Params", 1);
        }

        $t['user_fk'] = $this->customer->id;
        $t['timezone_mysql_fk'] = $this->finalBranch->branch_timezone_fk;

        return $t;
    }

    protected function preparePurchaseParams()
    {
        // There is not purchase for transfer
    }

    protected function getTrxSubType()
    {
        //
    }

    protected function findOrcreateReceiverUser()
    {
        $business = $this->getFinalBusiness();
        if (isset($this->data['to']['email']) && $this->data['to']['email']) {
            $receiver = \App\User::where('email', $this->data['to']['email'])
                ->where('business_fk', $business->business_pk)->first();
        } else {
            if (!empty($this->data['to']['phone'])) {
                $this->data['to']['phone'] = Utils::normalizeDigitsOnly($this->data['to']['phone']);
                $this->data['to']['phone'] = Utils::getFormattedPhoneNumber($this->data['to']['phone'], $this->data['to']['country_code'], 'NATIONAL');
                $this->data['to']['phone'] = Utils::normalizeDigitsOnly($this->data['to']['phone']);
            }
            $receiver = \App\User::where('phone', $this->data['to']['phone'])
                ->where('business_fk', $business->business_pk)->first();
        }

        // Create user
        if (!$receiver) {
            Log::info('Receiver User Not Found. Trying to create', [
                'business_pk' => $business->business_pk,
                'data' => $this->data,
            ]);
            $this->data['to']['business_fk'] = $business->business_pk;
            $data = Customer::prepareCustomerData($this->data['to'], $this->finalBranch);
            $data['business_pk'] = null; // Do not send welcome email
            $receiver = Customer::createForBusiness($data, $this->getFinalBusiness());

            $this->isNewReceiver = true;
            $this->receiverPin = $data['pin_code'];
        }

        // Save Receiver ID for Gift card queue item
        $this->giftCardQueue->receiver_fk = $receiver->id;
        $this->giftCardQueue->save();

        $this->receiver = $receiver;
    }

    /**
     * @param $giftCardQueuePk
     * @param $result
     * @param $platformName
     * @throws \Exception
     */
    public function updateStatusAndAttempts($giftCardQueuePk, $result, $platformName)
    {
        $giftCardQueue  = GiftCardQueue::model()->findByPk($giftCardQueuePk);
        $strStatus      = (isset($result['message_status'])) ? $result['message_status'] : 'failed' ;
        $status         = GiftCardQueue::buildStatus($strStatus);

        $giftCardQueue->email_sms_status_fk = $status;
        $giftCardQueue->error_log = ($status==5 && isset($result['error'])) ? json_encode($result['error']): null ;
        $giftCardQueue->utc_lastattempt = date('Y-m-d H:i:s');
        $giftCardQueue->attempts = $giftCardQueue->attempts + 1;

        if ($status==3 || $status==4) {
            $giftCardQueue->utc_sent = date('Y-m-d H:i:s');
        }

        if (isset($result['sms_sid']) && $result['sms_sid']!='') {
            $giftCardQueue->sms_sid = $result['sms_sid'];
        }

        if (!$giftCardQueue->save()) {
            throw new \Exception('Unable to save gift card queue');
        }
    }

    protected function sendConfirmation()
    {
        if ($this->receiver->email) {
            $this->sendConfirmationEmail();
        } elseif ($this->receiver->phone) {
            $this->sendConfirmationSMS();
        }
    }

    private function sendConfirmationEmail()
    {
        $locale = $this->data['language'];
        $business = $this->getFinalBusiness();
        $businessId = $business->business_pk;
        $tenantBusinessId = $this->business->business_pk;

        $businessProfile = BusinessProfile::where('business_fk', $tenantBusinessId)->first();
        $notifReqFk = $this->giftCardQueue->notification_requester_fk;
        $currency = Currency::forBranch($business->business_branch_pk);

        $pointsValue = $this->giftCardQueue->gift_card_value;
        $amount = $this->giftCardQueue->amount;

        $notificationPk = $this->giftCardQueue->getGiftCardNotificationIdReceiver();

        $offerId = $this->giftCardQueue->offer_fk;

        $giftCardOffer = Offer::getDetails($offerId);

        $giftCardImage = $giftCardOffer ? $giftCardOffer->offer_image1_large : null;

        // if ($businessProfile && $businessProfile->web_app_uri) {
        //     $serverUrl = rtrim($businessProfile->web_app_uri, '/');
        // } else {
            // TODO uncoment when the link to site/verifyAccount works for Members Web app
            $serverUrl = config('app.server_url');
        // }

        $emailProvider = EmailProvider::where('business_fk', $tenantBusinessId)->first();

        $branch = $this->finalBranch;

        $token = substr(microtime(true) . 'GC' . Str::random(60), 0, 60); //make sure is 60 characters

        $countryCode = null;
        if ($this->receiver->country_phone_fk) {
            $country = Country::find($this->receiver->country_phone_fk);
            $countryCode = $country->code ?? null;
        }

        ActivationToken::create([
            'id' => $token,
            'user_fk' => $this->receiver->id,
            'utc_link_expires' => date('Y-m-d H:i:s', strtotime(ACTIVATION_LINK_EXPIRES)),
            'phone' => $this->receiver->phone,
            'email' => $this->receiver->email,
            'country_code' => $countryCode,
            'intent' => 'gift_card_signup_welcome',
        ]);

        $uri = BusinessProfile::getWebAppUri($tenantBusinessId);
        if (!$uri) {
            $uri = rtrim(config('app.members_webapp_url'), '/');
        }

        $uri .= '/public/acceptGiftCard';

        $link = url($uri) . '?' . http_build_query([
            'u' => bin2hex($this->receiver->user_uid),
            'n' => $notificationPk,
        ]);

        if ($tenantBusinessId == NASSPAY_BUSINESS_ID) {
            $link = '';// No link for Nasspay
        }

        $subject = __('api.FRONTEND_TRANSFER_PTS_EMAIL_SUBJECT', [
            '{sender}' => $this->data['from']['name'],
            '{amount}' => $amount,
            '{currency}' => $currency->symbol,
            '{business_name}' => $branch->name,
        ], $locale);

        if ($pointsValue > 0) {
            $subject = __('api.FRONTEND_TRANSFER_POINTS_EMAIL_SUBJECT', [
                '{sender}' => $this->data['from']['name'],
                '{points}' => $pointsValue,
                '{business_name}' => $branch->name,
            ], $locale);
        }

        $transferEmail = new Email($emailProvider);

        $transferEmail
            ->subject($subject)
            ->view('emails.user.transferPoints.' . $locale)
            ->with([
                'username' => $this->data['to']['name'], //$this->receiver->email,
                'email' => $this->receiver->email,
                'businessName' => $branch->name,
                'businessLogo' => $branch->logo_image_medium,
                'pincode' => $this->receiverPin,
                'link' => $link,
                'isNewUser' => $this->isNewReceiver,
                'notifReqFk' => $notifReqFk,
                'senderName' => $this->data['from']['name'],
                'transferUnits' => $this->giftCardQueue->gift_card_value,
                'amount' => $amount,
                'currencySymbol' => $currency->symbol,
                'giftCardImage' => $giftCardImage,
                'webAppUrl' => $serverUrl,
            ]);

        Log::info('Transfer Points email dispatched', [
            'user_email' => $this->receiver->email,
            'business_name' => $business->name,
            'amount' => $amount,
            'points' => $pointsValue,
        ]);

        $mailMessageJob = new TransactionalMailMessage($this->receiver->id, $businessId);

        $mailMessageJob
            ->setProvider($emailProvider)
            ->with($transferEmail)
            ->setCallbackOnSent([
                'class' => GiftCardQueue::class,
                'method' => 'updateStatusByPk',
                'args' => [$this->giftCardQueue->gift_card_queue_pk],
            ])
            ->to($this->receiver->email);

        dispatch($mailMessageJob);
    }

    private function sendConfirmationSMS()
    {
        $locale = $this->data['language'];
        $business = $this->getFinalBusiness();
        $businessId = $business->business_pk;
        $tenantBusinessId = $this->business->business_pk;

        $businessProfile = BusinessProfile::where('business_fk', $tenantBusinessId)->first();
        $notifReqFk = $this->giftCardQueue->notification_requester_fk;
        $currency = Currency::forBranch($business->business_branch_pk);

        $pointsValue = $this->giftCardQueue->gift_card_value;
        $amount = $this->giftCardQueue->amount;

        $notificationPk = $this->giftCardQueue->getGiftCardNotificationIdReceiver();

        $offerId = $this->giftCardQueue->offer_fk;

        $giftCardOffer = Offer::getDetails($offerId);

        $giftCardImage = $giftCardOffer ? $giftCardOffer->offer_image1_large : null;

        $textProvider = SmsProvider::where('business_fk', $tenantBusinessId)->first();

        $branch = $this->finalBranch;

        $token = substr(microtime(true) . 'GC' . Str::random(60), 0, 60); //make sure is 60 characters

        $countryCode = null;
        if ($this->receiver->country_phone_fk) {
            $country = Country::find($this->receiver->country_phone_fk);
            $countryCode = $country->code ?? null;
        }

        ActivationToken::create([
            'id' => $token,
            'user_fk' => $this->receiver->id,
            'utc_link_expires' => date('Y-m-d H:i:s', strtotime(ACTIVATION_LINK_EXPIRES)),
            'phone' => $this->receiver->phone,
            'email' => $this->receiver->email,
            'country_code' => $countryCode,
            'intent' => 'gift_card_signup_welcome',
        ]);

        $uri = BusinessProfile::getWebAppUri($tenantBusinessId);
        if (!$uri) {
            $uri = rtrim(config('app.members_webapp_url'), '/');
        }

        $uri .= '/public/acceptGiftCard';

        $link = url($uri) . '?' . http_build_query([
            'u' => bin2hex($this->receiver->user_uid),
            'n' => $notificationPk,
        ]);

        $shortLink = Utils::shortUrl($link, $branch->name);

        if ($pointsValue > 0) {
            $smsBody = __('api.FRONTEND_TRANSFER_POINTS_SMS', [
                '{sender}' => $this->data['to']['name'],
                '{points}' => $pointsValue,
                '{business_name}' => $branch->name,
                '{link}' => $shortLink,
            ], $locale);
        } else {
            $smsBody = __('api.FRONTEND_TRANSFER_PTS_SMS', [
                '{sender}' => $this->data['to']['name'],
                '{amount}' => $amount,
                '{currency}' => $currency->symbol,
                '{business_name}' => $branch->name,
                '{link}' => $shortLink,
            ], $locale);
        }

        // Include the pin code in SMS body
        if ($this->isNewReceiver && !empty($this->receiverPin)) {
            // Get SMS vendor
            $smsVendor = $this->getSMSVendor($this->businessRules->smsvendor_fk);

            // Get the pin text based on SMS provider
            $pinText = ($smsVendor->sms_provider == SMS_PROVIDER_BROADNET || $smsVendor->sms_provider == SMS_PROVIDER_BROADNET2 || $smsVendor->sms_provider == SMS_PROVIDER_BROADNET3) ? 'FRONTEND_USER_PIN_BROADNET' : 'FRONTEND_USER_PIN';

            // Include the pin code to the SMS body
            $smsBody .= " " . __('api.'.$pinText, [
                    '{pin_code}' => $this->receiverPin,
            ], $locale);
        }

        $country = Country::findOrFail($this->receiver->country_phone_fk);

        $intlPhone = Utils::getFormattedPhoneNumber(
            $this->receiver->phone,
            $country->code,
            'E164'
        );

        dispatch((new TransactionalTextMessage($this->receiver->id, $businessId, $textProvider))
            ->content($smsBody)
            ->to($intlPhone)
            ->setCallbackOnSent([
                'class' => GiftCardQueue::class,
                'method' => 'updateStatusByPk',
                'args' => [$this->giftCardQueue->gift_card_queue_pk],
            ]));
    }
}
