<?php

namespace App\Rules;

use App\Traits\ProductRewardTrait;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\App;

class BusinessProductRewardsValidation implements Rule
{
    use ProductRewardTrait;

    protected object $businessBranches;

    public function __construct($businessBranches)
    {
        $this->businessBranches = $businessBranches;
    }

    public function passes($attribute, $value): bool
    {
        $productReward = $this->getProductRewardForBusiness($value, $this->businessBranches);

        return !$productReward->isEmpty();
    }

    /**
     * @return string
     */
    public function message(): string
    {
        return __('api.VALIDATION_ERROR_PRODUCT_NOT_VALID', [], App::getLocale());
    }
}
