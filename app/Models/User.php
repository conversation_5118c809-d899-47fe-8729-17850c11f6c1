<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use KangarooRewards\Common\Models\UserBusinessNotification;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $table = 'user';
    protected $connection = 'tenant';

    const CREATED_AT = 'utc_created';

    const UPDATED_AT = 'utc_updated';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_uid', 'first', 'last', 'email', 'phone', 'password', 'username', 'passwordreset', 'timezone_mysql_fk',
        'country_phone_fk', 'utc_link_expires', 'enabled', 'pin_enabled', 'verification_code',
        'pin_permission', 'platform_solution_fk', 'signedupat_fk', 'ip',
        'birth_date', 'gender', 'qrcode', 'language', 'image', 'gcm_device_token', 'device_token','business_fk'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password', 'remember_token', 'pin_permission',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'user_uid';
    }

    /**
     * Retrieve the model for a bound value.
     *
     * @param  mixed  $value
     * @param  string|null  $field
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function resolveRouteBinding($value, $field = null)
    {
        try {
            $value = hex2bin($value);
        } catch (\Exception $exc) {
            \Log::warning(print_r($exc->getTraceAsString(), true));
            abort(400, "The request was unacceptable, often due to missing a required parameter.");
        }
        return $this->where($this->getRouteKeyName(), $value)
            ->first() ?? abort(404, "Customer {$value} not found");
    }

    /**
     * @return HasOne
     */
    public function businessNotifications(): HasOne
    {
        return $this->hasOne(
            UserBusinessNotification::class,
            'user_fk', // Foreign key on the user_business_notifications table
            'id'       // Local key on the user table
        );
    }
}
