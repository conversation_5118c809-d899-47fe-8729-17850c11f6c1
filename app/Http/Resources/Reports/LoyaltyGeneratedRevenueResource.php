<?php

namespace App\Http\Resources\Reports;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Google\Cloud\BigQuery\Timestamp;

class LoyaltyGeneratedRevenueResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // if ($this->resource['date'] instanceof Timestamp) {
        //     // when returned without formatting the type is Google\Cloud\BigQuery\Timestamp
        //     $dateCreated = (new Carbon($this->resource['date']->get()));
        // } else {
        //     $dateCreated = (new Carbon($this->resource['date']));
        // }
        
        \Log::info(__METHOD__ . ' ', [
            'group_interval'=> $request->group_interval
        ]);

        return [
            'date' => $this->resource['date'],
            'total_amount' => !empty($this->resource['total_amount']) ? $this->resource['total_amount']->get() : 0,
            'customers_count' => $this->resource['customers_count'],
            'transactions_count' => $this->resource['transactions_count'],
            'campaigns_amount' => !empty($this->resource['campaigns_amount']) ? $this->resource['campaigns_amount']->get() : 0,
        ];
    }
}
