<?php

namespace Tests\Unit\Services;

use App\Models\GptChat;
use App\Models\GptChatCompletion;
use App\Services\ChatGPTService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use JsonException;
use Mockery;
use Tests\TestCase;
use stdClass;

class ChatGPTServiceTest extends TestCase
{
    private ChatGPTService $service;
    private $gptChat;
    private MockHandler $mockHandler;
    private $business;

    public function setUp(): void
    {
        parent::setUp();

        // Create a mock handler for Guzzle
        $this->mockHandler = new MockHandler();
        $handlerStack = HandlerStack::create($this->mockHandler);

        // Create a client with the mock handler
        $client = new Client(['handler' => $handlerStack]);

        // Create the service and inject the mocked client
        $this->service = new ChatGPTService();

        // Use reflection to set the private client property
        $reflection = new \ReflectionClass($this->service);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->service, $client);

        // Create a business object
        $this->business = new stdClass();
        $this->business->business_pk = 123;
        $this->business->business_uid = 'test-uid';

        // Create a mock GptChat
        $this->gptChat = Mockery::mock(GptChat::class);
        

        // Allow any method calls and return appropriate values
        $this->gptChat->shouldReceive('setAttribute')->withAnyArgs()->andReturnSelf();
        $this->gptChat->shouldReceive('getAttribute')->withAnyArgs()->andReturn(null);
        $this->gptChat->shouldReceive('__get')->with('id')->andReturn(1);
        $this->gptChat->shouldReceive('__get')->with('business')->andReturn($this->business);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test successful chat completion
     *
     * @return void
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws JsonException
     */
    public function testChatCompletionsSuccess(): void
    {
        // Mock the API response
        $responseBody = [
            'id' => 'chatcmpl-123',
            'object' => 'chat.completion',
            'model' => 'gpt-4o-mini',
            'choices' => [
                [
                    'message' => [
                        'content' => 'This is a test response'
                    ]
                ]
            ],
            'usage' => [
                'prompt_tokens' => 10,
                'completion_tokens' => 20,
                'total_tokens' => 30
            ]
        ];

        $this->mockHandler->append(
            new Response(200, [], json_encode($responseBody))
        );

        // Mock GptChatCompletion::create
        $mockCompletion = Mockery::mock(GptChatCompletion::class);
        GptChatCompletion::shouldReceive('create')
            ->once()
            ->andReturn($mockCompletion);

        // Test the method
        $result = $this->service->chatCompletions(
            $this->gptChat,
            ['prompt' => 'Test prompt', 'model' => 'gpt-4o-mini', 'temperature' => 0.7]
        );

        // Assert result is the mock completion
        $this->assertSame($mockCompletion, $result);
    }

    /**
     * Test chat completion with context
     *
     * @return void
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws JsonException
     */
    public function testChatCompletionsWithContext(): void
    {
        // Mock the API response
        $responseBody = [
            'id' => 'chatcmpl-123',
            'object' => 'chat.completion',
            'model' => 'gpt-4o-mini',
            'choices' => [
                [
                    'message' => [
                        'content' => 'This is a test response with context'
                    ]
                ]
            ],
            'usage' => [
                'prompt_tokens' => 15,
                'completion_tokens' => 25,
                'total_tokens' => 40
            ]
        ];

        $this->mockHandler->append(
            new Response(200, [], json_encode($responseBody))
        );

        // Mock GptChatCompletion::create
        $mockCompletion = Mockery::mock(GptChatCompletion::class);
        GptChatCompletion::shouldReceive('create')
            ->once()
            ->andReturn($mockCompletion);

        // Test the method with context
        $result = $this->service->chatCompletions(
            $this->gptChat,
            [
                'prompt' => 'Test prompt',
                'context' => 'You are a helpful assistant',
                'model' => 'gpt-4o-mini',
                'temperature' => 0.7
            ]
        );

        // Assert result is the mock completion
        $this->assertSame($mockCompletion, $result);
    }

    /**
     * Test chat completion with API error
     *
     * @return void
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws JsonException
     */
    public function testChatCompletionsApiError(): void
    {
        // Mock a request exception
        $request = new Request('POST', '/v1/chat/completions');
        $response = new Response(400, [], json_encode(['error' => 'Bad request']));
        $exception = new RequestException('Error communicating with server', $request, $response);

        $this->mockHandler->append($exception);

        // Mock Log::error
        Log::shouldReceive('error')
            ->once();

        // Expect exception
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('The server encountered an unexpected condition which prevented it from fulfilling the request');

        // Test the method
        $this->service->chatCompletions(
            $this->gptChat,
            ['prompt' => 'Test prompt', 'model' => 'gpt-4o-mini']
        );
    }

    /**
     * Test image generation success
     *
     * @return void
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws JsonException
     */
    public function testImageGenerationSuccess(): void
    {
        // Mock the API response
        $responseBody = [
            'data' => [
                [
                    'url' => 'https://example.com/image1.png'
                ]
            ]
        ];

        $this->mockHandler->append(
            new Response(200, [], json_encode($responseBody))
        );

        // Mock GptChatCompletion::create
        GptChatCompletion::shouldReceive('create')
            ->once()
            ->andReturn(true);

        // Test the method
        $result = $this->service->imageGeneration(
            $this->gptChat,
            ['prompt' => 'A beautiful sunset']
        );

        // Assert result contains the expected data
        $this->assertEquals($responseBody['data'], $result);
    }

    /**
     * Test image generation with API error
     *
     * @return void
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws JsonException
     */
    public function testImageGenerationApiError(): void
    {
        // Mock a request exception
        $request = new Request('POST', '/v1/images/generations');
        $response = new Response(400, [], json_encode(['error' => 'Bad request']));
        $exception = new RequestException('Error communicating with server', $request, $response);

        $this->mockHandler->append($exception);

        // Mock Log::error
        Log::shouldReceive('error')
            ->once();

        // Expect exception
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('The server encountered an unexpected condition which prevented it from fulfilling the request');

        // Test the method
        $this->service->imageGeneration(
            $this->gptChat,
            ['prompt' => 'A beautiful sunset']
        );
    }

    /**
     * Test getAIModel returns correct model
     *
     * @return void
     */
    public function testGetAIModel(): void
    {
        // Test with valid model
        $result = $this->invokeMethod($this->service, 'getAIModel', [['model' => 'gpt-4o']]);
        $this->assertEquals('gpt-4o', $result);

        // Test with invalid model (should return default)
        $result = $this->invokeMethod($this->service, 'getAIModel', [['model' => 'invalid-model']]);
        $this->assertEquals('gpt-4o-mini', $result);

        // Test with no model specified (should return default)
        $result = $this->invokeMethod($this->service, 'getAIModel', [[]]);
        $this->assertEquals('gpt-4o-mini', $result);
    }

    /**
     * Helper method to invoke private methods
     *
     * @param object $object
     * @param string $methodName
     * @param array $parameters
     * @return mixed
     */
    private function invokeMethod(object $object, string $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }
}