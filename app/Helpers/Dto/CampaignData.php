<?php
declare(strict_types=1);

namespace App\Helpers\Dto;

use App\Helpers\DataTransferObject\DataTransferObject;
use App\Models\V1\Campaign;
use App\Models\V1\Offer;
use Carbon\Carbon;

class CampaignData extends DataTransferObject
{
    /**
     * @var string|null
     */
    public $idempotency_key;

    protected $ignoreMissing = true;

    /** @var null|int */
    public $id = null;

    /** @var bool is this a campaign integration, Klaviyo, Mailchimp, ConstantContact */
    public $is_campaign_integration = false;

    /** @var string */
    public $name = '';

    /** @var integer|null  */
    public $audience_type = null;

    /** @var array|null  */
    public $trigger_schedule_timing = null;

    /** @var bool */
    public $expiry_flag = true;

    /** @var integer */
    public $send_by = 2; //push

    /** @var string|null */
    public $send_by_priority;

    /** @var array */
    public $targeted_customer_criteria = [];

    /** @var boolean */
    public $auto = false;

    /** @var string|null */
    public $schedule_date;

    /** @var string|null */
    public $schedule_time;

    /** @var boolean|integer */
    public $silent = 0;

    /** @var string|null */
    public $email_subject;

    /** @var string|null */
    public $email_body;

    /** @var string|null */
    public $sms_body;

    /** @var string|null */
    public $push_title;

    /** @var string|null */
    public $push_text;

    /** @var string|null */
    public $push_link;

    /** @var string|null */
    public $push_image_url;

    /** @var string|Carbon|null */
    public $delivery_date;

    /** @var string|Carbon|null */
    public $expiry_date;

    /** @var boolean */
    public $send_to_invalid_phone_number = false;

    /****** calculate campaign info
     *
     * /** @var integer
     */
    public $send_email = 0;

    /** @var integer */
    public $send_sms = 0;

    /** @var integer single sms segments */
    public $send_sms_segments = 1;

    /** @var integer */
    public $sent_ios_push_notif = 0;

    /** @var integer */
    public $sent_android_push_notif = 0;

    /** @var integer */
    public $frequency = 0;

    /** @var boolean */
    public $include_offer_link_in_sms = false;

    /** @var boolean */
    public $include_referral_link_in_sms = false;

    /** @var boolean */
    public $include_opt_in_link_in_sms = false;

    /** @var int|null */
    public $entity_mktg_package_credit_fk;

    /** @var boolean */
    public $exclude_dormants = true;

    /** @var boolean */
    public $apply_and_criteria = false;

    /** @var int|null */
    public $exclude_customer_in_previous_campaign_past_days = 0;

    /** @var int|null */
    public $exclude_customer_in_previous_campaign_past_hours = 0;

    /** @var array */
    public $exclude_customer_in_previous_campaign_ids = [];

    /** @var int */
    public $exclude_customer_in_previous_campaign_criteria = 0;

    /** @var int|null */
    public $exclude_customer_in_previous_automated_campaign_past_days = 0;

    /** @var int|null */
    public $exclude_customer_in_previous_automated_campaign_past_hours = 0;

    /** @var array */
    public $exclude_customer_in_previous_automated_campaign_ids = [];

    /** @var int */
    public $exclude_customer_in_previous_automated_campaign_criteria = 0;

    /** @var bool */
    public $exclude_customer_in_previous_campaign = false;

    /** @var bool */
    public $exclude_customer_in_previous_automated_campaign = false;

    /** @var array */
    public $offers = [];

    /** @var array */
    public $options = [];

    /** @var array */
    public $who_will_receive = [];

    /** @var int|null */
    public $top_spending_customers;

    /** @var string|null */
    public $specific_customer_email_phone;

    /** @var array|null */
    public $specific_customer_email_phone_array;

    /** @var array|null|string */
    public $product_reward_list;

    /** @var integer|null */
    public $customer_points_operator;

    /** @var integer|null */
    public $customer_have_points;

    /** @var integer|null */
    public $customer_have_end_points;

    /** @var array|null */
    public $customers_redemption_list;

    /** @var integer|null */
    public $customers_redemption_days;

    /** @var array|null */
    public $customers_redemption_branches;

    /** @var array|null */
    public $customers_offer_list;

    /** @var integer|null */
    public $customers_offer_days;

    /** @var array|null */
    public $customers_offer_branches;
    
    /** @var integer|null */
    public $customers_campaigned_offers_redemptions_operator;

    /** @var array|null */
    public $customers_campaigned_offers_redemptions_campaign_ids;

    /** @var integer|null */
    public $customers_campaigned_offers_redemptions_days;

    /** @var array|null */
    public $customers_campaigned_offers_redemptions_branches;

    /** @var array|null */
    public $customers_campaigned_offers_redemptions_offers_list;

    /** @var array|null */
    public $customers_campaigned_offers_redemptions_redemptions_list;

    /** @var array|null */
    public $customer_types_list;

    /** @var array|null */
    public $customer_tags_list;

    /** @var string|null */
    public $referred_start_date;

    /** @var string|null */
    public $referred_end_date;

    /** @var array|null */
    public $referral_status_list;

    /** @var integer|null */
    public $referred_customers_number;

    /** @var array|null */
    public $referred_customers_status_list;

    /** @var integer|null */
    public $lifetime_balance;

    /** @var integer|null */
    public $lifetime_balance_operator;

    /** @var integer|null */
    public $spending_weeks_amount;

    /** @var integer|null */
    public $spending_weeks_number;

    /** @var integer|null */
    public $spending_weeks_operator;

    /** @var array|null */
    public $segments_list;

    /** @var integer|null */
    public $no_purchases_days;
    
    /** @var integer|null */
    public $no_purchases_days_range_end;
    
    /** @var integer|null */
    public $no_purchases_days_range_operator;

    /** @var integer|null */
    public $no_purchases_flag;

    /** @var array|null */
    public $no_purchases_branches;

    /** @var integer|null */
    public $purchases_number;

    /** @var integer|null */
    public $purchase_weeks_number;

    /** @var integer|null */
    public $purchase_weeks_operator;

    /** @var string|null */
    public $visits_start_date;

    /** @var string|null */
    public $visits_end_date;

    /** @var array|null */
    public $visits_branches;

    /** @var array|null */
    public $customers_branches;

    /** @var array|null */
    public $customers_languages_list;

    /** @var string|null */
    public $customers_postal_code;

    /** @var string|null */
    public $booked_appointment_start_date;

    /** @var string|null */
    public $booked_appointment_end_date;

    /** @var array|null */
    public $booking_appointments_list;

    /** @var integer|null */
    public $booking_appointment_operator;

    /** @var integer|null */
    public $booking_appointment_days;

    /** @var integer|null */
    public $product_review_operator;

    /** @var integer|null */
    public $product_review_days;

    /** @var boolean */
    public $redirect_from_editor = false;

    /** @var string 'Edit, CancelClon, EditDraft' */
    public $mode = 'New';

    /**
     * Current campaign if mode is EditDraft or Edit
     * Previous campaign if mode is CancelClon
     */
    public $previous_campaign;

    /** @var bool|int */
    public $draft = 0;

    /** Pending number of message in the previous campaign.
     *  Use for calculate the credits:
     *  current credits + pending credits = allow credits
     */
    /** @var integer */
    public $send_email_pending = 0;

    /** @var integer */
    public $send_sms_pending = 0;

    /** @var integer */
    public $sent_ios_push_notif_pending = 0;

    /** @var integer */
    public $sent_android_push_notif_pending = 0;

    /** @var int */
    public $locked = 0;

    /** @var bool */
    public $isValidateEnoughCredit = false;

    /** @var bool */
    public $isValidateDeliveryTime = false;

    /** @var int */
    public $group_id = 0;

    /** @var string|null */
    public $ip = null;

    /** @var string|null */
    public $user_agent = null;

    /** @var array */
    public $ls_product_tags = [];

    /** @var int */
    public $ls_product_tags_days = 730;

    /** @var array */
    public $ls_product_brands = [];

    /** @var int */
    public $ls_product_brands_days = 1;

    /** @var array */
    public $ls_products = [];

    /** @var int */
    public $ls_products_days = 0;

    /** @var array */
    public $ls_products_branches = [];

    /** @var array */
    public $ls_categories = [];

    /** @var int */
    public $ls_categories_days = 0;

    /** @var null|string */
    public $shopify_account;

    /** @var array */
    public $shopify_categories = [];

    /** @var int */
    public $shopify_categories_days = 0;

    /** @var array */
    public $shopify_products = [];

    /** @var int */
    public $shopify_products_days = 0;

    /** @var array */
    public $shopify_product_vendors = [];

    /** @var int */
    public $shopify_product_vendors_days = 0;

    /** @var array */
    public $shopify_product_tags = [];

    /** @var int */
    public $shopify_product_tags_days = 0;

    /** @var null|string */
    public $lsEcom_account;

    /** @var array */
    public $lsEcom_categories = [];

    /** @var int */
    public $lsEcom_categories_days = 0;

    /** @var array */
    public $lsEcom_products = [];

    /** @var int */
    public $lsEcom_products_days = 0;

    /** @var array */
    public $lsEcom_product_vendors = [];

    /** @var int */
    public $lsEcom_product_vendors_days = 0;

    /** @var array */
    public $lsEcom_product_tags = [];

    /** @var int */
    public $lsEcom_product_tags_days = 0;

    /** @var array */
    public $lsEcom_product_brands = [];

    /** @var int */
    public $lsEcom_product_brands_days = 0;

    /** @var array */
    public $ecomz_products = [];

    /** @var int */
    public $ecomz_products_days = 0;

    /** @var array */
    public $ecomz_product_tags = [];

    /** @var int */
    public $ecomz_product_tags_days = 0;

    /** @var array */
    public $ecomz_product_brands = [];

    /** @var int */
    public $ecomz_product_brands_days = 0;

    /** @var array */
    public $ecomz_categories = [];

    /** @var int */
    public $ecomz_categories_days = 0;

    /** @var string|null */
    public $ecomz_account;

    /** @var array */
    public $woo_products = [];

    /** @var int */
    public $woo_products_days = 0;

    /** @var array */
    public $woo_product_tags = [];

    /** @var int */
    public $woo_product_tags_days = 0;

    /** @var array */
    public $woo_categories = [];

    /** @var int */
    public $woo_categories_days = 0;

    /** @var string|null */
    public $woo_account;

    /** @var array */
    public $bc_products = [];

    /** @var int */
    public $bc_products_days = 0;

    /** @var array */
    public $bc_product_brands = [];

    /** @var int */
    public $bc_product_brand_days = 0;

    /** @var array */
    public $bc_categories = [];

    /** @var int */
    public $bc_categories_days = 0;

    /** @var string|null */
    public $bc_account;

    /** @var array */
    public $magento_products = [];

    /** @var int */
    public $magento_products_days = 0;

    /** @var array */
    public $magento_categories = [];

    /** @var int */
    public $magento_categories_days = 0;

    /** @var string|null */
    public $magento_account;

    /** @var int */
    public $birthday_customers_criteria = 1;

    /** @var array */
    public $birthday_customers_types_list = [];

    /** @var int */
    public $birthday_notification_days = 1;

    /** @var int */
    public $pts_expire_inactive_days = 1;

    /** @var int */
    public $pts_expire_days = 1;

    /** @var int */
    public $earn_or_reward_number = 1;

    /** @var int */
    public $earn_or_reward_option = 1;

    /** @var int */
    public $after_first_visit_days = 0;

    /** @var array */
    public $after_first_visit_branches = [];

    /** @var int */
    public $last_purchase_days = 0;

    /** @var int */
    public $last_purchase_days_range_end = 0;

    /** @var int */
    public $last_purchase_days_range_operator = 1;

    /** @var array|null */
    public $last_purchase_branches;
    
    /** @var int */
    public $reg_last_purchase_days = 0;

    /** @var int */
    public $reg_last_purchase_days_range_end = 0;

    /** @var int */
    public $reg_last_purchase_days_range_operator = 1;

    /** @var array|null */
    public $reg_last_purchase_branches;

    /** @var string|null */
    public $template_data;

    /** @var string|null */
    public $template_general_styles;

    /** @var int */
    public $register_no_transaction_days = 7;

    /** @var string|Carbon|null */
    public $customers_registered_start_date;

    /** @var string|Carbon|null */
    public $customers_registered_end_date;

    /** @var array|null */
    public $moved_from_tiers_list;

    /** @var array|null */
    public $moved_to_tiers_list;

    /** @var integer */
    public $store_credit_operator = 1;

    /** @var integer|null */
    public $store_credit_amount;

    /** @var integer|null */
    public $store_credit_end_amount;

    /**
     * @var bool
     * Used for campaign exporting to marketing integration
     * When it's true, regardless of user consent status
     */
    public $ignore_consent = false;

    /** @var array */
    public $vend_products = [];

    /** @var int */
    public $vend_products_days = 0;

    /** @var array */
    public $vend_product_vendors = [];

    /** @var int */
    public $vend_product_vendors_days = 0;

    /** @var array */
    public $vend_product_tags = [];

    /** @var int */
    public $vend_product_tags_days = 0;

    /** @var array */
    public $vend_product_brands = [];

    /** @var int */
    public $vend_product_brands_days = 0;

    /** @var array */
    public $heartland_products = [];

    /** @var int */
    public $heartland_products_days = 0;

    /** @var array */
    public $heartland_categories = [];

    /** @var int */
    public $heartland_categories_days = 0;

    /** @var array */
    public $heartland_product_vendors = [];

    /** @var int */
    public $heartland_product_vendors_days = 0;

    /** @var string|int|null */
    public $heartland_account;

    /** @var array  */
    public $lsk_products = [];

    /** @var int */
    public $lsk_products_days = 1;

    /** @var array  */
    public $lsk_products_branches = [];

    /** @var int */
    public $lsk_products_quantity = 1;

    /** @var array  */
    public $lsk_accounting_groups = [];

    /** @var int  */
    public $lsk_accounting_groups_days = 1;

    /** @var array  */
    public $lsk_accounting_groups_branches = [];

    /** @var string|int|null */
    public $qrcode_account;

    /** @var array */
    public $qrcode_products = [];

    /** @var int */
    public $qrcode_products_days = 0;

    /** @var array */
    public $ecwid_products = [];

    /** @var int */
    public $ecwid_products_days = 1;

    /** @var array */
    public $ecwid_product_brands = [];

    /** @var int */
    public $ecwid_product_brands_days = 1;

    /** @var array */
    public $ecwid_categories = [];

    /** @var int */
    public $ecwid_categories_days = 1;

    /** @var string */
    public string $summary_storage = '';

    /** @var array  */
    public array $review_orders_date_range = [];

    /** @var array  */
    public array $review_products = [];

    /** @var int  */
    public int $review_products_days = 0;

    /** @var array  */
    public array $review_last_orders = [];

    /** @var int  */
    public int $predict_churn_probability = 0; // in %

    /** @var int  */
    public int $predict_churn_prob_operator = 1; // 1: >; 2: <; 3: >=; 4: <=; 5: =;

    /** @var array  */
    public array $geofences = [];

    public function __construct(array $parameters = [])
    {
        $this->group_id = time();

        parent::__construct($parameters);

        if ($this->exclude_customer_in_previous_automated_campaign_past_days
            || $this->exclude_customer_in_previous_automated_campaign_past_hours) {
            $this->exclude_customer_in_previous_automated_campaign = true;
        } else {
            $this->exclude_customer_in_previous_automated_campaign_past_days = 7; //default value
        }

        if ($this->exclude_customer_in_previous_campaign_past_days
            || $this->exclude_customer_in_previous_campaign_past_hours) {
            $this->exclude_customer_in_previous_campaign = true;
        } else {
            $this->exclude_customer_in_previous_campaign_past_days = 7; //default value
        }
    }

    /**
     * Legacy POST from  old Portal
     * @param $post
     * @return static
     */
    public static function fromPostRequest($post): self
    {
        $excludePastRegVal = 0;
        $excludePrevRegCampUnit = $post['excCustPrevRegCampUnit'] ?? 'DAYS';
        $excludePastRegCampaigns = isset($post['excludeCustPrevRegCamp']) && (int)$post['excludeCustPrevRegCamp'] === 1;

        if ($excludePastRegCampaigns) {
            $excludePastRegVal = (int)($post['excCustPrevRegCampDays'] ?? 0); // days or hours value
        }

        $excludePastAutoVal = 0;
        $excludePrevAutoCampUnit = $post['excCustPrevAutoCampUnit'] ?? 'DAYS';
        $excludePastAutoCampaigns = isset($post['excludeCustPrevAutoCamp']) && (int)$post['excludeCustPrevAutoCamp'] === 1;
        if ($excludePastAutoCampaigns) {
            $excludePastAutoVal = (int)($post['excCustPrevAutoCampDays'] ?? 0); // days or hours value
        }

        $sendPromoBy = CAMPAIGN_SEND_BY_PUSH;
        if (isset($post['sendPromoBy']) && $post['sendPromoBy'] === 'email_sms') {
            $sendPromoBy = CAMPAIGN_SEND_BY_EMAIL_SMS;
        }

        if (isset($post['sendPromoBy']) && $post['sendPromoBy'] === 'sms_push') {
            $sendPromoBy = CAMPAIGN_SEND_BY_SMS_PUSH;
        }

        if (isset($post['sendPromoBy']) && $post['sendPromoBy'] === 'email') {
            $sendPromoBy = CAMPAIGN_SEND_BY_EMAIL;
        }

        if (isset($post['sendPromoBy']) && $post['sendPromoBy'] === 'sms') {
            $sendPromoBy = CAMPAIGN_SEND_BY_SMS;
        }

        $auto = isset($post['autoCampaign']) ? (boolean)$post['autoCampaign'] : false;
        $expiryDate = null;
        $expiryFlag = true;
        if ($auto) {
            $expiryDate = empty($post['expiryDate']) ? MKT_CAMPAIGN_EXPIRY_DATE : $post['expiryDate'];
            $expiryFlag = empty($post['expiryDate']);
        }
        $branchIdOp13 = empty($post['branchIdOp13']) ? [] : $post['branchIdOp13'];
        if (!is_array($branchIdOp13)) {
            $branchIdOp13 = [$branchIdOp13];
        }

        $campaignDataParameters = [
            'idempotency_key' => empty($post['idempotency_key']) ? null : $post['idempotency_key'],
            'audience_type' => empty($post['audience_type']) ? null : $post['audience_type'],
            'summary_storage' => $post['summary_storage'] ?? '',
            'is_campaign_integration' => empty($post['isCustomerExport']) ? false : boolval($post['isCustomerExport']),
            'apply_and_criteria' => isset($post['applyConditionsAnd']) && (int)$post['applyConditionsAnd'] === 1,
            'exclude_dormants' => isset($post['excludeDormants']) && (int)$post['excludeDormants'] === 1,
            'exclude_customer_in_previous_campaign' => $excludePastRegCampaigns,
            'exclude_customer_in_previous_campaign_past_days' => $excludePrevRegCampUnit === 'DAYS' ? $excludePastRegVal : 0,
            'exclude_customer_in_previous_campaign_past_hours' => $excludePrevRegCampUnit === 'HOURS' ? $excludePastRegVal : 0,
            'exclude_customer_in_previous_automated_campaign' => $excludePastAutoCampaigns,
            'exclude_customer_in_previous_campaign_ids' => !empty($post['excludeCustPrevRegCampIds'])? json_decode($post['excludeCustPrevRegCampIds'], true):[],
            'exclude_customer_in_previous_campaign_criteria'=> isset($post['excludeCustPrevRegCriteria'])? (int)$post['excludeCustPrevRegCriteria']:0,
            'exclude_customer_in_previous_automated_campaign_past_days' => $excludePrevAutoCampUnit === 'DAYS' ? $excludePastAutoVal : 0,
            'exclude_customer_in_previous_automated_campaign_past_hours' => $excludePrevAutoCampUnit === 'HOURS' ? $excludePastAutoVal : 0,
            'exclude_customer_in_previous_automated_campaign_ids' => !empty($post['excludeCustPrevAutoCampIds'])? json_decode($post['excludeCustPrevAutoCampIds'], true):[],
            'exclude_customer_in_previous_automated_campaign_criteria'=> isset($post['excludeCustPrevAutoCriteria'])? (int)$post['excludeCustPrevAutoCriteria']:0,
            'send_by' => $sendPromoBy,
            'send_by_priority' => $post['sendEmailSms'] ?? null,
            'sms_body' => empty($post['messageSMS']) ? '' : trim($post['messageSMS']),
            'include_offer_link_in_sms' => (bool)($post['addOffersLinkFlag'] ?? false),
            'include_referral_link_in_sms' => (bool)($post['addReferralLinkFlag'] ?? false),
            'include_opt_in_link_in_sms' => (bool)($post['reOptInCampaign'] ?? false),
            'silent' => isset($post['silent']) ? (int)$post['silent'] : 0,
            'who_will_receive' => $post['whoWillReceive'],
            'top_spending_customers' => isset($post['topSpending']) ? (int)$post['topSpending'] : 50,
            'specific_customer_email_phone' => isset($post['specificCustomer']) ? $post['specificCustomer'] : '',
            'specific_customer_email_phone_array' => empty($post['specificCustomersList']) ? [] : $post['specificCustomersList'],
            'product_reward_list' => isset($post['productRewardListId']) ? $post['productRewardListId'] : [],
            'customer_points_operator' => isset($post['havePtsOperator']) ? (int)$post['havePtsOperator'] : 1,
            'customer_have_points' => isset($post['havePts']) ? (int)$post['havePts'] : 0,
            'customer_have_end_points' => isset($post['haveEndPts']) ? (int)$post['haveEndPts'] : 0,
            'customers_redemption_list' => empty($post['punchItemIds']) ? [] : $post['punchItemIds'],
            'customers_redemption_days' => isset($post['daysOp51']) ? (int)$post['daysOp51'] : 14,
            'customers_redemption_branches' => empty($post['branchIdOp51']) ? [] : $post['branchIdOp51'],
            'customers_offer_list' => empty($post['offerIds']) ? [] : $post['offerIds'],
            'customers_offer_days' => isset($post['daysOp50']) ? (int)$post['daysOp50'] : 14,
            'customers_offer_branches' => empty($post['branchIdOp50']) ? [] : $post['branchIdOp50'],
            'customers_campaigned_offers_redemptions_operator' => empty($post['campaignedOffersRedemptionsOperator']) ? 1 : (int)$post['campaignedOffersRedemptionsOperator'],
            'customers_campaigned_offers_redemptions_campaign_ids' => empty($post['campaignedOffersRedemptionsCampaignIds']) ? [] : $post['campaignedOffersRedemptionsCampaignIds'],
            'customers_campaigned_offers_redemptions_days' => empty($post['campaignedOffersRedemptionsDays']) ? 0 : (int)$post['campaignedOffersRedemptionsDays'],
            'customers_campaigned_offers_redemptions_branches' => empty($post['campaignedOffersRedemptionsBranches']) ? [] : $post['campaignedOffersRedemptionsBranches'],
            'customers_campaigned_offers_redemptions_offers_list' => empty($post['campaignedOffersRedemptionsOffersList']) ? [] : $post['campaignedOffersRedemptionsOffersList'],
            'customers_campaigned_offers_redemptions_redemptions_list' => empty($post['campaignedOffersRedemptionsRedemptionsList']) ? [] : $post['campaignedOffersRedemptionsRedemptionsList'],
            'customer_types_list' => empty($post['customerTypesId']) ? [] : $post['customerTypesId'],
            'customer_tags_list' => empty($post['tagsId']) ? [] : $post['tagsId'],
            'referred_start_date' => isset($post['referredBetwFirst']) ? $post['referredBetwFirst'] : '',
            'referred_end_date' => isset($post['referredBetwLast']) ? $post['referredBetwLast'] : '',
            'referral_status_list' => empty($post['referralStatus']) ? [] : $post['referralStatus'],
            'referred_customers_number' => isset($post['referredCustomersNumber']) ? (int)$post['referredCustomersNumber'] : 0,
            'referred_customers_status_list' => empty($post['referredCustomersStatus']) ? [] : $post['referredCustomersStatus'],
            'lifetime_balance' => isset($post['haveLifetime']) ? (int)$post['haveLifetime'] : 1,
            'lifetime_balance_operator' => isset($post['haveLifetimeOperator']) ? (int)$post['haveLifetimeOperator'] : 1,
            'spending_weeks_amount' => isset($post['spentWeeksAmount']) ? (int)$post['spentWeeksAmount'] : 0,
            'spending_weeks_number' => isset($post['spentWeeksNumber']) ? (int)$post['spentWeeksNumber'] : 0,
            'spending_weeks_operator' => isset($post['spentWeeksOperator']) ? (int)$post['spentWeeksOperator'] : 1,
            'segments_list' => empty($post['segmentsId']) ? [] : $post['segmentsId'],
            'no_purchases_days' => isset($post['noRegTrxDays']) ? (int)$post['noRegTrxDays'] : 0,
            'no_purchases_days_range_end' => isset($post['noRegTrxDaysRangeEnd']) ? (int)$post['noRegTrxDaysRangeEnd'] : 0,
            'no_purchases_days_range_operator' => isset($post['noRegTrxDaysRangeOperator']) ? (int)$post['noRegTrxDaysRangeOperator'] : 1,
            'no_purchases_flag' => isset($post['noRegTrxFlag']) ? (int) $post['noRegTrxFlag'] : 2,
            'no_purchases_branches' => $post['noRegTrxBranchIds'] ?? [],
            'purchases_number' => isset($post['atLeastOneTrxNum']) ? (int)$post['atLeastOneTrxNum'] : 0,
            'purchase_weeks_number' => isset($post['atLeastOneTrxIn']) ? (int)$post['atLeastOneTrxIn'] : 0,
            'purchase_weeks_operator' => isset($post['weeksOperator']) ? (int)$post['weeksOperator'] : 0,
            'visits_start_date' => isset($post['firstVisitBetwFirst']) ? $post['firstVisitBetwFirst'] : '',
            'visits_end_date' => isset($post['firstVisitBetwLast']) ? $post['firstVisitBetwLast'] : '',
            'visits_branches' => empty($post['branchIdOp18']) ? [] : $post['branchIdOp18'],
            'customers_branches' => empty($post['branchId']) ? [] : $post['branchId'],
            'customers_languages_list' => empty($post['customerLanguagesId']) ? [] : $post['customerLanguagesId'],
            'customers_postal_code' => isset($post['postalCode']) ? $post['postalCode'] : '',
            'booked_appointment_start_date' => $post['bookedAppointmentBetwFirst'] ?? '',
            'booked_appointment_end_date' => $post['bookedAppointmentBetwLast'] ?? '',
            'booking_appointments_list' => empty($post['bookingServiceId']) ? [] : $post['bookingServiceId'],
            'booking_appointment_operator' => isset($post['bookingServiceOperator']) ? (int)$post['bookingServiceOperator'] : 0,
            'booking_appointment_days' => isset($post['bookingLastAppointmentsDays']) ? (int)$post['bookingLastAppointmentsDays'] : 0,
            'product_review_operator' => isset($post['reviewOperator']) ? (int)$post['reviewOperator'] : 0,
            'product_review_days' => isset($post['reviewDays']) ? (int)$post['reviewDays'] : 0,
            'email_body' => empty($post['messageEmail']) ? '' : trim(stripslashes($post['messageEmail'])),
            'email_subject' => empty($post['email_subject']) ? '' : trim(stripslashes($post['email_subject'])),
            'push_title' => empty($post['pushNotificationTitle']) ? '' : trim(stripslashes($post['pushNotificationTitle'])),
            'push_text' => empty($post['pushNotificationMessage']) ? '' : trim(stripslashes($post['pushNotificationMessage'])),
            'push_link' => empty($post['pushNotificationLink']) ? '' : trim(stripslashes($post['pushNotificationLink'])),
            'push_image_url' => empty($post['pushNotificationImage']) ? '' : trim(stripslashes($post['pushNotificationImage'])),
            'redirect_from_editor' => (isset($post['redirectFromEditor']) && $post['redirectFromEditor'] == 'true') ? true : false,
            'send_to_invalid_phone_number' => isset($post['send_to_invalid_sms']) ? (boolean)$post['send_to_invalid_sms'] : false,
            'offers' => isset($post['selectedOffers']) ? self::getOffersFromPost($post) : [],
            'auto' => $auto,
            'frequency' => isset($post['frequency']) ? (int)$post['frequency'] : (int)$auto,
            'mode' => isset($post['mktProcessType']) ? $post['mktProcessType'] : 'New',
            'schedule_date' => (isset($post['scheduleFlag'], $post['scheduleDate']) && $post['scheduleFlag'] == 1) ? $post['scheduleDate'] : null,
            'schedule_time' => isset($post['scheduleTime']) ? $post['scheduleTime'] : null,
            'trigger_schedule_timing' => $post['trigger_schedule_timing'] ?? null,
            'isValidateEnoughCredit' => (isset($post['valEnoughCredit']) && $post['valEnoughCredit'] == 'true') ? true : false,
            'draft' => (isset($post['mktIsDraft']) && $post['mktIsDraft'] == 'true') ? 1 : 0,
            'isValidateDeliveryTime' => (isset($post['valAskSaveMKNewDateTime']) && $post['valAskSaveMKNewDateTime'] == 'true') ? true : false,
            'expiry_date' => $expiryDate,
            'expiry_flag' => $expiryFlag,
            'name' => isset($post['campaign_name']) ? $post['campaign_name'] : '',
            'ip' => $post['ip'] ?? null,
            'user_agent' => $post['user_agent'] ?? null,
            'ls_product_tags' => empty($post['productTags']) ? [] : $post['productTags'],
            'ls_product_tags_days' => empty($post['productTagsDays']) ? 730 : (int)$post['productTagsDays'],
            'ls_product_brands' => empty($post['lsRetailBrands']) ? [] : $post['lsRetailBrands'],
            'ls_product_brands_days' => empty($post['lsRetailBrandsDays']) ? 0 : (int)$post['lsRetailBrandsDays'],
            'ls_products' => empty($post['lsRetailProducts']) ? [] : json_decode($post['lsRetailProducts'], true),
            'ls_products_days' => empty($post['lsRetailProductsDays']) ? 0 : (int)$post['lsRetailProductsDays'],
            'ls_products_branches' => empty($post['lsRetailProductsBranches']) ? [] : $post['lsRetailProductsBranches'],
            'ls_categories' => empty($post['lsRetailCategories']) ? [] : json_decode($post['lsRetailCategories'], true),
            'ls_categories_days' => empty($post['lsRetailCategoriesDays']) ? 0 : (int)$post['lsRetailCategoriesDays'],
            'shopify_account' => empty($post['shopifyAccount']) ? null : $post['shopifyAccount'],
            'shopify_categories' => empty($post['shopifyCategories']) ? [] : json_decode($post['shopifyCategories'], true),
            'shopify_categories_days' => empty($post['shopifyCategoriesDays']) ? 0 : (int)$post['shopifyCategoriesDays'],
            'shopify_products' => empty($post['shopifyProducts']) ? [] : json_decode($post['shopifyProducts'], true),
            'shopify_products_days' => empty($post['shopifyProductsDays']) ? 0 : (int)$post['shopifyProductsDays'],
            'shopify_product_vendors' => empty($post['shopifyVendors']) ? [] : json_decode($post['shopifyVendors'], true),
            'shopify_product_vendors_days' => empty($post['shopifyVendorsDays']) ? 0 : (int)$post['shopifyVendorsDays'],
            'shopify_product_tags' => empty($post['shopifyTags']) ? [] : json_decode($post['shopifyTags'], true),
            'shopify_product_tags_days' => empty($post['shopifyTagsDays']) ? 0 : (int)$post['shopifyTagsDays'],
            'lsEcom_account' => empty($post['lsEcomAccount']) ? null : $post['lsEcomAccount'],
            'lsEcom_categories' => empty($post['lsEcomCategories']) ? [] : json_decode($post['lsEcomCategories'], true),
            'lsEcom_categories_days' => empty($post['lsEcomCategoriesDays']) ? 0 : (int)$post['lsEcomCategoriesDays'],
            'lsEcom_products' => empty($post['lsEcomProducts']) ? [] : json_decode($post['lsEcomProducts'], true),
            'lsEcom_products_days' => empty($post['lsEcomProductsDays']) ? 0 : (int)$post['lsEcomProductsDays'],
            'lsEcom_product_vendors' => empty($post['lsEcomVendors']) ? [] : json_decode($post['lsEcomVendors'], true),
            'lsEcom_product_vendors_days' => empty($post['lsEcomVendorsDays']) ? 0 : (int)$post['lsEcomVendorsDays'],
            'lsEcom_product_tags' => empty($post['lsEcomTags']) ? [] : json_decode($post['lsEcomTags'], true),
            'lsEcom_product_tags_days' => empty($post['lsEcomTagsDays']) ? 0 : (int)$post['lsEcomTagsDays'],
            'lsEcom_product_brands' => empty($post['lsEcomBrands']) ? [] : json_decode($post['lsEcomBrands'], true),
            'lsEcom_product_brands_days' => empty($post['lsEcomBrandsDays']) ? 0 : (int)$post['lsEcomBrandsDays'],
            'ecomz_product_tags' => empty($post['ecomzTags']) ? [] : json_decode($post['ecomzTags'], true),
            'ecomz_product_tags_days' => empty($post['ecomzTagsDays']) ? 0 : (int)$post['ecomzTagsDays'],
            'ecomz_product_brands' => empty($post['ecomzBrands']) ? [] : json_decode($post['ecomzBrands'], true),
            'ecomz_product_brands_days' => empty($post['ecomzBrandsDays']) ? 0 : (int)$post['ecomzBrandsDays'],
            'ecomz_products' => empty($post['ecomzProducts']) ? [] : json_decode($post['ecomzProducts'], true),
            'ecomz_products_days' => empty($post['ecomzProductsDays']) ? 0 : (int)$post['ecomzProductsDays'],
            'ecomz_categories' => empty($post['ecomzCategories']) ? [] : json_decode($post['ecomzCategories'], true),
            'ecomz_categories_days' => empty($post['ecomzCategoriesDays']) ? 0 : (int)$post['ecomzCategoriesDays'],
            'ecomz_account' => empty($post['ecomzAccount']) ? null : $post['ecomzAccount'],
            'woo_product_tags' => empty($post['wooTags']) ? [] : json_decode($post['wooTags'], true),
            'woo_product_tags_days' => empty($post['wooTagsDays']) ? 0 : (int)$post['wooTagsDays'],
            'woo_products' => empty($post['wooProducts']) ? [] : json_decode($post['wooProducts'], true),
            'woo_products_days' => empty($post['wooProductsDays']) ? 0 : (int)$post['wooProductsDays'],
            'woo_categories' => empty($post['wooCategories']) ? [] : json_decode($post['wooCategories'], true),
            'woo_categories_days' => empty($post['wooCategoriesDays']) ? 0 : (int)$post['wooCategoriesDays'],
            'woo_account' => empty($post['wooAccount']) ? null : $post['wooAccount'],
            'bc_product_brands' => empty($post['bcBrands']) ? [] : json_decode($post['bcBrands'], true),
            'bc_product_brand_days' => empty($post['bcBrandsDays']) ? 0 : (int)$post['bcBrandsDays'],
            'bc_products' => empty($post['bcProducts']) ? [] : json_decode($post['bcProducts'], true),
            'bc_products_days' => empty($post['bcProductsDays']) ? 0 : (int)$post['bcProductsDays'],
            'bc_categories' => empty($post['bcCategories']) ? [] : json_decode($post['bcCategories'], true),
            'bc_categories_days' => empty($post['bcCategoriesDays']) ? 0 : (int)$post['bcCategoriesDays'],
            'bc_account' => empty($post['bcAccount']) ? null : $post['bcAccount'],
            'magento_categories' => empty($post['magentoCategories']) ? [] : json_decode($post['magentoCategories'], true),
            'magento_categories_days' => empty($post['magentoCategoriesDays']) ? 0 : (int)$post['magentoCategoriesDays'],
            'magento_products' => empty($post['magentoProducts']) ? [] : json_decode($post['magentoProducts'], true),
            'magento_products_days' => empty($post['magentoProductsDays']) ? 0 : (int)$post['magentoProductsDays'],
            'magento_account' => empty($post['magentoAccount']) ? null : $post['magentoAccount'],
            'birthday_customers_criteria' => empty($post['customerCriteriaOp9']) ? 1 : (int)$post['customerCriteriaOp9'],
            'birthday_customers_types_list' => empty($post['customerTypesOp9']) ? [] : $post['customerTypesOp9'],
            'birthday_notification_days' => empty($post['daysBeforeBirthday']) ? 1 : (int)$post['daysBeforeBirthday'],
            'pts_expire_inactive_days' => empty($post['ptsExpireInactiveDays']) ? 0 : (int)$post['ptsExpireInactiveDays'],
            'pts_expire_days' => empty($post['ptsExpireDays']) ? 0 : (int)$post['ptsExpireDays'],
            'after_first_visit_days' => empty($post['afterFirstVisit']) ? 0 : (int)$post['afterFirstVisit'],
            'after_first_visit_branches' => $branchIdOp13,
            'earn_or_reward_number' => empty($post['nbRewardRedemp']) ? 1 : (int)$post['nbRewardRedemp'],
            'earn_or_reward_option' => empty($post['optRewardRedemp']) ? 1 : (int)$post['optRewardRedemp'],
            'last_purchase_days' => empty($post['noTrxDays']) ? 0 : (int)$post['noTrxDays'],
            'last_purchase_days_range_end' => empty($post['noTrxDaysRangeEnd']) ? 0 : (int)$post['noTrxDaysRangeEnd'],
            'last_purchase_days_range_operator' => isset($post['noTrxDaysRangeOperator']) ? (int)$post['noTrxDaysRangeOperator'] : 1,
            'last_purchase_branches' => $post['noTrxBranchIds'] ?? [],
            'reg_last_purchase_days' => empty($post['regLastTrxDays']) ? 0 : (int)$post['regLastTrxDays'],
            'reg_last_purchase_days_range_end' => empty($post['regLastTrxDaysRangeEnd']) ? 0 : (int)$post['regLastTrxDaysRangeEnd'],
            'reg_last_purchase_days_range_operator' => isset($post['regLastTrxDaysRangeOperator']) ? (int)$post['regLastTrxDaysRangeOperator'] : 1,
            'reg_last_purchase_branches' => $post['regLastTrxBranchIds'] ?? [],
            'register_no_transaction_days' => empty($post['regNoTrxDays']) ? 7 : (int)$post['regNoTrxDays'],
            'template_data' => empty($post['templateData']) ? null : trim(stripslashes($post['templateData'])),
            'template_general_styles' => empty($post['templateGeneralStyles']) ? null : trim(stripslashes($post['templateGeneralStyles'])),
            'customers_registered_start_date' => $post['registeredCustomersBetwFirst'] ?? '',
            'customers_registered_end_date' => $post['registeredCustomersBetwLast'] ?? '',
            'moved_from_tiers_list' => $post['movedFromCustomerTypeId'] ?? [],
            'moved_to_tiers_list' => $post['movedToCustomerTypeId'] ?? [],
            'store_credit_operator' => empty($post['haveGiftcardOperator']) ? 1 : (int)$post['haveGiftcardOperator'],
            'store_credit_amount' => isset($post['haveGiftcard']) ? (int)$post['haveGiftcard'] : 0,
            'store_credit_end_amount' => isset($post['haveEndGiftcard']) ? (int)$post['haveEndGiftcard'] : 0,
            'ignore_consent' => isset($post['ignore_consent']) ? (bool)$post['ignore_consent'] : false,
            'vend_product_tags' => empty($post['vendTags']) ? [] : json_decode($post['vendTags'], true),
            'vend_product_tags_days' => empty($post['vendTagsDays']) ? 0 : (int)$post['vendTagsDays'],
            'vend_products' => empty($post['vendProducts']) ? [] : json_decode($post['vendProducts'], true),
            'vend_products_days' => empty($post['vendProductsDays']) ? 0 : (int)$post['vendProductsDays'],
            'vend_product_vendors' => empty($post['vendVendors']) ? [] : json_decode($post['vendVendors'], true),
            'vend_product_vendors_days' => empty($post['vendVendorsDays']) ? 0 : (int)$post['vendVendorsDays'],
            'vend_product_brands' => empty($post['vendBrands']) ? [] : json_decode($post['vendBrands'], true),
            'vend_product_brands_days' => empty($post['vendBrandsDays']) ? 0 : (int)$post['vendBrandsDays'],
            'heartland_account' => empty($post['heartlandAccount']) ? null : $post['heartlandAccount'],
            'heartland_products' => empty($post['heartlandProducts']) ? [] : json_decode($post['heartlandProducts'], true),
            'heartland_products_days' => empty($post['heartlandProductsDays']) ? 0 : (int)$post['heartlandProductsDays'],
            'heartland_categories' => empty($post['heartlandCategories']) ? [] : json_decode($post['heartlandCategories'], true),
            'heartland_categories_days' => empty($post['heartlandCategoriesDays']) ? 0 : (int)$post['heartlandCategoriesDays'],
            'heartland_product_vendors' => empty($post['heartlandVendors']) ? [] : json_decode($post['heartlandVendors'], true),
            'heartland_product_vendors_days' => empty($post['heartlandVendorsDays']) ? 0 : (int)$post['heartlandVendorsDays'],
            'lsk_products' => empty($post['lskProducts']) ? [] : json_decode($post['lskProducts'], true),
            'lsk_products_days' => empty($post['lskProductsDays']) ? 1 : (int)$post['lskProductsDays'],
            'lsk_products_quantity' => empty($post['lskProductsQuantity']) ? 1 : (int)$post['lskProductsQuantity'],
            'lsk_products_branches' => empty($post['branchIdOp76']) ? [] : $post['branchIdOp76'],
            'lsk_accounting_groups' => empty($post['lskAccountingGroups']) ? [] : json_decode($post['lskAccountingGroups'], true),
            'lsk_accounting_groups_branches' => empty($post['branchIdOp75']) ? [] : $post['branchIdOp75'],
            'lsk_accounting_groups_days' => empty($post['lskAccountingGroupsDays']) ? 1 : (int)$post['lskAccountingGroupsDays'],
            'qrcode_account' => empty($post['qrCodeAccount']) ? null : $post['qrCodeAccount'],
            'qrcode_products' => empty($post['qrCodeProducts']) ? [] : json_decode($post['qrCodeProducts'], true),
            'qrcode_products_days' => empty($post['qrCodeProductsDays']) ? 0 : (int)$post['qrCodeProductsDays'],
            'ecwid_products' => empty($post['ecwidProducts']) ? [] : json_decode($post['ecwidProducts'], true),
            'ecwid_products_days' => empty($post['ecwidProductsDays']) ? 1 : (int)$post['ecwidProductsDays'],
            'ecwid_product_brands' => empty($post['ecwidBrands']) ? [] : json_decode($post['ecwidBrands'], true),
            'ecwid_product_brands_days' => empty($post['ecwidBrandsDays']) ? 1 : (int)$post['ecwidBrandsDays'],
            'ecwid_categories' => empty($post['ecwidCategories']) ? [] : json_decode($post['ecwidCategories'], true),
            'ecwid_categories_days' => empty($post['ecwidCategoriesDays']) ? 1 : (int)$post['ecwidCategoriesDays'],
            'review_orders_date_range' => empty($post['reviewOrdersDateRange']) ? [] : $post['reviewOrdersDateRange'],
            'review_last_orders' => empty($post['reviewLastOrders']) ? [] : $post['reviewLastOrders'],
            'review_products' => empty($post['reviewProducts']) ? [] : json_decode($post['reviewProducts'], true),
            'review_products_days' => empty($post['reviewProductsDays']) ? 0 : (int)$post['reviewProductsDays'],
            'predict_churn_probability' => empty($post['predictChurnProb']) ? 0 : (int)$post['predictChurnProb'],
            'predict_churn_prob_operator' => empty($post['predictChurnProbOp']) ? 1 : (int)$post['predictChurnProbOp'],
            'geofences' => empty($post['geofences']) ? [] : $post['geofences'],
        ];

        $campaignDataParameters = array_merge($campaignDataParameters, self::getCurrentCampaignDataFromPost($post));

        return new self($campaignDataParameters);
    }

    /**
     * Refine the raw data by populating the "who_will_receive" IDs and applying the corresponding dependent filters
     * @param $data
     * @return CampaignData
     */
    public static function transformRawData($data): CampaignData
    {
        // Extract the filter IDs and elements to be set in campaign data
        $filtersCriteria = self::splitTargetedCriteriaFromRawData($data);

        // Set the filter IDs in campaign data
        $data ['who_will_receive'] = $filtersCriteria ['filterIds'];

        // Set the filter criteria in a separated array to be merged into the campaign data
        $criteria = $filtersCriteria ['filterElements'];

        // Merge original data with current campaign data
        $campaignData = array_merge($data, $criteria, self::getCurrentCampaignData($data), self::setAdditionalFiltersForRawData($data));

        return new self($campaignData);
    }

    /**
     * Handle additional filter for the raw campaign data
     * @param $data
     * @return array
     */
    private static function setAdditionalFiltersForRawData($data): array
    {
        // Handle the expiry date
        $expiryDate = null;
        $expiryFlag = true;
        if ($data['auto']) {
            $requestExpiryDate = isset($data['expiry_date']) && !empty($data['expiry_date']);
            $expiryDate = $requestExpiryDate ? $data['expiry_date'] : MKT_CAMPAIGN_EXPIRY_DATE;
            $expiryFlag = $requestExpiryDate;
        }

        return [
            'expiry_date' => $expiryDate,
            'expiry_flag' => $expiryFlag
        ];
    }

    /**
     * @param $criteria
     * @return array[]
     */
    public static function splitTargetedCriteriaFromRawData($criteria): array
    {
        $filterIds = $filterElements = [];

        if (isset($criteria['targeted_customer_criteria'])) {
            // Build the filter IDs and elements
            foreach ($criteria['targeted_customer_criteria'] as $targetedFilters) {
                foreach ($targetedFilters as $key => $value) {
                    if ($key === 'id') {
                        // Extract 'id' element into the second array
                        $filterIds[] = $value;
                    } else {
                        // Extract elements into the first array
                        $filterElements[$key] = $value;
                    }
                }
            }

            // Unset the targeted customer criteria to use fill the common elements array below
            unset($criteria['targeted_customer_criteria']);
        }

        // Set the common filters after unset the targeted customer criteria
        $commonElements = $criteria;

        return [
            'filterIds' => $filterIds,
            'filterElements' => $filterElements,
            'commonElements' => $commonElements
        ];
    }

    private static function getCurrentCampaignDataFromPost($post)
    {
        if (!isset($post['mktID'])) {
            return [];
        }

        $currentCampaign = Campaign::find($post['mktID']);

        $data = [];

        if ($currentCampaign) {
            if (isset($post['mktProcessType']) && ($post['mktProcessType'] == 'Edit' || $post['mktProcessType'] == 'EditDraft')) {
                $data['id'] = (int)$post['mktID'];
            }
            $data['previous_campaign'] = $currentCampaign;
            $data['locked'] = $currentCampaign->locked;

            if ($currentCampaign->enabled == 1) {
                $data['send_email_pending'] = $currentCampaign->sent_email;
                $data['send_sms_pending'] = $currentCampaign->sent_sms;
                $data['sent_ios_push_notif_pending'] = $currentCampaign->sent_ios_push_notif;
                $data['sent_android_push_notif_pending'] = $currentCampaign->sent_android_push_notif;
            }
        }

        return $data;
    }

    /**
     * @param $requestData
     * @return array
     */
    private static function getCurrentCampaignData($requestData)
    {
        if (!isset($requestData['id'])) {
            return [];
        }

        $currentCampaign = Campaign::find($requestData['id']);

        $data = [];

        if ($currentCampaign) {
            if (isset($requestData['mode']) && ($requestData['mode'] == 'Edit' || $requestData['mode'] == 'EditDraft')) {
                $data['id'] = (int) $requestData['id'];
            }

            $data['previous_campaign'] = $currentCampaign;
            $data['locked'] = $currentCampaign->locked;

            if ($currentCampaign->enabled == 1) {
                $data['send_email_pending'] = $currentCampaign->sent_email;
                $data['send_sms_pending'] = $currentCampaign->sent_sms;
                $data['sent_ios_push_notif_pending'] = $currentCampaign->sent_ios_push_notif;
                $data['sent_android_push_notif_pending'] = $currentCampaign->sent_android_push_notif;
            }
        }

        return $data;
    }



    private static function getOffersFromPost($post)
    {
        if (!isset($post['selectedOffers'])) {
            return [];
        }

        $selectedOffers = $post['selectedOffers'];

        $offers = json_decode(stripslashes($selectedOffers), true);

        $offersArray = [];
        foreach ($offers as $offer) {
            if (isset($offer['offerId'], $offer['offerType'])) {
                $offersArray[] = [
                    'id' => $offer['offerId'],
                    'type' => Offer::getTypeString($offer['offerType'])
                ];
            } else {
                throw new \InvalidArgumentException('Invalid selected offers');
            }
        }

        return $offersArray;
    }
}