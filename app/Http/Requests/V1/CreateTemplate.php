<?php

namespace App\Http\Requests\V1;

use App\Helpers\V1\Utils;
use App\Rules\ExistsMulti;
use Illuminate\Foundation\Http\FormRequest;

class CreateTemplate extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|string|max:100',
            'template' => 'required|string|min:1',
            'thumbnail' => 'sometimes|string|nullable|max:255',
            'general_styles' => 'sometimes|array|nullable',
            'template_data' =>  'sometimes|array|nullable',
            'source_module' => 'string|required_if:is_default,=,0|in:marketing',
            'system_message_type' => 'sometimes|integer|in:1,2', // 1- transactional / 2- marketing
            'channel' => 'sometimes|integer|in:1,2,3',
            'is_default' => 'required_if:source_module,=,null|integer|in:0,1',
            'email_subject' => 'sometimes|string|nullable|max:255'
        ];
    }
}