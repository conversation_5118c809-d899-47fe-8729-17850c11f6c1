<?php

namespace App\Http\Controllers\Api\V1\Users;

use App\Helpers\ApiHelper;
use App\Http\Controllers\Api\V1\ApiController;
use App\Http\Resources\NotificationCollection;
use App\Http\Resources\NotificationResource;
use App\Models\Relation;
use App\Models\V1\Business;
use App\Models\V1\Notification;
use App\Services\InAppNotificationService;
use App\Traits\HasFeatureFlag;
use Auth;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Log;
use DB;
use LogicException;

/**
 * Notification resource representation.
 *
 * @Resource("Notification", uri="/users/{id}/notifications")
 */
class NotificationController extends ApiController
{
    /**
     * List all Notifications for a user
     *
     * GET /users/{id}/notifications
     *
     * @Get("/users/{id}/notifications")
     * @Versions({"v1"})
     * @Response(200, body={"data": [{"id": 35,"units": 500,"title": "Download Kangaroo and Earn"}]})
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user(); //regular user
        /** @var Business $business */
        $business = $this->getBusinessForAppKey();
        $this->sanitizeInput(); # Always sanitize the input first

        $this->validate($request, [
            'per_page' => 'sometimes|integer|min:1|max:' . config('app.pagination_limit'),
        ]);

        $conditions = [];
        if ($request->notification_type_id) {
            $conditions = ApiHelper::buildQueryConditions(
                $request->only(['notification_type_id'])
            ); //dd($conditions);
        }

        $relations = explode(',', $request->relationships);

        /*if ($business->business_pk != KNG_BUSINESS_ID) {
            $notifications = Notification::forUser([$user->id], $conditions, $relations)
                ->where('business_fk', $business->business_pk)
                ->simplePaginate((int) $request->per_page);
        } elseif (HasFeatureFlag::feature('UBP')) {
            $userIds = (array) Relation::where('app_user_fk', $user->id)->pluck('user_fk')->toArray();
            $notifications = Notification::forUser($userIds, $conditions, $relations)
                ->simplePaginate((int) $request->per_page);
        } else {
            $notifications = Notification::forUser([$user->id], $conditions, $relations)
                ->simplePaginate((int) $request->per_page);
        }*/

        $service = new InAppNotificationService($business, $business, $user);
        $notifications = $service->getNotificationsForUserFromBigQuery();

        $colResource = new NotificationCollection($notifications);
        return $colResource->response();
    }

    /**
     * Retrieve a Notification
     *
     * GET /users/{id}/notifications/{notificationId}
     *
     * @Get("/users/{id}/notifications/{notificationId}")
     * @Versions({"v1"})
     * @Transaction({
     *      @Response(200, body={"data":{[]}})
     *      @Response(404, body={})
     * })
     * @Parameters({
     *      @Parameter("include", type="string", required=false description="Include the notification's relations.")
     * })
     *
     * @param \Illuminate\Http\Request $request
     * @param string $notificationId
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $notificationId)
    {
        return $this->errorNotImplemented();
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return $this->errorNotImplemented();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        return $this->errorNotImplemented();
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, $id)
    {
        return $this->errorNotImplemented();
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        return $this->errorNotImplemented();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        return $this->errorNotImplemented();
    }
}
