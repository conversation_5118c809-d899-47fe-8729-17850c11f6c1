<?php

namespace KangarooRewards\Common\Traits;

use Illuminate\Database\Query\Builder;
use KangarooRewards\Common\Models\FrequentBuyer;

trait FrequentBuyerTrait
{
    /**
     * @param $business
     * @param $user
     * @param $relations
     * @return Builder
     */
    public static function forUser($business, $user, $relations)
    {
        $query = FrequentBuyer::where('enabled', 1)
            ->where('business_branch_fk', $business->business_branch_pk);

        if (in_array('frequent_buyer_users', $relations, true) && $user) {
            $query->with(['frequentBuyerUsers' => function ($query) use($user) {
                $query->where('user_fk', $user->id)
                    ->where('status', 1) // active
                    ->orderBy('frequent_buyer_user_pk', 'DESC');
            }]);
        }

        return $query;
    }
}
