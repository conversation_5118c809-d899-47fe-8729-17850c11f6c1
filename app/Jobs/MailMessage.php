<?php

namespace App\Jobs;

use App\Models\V1\EmailProvider;
use App\Mail\Email;
use Closure;
use Log;
use Mail;
//use Swift_Mailer;
//use Swift_SmtpTransport as SmtpTransport;

class MailMessage extends Job
{
    /**
     * The message content.
     *
     * @var null|Email
     */
    protected $message;

    /**
     * @var mixed
     */
    protected $to;

    /**
     * @var mixed
     */
    protected $emailProvider;

    protected $callbackOnSent = [];

    /**
     * Create a new job instance.
     *
     * @param EmailProvider|null $emailProvider
     */
    public function __construct(EmailProvider $emailProvider = null)
    {
        $this->emailProvider = $emailProvider;

        // \Log::info(__CLASS__ . ' Initialized');
    }

    /**
     * Set the email message context.
     *
     * @param object $message
     * @return $this
     */
    public function setProvider(EmailProvider $emailProvider = null)
    {
        $this->emailProvider = $emailProvider;

        return $this;
    }

    /**
     * Set the email message context.
     *
     * @param object $message
     * @return $this
     */
    public function with(Email $message)
    {
        $this->message = $message;

        // \Log::info(__CLASS__ . ' with', ['template' => $this->message->view]);

        return $this;
    }

    /**
     * Set the receiver email address.
     *
     * @param string $to - receiver email address
     * @return $this
     */
    public function to($to)
    {
        $this->to = $to;

        return $this;
    }

    /**
     * Set callback to call after the message is sent.
     *
     * @param array $callbackOnSent
     * @return $this
     */
    public function setCallbackOnSent($callbackOnSent)
    {
        $this->callbackOnSent = $callbackOnSent;

        return $this;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Backup your default mailer
        //$backupMailer = Mail::getSwiftMailer();

//        if ($this->emailProvider) {
//            // Setup a new SmtpTransport instance for Gmail
//            $transport = SmtpTransport::newInstance($this->emailProvider->smtp_host, $this->emailProvider->smtp_port, 'ssl');
//            $transport->setUsername($this->emailProvider->smtp_username);
//            $transport->setPassword($this->emailProvider->smtp_password);
//
//            // Assign a new SmtpTransport to SwiftMailer
//            $gmail = new Swift_Mailer($transport);
//
//            // Assign it to the Laravel Mailer
//            Mail::setSwiftMailer($gmail);
//
//            Log::info(__METHOD__, ['user_email' => $this->to, 'emailProvider' => $this->emailProvider->id]);
//        }

        //send email
        Mail::to($this->to)->send($this->message);

        Log::info(__METHOD__ . ' Email sent', ['user_email' => $this->to, 'template' => $this->message->view]);

        // Restore your original mailer
        //Mail::setSwiftMailer($backupMailer);

        $this->onMessageSent();
    }

    public function getEmailMessage(): ?Email
    {
        return $this->message;
    }

    private function onMessageSent()
    {
        if (empty($this->callbackOnSent)) {
            return;
        }

        try {
            $className = $this->callbackOnSent['class'];
            $method = $this->callbackOnSent['method'];
            $args = $this->callbackOnSent['args'];

            if (!class_exists($className)) {
                return;
            }

            $reflection = new \ReflectionClass($className);

            if (!$reflection->hasMethod($method)) {
                return;
            }

            $status = EMAIL_SMS_STATUS_SENT;

            // The order of params is IMPORTANT
            array_push($args, $status);

            call_user_func_array([$className, $method], $args);

            Log::info(__METHOD__, ['user_email' => $this->to, 'callbackOnSent' => $this->callbackOnSent]);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, ['user_email' => $this->to, 'callbackOnSent' => $this->callbackOnSent]);
        }
    }
}
