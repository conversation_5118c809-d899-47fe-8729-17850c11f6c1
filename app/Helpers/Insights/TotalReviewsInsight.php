<?php

namespace App\Helpers\Insights;

use App\Models\V1\PosBranch;
use KangarooRewards\Reviews\Traits\ReviewBigQueryTrait;

class TotalReviewsInsight extends AbstractInsightBuilder
{
    use ReviewBigQueryTrait;

    public const INSIGHT_ID = 'total-reviews';

    public function getInsightTitle(): string
    {
        return 'Total reviews';
    }

    public function prepareFilters(): array
    {
        return [
            'product_id' => ['eq'],
            'pos_account_id' => ['eq']
        ];
    }

    public function prepareValueLists(): array
    {
        $accountIds = PosBranch::where('enabled', 1)
            ->where('pos_system_fk', POS_SYSTEM_KANGAROO_REVIEWS)
            ->where('business_fk', $this->business->business_pk)
            ->selectRaw('account_id as value, store_domain as label')
            ->distinct()
            ->get();

        return [
            'product_id' => [], // can be any product id
            'pos_account_id' => $accountIds
        ];
    }

    protected function prepareInsightData()
    {
        if ($this->groupBy == 'rating') {
            $dateFormat = 'rating';
        } else {
            $dateFormat = $this->formatDatasetKey('created_at');
        }

        $reviewsQuery = $this->getReviewsCollection($this->business->business_pk, $this->startDate, $this->endDate, $dateFormat);

        $this->applyInsightfilters($reviewsQuery, $this->data);

        return $this->queryBigQuery($reviewsQuery);
    }
}