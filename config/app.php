<?php

use Illuminate\Support\Facades\Facade;

return [

    /*
    |--------------------------------------------------------------------------
    | Application Name
    |--------------------------------------------------------------------------
    |
    | This value is the name of your application. This value is used when the
    | framework needs to place the application's name in a notification or
    | any other location as required by the application or its packages.
    */

    'name' => env('APP_NAME', 'Developers - Kangaroo Rewards'),

    /*
    |--------------------------------------------------------------------------
    | Application Environment
    |--------------------------------------------------------------------------
    |
    | This value determines the "environment" your application is currently
    | running in. This may determine how you prefer to configure various
    | services your application utilizes. Set this in your ".env" file.
    |
    */

    'env' => env('APP_ENV', 'production'),

    /*
    |--------------------------------------------------------------------------
    | Application Debug Mode
    |--------------------------------------------------------------------------
    |
    | When your application is in debug mode, detailed error messages with
    | stack traces will be shown on every error that occurs within your
    | application. If disabled, a simple generic error page is shown.
    |
    */

    'debug' => (bool) env('APP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Application URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | your application so that it is used when running Artisan tasks.
    |
    */

    'url' => env('APP_URL', 'https://api.kangaroorewards.com'),

    'asset_url' => env('ASSET_URL'),

    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. We have gone
    | ahead and set this to a sensible default for you out of the box.
    |
    */

    'timezone' => 'UTC',

    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by the translation service provider. You are free to set this value
    | to any of the locales which will be supported by the application.
    |
    */

    'locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Application Fallback Locale
    |--------------------------------------------------------------------------
    |
    | The fallback locale determines the locale to use when the current one
    | is not available. You may change the value to correspond to any of
    | the language folders that are provided through your application.
    |
    */

    'fallback_locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is used by the Illuminate encrypter service and should be set
    | to a random, 32 character string, otherwise these encrypted strings
    | will not be safe. Please do this before deploying an application!
    |
    */

    'key' => env('APP_KEY'),

    'cipher' => 'AES-256-CBC',

    /*
    |--------------------------------------------------------------------------
    | Maintenance Mode Driver
    |--------------------------------------------------------------------------
    |
    | These configuration options determine the driver used to determine and
    | manage Laravel's "maintenance mode" status. The "cache" driver will
    | allow maintenance mode to be controlled across multiple machines.
    |
    | Supported drivers: "file", "cache"
    |
    */

    'maintenance' => [
        'driver' => 'file',
        // 'store'  => 'redis',
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log settings for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Settings: "single", "daily", "syslog", "errorlog"
    |
    */

    'log' => env('APP_LOG', 'daily'),

    'log_level' => env('APP_LOG_LEVEL', 'debug'),

    'log_max_files' => 300,

    /*
    |--------------------------------------------------------------------------
    | Custom App Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure some settings for your application.
    |
    */

    'server_url' => env('SERVER_URL', 'https://www.kangaroorewards.com'),
    'platform_id' => 10,
    'platform_name' => 'API',
    'api_app_token' => env('API_APP_TOKEN'),
//    'activation_link_expires' => '+48 hours',
    'pagination_limit' => 100,
    'employees_pagination_limit' => 300,
    'jwt_key' => env('JWT_KEY'),
    'app_store_url' => 'https://itunes.apple.com/ca/app/kangaroo-rewards/id673355611?mt=8',
    'google_play_url' => 'https://play.google.com/store/apps/details?id=com.mobicept.kangaroo.rewards.smartphone',

    'short_url_api_url' => env('SHORT_URL_API_URL'),
    'short_url_username' => env('SHORT_URL_USERNAME'),
    'short_url_password' => env('SHORT_URL_PASSWORD'),
    'short_url_google_key' => env('SHORT_URL_GOOGLE_KEY'),

    'paypal_mode' => env('PAYPAL_MODE'),
    'paypal_log_level' => env('PAYPAL_LOG_LEVEL'),
    'paypal_client_id' => env('PAYPAL_CLIENT_ID'),
    'paypal_client_secret' => env('PAYPAL_CLIENT_SECRET'),
    'paypal_webhook_id' => env('PAYPAL_WEBHOOK_ID'),

    'yii_root_abs' => env('YII_ROOT_ABS'),
    'members_webapp_url' => env('MEMBERS_WEBAPP_URL'),

    'SENDGRID_API_KEY' => env('SENDGRID_API_KEY'),
    'firebase_key' => env('FIREBASE_API_KEY'),
    'bp_app_firebase_key' => env('BP_APP_FIREBASE_API_KEY'),

    'ADMIN_EMAIL' => env('ADMIN_EMAIL'),
    'MAIL_INFO_EMAIL' => env('MAIL_INFO_EMAIL'),
    'MAIL_TECH_SUPPORT' => env('MAIL_TECH_SUPPORT'),
    'MAIL_BCC_EMAIL' => env('MAIL_BCC_EMAIL'),

    'SHORT_URL_API_URL' => env('SHORT_URL_API_URL'),
    'SHORT_URL_USERNAME' => env('SHORT_URL_USERNAME'),
    'SHORT_URL_PASSWORD' => env('SHORT_URL_PASSWORD'),
    'SHORT_URL_GOOGLE_KEY' => env('SHORT_URL_GOOGLE_KEY'),

    'NEXMO_API_KEY' => env('NEXMO_API_KEY'),
    'NEXMO_API_TOKEN' => env('NEXMO_API_TOKEN'),

    'PLIVO_API_KEY' => env('PLIVO_API_KEY'),
    'PLIVO_API_TOKEN' => env('PLIVO_API_TOKEN'),

    'TWILIO_ACCOUNT_SID' => env('TWILIO_ACCOUNT_SID'),
    'TWILIO_AUTH_TOKEN' => env('TWILIO_AUTH_TOKEN'),


    'MAIL_FROM' => env('MAIL_FROM'),
    'MAIL_FROM_NAME' => env('MAIL_FROM_NAME'),
    'TWILIO_FROM' => env('TWILIO_FROM'),

    'INTEGRATION_API_CLIENT_CREDENTIALS_CLIENT_ID' => env('INTEGRATION_API_CLIENT_CREDENTIALS_CLIENT_ID'),
    'INTEGRATION_API_CLIENT_CREDENTIALS_CLIENT_SECRET' => env('INTEGRATION_API_CLIENT_CREDENTIALS_CLIENT_SECRET'),
    'INTEGRATION_API_SERVER' => env('INTEGRATION_API_SERVER'),
    'google_reCAPTCHA_secret' => env('GOOGLE_RECAPTCHA_SECRET'),

    'STRIPE_SECRET_ACCOUNT_PACKAGE' => env('STRIPE_SECRET_ACCOUNT_PACKAGE'),

    'TWILIO_VERIFY_SERVICE_SID' => env('TWILIO_VERIFY_SERVICE_SID'),
    'TWILIO_VERIFY_EMAIL_TEMPLATE_ID' => env('TWILIO_VERIFY_EMAIL_TEMPLATE_ID'),

    'GOOGLE_CLOUD_PROJECT' => env('GOOGLE_CLOUD_PROJECT'),
    'GOOGLE_APPLICATION_CREDENTIALS' => env('GOOGLE_APPLICATION_CREDENTIALS'),
    'GOOGLE_APPLICATION_CREDENTIALS_AUTH' => env('GOOGLE_APPLICATION_CREDENTIALS_AUTH'),

    'CHAT_GPT_APP_KEY' => env('CHAT_GPT_APP_KEY'),
    'ANTHROPIC_API_KEY' => env('ANTHROPIC_API_KEY'),
    'GROQ_API_KEY' => env('GROQ_API_KEY'),
    'GOOGLE_GEMINI_API_KEY' => env('GOOGLE_GEMINI_API_KEY'),
    'COHERE_API_KEY' => env('COHERE_API_KEY'),
    'CEREBRAS_API_KEY' => env('CEREBRAS_API_KEY'),

    'signup_widget_url_v2' => env('SIGNUP_WIDGET_URL_V2'),

    'uber_client_id' => env('UBER_CLIENT_ID'),
    'uber_client_secret' => env('UBER_CLIENT_SECRET'),
    'uber_organization_id' => env('UBER_ORGANIZATION_ID'),

    'trd_stripe_api_publishable_key' => env('TRD_STRIPE_API_PUBLISHABLE_KEY'),
    'trd_stripe_api_secret' => env('TRD_STRIPE_API_SECRET'),
    'trd_stripe_webhook_secret' => env('TRD_STRIPE_WEBHOOK_SECRET'),

    'amazon_partner_id_us' => env('AMAZON_PARTNER_ID_US'),
    'aws_access_key_us' => env('AWS_ACCESS_KEY_US'),
    'aws_secret_key_us' => env('AWS_SECRET_KEY_US'),

    'amazon_partner_id_ca' => env('AMAZON_PARTNER_ID_CA'),
    'aws_access_key_ca' => env('AWS_ACCESS_KEY_CA'),
    'aws_secret_key_ca' => env('AWS_SECRET_KEY_CA'),
    'amazon_base_url_domain_na' => env('AMAZON_BASE_URL_DOMAIN_NA'),

    'amazon_partner_id_uk' => env('AMAZON_PARTNER_ID_UK'),
    'aws_access_key_uk' => env('AWS_ACCESS_KEY_UK'),
    'aws_secret_key_uk' => env('AWS_SECRET_KEY_UK'),
    'amazon_base_url_domain_eu' => env('AMAZON_BASE_URL_DOMAIN_EU'),

    'CUSTOM_QUEUE_DRIVER' => env('CUSTOM_QUEUE_DRIVER', 'sqs'),
    'RESELLER_ENV_ID' => env('RESELLER_ENV_ID', 'KANGAROO_REWARDS'),
    'IP_WHOIS_KEY' => env('IP_WHOIS_KEY'),

    /*
    |--------------------------------------------------------------------------
    | Autoloaded Service Providers
    |--------------------------------------------------------------------------
    |
    | The service providers listed here will be automatically loaded on the
    | request to your application. Feel free to add your own services to
    | this array to grant expanded functionality to your applications.
    |
    */

    'providers' => [

        /*
         * Laravel Framework Service Providers...
         */
        Illuminate\Auth\AuthServiceProvider::class,
        Illuminate\Broadcasting\BroadcastServiceProvider::class,
        Illuminate\Bus\BusServiceProvider::class,
        Illuminate\Cache\CacheServiceProvider::class,
        Illuminate\Foundation\Providers\ConsoleSupportServiceProvider::class,
        Illuminate\Cookie\CookieServiceProvider::class,
        Illuminate\Database\DatabaseServiceProvider::class,
        Illuminate\Encryption\EncryptionServiceProvider::class,
        Illuminate\Filesystem\FilesystemServiceProvider::class,
        Illuminate\Foundation\Providers\FoundationServiceProvider::class,
        Illuminate\Hashing\HashServiceProvider::class,
        Illuminate\Mail\MailServiceProvider::class,
        Illuminate\Notifications\NotificationServiceProvider::class,
        Illuminate\Pagination\PaginationServiceProvider::class,
        Illuminate\Pipeline\PipelineServiceProvider::class,
        Illuminate\Queue\QueueServiceProvider::class,
        Illuminate\Redis\RedisServiceProvider::class,
        Illuminate\Auth\Passwords\PasswordResetServiceProvider::class,
        Illuminate\Session\SessionServiceProvider::class,
        Illuminate\Translation\TranslationServiceProvider::class,
        Illuminate\Validation\ValidationServiceProvider::class,
        Illuminate\View\ViewServiceProvider::class,

        // Package Service Providers
        KangarooRewards\Survey\SurveyServiceProvider::class,
        KangarooRewards\ProductsBatchImport\ProductsBatchImportServiceProvider::class,
        KangarooRewards\Import\ImportServiceProvider::class,
        KangarooRewards\Geofencing\GeofencingServiceProvider::class,
        // Laravel\Passport\PassportServiceProvider::class,
        KangarooRewards\BatchImport\BatchImportServiceProvider::class,
        KangarooRewards\RecommendationFeed\RecommendationFeedServiceProvider::class,
        KangarooRewards\Reports\ReportsServiceProvider::class,
        KangarooRewards\Reviews\ReviewsServiceProvider::class,
        KangarooRewards\UiProxy\UiProxyServiceProvider::class,

        // Application Service Providers...
        App\Providers\AppServiceProvider::class,
        // App\Providers\BroadcastServiceProvider::class,
        App\Providers\AuthServiceProvider::class,
        App\Providers\EventServiceProvider::class,
        App\Providers\RouteServiceProvider::class,
        App\Providers\PassportServiceProvider::class,
        App\Providers\GoogleLoginServiceProvider::class,
        App\Providers\ModelRepositoryServiceProvider::class,
        App\Providers\ServiceRepositoryProvider::class, 
    ],

    /*
    |--------------------------------------------------------------------------
    | Class Aliases
    |--------------------------------------------------------------------------
    |
    | This array of class aliases will be registered when this application
    | is started. However, feel free to register as many as you wish as
    | the aliases are "lazy" loaded so they don't hinder performance.
    |
    */

    'aliases' => Facade::defaultAliases()->merge([
        // 'ExampleClass' => App\Example\ExampleClass::class,
    ])->toArray(),
];
