<?php

namespace KangarooRewards\RecommendationFeed\Repositories;

use KangarooRewards\RecommendationFeed\Models\Recommendation;
use KangarooRewards\Common\Models\User;

class RecommendationRepository
{
    protected Recommendation $recommendation;

    public function __construct(Recommendation $recommendation)
    {
        $this->recommendation = $recommendation;
    }

    public function updateSeenAt($userId, $recommendationIds): void
    {
        $user = User::find($userId);

        foreach ($recommendationIds as $recommendationId) {
            $user->recommendations()->updateExistingPivot($recommendationId, ['seen_at' => now()]);
        }
        return;
    }

    public function attachToUser($userId, $recommendationId): void
    {
        $user = User::find($userId);
        $recommendation = Recommendation::find($recommendationId);

        if ($this->checkIfExists($userId, $recommendationId)) {
            if ($this->checkIfIsSeen($userId, $recommendationId)) {
                if ($recommendation->type_id != RECOMMENDATION_TYPE_BLOG) {
                    $user->recommendations()->attach($recommendationId);
                }
            }
        } else {
            $user->recommendations()->attach($recommendationId);
        }
    }

    public function checkIfIsSeen($userId, $recommendationId): bool
    {
        $user = User::find($userId);

        return $user->recommendations()
                ->wherePivot('recommendation_id', $recommendationId)
                ->whereNotNull('recommendation_user.seen_at')
                ->count() === $user->recommendations()
                ->wherePivot('recommendation_id', $recommendationId)
                ->count();
    }

    public function checkIfExists($userId, $recommendationId)
    {
        $user = User::find($userId);

        return $user->recommendations()
            ->wherePivot('recommendation_id', $recommendationId)
            ->exists();
    }

    public function getPerType($typeId)
    {
        return $this->recommendation->where('type_id', $typeId)->get();
    }

    public function all()
    {
        return $this->recommendation->all();
    }

    public function update($userId, $id, $data)
    {
        $user = User::findOrFail($userId);
        $recommendation = Recommendation::findOrFail($id);
        return $user->recommendations()
            ->updateExistingPivot($recommendation, $data);
    }

    public function create($data)
    {
        return Recommendation::create($data);
    }

    public function getByAction($action)
    {
        return $this->recommendation->where('action', $action)->get();
    }

    public function destroy($userId, $pivotId)
    {
        $user = User::findOrFail($userId);

        $recommendation = $user->recommendations()->wherePivot('id', $pivotId)->first();
        if ($recommendation) {
            if ($user->id === $recommendation->pivot->user_id) {
                return $user->recommendations()->updateExistingPivot(
                    $recommendation->id,
                    ['seen_at' => now()]
                );
            }
        }
        return false;
    }
}
