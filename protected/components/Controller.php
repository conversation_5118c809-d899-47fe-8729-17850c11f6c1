<?php

use Google\Protobuf\Enum;

/**
 * Controller is the customized base controller class.
 * All controller classes for this application should extend from this base class.
 */
class Controller extends CController
{
    /**
     * @var string the default layout for the controller view. Defaults to '//layouts/column1',
     * meaning using a single column layout. See 'protected/views/layouts/column1.php'.
     */
    //public $layout='//layouts/column1';
    public $layout = '//layouts/main';

    /**
     * @var array context menu items. This property will be assigned to {@link CMenu::items}.
     */
    public $menu = array();

    protected $profilingMessages = [];
    protected $initTime;

    /**
     * @var array the breadcrumbs of the current page. The value of this property will
     * be assigned to {@link CBreadcrumbs::links}. Please refer to {@link CBreadcrumbs::links}
     * for more details on how to specify this property.
     */
    public $breadcrumbs = array();

    public $mainmenu = array();

    //    public function accessRules()
    //    {
    //        return parent::accessRules();
    //    }

    protected function beforeAction($action)
    {
        if (Yii::app()->params['envParam'] !== 'PROD') {
            $route = Yii::app()->controller->getRoute(); // e.g., "site/index"

            $cspHeader = $this->checkCspRouteControllerDomains($route);
            // dd($cspHeader);
            header($cspHeader);
        }

        if (Utils::isApi()) {
            $this->authenticateViaBearerToken();
            $this->afterAction($action); // required to trigger before action to log the request
            return true;
        }

        return parent::beforeAction($action);
    }

    public function redirectToNewWebSite(string $page = '')
    {
        $url = 'https://loyalty.kangaroorewards.com/';

        if (Yii::app()->params['envParam'] !== 'PROD') {
            return; // Do not redirect for local,dev,staging
        }

        if (isset($_GET['lang']) && $_GET['lang'] !== 'en') {
            return; // Do not redirect if the language is other than English. Temporary
        }

        if (Yii::app()->params['envReseller'] === Utils::RESELLER_WL1) {
            $url = 'https://loyalty-club.netlify.app/';
        }

        // header("HTTP/1.1 301 Moved Permanently");
        // header("Location: {$url}");

        if ($page) {
            $url .= $page;
        }

        Yii::app()->getRequest()->redirect($url, true, 301);
    }

    //     CASE
    //     WHEN pos_system_fk in (7,10,17,22) 
    //     THEN store_domain
    //     WHEN pos_system_fk in (2)
    //     THEN store_name
    //     ELSE '' 
    // END AS domain
    // "

    protected function checkCspRouteControllerDomains($route)
    {
        $domains = [];
        $cspDomains = $this->getDefaultKangarooCspDomains($route);
        $route = Yii::app()->controller->getRoute(); // e.g., "site/index"

        $subPath = "";
        foreach (PosSystem::ALLOW_DOMAIN_CSP_ROUTES as $path) {
            if (str_contains($route, $path)) {
                $subPath = $path;
                continue;
            }
        }

        if (empty($subPath)) {
            return $this->generateCspHeader($cspDomains, $route);
        }

        $allowedCspIntegrations = PosSystem::ALLOW_DOMAIN_CSP_INTEGRATIONS;
        foreach (array_keys($allowedCspIntegrations) as $posSystemId) {
            $fieldName = $allowedCspIntegrations[$posSystemId];
            $posDomains = PosSystem::getAllowedCspIntegrations($posSystemId, $fieldName);
            // dd($posDomains);

            switch ($posSystemId) {
                case 2:
                    // $formattedPosDomains = array_map(function ($domain) use ($fieldName){
                    //     return PosSystem::vendGetAllowedBaseUrl($domain[$fieldName]);
                    // }, $posDomains);
                    // // dd($formattedPosDomains);
                    $formattedPosDomains = ['*.retail.lightspeed.app'];
                    break;
                case 7:
                case 10:
                case 22:
                    $formattedPosDomains = array_map(function ($domain) use ($fieldName) {
                        return PosSystem::concatHttpsAllowedBaseUrl($domain[$fieldName]);
                    }, $posDomains);
                    break;
                case 17:
                    // $formattedPosDomains = array_map(function ($domain) use ($fieldName) {
                    //     return $domain[$fieldName];
                    // }, $posDomains);
                    $formattedPosDomains = ["*.retail.heartland.us"];
                    break;
                default:
                    break;
            }
            $domains = array_merge($domains, $formattedPosDomains);
        }

        $cspDomains['domains'] = array_merge($domains, $cspDomains['domains']);

        return $this->generateCspHeader($cspDomains, $route);
    }

    private function getDefaultKangarooCspDomains($route)
    {
        $domains = [
            Yii::app()->params['serverUrl'],
            Yii::app()->params['betaUrl'],
            Yii::app()->params['kangarooApiUri'],
            Yii::app()->params['integration_api_url'],
            Yii::app()->params['membersUrl'],
            Yii::app()->params['app.shopify_admin_url'],
            // config('app.signup_page_url'),
            // config('app.self_signup_url'),
        ];

        $scriptDomains = array_merge($domains, [
            'https://cdn.jsdelivr.net',
            'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0-alpha.5/css/',
            'https://ajax.googleapis.com/ajax/libs/jquery/',
            'https://cdn.ckeditor.com',
            'https://js.stripe.com/v3/',
            'https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0-alpha.5/js/',
            'https://accounts.google.com',
            'https://ajax.googleapis.com/ajax/libs/jqueryui/',
            'https://cdn.datatables.net',
            'https://apis.google.com/js/',
            'https://checkout.stripe.com/',
            'https://cdn.jsdelivr.net/gh/TwilioDevEd/message-segment-calculator/docs/scripts/',
            'https://cdn.quilljs.com/1.3.5/quill.min.js',
            'https://ajax.googleapis.com/ajax/libs/jqueryui/1.13.2/jquery-ui.min.js',
            'https://www.google.com/recaptcha/api.js',
            'https://cdnjs.cloudflare.com',
            'https://maps.googleapis.com',
            'https://www.gstatic.com/recaptcha/releases/',
            'http://ajax.googleapis.com',
            'https://apis.google.com',
            'http://checkout.stripe.com',
            'https://www.google-analytics.com',
            'https://js.hsforms.net/forms/shell.js',
            'https://js.pusher.com/7.0.3/pusher.min.js',
            'https://www.paypalobjects.com/js/external/api.js',
            'http://cdn.quilljs.com',
        ]);

        if (str_contains($route, 'systemAdmin')) {
            $scriptDomains = array_merge($scriptDomains, [
                "'unsafe-eval'"
            ]);
        }

        $styleDomains = [
            "'unsafe-inline'",
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://ajax.googleapis.com',
            'https://cdn.quilljs.com',
            'https://cdn.jsdelivr.net',
            'https://code.jquery.com',
            'https://cdn.datatables.net',
            'https://cdnjs.cloudflare.com/',
            'https://maxcdn.bootstrapcdn.com',
            'https://accounts.google.com',
            'http://cdn.quilljs.com',
        ];

        $connectDomains = [
            'https://forms.hubspot.com',
            'https://maps.googleapis.com',
            'https://js.pusher.com',
            'https://accounts.google.com',
            'wss://ws-us2.pusher.com',
        ];

        $imgDomains = [
            Yii::app()->params['serverUrl'],
            'https://ajax.googleapis.com',
            'https://ci3.googleusercontent.com',
            'https://ci4.googleusercontent.com',
            'https://ci5.googleusercontent.com',
            'https://ci6.googleusercontent.com',
            'https://f.hubspotusercontent20.net',
            'https://maps.googleapis.com',
            'https://maps.gstatic.com',
            'https://q.stripe.com',
            'https://www.msmaster.qa.paypal.com',
        ];

        return [
            'domains' => $domains,
            'scriptSrc' => $scriptDomains,
            'styleSrc' => $styleDomains,
            'connectSrc' => $connectDomains,
            'imgSrc' => $imgDomains,
        ];
    }

    private function generateCspHeader($cspDomains, $route)
    {
        $domains = implode(' ', $cspDomains['domains']);
        $scriptSrcDomains = implode(' ', $cspDomains['scriptSrc']);
        $styleSrcDomains = implode(' ', $cspDomains['styleSrc']);
        $connectSrcDomains = implode(' ', $cspDomains['connectSrc']);
        $imgSrcDomains = implode(' ', $cspDomains['imgSrc']);

        $cspHeaders = [
            "default-src 'self';",
            "frame-ancestors 'self' $domains;",
            "frame-src 'self' https://js.stripe.com https://accounts.google.com https://www.google.com;",
            "object-src 'self';",
            "script-src 'self' $scriptSrcDomains 'unsafe-inline' 'unsafe-eval';",
            "connect-src 'self' $connectSrcDomains;",
            "style-src 'self' $styleSrcDomains;",
            "font-src 'self' data:;",
            "img-src 'self' data: $imgSrcDomains;",
        ];

        // check system admin business signup source
        $signupSource = Yii::app()->request->getParam('utm_source');
        $isAdminBusinessSignup = str_contains($route, 'merchant/signup') && $signupSource === 'system-admin';

        if (!str_contains($route, 'systemAdmin') && !$isAdminBusinessSignup) {
            $cspHeaders = array_merge($cspHeaders, [
                "require-trusted-types-for 'script'"
            ]);
        }

        return "Content-Security-Policy: " . implode(' ', $cspHeaders);
    }

    /**
     *
     */
    protected function authenticateViaBearerToken()
    {
        Yii::log(json_encode([
            'requestId' => Yii::app()->request->getRequestId(),
            'userAgent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ipAddress' => Yii::app()->request->userHostAddress,
            'hostname' => gethostname(),
            'message' => 'Authentication attempt',
        ]), CLogger::LEVEL_INFO,  __METHOD__);

        try {
            $providerToken = $this->validateAuthenticatedRequest();

            $identity = new UserBearerTokenIdentity($providerToken->user_fk);

            if (!$identity->authenticate()) {
                // $identity->errorMessage
                throw new CHttpException(401, 'Unauthenticated');
            }

            if (!Yii::app()->user->login($identity, $duration = 0)) {
                throw new CHttpException(401, 'Unauthenticated');
            }
            Yii::log(json_encode([
                'requestId' => Yii::app()->request->getRequestId(),
                'userAgent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'ipAddress' => Yii::app()->request->userHostAddress,
                'providerTokenId' => $providerToken->id,
                'userId' => $providerToken->user_fk,
                'userIdLoggedIn' => Yii::app()->user->getId(),
                'hostname' => gethostname(),
                'message' => 'User authenticated',
            ]), CLogger::LEVEL_INFO,  __METHOD__);
        } catch (Exception $e) {
            Yii::log($e . ' ' . json_encode([
                'requestId' => Yii::app()->request->getRequestId(),
                'userAgent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'ipAddress' => Yii::app()->request->userHostAddress,
                'hostname' => gethostname(),
                'message' => 'Unauthenticated',
            ]), CLogger::LEVEL_INFO,  __METHOD__);

            Utils::sendResponse(401, json_encode([
                'error' => 'Unauthenticated',
                'message' => $e->getMessage(),
            ]));
        }
    }

    /**
     *
     */
    protected function removeNbpAccessTokenCookie()
    {
        unset($_COOKIE['merchant_access_token']);
        $urlParts = parse_url(Yii::app()->params['serverUrl']);
        setcookie('merchant_access_token', '', -1, '/', $urlParts['host'], true, true);
    }

    /**
     *
     */
    public static function removeNbpCookie()
    {
        if (isset($_COOKIE['nbp_origin'])) {
            $_COOKIE['nbp_origin'] = false;
            $urlParts = parse_url(Yii::app()->params['serverUrl']);
            setcookie('nbp_origin', false, -1, '/', $urlParts['host'], true, true);
            // Yii::app()->session->add('user_verified', false);
        }
    }

    /**
     *
     */
    protected function setNbpCookie()
    {
        $urlParts = parse_url(Yii::app()->params['serverUrl']);
        $expiryDate = new DateTime('+1 month');
        setcookie('nbp_origin', true, $expiryDate->getTimeStamp(), '/', $urlParts['host'], true, true);
        $_COOKIE['nbp_origin'] = true;
        Yii::app()->session->add('user_verified', true);
    }

    /**
     *
     */
    protected function authenticateViaBearerTokenFromNBP($access_token)
    {
        try {
            $providerToken = $this->validateAuthenticatedRequestFromNBP($access_token);

            if (!$providerToken) {
                Yii::log('$providerToken is null. ' . $access_token, CLogger::LEVEL_ERROR, PlatformSolution::PLATFORM_NAME_WEBSITE . '_' . __METHOD__);
                Yii::app()->user->logout();
                $this->removeNbpCookie();
                $this->removeNbpAccessTokenCookie();
                return;
            }

            $userEntities = UserEntityPermission::getUserEntitiesByRole($providerToken->user_fk, __METHOD__, PlatformSolution::PLATFORM_NAME_WEBSITE);
            $businessModel = Business::model()->findByPk($userEntities['businessId']);
            if ($businessModel && !$businessModel->enabled) {
                Yii::log('Business is DISABLED. Entities: ' . CJSON::encode($userEntities), CLogger::LEVEL_ERROR, PlatformSolution::PLATFORM_NAME_WEBSITE . '_' . __METHOD__);
                Yii::app()->user->logout();
                self::removeNbpCookie();
                $this->removeNbpAccessTokenCookie();
                return;
            }

            $identity = new UserBearerTokenIdentity($providerToken->user_fk);

            if (!$identity->authenticate()) {
                self::removeNbpCookie();
                $this->removeNbpAccessTokenCookie();
                // throw new CHttpException(401, 'Unauthenticated from NBP');
            }

            if (!Yii::app()->user->login($identity, $duration = 0)) {
                self::removeNbpCookie();
                $this->removeNbpAccessTokenCookie();
                // throw new CHttpException(401, 'Unauthenticated from NBP');
            }

            // Set nbp_origin cookie if logged through NBP
            $this->setNbpCookie();

            Yii::log(json_encode([
                'requestId' => Yii::app()->request->getRequestId(),
                'userAgent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'ipAddress' => Yii::app()->request->userHostAddress,
                'providerTokenId' => $providerToken->id,
                'userId' => $providerToken->user_fk,
                'message' => 'User authenticated',
            ]), CLogger::LEVEL_INFO,  __METHOD__);
        } catch (Exception $e) {
            Yii::log(json_encode([
                'requestId' => Yii::app()->request->getRequestId(),
                'userAgent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'ipAddress' => Yii::app()->request->userHostAddress,
                'message' => 'Unauthenticated',
            ]) . ' ' . $e, CLogger::LEVEL_INFO,  __METHOD__);

            // Utils::sendResponse(401, json_encode([
            //     'error' => 'Unauthenticated',
            //     'message' => $e->getMessage(),
            // ]));
        }
    }

    /**
     * @return OauthProviderToken
     * @throws CHttpException
     */
    private function validateAuthenticatedRequest(): \OauthProviderToken
    {
        $headers = Utils::getallheaders();
        $jwt = trim((string)preg_replace('/^(?:\s+)?Bearer\s/', '', $headers['Authorization'] ?? ''));

        $providerToken = OauthProviderToken::model()->findByAttributes([
            'access_token' => $jwt,
        ]);

        if (!$providerToken) {
            throw new CHttpException(401, 'Unauthenticated');
        }

        if ($providerToken->expires < time()) {
            throw new CHttpException(401, 'The Access Token has expired');
        }

        return $providerToken;
    }


    /**
     * @return OauthProviderToken|null
     * @throws CHttpException
     */
    private function validateAuthenticatedRequestFromNBP($access_token)
    {
        $jwt = $access_token;

        $providerToken = OauthProviderToken::model()->findByAttributes([
            'access_token' => $jwt,
        ]);

        if (!$providerToken) {
            self::removeNbpCookie();
            $this->removeNbpAccessTokenCookie();
        }

        if ($providerToken && $providerToken->expires < time()) {
            self::removeNbpCookie();
            $this->removeNbpAccessTokenCookie();
        }

        return $providerToken;
    }

    public function afterAction($action)
    {
        if (in_array($action->controller->id . '/' . $action->id, [
            'site/ajaxCheckSession',
            'site/Logger',
            'merchant/healthCheck',
            'pos/lightSpeedEcom/initJS',
            'pos/lightSpeedEcom/initCSS',
        ], true)) {
            parent::afterAction($action);
            return;
        }

        // IMPORTANT: Do not make DB calls to keep the landing page detached from DB
        try {
            //            Yii::app()->getDb()
            if (!Yii::app()->db_audit) {
                parent::afterAction($action);
                return;
            }
            if (!Yii::app()->db) {
                parent::afterAction($action);
                return;
            }
        } catch (Exception $e) {
            return;
        }

        if (!Utils::feature('AUDIT_STORE_REQUESTS')) {
            parent::afterAction($action);
            return;
        }

        try {
            $module = isset($this->module) ? $this->module->getName() . '/' : '';

            $request = new Request;
            $request->created_at = date('Y-m-d H:i:s');
            $request->endpoint = $module . $action->controller->id . '/' . $action->id;
            $request->query_string = isset($_SERVER["QUERY_STRING"]) ? rawurlencode($_SERVER["QUERY_STRING"]) : null;
            $request->user_agent = (isset($_SERVER['HTTP_USER_AGENT'])) ? $_SERVER['HTTP_USER_AGENT'] : '';
            $request->ip = Yii::app()->request->userHostAddress;
            $request->accept_lang = isset($_SERVER['HTTP_ACCEPT_LANGUAGE']) ? $_SERVER['HTTP_ACCEPT_LANGUAGE'] : '';
            $request->app_lang = Yii::app()->language;
            $request->http_referer = isset($_SERVER['HTTP_REFERER']) ? rawurlencode($_SERVER['HTTP_REFERER']) : '';
            $request->memory = Yii::getLogger()->getMemoryUsage();
            $request->execution_time = Yii::getLogger()->getExecutionTime();
            $request->user_fk = Yii::app()->user->id;
            $request->session_id = session_id();
            $request->session = json_encode($_SESSION);
            if (!$request->save(false)) {
                Yii::log(json_encode($request->getErrors()), CLogger::LEVEL_ERROR,  __METHOD__);
            }
            parent::afterAction($action);
        } catch (Exception $e) {
            Yii::log($e, CLogger::LEVEL_ERROR,  __METHOD__);
        }
    }

    public function init()
    {
        parent::init();

        if (!empty($_GET['lang'])) {
            Yii::app()->session->add('language', $_GET['lang']);
        }

        if (null == Yii::app()->session->get('language')) {
            Yii::app()->session->add('language', 'en');
        }

        // if (null != Yii::app()->session->get('loginas_tenant')) {
        //     // dd(Yii::app()->session->get('loginas_tenant'));
        //     $businessId = Yii::app()->session->get('loginas_tenant');
        //     $tenant = Tenant::model()->findByPk($businessId);
        //     if ($tenant) {
        //         $this->connectToTenantDB($tenant);
        //     }
        // }

        Yii::app()->language = Yii::app()->session->get('language');
    }

    protected function getUserEntities()
    {
        $userId = Yii::app()->user->id;
        if ($userId) {
            $userEntities = UserEntityPermission::getUserEntitiesByRole($userId, __METHOD__, PlatformSolution::PLATFORM_NAME_WEBSITE);
            if (!$userEntities) {
                throw new CHttpException(403, Yii::t('backend', 'EX_ACCESS_DENIED'));
            }
            return $userEntities;
        }
    }

    public function logProfiling()
    {
        Yii::log(json_encode([
            'businessId' => $this->userEntities['businessId'],
            'profiling' => $this->profilingMessages,
        ]), CLogger::LEVEL_INFO, __METHOD__);
    }

    public function addProfilingRecord($key, $time)
    {
        $this->profilingMessages[] = [
            $key => (microtime(true) - $time),
        ];
    }

    /**
     * Create tenant Database
     *
     * Used in : Website()
     *
     * @version Octomber 2017 release V4.8.6
     * @since 4.8.6
     * <AUTHOR>
     * @access public
     * @return void
     */
    private function connectToTenantDB($tenant)
    {
        $connectionString = 'mysql:host=' . $tenant->db_host . ';dbname=' . $tenant->db_name;
        Yii::app()->db->setActive(false);
        Yii::app()->db->connectionString = $connectionString;
        Yii::app()->db->setActive(true);
    }

    /**
     * Returns the user id for Access Token from cookie
     *
     * Used in : Website()
     *
     * @version November 2017 release V4.9.1
     * @since 4.9.1
     * <AUTHOR>
     * @access public
     * @param $platformId
     * @param string $redirectTo
     * @return integer
     */
    protected function getUserIdForAccessToken($platformId, $redirectTo = '')
    {
        if (!$platformId) {
            $platformId = 1;
        }

        $platform = PlatformSolution::model()->findByPk($platformId);
        $platformName = $platform->name;
        $ip = Yii::app()->request->userHostAddress;

        try {
            $trace = debug_backtrace();
            $calledFrom1 = isset($trace[1]['function']) ? $trace[1]['function'] : '';
            $calledFrom2 = isset($trace[2]['function']) ? $trace[2]['function'] : '';
            $calledFrom = $calledFrom1 . '->' . $calledFrom2;

            if (!isset($_COOKIE['merchant_access_token'])) {
                Yii::log('Access Token not found in cookie IP:' . $ip . ' QUERY_STRING:' . $_SERVER["QUERY_STRING"] . "\ntrace=" . Utils::getTraces(), CLogger::LEVEL_WARNING, $platformName . '_' . __METHOD__);
                $this->redirect('/merchant/login' . $redirectTo);
            }

            // Get access token from cookie
            $accessToken = $_COOKIE['merchant_access_token'] ?? '';

            // hash the access token
            // Using hash because it's shorter and can be indexed in DB
            $tokenHash = hash('sha512', $accessToken);

            // Find the token record by hash
            $providerToken = OauthProviderToken::model()->findByAttributes([
                'provider_id' => OAUTH_PROVIDER_KANGAROO_API,
                'token_hash' => $tokenHash,
            ]);

            if (!$providerToken) {
                Yii::log('Access Token not found in DB IP:' . $ip . ' accessToken=' . $accessToken, CLogger::LEVEL_WARNING, $platformName . '_' . __METHOD__);
                Yii::app()->user->setFlash('error', Yii::t('backend', 'BACKEND_AUTH_JWT_EXPIRED'));
                $this->redirect('/merchant/login' . $redirectTo);
            }

            // Rebuild token object
            $token = new \League\OAuth2\Client\Token\AccessToken([
                'access_token' => $providerToken->access_token,
                'refresh_token' => $providerToken->refresh_token,
                'expires' => $providerToken->expires,
            ]);

            // dd($token);

            // Kangaroo Provider
            $kangarooApiProvider = new \KangarooRewards\OAuth2\Client\Provider\Kangaroo([
                'clientId' => Yii::app()->params['kangarooApiClientId'],
                'clientSecret' => Yii::app()->params['kangarooApiClientSecret'],
                'urlAccessToken' => Yii::app()->params['kangarooApiUri'] . '/oauth/token',
                'urlResourceOwnerDetails' => Yii::app()->params['kangarooApiUri'] . '/me',
                'redirectUri' => null,
            ]);

            //check if the token has expired refresh it and save in database
            if ($token->hasExpired()) {
                Yii::log('Before refresh token. UserId=' . $providerToken->user_fk
                    . ' expires=' . $token->getExpires() . ' refresh_token=' . $token->getRefreshToken()
                    . ' IP:' . $ip, CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);

                $newToken = $kangarooApiProvider->getAccessToken('refresh_token', [
                    'refresh_token' => $token->getRefreshToken(),
                ]);

                Yii::log('Token expired. Got NEW TOKEN for UserId=' . $providerToken->user_fk
                    . ' expires=' . $newToken->getExpires() . ' IP:' . $ip, CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);

                // Store the newToken in DB
                $newProviderToken = new OauthProviderToken;
                $newProviderToken->provider_id = OAUTH_PROVIDER_KANGAROO_API;
                $newProviderToken->token_hash = hash('sha512', $newToken->getToken());
                $newProviderToken->access_token = $newToken->getToken();
                $newProviderToken->expires = $newToken->getExpires();
                $newProviderToken->refresh_token = $newToken->getRefreshToken();
                $newProviderToken->user_fk = $providerToken->user_fk;
                $newProviderToken->save();

                // Store the new Token in cookie
                $urlParts = parse_url(Yii::app()->params['serverUrl']);
                $expiryDate = new DateTime('+1 month');
                setcookie('merchant_access_token', $newToken->getToken(), $expiryDate->getTimeStamp(), '/', $urlParts['host'], true, true);
                // Set new access token in COOKIE array also because getUserIdForAccessToken
                // is being called multiple times in the same request
                // 1 - beforeAction; 2 - DefaultController; 3 - default\index
                $_COOKIE['merchant_access_token'] = $newToken->getToken();
            }

            Yii::log('User ID retrieved for AccessToken ID=' . $providerToken->id
                . ' UserId=' . $providerToken->user_fk . ' calledFrom=' . $calledFrom
                . ' IP:' . $ip, CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);
        } catch (\Exception $e) {
            Yii::log('Error while retrieving user for Access Token UserId=' . $providerToken->user_fk
                . ' calledFrom=' . $calledFrom . ' IP:' . $ip
                . ' Error=' . $e . "\n"
                . ' providerTokenID=' . $providerToken->id
                . ' tokenHash=' . $tokenHash
                . ' accessToken=' . $accessToken, CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);

            Yii::app()->user->setFlash('error', Yii::t('backend', 'BACKEND_AUTH_JWT_EXPIRED'));
            $this->redirect('/merchant/login' . $redirectTo);
        }

        return $providerToken->user_fk;
    }

    public function render($view, $data = null, $return = false)
    {
        if (Utils::isApi()) {
            //        if (isset($_GET['api']) && (int) $_GET['api'] === 1) {
            // json is output things like "key":, which causes a parsing error
            // fix that by converting bin to hex when string is bin
            $data = $this->jsonPrep($data);
            //            print json_encode($data);
            Utils::sendResponse(200, CJSON::encode($data));
            Yii::app()->end();
        }

        return parent::render($view, $data, $return);
    }

    // convert object into json ready format
    private function jsonPrep($data)
    {
        if (is_object($data)) {
            $new = (object) array();
            foreach ($data as $key => $value) {
                if (is_string($value) && preg_match("/_uid$/", $key)) {
                    $value = bin2hex($value);
                }
                $new->{$key} = $value;
            }
            return $new;
        } else {
            foreach ($data as $key => &$value) {
                if (is_object($value) || is_array($value)) {
                    $value = $this->jsonPrep($value);
                    if (is_array($value) && isset($value['branch_uid'])) {
                        $value['branch_uid'] = bin2hex($value['branch_uid']);
                    }
                }
            }
            return $data;
        }
    }

    public function renderPartial($view, $data = null, $return = false, $processOutput = false)
    {
        if (Utils::isApi()) {
            // Except. Do not return json for this method
            if (strpos($view ?? '', 'marketingMultiOfferEmail') !== false) {
                return parent::renderPartial($view, $data, $return, $processOutput);
            }
            $data = $this->jsonPrep($data);
            print json_encode($data);
            //Utils::sendResponse(200, CJSON::encode($data));
            Yii::app()->end();
        }

        return parent::renderPartial($view, $data, $return, $processOutput);
    }
}
