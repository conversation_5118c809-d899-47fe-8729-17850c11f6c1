<?php

namespace App\Traits;

use App\Models\MerchantProfile;

trait MerchantProfileTrait
{
    /**
     * List the app modules that will be used in the BP mobile app
     * @since 20/02/2024
     * <AUTHOR>
     * @return array[]
     */
    public function getAppModulesList(): array
    {
        return [
            [
                'id' => 1,
                'name' => 'Offers',
                'pinned' => true
            ],
            [
                'id' => 2,
                'name' => 'Rewards',
                'pinned' => true
            ],
            [
                'id' => 3,
                'name' => 'Marketing',
                'pinned' => false
            ],
            [
                'id' => 4,
                'name' => 'Settings',
                'pinned' => false
            ],
            [
                'id' => 5,
                'name' => 'Calendar',
                'pinned' => false
            ],
            [
                'id' => 6,
                'name' => 'Customers',
                'pinned' => false
            ]
        ];
    }

    /**
     * Return module IDs
     * @return array
     */
    public function getModuleIds(): array
    {
        return collect($this->getAppModulesList())->pluck('id')->toArray();
    }

    /**
     * Get user favorite modules
     * @param $userId
     * @return mixed
     */
    public function getUserFavoriteModules($userId): mixed
    {
        $profile = MerchantProfile::select('favorite_modules')
            ->where('user_fk', $userId)
            ->first();

        if (!empty($profile->favorite_modules)) {
            $userModuleList = json_decode($profile->favorite_modules, true);
            $defaultModuleList = $this->getAppModulesList();
            // Get only the keys that are missing in $array1
            $missingModuleList = array_diff_key($defaultModuleList, $userModuleList);

            // Merge missing values into $array1
            $finalModuleList = array_merge($userModuleList, $missingModuleList);

            return $finalModuleList;
        }

        return $this->getAppModulesList();
    }

    /**
     * Save user favorite modules with pin status
     * @param $userId
     * @param $userArr
     * @return array
     */
    public function saveUserFavoriteModules($userId, $userArr): array
    {
        // Create an associative array from the user selection modules list for easy lookup by id
        $userArr = array_column($userArr, null, 'id');

        // Prepare the user favorite data
        $data = [];
        foreach ($this->getAppModulesList() as $module) {
            // Handle the pinned status
            $module['pinned'] = $userArr[$module['id']]['pinned'] ?? false;

            // Set the module element to data array
            $data[$module['id']] = $module;
        }

        // Remove the keys from array
        $data = array_values($data);

        // Save favorite modules
        $merchantProfile = MerchantProfile::where('user_fk', $userId)->first();

        if (empty($merchantProfile)) {
            $merchantProfile = new MerchantProfile();
            $merchantProfile->user_fk = $userId;
        }

        $merchantProfile->favorite_modules = json_encode($data);
        $merchantProfile->save();

        return $data;
    }
}
