<?php

namespace App\Jobs;


use App\Http\Resources\ExportTransactionResource;
use App\Models\V1\BusinessRules;
use App\Models\V1\PosBranch;
use App\Services\FileDownloader;
use App\Traits\BusinessMenuTrait;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\V1\Business;
use App\Models\V1\BusinessBranch;
use KangarooRewards\Common\Traits\BusinessBranchTrait;

class ExportTransactions extends Job
{
    use BusinessBranchTrait;

    public int $timeout = 3600; // 1 hour
    public int $chunkSize = 3000;

    private $businessId;

    private $conditions;

    private $emailTo;

    private $rule;

    private $transactionDetailsConfig;

    private $exportExternalOrderId;

    private $includeALaCarteItems;

    public function __construct($businessId, $conditions, $emailTo = null)
    {
        $this->businessId = $businessId;
        $this->emailTo = $emailTo;
        $this->conditions = $conditions;
    }

    private function getBusinessRules()
    {
        if (!$this->rule) {
            $this->rule = BusinessRules::select([
                'batch_import_settings',
                'crm_flag',
                'receipt_flag',
                'transaction_custom_fields',
                'allow_account_number_for_login_and_rewarding'
            ])
                ->where('business_fk', $this->businessId)
                ->firstOrFail();
        }

        return $this->rule;
    }

    private function getInclude()
    {
        if (!empty($this->conditions['include'])) {
            return explode(',', $this->conditions['include']);
        }
        return [];
    }

    private function getTransactionDetailsConfig()
    {
        if (!$this->transactionDetailsConfig) {
            $rules = $this->getBusinessRules();
            $this->transactionDetailsConfig = [];
            if (!empty($rules->transaction_custom_fields)) {
                $customFields = json_decode($rules->transaction_custom_fields, true);
                foreach ($customFields as $customField) {
                    if (isset($customField['isHidden']) && intval($customField['isHidden']) === 0) {
                        $this->transactionDetailsConfig[] = $customField;
                    }
                }
            }
        }

        return $this->transactionDetailsConfig;
    }

    private function includeALaCarteItems()
    {
        if (!isset($this->includeALaCarteItems)) {
            $this->includeALaCarteItems = BusinessMenuTrait::isMenuEnabled($this->businessId, 5);
        }

        return $this->includeALaCarteItems;
    }

    /**
     * @return void
     */
    public function handle(): void
    {
        Log::info('ExportTransactions started', [
            'business_id' => $this->businessId,
            'conditions' => $this->conditions,
            'admin_email' => $this->emailTo
        ]);

        try {
            $this->rule = $this->getBusinessRules();

            if ($this->conditions['list_type'] == 'punch_trx') {
                $tableHeaders = $this->getExportPunchHeaders();
            } else {
                $tableHeaders = $this->getExportPointsHeaders();
            }

            $headers = array_column($tableHeaders, 'header');
            $outputFields = array_column($tableHeaders, 'output_field');

            ExportTransactionResource::setExtraData([
                'includeALaCarteItems' => $this->includeALaCarteItems()
            ]);

            // Get base query to check for records are not empty
            $query = $this->getExportDataQuery();
            $hasRecords = $query->exists();
            if (!$hasRecords) {
                // No records found - log and exit
                Log::warning("Export process found no records matching criteria", [
                    'businessId' => $this->businessId,
                    'conditions' => $this->conditions
                ]);

                return;
            }

            // Set the date range fields and initialize file downloader
            $d1 = new \DateTime($this->conditions['first_date']);
            $d2 = new \DateTime($this->conditions['last_date']);
            $fileName = 'Transactions-' . $d1->format('Y-m-d') . '-' . $d2->format('Y-m-d');
            $fileDownloader = new FileDownloader($this->businessId, $fileName, null, $outputFields, $headers);

            // Create the CSV file and prepare it for streaming
            $fileDownloader->createEmptyCsvFile($headers);

            // Process in chunks
            $this->processDataInChunks($fileDownloader, $outputFields);

            // Finalize the file
            $fileDownloader->finalizeCsvFile();

            // Send the CSV to the admin email
            if (isset($this->emailTo)) {
                $fileDownloader->sendByEmail($this->emailTo);
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__, [
                'e' => $e,
                'businessId' => $this->businessId,
                'conditions' => $this->conditions
            ]);
        }
    }

    private function getExportPunchHeaders()
    {
        $tableHeader = [];

        $tableHeader[] = [
            'header' => __('api.EXPORT_ID'),
            'output_field' => 'transaction_pk',
        ];

        $tableHeader[] = [
            'header' => __('api.EXPORT_GENERIC_DATE_TIME'),
            'output_field' => 'date_created',
        ];

        $tableHeader[] = [
            'header' => __('api.EXPORT_GENERIC_TRANSACTION'),
            'output_field' => 'trx_subtype_name',
        ];

        if (in_array($this->conditions['type'], [1, 4, 5, 6, 7, 8, 9, 10])) {// reward
            $tableHeader[] = [
                'header' => __('api.EXPORT_TRX_LIST_HEADER_REWARDED_PUNCHES'),
                'output_field' => 'units_exchange',
            ];
        } elseif ($this->conditions['type'] == 2) { //redemption
            $tableHeader[] = [
                'header' => __('api.EXPORT_TRX_LIST_HEADER_REDEEMED_PUNCHES'),
                'output_field' => 'units_exchange',
            ];
        } else {
            $tableHeader[] = [
                'header' => ucfirst(__('api.EXPORT_PUNCHES')),
                'output_field' => 'units_exchange',
            ];
        }

        $tableHeader[] = [
            'header' => __('api.EXPORT_GENERIC_USERNAME'),
            'output_field' => 'username',
        ];
        $tableHeader[] = [
            'header' => __('api.EXPORT_TRX_LIST_HEADER_USER_BALANCE'),
            'output_field' => 'user_balance',
        ];
        $tableHeader[] = [
            'header' => __('api.EXPORT_GENERIC_BUSINESS_NAME'),
            'output_field' => 'business_name',
        ];
        $tableHeader[] = [
            'header' => __('api.EXPORT_GENERIC_CLERK_NAME'),
            'output_field' => 'clerk_username',
        ];

        $importTransactionSettings = isset($this->rule->batch_import_settings) ? json_decode($this->rule->batch_import_settings, true) : [];
        foreach ($importTransactionSettings as $setting) {
            if (!($setting['isHidden'])) {
                if ($setting['field'] == 'card_number') {
                    $field = 'card_number';
                } elseif ($setting['field'] == 'language_fk') {
                    $field = 'lang';
                } else {
                    $field = $setting['field'];
                }

                $tableHeader[] = [
                    'header' => $setting['text'],
                    'output_field' => $field,
                ];
            }
        }

        if ($this->includeALaCarteItems()) {
            $tableHeader[] = [
                'header' => __('api.EXPORT_A_LA_CARTE'),
                'output_field' => 'products',
            ];
        }

        return $tableHeader;
    }

    /**
     * @return array
     */
    private function getExportPointsHeaders(): array
    {
        $tableHeader = [];

        $tableHeader[] = [
            'header' => __('api.EXPORT_ID'),
            'output_field' => 'transaction_pk',
        ];

        $tableHeader[] = [
            'header' => __('api.EXPORT_GENERIC_DATE_TIME'),
            'output_field' => 'date_created',
        ];

        $tableHeader[] = [
            'header' => __('api.EXPORT_GENERIC_TRANSACTION'),
            'output_field' => 'trx_subtype_name',
        ];

        if (in_array($this->conditions['type'], [1, 4, 5, 6, 7, 8, 9, 10])) { //reward
            $tableHeader[] = [
                'header' => __('api.EXPORT_TRX_LIST_HEADER_REWARDED_POINTS'),
                'output_field' => 'units_exchange',
            ];
        } else if ($this->conditions['type'] == 2) { //'redemption'
            $tableHeader[] = [
                'header' => __('api.EXPORT_TRX_LIST_HEADER_REDEEMED_POINTS'),
                'output_field' => 'units_exchange',
            ];
//        } else if ($this->conditions['type'] == 'all_trx' || $this->conditions['type'] == 'refund') {
        } else if (in_array($this->conditions['type'], [0, 3])) {
            $tableHeader[] = [
                'header' => __('api.BATCH_EXPORT_POINTS'),
                'output_field' => 'units_exchange',
            ];
        }

        $tableHeader[] = [
            'header' => __('api.EXPORT_GENERIC_AMOUNT'),
            'output_field' => 'amount',
        ];

        $transactionDetailFields = $this->getTransactionDetailsConfig();
        foreach ($transactionDetailFields as $transactionCustomField) {
            $tableHeader[] = [
                'header' => $transactionCustomField['text'],
                'output_field' => 'trx_' . $transactionCustomField['field']
            ];
        }

        $tableHeader[] = [
            'header' => __('api.EXPORT_GENERIC_USERNAME'),
            'output_field' => 'username',
        ];

        if ($this->rule->crm_flag) {
            $tableHeader[] = [
                'header' => __('api.BATCH_EXPORT_EMAIL'),
                'output_field' => 'email',
            ];
            $tableHeader[] = [
                'header' => __('api.BATCH_EXPORT_PHONE'),
                'output_field' => 'phone',
            ];

            $rules = $this->getBusinessRules();
            if (isset($rules) && (int)$rules->allow_account_number_for_login_and_rewarding === 1) {
                $tableHeader [] = [
                    'header' => __('api.ACCOUNT_NUMBER'),
                    'output_field' => 'account_number',
                ];
            }
        }

        $tableHeader[] = [
            'header' => __('api.EXPORT_TRX_LIST_HEADER_USER_BALANCE'),
            'output_field' => 'user_balance',
        ];

        $tableHeader[] = [
            'header' => __('api.EXPORT_TRX_LIST_HEADER_GIFTCARD_BALANCE'),
            'output_field' => 'giftcard_balance',
        ];

        $tableHeader[] = [
            'header' => __('api.EXPORT_GENERIC_BUSINESS_NAME'),
            'output_field' => 'business_name',
        ];

        $tableHeader[] = [
            'header' => __('api.EXPORT_GENERIC_CLERK_NAME'),
            'output_field' => 'clerk_username',
        ];

        if ((in_array($this->conditions['type'], [0, 1, 4, 5, 6, 7, 8, 9, 10])) &&
            $this->rule->receipt_flag) { //all_trx,reward
            $tableHeader[] = [
                'header' => __('api.EXPORT_GENERIC_RECEIPT_NUMBER'),
                'output_field' => 'receipt_number',
            ];
        }

        $include = $this->getInclude();
        if (in_array('coupon', $include)) {
            $tableHeader[] = [
                'header' => __('api.EXPORT_GENERIC_COUPON'),
                'output_field' => 'coupon_code',
            ];
        }

        $importTransactionSettings = isset($this->rule->batch_import_settings) ? json_decode($this->rule->batch_import_settings, true) : [];
        foreach ($importTransactionSettings as $setting) {
            if (!($setting['isHidden'])) {
                if ($setting['field'] == 'card_number') {
                    $field = 'card_number';
                } elseif ($setting['field'] == 'language_fk') {
                    $field = 'lang';
                } else {
                    $field = $setting['field'];
                }

                $tableHeader[] = [
                    'header' => $setting['text'],
                    'output_field' => $field,
                ];
            }
        }

        if ($this->exportExternalOrderId()) {
            $tableHeader[] = [
                'header' => __('api.EXTERNAL_ORDER_ID'),
                'output_field' => 'external_order_id',
            ];
        }

        if ($this->includeALaCarteItems()) {
            $tableHeader[] = [
                'header' => __('api.EXPORT_A_LA_CARTE'),
                'output_field' => 'products',
            ];
        }
        return $tableHeader;
    }

    private function exportExternalOrderId():bool
    {
        if (!isset($this->exportExternalOrderId)) {
            $posBranch = PosBranch::where([
                'business_fk' => $this->businessId,
                'pos_system_fk' => POS_SYSTEM_SHOPIFY,
                'enabled' => 1
            ])->first();

            $this->exportExternalOrderId = isset($posBranch);
        }

        return $this->exportExternalOrderId;
    }

    /**
     * @return Builder
     */
    private function getExportDataQuery(): Builder
    {
        $mainBranchId = $this->getDefaultBranchId($this->businessId);

        $businessModel = Business::findOrFail($this->businessId);
        $entityTypeFk = $businessModel->entity_type_fk;
        $mainBranchTmz = $this->getBranchTimezoneName($mainBranchId);
        $branchEntityTypeFk = BusinessBranch::getBranchEntityTypeByBusinessEntityType($entityTypeFk);
        $transactionCustomFields = $this->getTransactionDetailsConfig();

        // Set the attributes to select
        $attributes = array(
            'trx.transaction_pk',
            DB::raw("CONVERT_TZ(trx.utc_created, @@global.time_zone, '{$mainBranchTmz}') as date_created"),
            'trxst.name as trx_subtype_name',
            DB::raw('coalesce(a.units_exchange, 0) as units_exchange'),
            'a.amount as activity_amount',
            DB::raw('IFNULL(p.amount,"") as amount'),
            DB::raw('IFNULL(u.username,"") as username'),
            DB::raw('IFNULL(u.first,"") as first'),
            DB::raw('IFNULL(u.last,"") as last'),
            DB::raw('IFNULL(u.email,"") as email'),
            'u.country_phone_fk',
            DB::raw('IFNULL(u.phone,"") as phone'),
            DB::raw('(uep.current_punches + uep.current_active_points) as user_balance'),
            'uep.giftcard as giftcard_balance',
            'bb.name as business_name',
            DB::raw('( SELECT username FROM user as uu WHERE uu.id = p.user_clerk_fk ) AS clerk_username'),
            'trx.user_fk',
            'trx.transaction_sub_type_fk',
            'bb.logo_image_thumbnail as business_logo_thumbnail',
            'a.punch_offer_fk',
            'p.purchase_pk',
            'p.user_clerk_fk',
        );

        if ($this->rule->crm_flag) {
            $attributes[] = 'u.country_phone_fk';

            if ((int)$this->rule->allow_account_number_for_login_and_rewarding === 1) {
                $attributes[] = 'ubp.account_number';
            }
        }

        if ($this->rule->receipt_flag) {
            $attributes[] = 'r.number as receipt_number';
        }

        if (count($transactionCustomFields) > 0 || $this->exportExternalOrderId()) {
            $attributes[] = 'transaction_details.external_order_id as external_order_id';
            $attributes[] = 'transaction_details.custom_field_1 as trx_custom_field_1';
            $attributes[] = 'transaction_details.custom_field_2 as trx_custom_field_2';
            $attributes[] = 'transaction_details.custom_field_3 as trx_custom_field_3';
            $attributes[] = 'transaction_details.custom_field_4 as trx_custom_field_4';
            $attributes[] = 'transaction_details.custom_field_5 as trx_custom_field_5';
        }

        $joinBatchImport = false;
        $joinLanguage = false;

        $importTransactionSettings = isset($this->rule->batch_import_settings) ? json_decode($this->rule->batch_import_settings, true) : [];
        foreach ($importTransactionSettings as $setting) {
            if (!($setting['isHidden'])) {
                if ($setting['field'] == 'card_number') {
                    $attributes[] = DB::raw('CONCAT("****", SUBSTRING(it.card_number, -6)) as card_number');
                } elseif ($setting['field'] == 'language_fk') {
                    $attributes[] = 'lang.abreviation as lang';
                    $joinLanguage = true;
                } else {
                    $attributes[] = 'it.' . $setting['field'];
                }
                $joinBatchImport = true;
            }
        }

        $businessId = $this->businessId;

        $query = DB::table('transaction as trx')
            ->join('business_branch as bb', 'bb.business_branch_pk', '=', 'trx.entity_fk')
            ->join('user as u', function ($join) use ($businessId) {
                $join->on('u.id', '=', 'trx.user_fk')
                    ->where('u.business_fk', $businessId);
            })
            ->join('activity as a', 'a.transaction_fk', '=', 'trx.transaction_pk')
            ->leftJoin('purchases as p', 'p.transaction_fk', '=', 'trx.transaction_pk')
            ->leftJoin('receipts as r', 'p.receipt_fk', '=', 'r.receipt_pk')
            ->leftJoin('user_entity_points as uep', function ($join) {
                $join->on('trx.user_fk', '=', 'uep.user_fk')
                    ->on('bb.business_fk', '=', 'uep.entity_fk');
            })
            ->leftJoin('transaction_sub_type as trxst', 'trxst.transaction_sub_type_pk', '=', 'trx.transaction_sub_type_fk')
            ->where('trx.entity_type_fk', $branchEntityTypeFk)
            ->whereRaw('(CONVERT_TZ(trx.utc_created, @@global.time_zone, ?) BETWEEN ? AND ?)',
                [$mainBranchTmz, $this->conditions['first_date'], $this->conditions['last_date']])
            ->whereIn('trx.entity_fk', $this->conditions['branches'])
            ->whereIn('trx.transaction_type_fk', $this->conditions['transaction_types'])
            ->whereIn('trx.transaction_sub_type_fk', $this->conditions['transaction_sub_types'])
            ->groupBy('trx.transaction_pk')
            ->orderBy('trx.transaction_pk', 'DESC');
        if ($joinBatchImport) {
            $query->leftJoin('import_transactions as it', 'it.transaction_fk', '=', 'trx.transaction_pk');
            if ($joinLanguage) {
                $query->leftJoin('language as lang', 'lang.language_pk', '=', 'it.language_fk');
            }
        }

        if (count($transactionCustomFields) > 0 || $this->exportExternalOrderId()) {
            $query->leftJoin('transaction_details', 'transaction_details.transaction_fk', '=', 'trx.transaction_pk');
        }

        $include = $this->getInclude();
        if (in_array('coupon', $include)) {
            $attributes[] = 'coupons.code as coupon_code';
            $query->leftJoin('coupons', 'coupons.transaction_fk', '=','trx.transaction_pk');
        }

        if($this->conditions['type'] == 2) {
            $query->join('user_product_reward', 'user_product_reward.purchase_fk', '=','p.purchase_pk');
        }

        if ((int)$this->rule->allow_account_number_for_login_and_rewarding === 1) {
            $query->leftJoin('user_business_profile as ubp', function ($join) use ($businessId) {
                $join->on('ubp.user_fk', '=', 'u.id')
                    ->where('ubp.business_fk', $businessId);
            });
        }

        return $query->select($attributes);
    }

    /**
     * Process data in chunks to avoid memory issues
     * @param FileDownloader $fileDownloader
     * @param array $outputFields
     * @return void
     */
    private function processDataInChunks(FileDownloader $fileDownloader, array $outputFields): void
    {
        // Get base query
        $query = $this->getExportDataQuery();

        // Get total count for progress tracking
        $processedCount = 0;

        // Use cursor for efficient iteration
        $query->orderBy('trx.transaction_pk')->chunk($this->chunkSize, function ($records) use ($fileDownloader, $outputFields, &$processedCount) {
            // Transform the current chunk
            $transformedData = ExportTransactionResource::collection($records);

            // Append to CSV
            $fileDownloader->appendToCsvFile($transformedData, $outputFields);

            // Update progress
            $processedCount += count($records);
//            Log::info("Export transaction progress", [
//                'businessId' => $this->businessId, 'records_processed_nb' => $processedCount
//            ]);

            // Free up memory
            unset($records);
            unset($transformedData);
            gc_collect_cycles();
        });
    }
}