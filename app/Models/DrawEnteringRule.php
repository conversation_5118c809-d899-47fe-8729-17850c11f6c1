<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\DrawEnteringRule
 *
 * @property int $draw_entering_rules_pk
 * @property string|null $draw_entering_rules_title
 * @property string|null $draw_entering_rules_description
 * @property int $enabled
 * @method static \Illuminate\Database\Eloquent\Builder|DrawEnteringRule newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DrawEnteringRule newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DrawEnteringRule query()
 * @mixin \Eloquent
 */
class DrawEnteringRule extends Model
{
    protected $table = 'draw_entering_rules';
    protected $primaryKey = 'draw_entering_rules_pk';
    protected $connection = 'tenant';

    const CREATED_AT = null;
    const UPDATED_AT = null;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [

    ];
}
