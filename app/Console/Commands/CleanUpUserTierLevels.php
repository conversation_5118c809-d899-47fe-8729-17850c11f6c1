<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CleanUpUserTierLevels extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'kangaroo:cleanup-user-tier-levels
                            {--chunk=100 : The number of users to process in each batch per business}
                            {--keep=3 : The number of latest records to keep per user}                           
                            {--dry-run : Simulate the pruning process without deleting any records}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'For enabled businesses, remove old user_tier_levels records, keeping N latest per user.';

    /**
     * The user tiers table name.
     *
     * @var string
     */
    protected string $userTiersTable = 'user_tier_levels';

    /**
     * The business tiers setup table name.
     *
     * @var string
     */
    protected string $businessSetupTable = 'business_tiers_setup';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $userChunkSize = (int) $this->option('chunk');
        $recordsToKeep = (int) $this->option('keep');
        $isDryRun = $this->option('dry-run');
        $orderByColumn = 'user_tier_level_pk';
        $orderByDir = 'DESC';

        if ($isDryRun) {
            $this->warn('*****************************');
            $this->warn('*** DRY RUN MODE ENABLED - No records will be deleted.  ***');
            $this->warn('*****************************');
            $this->line('');
        }

        if ($userChunkSize <= 0 || $recordsToKeep <= 0) {
            $this->error('Chunk size and records to keep must be positive integers.');
            return Command::FAILURE;
        }

        $this->info("Starting pruning process for '{$this->userTiersTable}' based on enabled businesses");
        $this->line("Keeping the latest {$recordsToKeep} records per user per business");
        $this->line("Processing users in chunks of {$userChunkSize} per business.");

        $businessIds = $this->getEnabledBusinessIds();
        if (empty($businessIds)) {
            $this->info("No enabled businesses found in '{$this->businessSetupTable}'. Nothing to prune.");
            return Command::SUCCESS;
        }

        $totalBusinesses = count($businessIds);
        $this->info("Found {$totalBusinesses} enabled businesses to process.");

        $globalTotalDeletedCount = 0;
        $processedBusinessCount = 0;

        $businessProgressBar = $this->output->createProgressBar($totalBusinesses);
        $businessProgressBar->start();

        // Iterate through each enabled business
        foreach ($businessIds as $businessId) {
            $result = $this->processBusiness(
                businessId: $businessId,
                userChunkSize: $userChunkSize,
                recordsToKeep: $recordsToKeep,
                orderByColumn: $orderByColumn,
                orderByDir: $orderByDir,
                isDryRun: $isDryRun
            );
            $globalTotalDeletedCount += $result['deletedCount'];
            $processedBusinessCount++;
            $businessProgressBar->advance();
        }

        $businessProgressBar->finish();

        $finalActionVerb = $isDryRun ? 'would be' : 'were';
        $this->line('');
        $this->info("--- Pruning process completed for all {$processedBusinessCount} enabled businesses. ---");
        if ($isDryRun) {
            $this->info("*** (DRY RUN) Total records that {$finalActionVerb} deleted across all processed businesses:
             {$globalTotalDeletedCount} ***");
        } else {
            $this->info("Total records {$finalActionVerb} deleted across all processed businesses: 
            {$globalTotalDeletedCount}");
        }

        return Command::SUCCESS;
    }

    /**
     * Fetches enabled business FKs from the business setup table.
     *
     * @return array
     */
    protected function getEnabledBusinessIds(): array
    {
        $this->line("Fetching enabled businesses from '{$this->businessSetupTable}'...");
        try {
            return DB::table($this->businessSetupTable)
                ->where('enabled', 1)
                ->orderBy('business_fk')
                ->pluck('business_fk')
                ->all();
        } catch (\Exception $e) {
            $this->error("Error fetching enabled businesses from '{$this->businessSetupTable}': " . $e->getMessage());
            Log::error("PruneUserTiersCommand: Failed to fetch businesses. Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Processes a single business, pruning user tier levels.
     *
     * @param int $businessId
     * @param int $userChunkSize
     * @param int $recordsToKeep
     * @param string $orderByColumn
     * @param string $orderByDir
     * @param bool $isDryRun
     * @return array
     */
    protected function processBusiness(
        int $businessId,
        int $userChunkSize,
        int $recordsToKeep,
        string $orderByColumn,
        string $orderByDir,
        bool $isDryRun
    ): array {
        $this->line("");
        $this->info("--- Processing Business FK: {$businessId} ---");

        $totalDeletedCountForBusiness = 0;
        $processedUserCountForBusiness = 0;
        $deleteIds = [];

        $totalUsersInBusiness = DB::table($this->userTiersTable)
            ->where('business_fk', $businessId)
            ->distinct('user_fk')
            ->count('user_fk');

        if ($totalUsersInBusiness == 0) {
            $this->line(" No users found for Business FK: {$businessId} in '{$this->userTiersTable}'. Skipping.");
            return ['deletedCount' => 0, 'processedUsers' => 0];
        }

        $this->line(" Found {$totalUsersInBusiness} distinct users for this business. 
        Processing in chunks of {$userChunkSize}...");

        DB::table($this->userTiersTable)
            ->select('user_fk')
            ->where('business_fk', $businessId)
            ->distinct()
            ->orderBy('user_fk')
            ->chunk($userChunkSize, function ($users) use (
                $businessId,
                &$totalDeletedCountForBusiness,
                &$processedUserCountForBusiness,
                &$deleteIds,
                $recordsToKeep,
                $orderByColumn,
                $orderByDir,
                $isDryRun
            ) {
                $userIds = $users->pluck('user_fk')->all();
                if (empty($userIds)) {
                    return true;
                }

                $idsToKeepForThisBatch = [];

                foreach ($userIds as $userId) {
                    try {
                        $ids = DB::table($this->userTiersTable)
                            ->where('business_fk', $businessId)
                            ->where('user_fk', $userId)
                            ->orderBy($orderByColumn, $orderByDir)
                            ->limit($recordsToKeep)
                            ->pluck('user_tier_level_pk')
                            ->all();
                        $idsToKeepForThisBatch = array_merge($idsToKeepForThisBatch, $ids);
                    } catch (\Exception $e) {
                        $this->error("\nError fetching records to keep for User FK: {$userId}, 
                        Business FK: {$businessId}. Error: " . $e->getMessage());
                        Log::error("PruneUserTiersCommand: Failed to fetch IDs to keep. " . json_encode([
                            'business_fk' => $businessId, 'user_fk' => $userId, 'error' => $e->getMessage()
                        ]));
                    }
                }

                $deletedCount = 0;
                $baseQuery = DB::table($this->userTiersTable)
                    ->where('business_fk', $businessId)
                    ->whereIn('user_fk', $userIds);

                if (!empty($idsToKeepForThisBatch)) {
                    $baseQuery->whereNotIn('user_tier_level_pk', $idsToKeepForThisBatch);
                }

                $cloneQuery = clone $baseQuery;
                $deleteIds = array_merge($deleteIds, $cloneQuery->pluck('user_tier_level_pk')->toArray());

                try {
                    if ($isDryRun) {
                        $deletedCount = $baseQuery->count();
                    } else {
                        $deletedCount = $baseQuery->delete();
                    }

                    $totalDeletedCountForBusiness += $deletedCount;

                    if ($deletedCount > 0) {
                        $actionVerb = $isDryRun ? 'Would delete' : 'Deleted';
                        $this->comment("   {$actionVerb} {$deletedCount} records for user chunk in business {$businessId}.");
                    }
                } catch (\Exception $e) {
                    $actionVerb = $isDryRun ? 'count' : 'delete';
                    $this->error("\nError trying to {$actionVerb} records for user chunk (Business: 
                    {$businessId}, users starting {$userIds[0]}): " . $e->getMessage());
                    Log::error("PruneUserTiersCommand: Failed {$actionVerb} chunk. " . json_encode([
                        'business_fk' => $businessId,
                        'user_ids' => $userIds,
                        'error' => $e->getMessage()
                    ]));
                }

                $processedUserCountForBusiness += count($userIds);

                return true;
            });

        $actionVerbPast = $isDryRun ? 'would be' : 'were';
        $this->line(" Finished processing Business FK: {$businessId}. Records that {$actionVerbPast} 
        deleted: {$totalDeletedCountForBusiness}. Users processed: {$processedUserCountForBusiness}.");
        $this->line(" Deleted IDs: " . json_encode($deleteIds));

        return ['deletedCount' => $totalDeletedCountForBusiness, 'processedUsers' => $processedUserCountForBusiness];
    }
}
