<?php

namespace App\Models;

use App\Models\V1\Transaction;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\PointsExpirationTransaction
 *
 * @property int $transaction_fk
 * @property int $redeemed_points
 * @property int $earned_points
 * @property int $user_fk
 * @property \Illuminate\Support\Carbon $expired_at The date points expired
 * @property int $business_fk
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\PointsExpiration newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\PointsExpiration newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\PointsExpiration query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\PointsExpiration whereId($value)
 * @mixin \Eloquent
 */
class PointsExpirationTransaction extends Model
{
    protected $table = 'points_expiration_transactions';
    protected $primaryKey = 'transaction_fk';
    protected $connection = 'tenant';

    const CREATED_AT = 'expired_at';
    const UPDATED_AT = null;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
//    protected $fillable = [];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        //
    ];

    public function transaction(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Transaction::class, 'transaction_fk', 'transaction_pk');
    }
}
