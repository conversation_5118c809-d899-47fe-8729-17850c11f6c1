<?php

namespace App\Http\Resources;

use App\Models\V1\Language;
use App\Models\V1\Product;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        $langId = $this->additional['langId'] ?? 1;

        return [
            $this::$wrap => [
                'id' => $this->product_pk,
                'title' => Product::getTextByLanguage($this->product_name, $langId),
                'description' => Product::getTextByLanguage($this->product_description, $langId),
                'images' => $this->getImages(),
                'product_sku' => isset($this->pos_system_fk) && intval($this->pos_system_fk) === POS_SYSTEM_LIGHTSPEED ? $this->ls_system_sku : $this->product_sku,
                'external_product_id' => $this->when(isset($this->pos_product_id), $this->pos_product_id),
                'external_variant_id' => $this->when(isset($this->pos_item_id), $this->pos_item_id),
                'actual_price' => (float) $this->product_actual_price,
                'real_price' => (float) $this->product_real_price,
                'terms_conditions' => Product::getTextByLanguage($this->product_term_condition, $langId),
                'link' => Product::getTextByLanguage($this->product_link, $langId),
                'product_languages' => $this->getProductNameLanguages(),
            ]
        ];
    }

    /**
     * Returns the list of product rewards images
     *
     * @return array
     */
    protected function getImages(): array
    {
        $images = [];

        $_images = json_decode($this->product_image, false);

        for ($i = 1; $i <= 4; $i++) {
            if (isset($_images->large->{$i})) {
                $images[] = [
                    'large' => config('app.server_url') . '/' . ltrim($_images->large->{$i}, '/'),
                    'medium' => config('app.server_url') . '/' . ltrim($_images->medium->{$i}, '/'),
                    'thumbnail' => config('app.server_url') . '/' . ltrim($_images->thumb->{$i}, '/'),
                    'path' => config('app.server_url') . '/' . ltrim($_images->large->{$i}, '/'),
                    'default' => $i === $_images->default,
                ];
            }
        }

        return $images;
    }

    protected function getProductNameLanguages(): array
    {
        $_nameObj = json_decode($this->product_name, false);
        $_descrObj = json_decode($this->product_description, false);
        $_termsObj = json_decode($this->product_term_condition, false);
        $_linkObj = json_decode($this->product_link, false);

        $result = [];
        foreach ($_nameObj as $key => $value) {
            $language = Language::find($key);
            $result[] = [
                'language_id' => (int) $key,
                'title' => (string) $value,
                'description' => $_descrObj->$key ?? null,
                'terms_conditions' => $_termsObj->$key ?? null,
                'link' => $_linkObj->$key ?? null,
                'language' => [
                    'id' => $language->language_pk,
                    'abbreviation' => $language->abreviation,
                    'name' => $language->name,
                ],
            ];
        }

        return $result;
    }
}
