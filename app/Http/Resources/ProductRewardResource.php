<?php

namespace App\Http\Resources;

use App\Models\V1\Language;
use App\Models\V1\Product;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductRewardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        $relations = explode(',', $request->relationships);

        $includeProduct = in_array('product', $relations, true) ||
            in_array('product_rewards.product', $relations, true);

        // in case it was accidentally called from inside product reward
        if (in_array('product_reward', $relations, true)) {
            $includeProduct = false;
        }

        return [
            $this::$wrap => [
                'id' => (int) $this->product_reward_pk,
                'points' => (int) $this->point_value,
                'published' => (int) $this->published,
                'product' => $this->when($includeProduct, function () use ($includeProduct) {
                    return $this->includeProduct($includeProduct);
                }),
            ]
        ];
    }

    private function includeProduct($includeProduct)
    {
        // to avoid extra query when checking $this->>product
        if (!$includeProduct) {
            return null;
        }

        if (empty($this->product)) {
            return null;
        }

        return (new ProductResource($this->product))->response()->getData()->data;

        //        return [
        //            'id' => $this->product_fk,
        //            'title' => Product::getTextByLanguage($this->product->product_name, $langId = 1),
        //            'description' => Product::getTextByLanguage($this->product->product_description, $langId = 1),
        //            'images' => $this->getImages(),
        //            'product_sku' => $this->product->product_sku,
        //            'actual_price' => (float) $this->product->product_actual_price,
        //            'real_price' => (float) $this->product->product_real_price,
        //            'terms_conditions' => Product::getTextByLanguage($this->product->product_term_condition, $langId = 1),
        //            'link' => Product::getTextByLanguage($this->product->product_link, $langId = 1),
        //            'product_languages' => $this->getProductNameLanguages(),
        //        ];
    }

    /**
     * Returns the list of product rewards images
     *
     * @return array
     */
    protected function getImages(): array
    {
        $images = [];

        $_images = json_decode($this->product->product_image, false);

        for ($i = 1; $i <= 4; $i++) {
            if (isset($_images->large->{$i})) {
                $images[] = [
                    'large' => config('app.server_url') . '/' . ltrim($_images->large->{$i}, '/'),
                    'medium' => config('app.server_url') . '/' . ltrim($_images->medium->{$i}, '/'),
                    'thumbnail' => config('app.server_url') . '/' . ltrim($_images->thumb->{$i}, '/'),
                    'default' => $i === $_images->default,
                ];
            }
        }

        return $images;
    }

    private function getProductNameLanguages(): array
    {
        $_nameObj = json_decode($this->product->product_name, false);
        $_descrObj = json_decode($this->product->product_description, false);

        $result = [];
        foreach ($_nameObj as $key => $value) {
            $language = Language::find($key);
            $result[] = [
                'language_id' => (int) $key,
                'title' => (string) $value,
                'description' => $_descrObj->$key ?? null,
                'language' => [
                    'id' => $language->language_pk,
                    'abbreviation' => $language->abreviation,
                    'name' => $language->name,
                ],
            ];
        }

        return $result;
    }
}
