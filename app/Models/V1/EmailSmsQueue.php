<?php
/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 2019-11-26
 * Time: 9:13 AM
 */

namespace App\Models\V1;

use Illuminate\Database\Eloquent\Model;

class EmailSmsQueue extends Model
{
    protected $table = 'email_sms_queue';
    protected $primaryKey = 'email_sms_queue_pk';
    protected $connection = 'tenant';

    /**
     * The name of the "created at" column.
     *
     * @var string
     */
    const CREATED_AT = 'utc_created';

    /**
     * The name of the "updated at" column.
     *
     * @var string
     */
    const UPDATED_AT = 'utc_updated';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
    ];

    /**
     * Get the campaign for reward.
     */
    public function campaign(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Campaign::class, 'entity_campaign_fk', 'entity_campaign_pk');
    }
}
