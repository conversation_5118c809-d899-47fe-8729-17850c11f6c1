<?php
/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 5/6/2021
 * Time: 11:28 AM
 */

namespace App\Helpers\Dto;


use App\Helpers\DataTransferObject\DataTransferObject;

class BusinessRuleData extends DataTransferObject
{
    /** @var float|integer|null */
    public $reward_ratio;

    /** @var integer|null */
    public $reward_milestone_amount;

    /** @var integer|null */
    public $redemption_ratio;

    /** @var float|integer|null */
    public $redemption_amount;

    /** @var integer|null */
    public $welcome_points;

    /** @var int|bool|null */
    public int|bool|null $round_up_points = null;

    /** @var int|bool|null */
    public int|bool|null $tiers_earning_override = null;

    /** @var array|null  */
    public array|null $tiers_earning_rules = null;
}