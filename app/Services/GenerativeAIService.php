<?php

namespace App\Services;

use App\Http\Resources\StaticAssetResource;
use App\Models\GptChat;
use App\Models\V1\BusinessProfile;
use App\Models\V1\Language;
use App\Traits\StaticAssetTrait;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use RuntimeException;
use JsonException;
use GuzzleHttp\Client;

class GenerativeAIService
{
    use StaticAssetTrait;

    private $business;
    private array $input;
    private ChatGPTService $gptService;
    private array $results;

    public function __construct($business, array $input)
    {
        $this->business = $business;
        $this->input = $input;
        $this->gptService = new ChatGPTService();
    }

    public function getResults(): array
    {
        return $this->results;
    }

    /**
     * @throws GuzzleException
     * @throws InvalidArgumentException
     * @throws JsonException
     */
    public function generate(): void
    {
        //$this->input['prompt'] = $this->input['description'];
        if (isset($this->input['title'])) {
            $this->input['prompt'] = $this->input['title'] . '. ' . $this->input['description'] ?? '';
        } else {
            $this->input['prompt'] = $this->input['description'] ?? '';
        }
        unset($this->input['description']);

        if ($this->input['type'] === 'subject_line') {
            $this->generateSubjectLines();
        } elseif ($this->input['type'] === 'sms_content') {
            $this->generateSMSContent();
        } elseif ($this->input['type'] === 'image') {
            $this->generateImages();
        } elseif ($this->input['type'] === 'email_content') {
            $this->generateEmailContent();
        } elseif ($this->input['type'] === 'review') {
            $this->generateReviewContent();
        } elseif ($this->input['type'] === 'review_reply') {
            $this->generateReviewReplyContent();
        } elseif ($this->input['type'] === 'question') {
            $this->generateQuestionContent();
        } elseif ($this->input['type'] === 'question_reply') {
            $this->generateQuestionReplyContent();
        } else {
            throw new InvalidArgumentException('Unknown type: ' . $this->input['type']);
        }
    }

    public function getAllSuggesters(): array
    {
        $languages = Language::select(\DB::raw('language_pk as id, name'))->where('enabled', 1)->get();

        return [
            ['type' => 'subject_line', 'campaign_type' => $this->getCampaignTypes(), 'promo_type' => $this->getPromoTypes()],
            ['type' => 'sms_content', 'campaign_type' => $this->getCampaignTypes(), 'promo_type' => $this->getPromoTypes()],
            ['type' => 'image', 'campaign_type' => $this->getCampaignTypes(), 'promo_type' => $this->getPromoTypes()],
            [
                'type' => 'email_content',
                'language' => new ResourceCollection($languages),
                'voice_tone' => $this->getVoiceTones(),
                'writing_style' => $this->getWritingStyles(),
                'campaign_type' => $this->getCampaignTypes(),
                'promo_type' => $this->getPromoTypes(),
            ],
        ];
    }

    /**
     * @throws GuzzleException
     * @throws RuntimeException
     * @throws JsonException
     */
    private function generateSubjectLines(): void
    {
        $n = $this->input['n'] ?? 5;
        $this->input['context'] = "You are helpful marketing expert assistant. Do not explain what you are doing. ";
        $this->input['context'] .= "Respond with a list of subject lines and nothing else. ";
        $this->input['context'] .= "Suggest {$n} email subject lines in the following context: ";
        $this->input['source'] = 'campaign_subject_lines';

        if ($commonContext = $this->prepareCommonContext()) {
            $this->input['context'] .= $commonContext;
        }

        $gptChat = new GptChat();
        $gptChat->title = filter_var($this->input['title'], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $gptChat->business_fk = $this->business->business_pk;
        $gptChat->save();

        $chat = $this->gptService->chatCompletions($gptChat, $this->input);

        if (!$chat) {
            throw new RuntimeException('The server encountered an unexpected condition which prevented it from fulfilling the request');
        }

        $this->results = $this->extractMessages($chat->message);
    }

    /**
     * @throws GuzzleException
     * @throws RuntimeException
     * @throws JsonException
     */
    private function generateSMSContent(): void
    {
        /*$this->input['context'] = "You are helpful marketing expert assistant.
        Suggest a short TEXT message that is compliant with SMS laws and regulations.
        The TEXT message must contain maximum 140 characters.
        The TEXT message must include the following business name: {$this->business->name}. {$aboutBusiness}.
        The TEXT message content should be in the following context";*/
        $this->input['context'] = "As an expert in marketing compliance, please provide a concise SMS message adhering to legal regulations. 
        This message should not exceed 140 characters and include the business name: {$this->business->name}. Add content relevant to {$this->aboutBusiness()}. ";

        $this->input['source'] = 'campaign_sms_content';
        if ($commonContext = $this->prepareCommonContext()) {
            $this->input['context'] .= $commonContext;
        }

        $gptChat = new GptChat();
        $gptChat->title = filter_var($this->input['title'], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $gptChat->business_fk = $this->business->business_pk;
        $gptChat->save();

        $chat = $this->gptService->chatCompletions($gptChat, $this->input);

        if (!$chat) {
            throw new RuntimeException('The server encountered an unexpected condition which prevented it from fulfilling the request');
        }

        $this->results = [['content' => str_replace('"', '', $chat->message)]];
    }

    /**
     * @throws JsonException
     * @throws GuzzleException
     * @throws Exception
     */
    private function generateImages(): void
    {
        $gptChat = new GptChat();
        $gptChat->title = filter_var($this->input['title'], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $gptChat->business_fk = $this->business->business_pk;
        $gptChat->save();

        $results = $this->gptService->imageGeneration($gptChat, $this->input);

        $this->prepareImagesResponse($results);
        //$this->results = $this->makeGoogleVertexAIApiRequest($gptChat, $this->input);
    }

    /**
     * @throws Exception
     */
    private function prepareImagesResponse($results): void
    {
        $imageSize = explode('x', $this->input['size'] ?? '1024x1024');

        foreach ($results as $result) {
            $imageUploader = new ImageDownloader($result['url'], $this->business, 'marketing');
            $imagePath = $imageUploader->download();

            $staticAsset = self::createStaticAsset($this->business, [
                'original_path' => $imagePath,
                'large_path' => $imagePath,
                'medium_path' => $imagePath,
                'thumbnail_path' => $imagePath,
                'name' => $this->input['title'],
                'width' => $imageSize[0],
                'height' => $imageSize[1],
                'source_module' => 'marketing',
            ]);

            $this->results[] = (new StaticAssetResource($staticAsset))->response()->getData()->data;
        }
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    private function generateEmailContent(): void
    {
        $this->input['context'] = $this->prepareContextForEmail();
        $this->input['prompt'] = 'Write the best marketing email promoting this product or service: ' .
            $this->input['prompt'];
        $this->input['source'] = 'campaign_email_content';
        //$this->input['model'] = 'gpt-4.1-mini';

        $gptChat = new GptChat();
        $gptChat->title = filter_var($this->input['title'], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $gptChat->business_fk = $this->business->business_pk;
        $gptChat->save();

        $chat = $this->gptService->chatCompletions($gptChat, $this->input);

        if (!$chat) {
            throw new RuntimeException('The server encountered an unexpected condition which prevented it from fulfilling the request');
        }

        // remove ```html from the message content
        $cleanedMessage = str_replace('```html', '', $chat->message);
        $cleanedMessage = str_replace('```', '', $cleanedMessage);

        $this->results = [['content' => $cleanedMessage]];
        //$this->results = [['content' => $chat->message]];
    }

    public function aboutBusiness(): string
    {
        $aboutBusiness = '';
        if (trim($this->business->about)) {
            $aboutBusiness = rtrim($this->business->about, '.');
            $aboutBusiness = trim($aboutBusiness);
        }

        return $aboutBusiness;
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    private function makeGoogleVertexAIApiRequest(GptChat $gptChat, array $validated): array
    {
        return (new GoogleGeminiService())->imageGeneration($gptChat, $validated);
    }

    private function extractMessages($text): array
    {
        $lines = explode("\n", $text);
        $messages = [];

        foreach ($lines as $line) {
            // Remove leading numbers and trim whitespace
            $cleanedLine = $this->removeNumbersAndQuotes($line);

            if (!empty($cleanedLine)) {
                $messages[] = $cleanedLine;
            }
        }

        return $messages;
    }

    private function removeNumbersAndQuotes($str): string
    {
        // Remove any number followed by a dot, any number within parentheses, and double quotes
        $str = preg_replace('/\b\d+\.\s*|\(\s*\d+\s*\)\s*|"/', '', $str);

        // Remove numbers followed by a parenthesis
        $str = preg_replace('/^\d+\)\s*/', '', $str);

        return trim($str);
    }

    private function prepareCommonContext(): string
    {
        $campaignType = isset($this->input['campaign_type']) ? filter_var($this->input['campaign_type'], FILTER_SANITIZE_FULL_SPECIAL_CHARS) : '';
        $promoType = isset($this->input['promo_type']) ? filter_var($this->input['promo_type'], FILTER_SANITIZE_FULL_SPECIAL_CHARS) : '';

        $context = '';

        if ($campaignType && strtolower($campaignType) !== 'other') {
            $context .= "Campaign type: {$campaignType}. ";
        }

        if ($promoType && strtolower($promoType) !== 'other') {
            $context .= "Promotion type: {$promoType}. ";
        }

        return $context;
    }

    private function prepareContextForEmail(): string
    {
        $language = isset($this->input['language']) ? filter_var($this->input['language'], FILTER_SANITIZE_FULL_SPECIAL_CHARS) : 'English';
        $voiceTone = isset($this->input['voice_tone']) ? filter_var($this->input['voice_tone'], FILTER_SANITIZE_FULL_SPECIAL_CHARS) : '';
        $writingStyle = isset($this->input['writing_style']) ? filter_var($this->input['writing_style'], FILTER_SANITIZE_FULL_SPECIAL_CHARS) : '';
        $returnHtml = isset($this->input['html_content']) && $this->input['html_content'];
        $businessName = $this->business->marketing_name ?? $this->business->name;

        $context = "Please ignore all previous instructions. Please respond only in the {$language} language. You are a professional email marketer. ";
        if ($voiceTone) {
            $context .= "You have a {$voiceTone} tone of voice. ";
        }
        if ($writingStyle) {
            $context .= "You have a {$writingStyle} writing style. ";
        }

        if ($commonContext = $this->prepareCommonContext()) {
            $context .= $commonContext;
        }
        if (isset($this->input['about_in_context']) && $this->input['about_in_context'] && $this->aboutBusiness()) {
            $context .= "About US: {$this->aboutBusiness()} ";
        }
        $context .= "Do not explain what you are doing. Do not include placeholders. Do not include subject. ";
        $context .= "Do not include discount codes unless I tell you that. ";
        $context .= "Do not include date, time and year unless I tell you that. ";
        $context .= "Please include the company name: {$businessName}. ";

        if ($returnHtml) {
            $context .= "Respond in HTML format, include meta utf-8 charset, and with inline CSS styles, so that it is email client safe. ";
            $context .= "Ensure the response is HTML text without any Markdown or special formatting. ";
        }

        if ($returnHtml && $this->business->logo_image_thumbnail) {
            $logoPath = config('app.server_url') . '/' . ltrim($this->business->logo_image_thumbnail, '/');
            $context .= "Include the following logo on top: {$logoPath} ";
        }

        $businessProfile = BusinessProfile::select(['color_buttons'])
            ->where('business_fk', $this->business->business_pk)
            ->first();

        if ($businessProfile && $businessProfile->color_buttons) {
            $context .= "Use the following primary color: {$businessProfile->color_buttons} ";
        }

        return $context;
    }

    private function parseTitleFromDescription($description, $maxLength = 255)
    {
        $description = filter_var($description, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $matches = [];
        preg_match('/^[^.!?]*[.!?]/', $description, $matches);
        $firstPhase = $matches[0] ?? $description;
        if (strlen($firstPhase) > $maxLength) {
            return substr($firstPhase, 0, $maxLength);
        } else {
            return $firstPhase;
        }
    }

    private function generateReviewContent(): void
    {
        $type = $this->input['source'] == 1 ? "product" : "website";
        $this->input['context'] = "Please generate a {$type} review based on the detected language. Respond in plain text. ";
        if ($this->input['source'] == 1) {// product review
            $this->input['context'] .= $this->prepareProductInfo();
            $this->input['context'] .= "If the user leave a positive experience, please describe the user's positive experience with the product and the customer service. What did the user like the most. Share how the product met or exceeded the user's expectations. What features of the product stood out to the user. How did the product benefit the user. ";
            $this->input['context'] .= "If the user leave a neutral experience, describe the user's experience with the product. What aspects were satisfactory and which could be improved. What the user's expectations for the product, and how did it match up. Share both the positives and the negatives of the user's experience with the product.";
            $this->input['context'] .= "If the user leave a negative experience, please describe the issues the user faced with the product and how it affected your experience. What were the user's expectations for the product, and how did it fall short. Share any suggestions for improving the product. ";
            $this->input['context'] .= "If the user leave a review for the specific features, please talk about the user's experience with specific features of the product. How did they perform. Describe how certain features of the product were useful or not useful. What improvements would the user suggest for specific features of the product.";
            $this->input['context'] .= "How are the user using the product in his daily life or business? Describe the impact it has had. Share a story about a particular instance where the product was especially useful or disappointing. Describe the context in which the user use the product and how well it serves that purpose.";
        }

        $gptChat = new GptChat();
        $gptChat->title = $this->parseTitleFromDescription($this->input['prompt']);
        $gptChat->business_fk = $this->business->business_pk;
        $gptChat->save();

        $chat = $this->gptService->chatCompletions($gptChat, $this->input);

        if (!$chat) {
            throw new RuntimeException('The server encountered an unexpected condition which prevented it from fulfilling the request');
        }

        $this->results = [['content' => str_replace('"', '', $chat->message)]];
    }

    private function generateReviewReplyContent(): void
    {
        $this->input['context'] = "The original review content is: " . $this->input['reply_target_content'] . ' ';
        $this->input['context'] .= "Please generate a reply to this review, and based on the detected language. Respond in plain text. ";

        $this->input['context'] .= $this->prepareAdditionalReplyTips();
        $this->input['context'] .= $this->prepareProductInfo();
        $this->input['context'] .= "If the user leaves a positive experience, thank the customer for their positive feedback and mention specific aspects they appreciated. Express gratitude for their feedback and invite them to join your loyalty program or follow your social media for updates. Highlight new features or products based on the customer's positive feedback. ";
        $this->input['context'] .= "If the user leaves a negative experience, apologize for the negative experience and offer a solution or compensation if applicable. Acknowledge the customer's issues and explain any steps taken to rectify the situation. Invite the customer to contact your support team for further assistance and assure them of better service in the future. ";
        $this->input['context'] .= "Thank the customer for their review and ask for more details about their experience to help you improve. Express interest in understanding their concerns better and request additional information. Invite the customer to provide further details through direct contact for a more personalized resolution. ";
        $this->input['context'] .= "Inform the customer about upcoming products or special offers. Express gratitude for their feedback and invite them to join your loyalty program or follow your social media for updates. Highlight new features or products based on the customer's positive feedback.";

        $gptChat = new GptChat();
        $gptChat->title = $this->parseTitleFromDescription($this->input['prompt']);
        $gptChat->business_fk = $this->business->business_pk;
        $gptChat->save();

        $chat = $this->gptService->chatCompletions($gptChat, $this->input);

        if (!$chat) {
            throw new RuntimeException('The server encountered an unexpected condition which prevented it from fulfilling the request');
        }

        $this->results = [['content' => str_replace('"', '', $chat->message)]];
    }

    private function generateQuestionContent(): void
    {
        $type = $this->input['source'] == 1 ? "product" : "website";
        $this->input['context'] = "Please generate a {$type} question based on the detected language. Respond in plain text. ";
        $this->input['context'] .= $this->prepareProductInfo();

        $gptChat = new GptChat();
        $gptChat->title = $this->parseTitleFromDescription($this->input['prompt']);
        $gptChat->business_fk = $this->business->business_pk;
        $gptChat->save();

        $chat = $this->gptService->chatCompletions($gptChat, $this->input);

        if (!$chat) {
            throw new RuntimeException('The server encountered an unexpected condition which prevented it from fulfilling the request');
        }

        $this->results = [['content' => str_replace('"', '', $chat->message)]];
    }

    private function generateQuestionReplyContent(): void
    {
        $this->input['context'] = "The original question is: " . $this->input['reply_target_content'] . ' ';
        $this->input['context'] .= "Please generate a reply to this question and based on the detected language. Respond in plain text. ";

        if ($this->input['role'] == 'admin') {
            $this->input['context'] .= $this->prepareAdditionalReplyTips();
        } else {
            $this->input['context'] .= "You are a customer or a verified buyer. ";
        }

        $this->input['context'] .= $this->prepareProductInfo();

        $gptChat = new GptChat();
        $gptChat->title = $this->parseTitleFromDescription($this->input['prompt']);
        $gptChat->business_fk = $this->business->business_pk;
        $gptChat->save();

        $chat = $this->gptService->chatCompletions($gptChat, $this->input);

        if (!$chat) {
            throw new RuntimeException('The server encountered an unexpected condition which prevented it from fulfilling the request');
        }

        $this->results = [['content' => str_replace('"', '', $chat->message)]];
    }

    private function prepareProductInfo(): string
    {
        $context = '';
        if ($this->input['source'] == 1) {// product review
            $context = "This product's title is: " . $this->input['product']['title'] . '. ';
            if (isset($this->input['product']['description'])) {
                $context .= "This product's description is: " . $this->input['product']['description'];
            }
        }
        return $context;
    }

    private function prepareAdditionalReplyTips(): string
    {
        return "You are a business employee. Ensure the prompts encourage a friendly, respectful, and professional tone. " .
            "Allow prompts to be tailored based on the business’s brand voice and the nature of the product. " .
            "Incorporate context-awareness to adapt replies to specific details mentioned in the reviews. ";
    }

    private function getCampaignTypes(): array
    {
        return [
            'promotion' => 'Promotion',
            'product_announcement' => 'Product Announcement',
            'newsletter' => 'Newsletter',
            'other' => 'Other',
        ];
    }
    private function getPromoTypes(): array
    {
        return [
            'store_wide_sale' => 'Store Wide Sale',
            'product' => 'Specific Product',
            'other' => 'Other',
        ];
    }

    private function getVoiceTones(): array
    {
        return [
            "default" => "Default",
            "authoritative" => "Authoritative",
            "caring" => "Caring",
            "casual" => "Casual",
            "cheerful" => "Cheerful",
            "coarse" => "Coarse",
            "conservative" => "Conservative",
            "conversational" => "Conversational",
            "creative" => "Creative",
            "dry" => "Dry",
            "edgy" => "Edgy",
            "enthusiastic" => "Enthusiastic",
            "expository" => "Expository",
            "formal" => "Formal",
            "frank" => "Frank",
            "friendly" => "Friendly",
            "fun" => "Fun",
            "funny" => "Funny",
            "humorous" => "Humorous",
            "informative" => "Informative",
            "irreverent" => "Irreverent",
            "journalistic" => "Journalistic",
            "matteroffact" => "Matter of fact",
            "nostalgic" => "Nostalgic",
            "objective" => "Objective",
            "passionate" => "Passionate",
            "poetic" => "Poetic",
            "playful" => "Playful",
            "professional" => "Professional",
            "provocative" => "Provocative",
            "respectful" => "Respectful",
            "romantic" => "Romantic",
            "sarcastic" => "Sarcastic",
            "serious" => "Serious",
            "subjective" => "Subjective",
            "sympathetic" => "Sympathetic",
            "trendy" => "Trendy",
            "trustworthy" => "Trustworthy",
        ];
    }

    private function getWritingStyles(): array
    {
        return [
            "default" => "Default",
            "academic" => "Academic",
            "analytical" => "Analytical",
            "argumentative" => "Argumentative",
            "conversational" => "Conversational",
            "creative" => "Creative",
            "critical" => "Critical",
            "descriptive" => "Descriptive",
            "epigrammatic" => "Epigrammatic",
            "epistolary" => "Epistolary",
            "expository" => "Expository",
            "informative" => "Informative",
            "instructive" => "Instructive",
            "journalistic" => "Journalistic",
            "metaphorical" => "Metaphorical",
            "narrative" => "Narrative",
            "persuasive" => "Persuasive",
            "poetic" => "Poetic",
            "satirical" => "Satirical",
            "technical" => "Technical"
        ];
    }
}
