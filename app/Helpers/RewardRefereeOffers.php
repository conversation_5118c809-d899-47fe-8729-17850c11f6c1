<?php

namespace App\Helpers;

use App\Helpers\Dto\Referral\RewardReferralData;
use App\Helpers\TargetOffer\BasicTargetOffer;
use App\Helpers\TargetOffer\PointMultiplierTargetOffer;
use App\Models\UserBusinessPointExempt;
use App\Models\V1\BusinessBranch;
use App\Models\V1\Notification;
use App\Models\V1\Offer;

class RewardRefereeOffers
{
    private $transactions = [];
    /**
     * @var RewardReferralData
     */
    protected $data;
    protected $referralProgram;
    protected $referralModel;
    private $platformId;

    protected $customer;

    public function __construct($referralProgram, $referralModel, RewardReferralData $data, $customer)
    {
        $this->referralProgram = $referralProgram;
        $this->referralModel = $referralModel;
        $this->data = $data;
        $this->customer = $customer;
        $this->platformId = $data->platform_id;
    }

    public function getTransactions()
    {
        return $this->transactions;
    }

    /**
     * Target offer and assign offer to the user, similar functionality as target offer through campaign
     * @return void
     */
    public function process()
    {
        $userBusinessPointExempt = UserBusinessPointExempt::findUserBusinessPointExempt($this->customer->id, $this->platformId);
        if ($userBusinessPointExempt) {
            info(__METHOD__ . ' User is points exempt', [
                'userId' => $this->customer->id,
                'referral_pk' => $this->referralModel->referral_pk,
                'platformId' => $this->platformId
            ]);
            return;
        }

        $branch = BusinessBranch::where('business_branch_pk', $this->data->branch->business_branch_pk)->first();
        $offerIds = $this->getOfferIds();

        foreach ($offerIds as $offerId) {
            $offer = Offer::where('offer_pk', $offerId)->first();
            if ($offer) {
                switch ($offer->offer_type_fk) {
                    case 8:
                    case 9:
                    case 10:
                        $tagOffer = new BasicTargetOffer($offer, $branch, $this->customer->id);
                        break;
                    case 1:
                        $tagOffer = new PointMultiplierTargetOffer($offer, $branch, $this->customer->id);
                        break;
                    default:
                        throw new \LogicException("Referral reward offer offer_type_fk:{$offer->offer_type_fk} have not been implemented");
                }
            }

            $tagOffer->handle();
            $tagOfferTransactions = $tagOffer->getTransactions();
            $this->transactions = array_merge($this->transactions, $tagOfferTransactions);
            if (count($tagOfferTransactions) > 0) {
                //Only targeted offer will have transactions
                //Public offer do not send Notification
                $notificationParams = $this->prepareNotificationParams();
                $notif = Notification::create(array_merge([
                    'timezone_mysql_fk' => $this->data->branch->branch_timezone_fk,
                    'notificationType' => NOTIFICATION_TYPE_NOTIFICATION,
                    'units_exchange' => 0,
                    'amount' => 0,
                    'business_fk' => $this->data->business->business_pk,
                    'business_branch_fk' => $this->data->branch->business_branch_pk,
                    'utc_publish' => date("Y-m-d H:i:s"),
                    'offer_fk' => $offerId,
                    'gift_card_queue_fk' => null,
                ], $notificationParams));

                info(__METHOD__, [
                    'user' => $this->customer->id,
                    'offer' => $offerId,
                    'notification' => $notif->notification_pk ?? null
                ]);
            }
        }
    }

    public function validate()
    {
        //The referral programs condition have been validated in the function RewardReferral::validateNoMatchConditions
        //Access to this function only when condition met

        if (empty($this->referralProgram->referee_earns_offers)) {
            return false;
        }

        return true;
    }

    protected function prepareNotificationParams()
    {
        return [
            'notificationRequester' => NOTIF_REQ_REFERRAL_REFEREE_REWARD,
            'user_receiver_fk' => $this->customer->id,
            'user_sender_fk' => $this->referralModel->referer_fk,
            'notification_text' => 'NOTIF_TEXT_REFEREE_EARNED_OFFER',
        ];
    }

    protected function getOfferIds()
    {
        return json_decode($this->referralProgram->referee_earns_offers, true);
    }
}