<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\GptChatCompletion
 *
 * @property int $id
 * @property string $prompt
 * @property string $message
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property int $usage_prompt_tokens
 * @property int $usage_completion_tokens
 * @property int $usage_total_tokens
 * @property array|null $message_info
 * @property int $gpt_chat_id
 * @property int|null $entity_campaign_fk
 * @property-read \App\Models\GptChat|null $chat
 * @method static \Illuminate\Database\Eloquent\Builder|GptChatCompletion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GptChatCompletion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GptChatCompletion query()
 * @mixin \Eloquent
 */
class GptChatCompletion extends Model
{
    use HasFactory;

    protected $table = 'gpt_chat_completions';
    protected $primaryKey = 'id';
    protected $connection = 'tenant';

    const CREATED_AT = 'created_at';
    const UPDATED_AT = null;

    protected $fillable = [
        'message',
        'prompt',
        'usage_prompt_tokens',
        'usage_completion_tokens',
        'usage_total_tokens',
        'message_info',
        'gpt_chat_id',
        'context',
        'entity_campaign_fk',
    ];

    protected $casts = [
        'message_info' => 'array',
    ];

    public function chat(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(GptChat::class, 'gpt_chat_id', 'id');
    }
}
