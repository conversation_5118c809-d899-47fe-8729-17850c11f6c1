<?php

namespace KangarooRewards\Common\Services;

use App\Models\BusinessCallToAction;
use KangarooRewards\Common\Repositories\BusinessRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class BusinessService
{
    protected BusinessRepository $business;

    public function __construct(BusinessRepository $businessRepository)
    {
        $this->business = $businessRepository;
    }

    public function getBranches($id)
    {
        return $this->business->getBranches($id);
    }

    public function getBranchesIds($id)
    {
        return $this->business->getBranchesIds($id)->toArray();
    }

    public function getBranchesWithOffers($id)
    {
        return $this->business->getBranches($id)->load('branches.offers');
    }

    public function find($id)
    {
        return $this->business->find($id);
    }


    public function getTierProgram($id)
    {
        $businessTiersSetupService = app(BusinessTiersSetupService::class);
        return $businessTiersSetupService->getBusinessTierProgram($id);
    }

    public function getActiveTierProgram($id)
    {
        return $this->getTierProgram($id)->where('enabled', 1);
    }

    public function all()
    {
        return $this->business->all()->where('enabled', 1)->where('entity_type_fk', 1);
    }

    public function getSocialMediaAccountsHavingRewardPoints($id): Collection
    {
        $branchId = $this->business->defaultBranch($id)->business_branch_pk;
        $entityType = 2;

        //TODO: extract this to a config file
        $platforms = [
            'Facebook' => ['facebook_url', 'facebook_reward_points'],
            'Twitter' => ['twitter_url', 'twitter_reward_points'],
            'Google Plus' => ['google_plus_url', 'google_plus_reward_points'],
            'Foursquare' => ['foursquare_url', 'foursquare_reward_points'],
            'Instagram' => ['instagram_url', 'instagram_reward_points'],
            'Pinterest' => ['pinterest_url', 'pinterest_reward_points'],
            'Trip Advisor' => ['trip_advisor_url', 'trip_advisor_reward_points'],
            'Yellow Pages' => ['yellow_pages_url', 'trip_advisor_reward_points'],
            'Yelp' => ['yelp_url', 'yelp_reward_points'],
            'Youtube' => ['youtube_link', 'youtube_reward_points'],
            'Linkedin' => ['linkedin_url', 'linkedin_reward_points'],
            'Snapchat' => ['snapchat_url', 'snapchat_reward_points'],
            'Tiktok' => ['tiktok_url', 'tiktok_reward_points'],
            'Google Reviews' => ['google_reviews_url', 'google_reviews_reward_points'],
        ];

        //TODO: this code should be removed from the service
        $selectedPlatforms = [];
        // Iterate through the platforms
        foreach ($platforms as $platform => $columns) {
            $hasRewardPoints = DB::table('social_media')
                ->where('entity_fk', $branchId)
                ->where('entity_type_fk', $entityType)
                ->where(function ($query) use ($columns) {
                    foreach ($columns as $column) {
                        $query->whereNotNull($column);
                    }
                })
                ->exists();

            if ($hasRewardPoints) {
                $selectedPlatforms[] = $platform;
            }
        }

        return collect($selectedPlatforms);
    }

    public function getAdmins($businessId)
    {
        return $this->business->getAdmins($businessId);
    }

    public function hasSignUpBonus($businessId): bool
    {
        $branchIds = $this->getBranchesIds($businessId);

        $result = BusinessCallToAction::whereIn('business_branch_fk', $branchIds)
            ->where('call_to_action_fk', CALL_TO_ACTION_SIGNUP)
            ->where('enabled', 1)
            ->where(function ($query) {
                $query->where('nb_of_points', '!=', 0)
                    ->orWhereNotNull('nb_of_points');
            })
            ->where('nb_of_points', '!=', '')
            ->get()
            ->count();

        return ($result === 0);
    }
}
