<?php

namespace App\Traits;

use App\Models\V1\Product;
use App\Models\V1\ProductBranch;
use KangarooRewards\Common\Traits\BusinessBranchTrait;

trait ProductTrait
{
    use BusinessBranchTrait;

    /**
     * Get product by external ID
     * @param $externalId
     * @param $accountId
     * @param null $posSystemId
     * @return mixed
     */
    public function getProductByExternalId($externalId, $accountId, $posSystemId = null): mixed
    {
        $product = Product::where('pos_product_id', $externalId)
            ->where('pos_account_id', $accountId);

        if (!empty($posSystemId)) {
            $product->where('pos_system_fk', $posSystemId);
        }

        return $product->first();
    }

    /**
     * Normalize product name by language ID
     * @param $productName
     * @param int $langId
     * @return string
     */
    public function normalizeProductNameByLang($productName, int $langId = 1): string
    {
        //Prepare product name attribute
        $productTitle = trim(preg_replace('!\s+!', ' ', $productName));
        $productTitle = stripslashes($productTitle);
        $productNameArr[$langId] = $productTitle;

        return json_encode($productNameArr, JSON_UNESCAPED_UNICODE);
    }

    /**
     * Create product
     * @param $attributes
     * @return mixed
     */
    public function createProduct($attributes): mixed
    {
        $product = new Product();

        $product->fill($attributes);

        $product->save();

        return $product->product_pk;
    }

    /**
     * Save product branches
     * @param $productId
     * @param $businessId
     * @return void
     */
    public function saveProductBranches($productId, $businessId): void
    {
        // Get enabled business branches
        $branches = $this->getEnabledBusinessBranches($businessId);

        foreach ($branches as $branch) {
            $productBranch = new ProductBranch();
            $productBranch->product_fk = $productId;
            $productBranch->business_branch_fk = $branch->business_branch_pk;
            $productBranch->timezone_mysql_fk = $branch->timezone_mysql_fk;
            $productBranch->enabled = 1;
            $productBranch->save();
        }
    }

    /**
     * Create / Update product
     * @param $attributes
     * @param $product
     * @param $businessId
     * @return void
     */
    public function saveProduct($attributes, $product = null, $businessId = null): void
    {
        if (empty($product)) {
            $productId = $this->createProduct($attributes);
            $this->saveProductBranches($productId, $businessId);
        } else {
            $product->fill($attributes);
            $product->save();
        }
    }

    /**
     * Find product by branch IDs
     * @param $productId
     * @param $branchIds
     * @return mixed
     */
    public function findProductByBranchIds($productId, $branchIds): mixed
    {
        return Product::from('products as p')
            ->select([
                'p.product_pk',
                'p.product_name',
                'p.product_description',
                'p.product_term_condition',
                'p.product_link',
                'p.product_sku',
                'p.ls_system_sku',
                'p.pos_item_id',
                'p.pos_product_id',
                'p.pos_system_fk',
                'p.product_image',
                'p.product_actual_price',
                'p.product_real_price',
                'p.product_term_condition',
                'p.product_link',
            ])
            ->join('product_branches as pb', 'p.product_pk', '=', 'pb.product_fk')
            ->whereIn('business_branch_fk', $branchIds->toArray())
            ->where('p.product_pk', $productId)
            ->where('p.enabled', 1)
            ->first();
    }

    public function findProductsByIds($productIds)
    {
        return Product::whereIn('product_pk', $productIds)
            ->where('enabled', 1)
            ->get();
    }

    public function deleteProduct($product)
    {
        $product->enabled = 0;
        $product->save();
    }
}
