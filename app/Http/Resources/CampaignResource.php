<?php

namespace App\Http\Resources;

use App\Helpers\Campaign\CampaignBinder;
use App\Helpers\Dto\PointsLabelData;
use App\Models\V1\CatalogItem;
use App\Models\V1\Currency;
use App\Models\V1\Offer;
use App\Models\V1\Business;
use App\Services\CampaignAudience;
use App\Traits\BusinessRulesTrait;
use App\Traits\EntityCampaignTrait;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class CampaignResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     *
     * @return array
     */
    public function toArray($request)
    {
        $business = Business::findByPk($this->credit->package->entity_fk);
        $options = is_string($this->options) ? json_decode((string) $this->options, false) : (object) $this->options;
        $pushImage = '';
        if (isset($options->pushNotificationImage) && !empty($options->pushNotificationImage)) {
            $pushImage = $options->pushNotificationImage;

            // Check if the URL does not start with "https://" (as the URL might include the full or shorted link directly)
            if (!preg_match('/^https:\/\//i', $pushImage)) {
                $pushImage = config('app.server_url') . '/' . ltrim($pushImage, '/');
            }
        }

        $include = explode(',', $request->include);

        $includeSettings = (in_array('business_settings', $include, true)
            || in_array('campaign.business_settings', $include, true));

        //\Log::info(__METHOD__, ['include' => $include, 'includeSettings' => $includeSettings]);

        $includeSocialMedia = isset($this->social_media);

        $includeCampaignSummary = in_array('summary', $include, true);

        $includeCampaignStatistics = in_array('statistics', $include, true);

        $pushTitle = $options->pushNotificationTitle ?? '';
        if(empty(trim($pushTitle))) {
            if (isset($options->campaign_subject) && !empty(trim($options->campaign_subject))) {
                $pushTitle = trim($options->campaign_subject);
            } elseif (isset($options->messagePush) && !empty(trim($options->messagePush))) {
                $pushTitle = trim($options->messagePush);
            }
        }

        if ($includeCampaignSummary) {
            // Get campaign summary details
            $service = new CampaignAudience($business, $this->options, $request->ip(), $request->header('Authorization'));
            $service = $service->filter();

            // Get campaign summary
            $campaignSummary = $service->getSummary()->toArray();
        }

        return [
            $this::$wrap => [
                'id' => $this->entity_campaign_pk,
                'group_id' => $this->group_id,
                'name' => $this->campaign_name,
                'subject' => $this->email_subject,
                'html_content' => $this->email_body,
                'plain_content' => $this->sms_body,
                'push_title' => $pushTitle,
                'push_body' => $options->pushNotificationMessage ?? '',
                'push_link' => $options->pushNotificationLink ?? '',
                'push_image' => $pushImage,
                'status' => $this->getCampaignStatus(),
                'type' => $this->getCampaignType(),
                'frequency' => (int)$this->frequency,
                'delivery_methods' => $this->getCampaignDeliveryMethod(),
                'created_at' => (new Carbon($this->utc_created))->toIso8601String(),
                'scheduled_at' => (new Carbon($this->utc_start))->toIso8601String(),
                'expires_at' => (new Carbon($this->expiry_date))->toIso8601String(),
                'offers' => $this->getOffers($request),
                'options' => $this->sanitizeCampaignOptions($options),
                $this->mergeWhen($includeCampaignSummary, [
                    'summary' => [
                        'total_targeted_customers' => $campaignSummary['totalTargeted'] ?? 0,
                        'total_reachable_customers' => $campaignSummary['totalReachable'] ?? 0,
                        'total_unreachable_customers' => $campaignSummary['totalUnReachable'] ?? 0
                    ]
                ]),
                $this->mergeWhen($includeCampaignStatistics, [
                    'statistics' => $this->getCampaignStatistics($business, $this->entity_campaign_pk, $business->entity_type_fk)
                ]),
                'business' => Business::transformItem($business),
                $this->mergeWhen($includeSettings, [
                    'business_settings' => [
                        'app_context' => [
                            'points_label' => $this->getPointsLabel($request->hasHeader('Accept-Language')),
                        ],
                    ],
                ]),
                $this->mergeWhen($includeSocialMedia, [
                    'social_media' => $this->social_media
                ])
            ]
        ];
    }

    /**
     * @param bool $acceptLanguageHeader
     * @return PointsLabelData
     */
    protected function getPointsLabel(bool $acceptLanguageHeader = false): PointsLabelData
    {
        return BusinessRulesTrait::getPointsLabelForBusiness($this->business_fk, $acceptLanguageHeader);
    }

    protected function getOffers($request)
    {
        $items = json_decode((string) $this->offers_campaigned);

        if (!$items) {
            return [];
        }

        $offers = [];

        foreach ($items as $item) {
            if (in_array($item->offerType, [1,2,3,4,8,9,10,11,13,14])) {

                $offer = Offer::getDetails($item->offerId, 60);

                if ($offer) {
                    $_offer = new OfferResource($offer);

                    $offers[] = $_offer->toArray($request)['data'];
                }

            } elseif(in_array($item->offerType, [15,16,17,18])) {

                $catalogItem = CatalogItem::findById($item->offerId);

                if ($catalogItem) {
                    $offers[] = (new CatalogItemResource($catalogItem))->toArray($request)['data'];
                }
            }
        }

        return $offers;
    }

    /**
     * Get campaign type whether it's regular or recurring (auto)
     * @return string
     */
    private function getCampaignType(): string
    {
        if ($this->auto == 1) {
            return 'recurring';
        } else {
            return 'regular';
        }
    }

    /**
     * Get the campaign delivery method
     * @return array
     */
    private function getCampaignDeliveryMethod(): array
    {
        $sms = $email = $push = false;
        switch ($this->campaign_type){
            case 1:
                $sms = $email = $push = true;
                break;
            case 2:
                $push = true;
                break;
            case 3:
                $email = true;
                break;
            case 4:
                $sms = true;
        }

        return [
            'sms' => $sms,
            'email' => $email,
            'push' => $push
        ];
    }

    /**
     * Check if the campaign is scheduled by comparing the creation and start date
     * @return bool
     */
    private function isCampaignScheduled(): bool
    {
        $startDate = strtotime($this->utc_start);
        $creationDate = strtotime($this->utc_created);

        if ($startDate > $creationDate) {
            return true;
        }

        return false;
    }

    /**
     * Get the campaign status (draft, canceled, active, finished)
     * @return string
     */
    private function getCampaignStatus(): string
    {
        if ($this->canceled == 1) {
            return 'canceled';
        }

        if ($this->draft == 1) {
            return 'draft';
        }

        if ($this->enabled == 1 || (
                $this->draft == 0 &&
                $this->locked == 0 &&
                $this->canceled == 0 &&
                $this->enqueued == 0 &&
                in_array($this->status, ["QUEUE_PROCESSING", "WAITING", "PENDING_APPROVAL"])
        )) {
            if ($this->isCampaignScheduled()) {
                return 'scheduled';
            } else {
                return 'active';
            }
        }

        return 'completed';
    }

    /**
     * @param $request
     * @return bool
     */
    private function isAdmin($request): bool
    {
        return $request->user() && !$request->user()->isUser();
    }

    /**
     * Filter the campaign options by removing the unused attributes and return only the selected filters
     * <AUTHOR>
     * @since 15/11/2023
     * @param $options
     * @return array
     */
    private function sanitizeCampaignOptions($options): array
    {
        $options = (array) $options;

        if (isset($options['campaign_data'])) {
            $data = EntityCampaignTrait::sanitizeCampaignData($options['campaign_data']);
        } else {
            $data = EntityCampaignTrait::transformCampaignOptionsCriteria($options);
        }

        return $data;
    }

    /**
     * @param Business $business
     * @param $campaignId
     * @param $entityTypeId
     * @return array|null
     */
    private function getCampaignStatistics(Business $business, $campaignId, $entityTypeId): ?array
    {
        $campaignDetails = EntityCampaignTrait::getCampaignDetails($business->business_pk, $campaignId, $entityTypeId);

        if (empty($campaignDetails)) {
            return null;
        }

        // Get default branch ID
        $branchId = $business->defaultBranch()->business_branch_pk;

        // Get currency
        $currency = Currency::forBranch($branchId);
        $currencySymbol = ($currency && $currency->symbol) ? $currency->symbol : '';

        // Calculate the overall purchases
        $overallPurchases = $campaignDetails->total_purchase_amount > 0 ? ($campaignDetails->amount_users_purchase / $campaignDetails->total_purchase_amount) * 100 : 0;

        return [
            'currency_symbol' => $currencySymbol,
            'purchases_amount' => number_format($campaignDetails->amount_users_purchase, 2, '.', ','),
            'overall_purchases' => number_format($overallPurchases, 2, '.', ','),
            'revenue_from_email_opens' => number_format($campaignDetails->amount_users_purchase_email),
            'total_coupons_redeemed' => number_format($campaignDetails->users_coupon_redeem),
            'unique_customer_visits' => number_format($campaignDetails->users_purchase),
            'total_customer_visits' => number_format($campaignDetails->total_visits)
        ];
    }
}
