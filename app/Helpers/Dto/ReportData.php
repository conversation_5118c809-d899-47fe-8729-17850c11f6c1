<?php

namespace App\Helpers\Dto;

use App\Helpers\DataTransferObject\DataTransferObject;

class ReportData extends DataTransferObject
{
    /** @var bool */
    protected $ignoreMissing = true;

    /** @var string */
    public string $date_from = '';

    /** @var string */
    public string $date_to = '';

    /** @var string */
    public string $group_interval = '';

    /** @var array */
    public array $fields = [];

    /** @var mixed */
    public mixed $per_page = 15;

    /** @var int|string */
    public mixed $page = 1;

    /** @var string */
    public string $sort = '';

    /** @var string */
    public string $customer_id = '';

    /** @var string */
    public string $transactions_type_group = ''; // amount, visits, activity

    /**
     * A Fully Qualified Class Name is required here. Do not remove
     *
     * @var \App\Helpers\Dto\ReportFilterData[]
     */
    public array $filters = [];

    public array $columns = [];

    /**
     * @param array $data
     * @return static
     */
    public static function fromRequestData(array $data): self
    {
        return new self($data);
    }
}
