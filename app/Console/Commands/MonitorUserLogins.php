<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Notifications\SystemLoginAlert;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Notification;
use App\Services\LoginMonitoringService;

class MonitorUserLogins extends Command
{
    protected $signature = 'kangaroo:monitor-logins';
    protected $description = 'Monitor login patterns and trigger alerts for abnormal behavior';

    private LoginMonitoringService $loginMonitoringService;

    public function __construct(LoginMonitoringService $loginMonitoringService)
    {
        parent::__construct();
        $this->loginMonitoringService = $loginMonitoringService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     *
     */
    public function handle(): void
    {
        $this->loginMonitoringService->monitorLoginPatterns()->sendAlertNotifications();

        $this->info('Login monitoring completed.');
    }
}
