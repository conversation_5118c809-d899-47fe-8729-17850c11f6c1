<?php
namespace App\Console\Commands;

use App\Models\V1\Activity;
use App\Models\V1\Business;
use App\Models\V1\BusinessBranch;
use App\Models\V1\Transaction;
use App\Models\V1\UserEntityPoints;
use App\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UbpVerifyUnits extends Command
{
    protected $signature = 'ubp:verify-units {init?}';
    protected $description = 'See if user points are the same before and after migration.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    private function remove_random($users, $survivalRate)
    {
        $result = [];
        foreach ($users as $user) {
            if (rand(0, $survivalRate) == 0) {
                $result[] = $user;
            }
        }
        return $result;
    }

    private $values_output = [];
    // Query users based on parameters and begin ubp process for each user
    public function handle()
    {
        $boolMigrated = $this->argument('init') != 'init';
        $this->start_time = microtime(true);
        $this->log('-----------------------------------');
        $this->log(" > Unit test verification start");

        if (!$boolMigrated) {
            $this->log(" > Resetting the test");
            DB::table("temp_ubp_unit_test_values")
                ->where("value_type", "current_active_points")
                ->delete();
        }
        $businesses = DB::table("business")
            //->where('business_pk', 567)
            //->where('enabled', 1)
            ->get();
        $intBusinesses = count($businesses);
        $intBusinessItr = -1;
        foreach ($businesses as $business) {
            $intBusinessItr++;
            //if ($intBusinessItr%3 !== 0)
            //  continue;
            $intBusinessPercent = round(($intBusinessItr / $intBusinesses) * 100, 2);
            $this->log(" > Business: " . $business->business_pk . " (%{$intBusinessPercent})");
            if (!$boolMigrated) {
                $users = DB::table("followers")
                    ->where("entity_fk", $business->business_pk)
                    ->where("entity_type_fk", 1)
                    ->pluck("user_fk")
                    ->toArray();
            } else {
                $users = DB::table("temp_ubp_unit_test_values")
                    ->where("value_type", "current_active_points")
                    ->where("business_fk", $business->business_pk)
                    ->groupBy("user_fk")
                    ->pluck("user_fk")
                    ->toArray();
                $migrated = DB::table("user")
                    ->where("business_fk", $business->business_pk)
                    ->whereNotNull("migrated_fk")
                    ->pluck("id", "migrated_fk")
                    ->toArray();
            }
            $usersBackup = $users;
            $users = $this->remove_random($users, 5000);
            $intUsers = count($users);
            if (count($users) < 20) { // make sure the same size is big enough
                $users = $this->remove_random($usersBackup, 10);
                if (count($users) < 20) {
                    $users = $this->remove_random($usersBackup, 2);
                }
            }
            $intUsers = count($users);
            $this->log(" > Users: " . $intUsers);
            $intItr=-1;
            foreach ($users as $intUser) {
                $intItr++;
                if (rand() !== 0) {
                    continue;
                }
                $intOriginalUser = $intUser;
                $strLineOutput = "";
                if ($boolMigrated) {
                    if (isset($migrated[$intUser])) {
                        $intUser = $migrated[$intUser];
                    }
                }
                $intPercent = round(($intItr/$intUsers)*100, 2);
                $this->log(" > User {$intUser} (%{$intPercent})");
                $balance = $this->getBalance($intUser, $business);
                $current_active_points = $balance->current_active_points ?? 0;
                $this->log(" > {$current_active_points} points");
                if (!$boolMigrated) {
                    $this->values_output[] = [
                        'business_fk' => $business->business_pk,
                        'user_fk' => $intUser,
                        'value_type' => 'current_active_points',
                        'before_value' => $current_active_points,
                        'created' => date('Y-m-d-H:i:s')
                    ];
                    $this->cache_out();
                } else {
                    $this->sql_buffer("UPDATE temp_ubp_unit_test_values SET after_value = {$current_active_points}, migrated_fk={$intUser} WHERE user_fk = {$intOriginalUser} AND business_fk = {$business->business_pk}");
                }
            }
            //break;
        }
        $this->cache_out(true);
        $this->sql_buffer_flush();
        $this->end();
    }

    private function cache_out($boolForceOutput = false)
    {
        $intValues = count($this->values_output);
        if ($intValues > 100 || ($boolForceOutput && $intValues != 0)) {
            $this->log(" > Inserting values from cache");
            DB::table('temp_ubp_unit_test_values')
                ->insert($this->values_output);
            $this->values_output = [];
        }
    }

    private $arrSqlBuffer = [];
    private function sql_buffer($strUpdateQuery)
    {
        $this->arrSqlBuffer[] = $strUpdateQuery;
        if (count($this->arrSqlBuffer) > 1000) {
            $this->sql_buffer_flush();
        }
    }
    private function sql_buffer_flush()
    {
        if (!count($this->arrSqlBuffer)) {
            return;
        }
        $this->log(" > Flushing update buffer");
        $strQuery = join(";", $this->arrSqlBuffer);
        $this->arrSqlBuffer = [];
        DB::unprepared($strQuery);
    }

    public function getBalance($intUser, $business)
    {
        if ($business->coalition == 1) {
            $balance = UserEntityPoints::forCoalition($intUser, $business->business_pk, $business->entity_type_fk);
        } else {
            $balance = UserEntityPoints::forBusiness($intUser, $business->business_pk, $business->entity_type_fk);
        }
        return $balance;
    }

    private function end()
    {
        $intTotalTime = (microtime(true) - $this->start_time);
        $intTotalTimeHours = round($intTotalTime / 60 / 60, 2);
        $this->log(" > Unit test finished in {$intTotalTime} seconds");
        $this->log(" > Unit test finished in {$intTotalTimeHours} hours");
        exit;
    }

    private function log($strout)
    {
        print $strout . "\n";
    }
}
