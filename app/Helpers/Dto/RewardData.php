<?php
/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 5/5/2021
 * Time: 5:07 PM
 */

namespace App\Helpers\Dto;


use App\Helpers\DataTransferObject\DataTransferObject;
use App\Http\Requests\V1\CreateReward;
use Carbon\Carbon;

class RewardData extends DataTransferObject
{
    /** @var integer */
    public $type_id;

    /** @var integer */
    public $subtype_id = 0; // virtual, not stored in the DB, to differentiate same offer type

    /** @var integer */
    public $targeted_flag = 0;

    /** @var string|Carbon|null */
    public $publish_at;

    /** @var string|Carbon|null */
    public $expires_at;

    /** @var integer|null */
    public $never_expires_flag = 0;

    /** @var float|int */
    public $discount_value = 0;

    /** @var float|int */
    public $real_value = 0;

    /** @var float|int */
    public $amount = 0;

    /** @var int */
    public $points = 0;

    /** @var int|null */
    public $shipping_option;

    /** @var bool|integer */
    public $partner_reward = false;

    /** @var integer|string|null */
    public int|string|null $partner_reward_type = null;

    /** @var bool */
    public $redeem_for_gift_card = false;

    /** @var \App\Helpers\Dto\TierData[]  */
    public $redemption_user_tiers = [];

    /** @var string|null */
    public $external_id = null;
    
    /** @var string|null */
    public $external_category_id = null;

    /** @var array */
//    public $available_at_branches = [];

    /**
     * A Fully Qualified Class Name is required here. Do not remove
     *
     * @var \App\Helpers\Dto\RewardLanguageData[]
     */
    public $reward_languages;

    /**
     * A Fully Qualified Class Name is required here. Do not remove
     *
     * @var \App\Helpers\Dto\OfferImageData[]
     */
    public $images = [];

    /**
     * A Fully Qualified Class Name is required here. Do not remove
     *
     * @var \App\Helpers\Dto\OfferBranchData[]
     */
    public $available_at_branches;

    /**
     * A Fully Qualified Class Name is required here. Do not remove
     *
     * @var \App\Models\V1\BusinessBranch[]
     */
    public $branchModels = [];

    /**
     * @param CreateReward $request
     * @return static
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public static function fromRequest(CreateReward $request): self
    {
        return new self(array_merge(
            $request->all(),
            ['branchModels' => $request->getAvailableAtBranches()]
        ));
    }

    /**
     * @var int[]
     */
    public $products = [];

    /**
     * @var \App\Helpers\Dto\PosCategoryData[]
     */
    public $categories = [];
}