<?php

namespace App\Models;

use App\Models\V1\Business;
use Illuminate\Database\Eloquent\Model;
use KangarooRewards\Common\Models\SurveyQuestionAnswers;

class Survey extends Model
{
    const CREATED_AT = 'created_at';
    const UPDATED_AT = null;

    protected $table = 'surveys';
    protected $primaryKey = 'survey_pk';
    protected $connection = 'tenant';

    public function business(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_fk', 'business_pk');
    }

    public function questionsAnswers()
    {
        return $this->hasMany(SurveyQuestionAnswers::class, 'survey_fk', 'survey_pk');
    }

    public function questionsAndAnswers()
    {
        return $this->questionsAnswers()->with(['question'])
            ->groupBy(['survey_question_answers.survey_question_fk'])
            ->get();
    }
}
