<?php
/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 2019-11-26
 * Time: 3:08 PM
 */


return [
    'BACKEND_MKTCAMPAIGN_EMAIL_HELLO' => 'Hello!',
    'BACKEND_MKTCAMPAIGN_EMAIL_HI' => 'Hi :{first_name},',
    'BACKEND_MKTCAMPAIGN_EMAIL_CONTEXT_OFFER' => 'Here is an exclusive offer for You from :{business_name}:',
    'BACKEND_MKTCAMPAIGN_EMAIL_CONTEXT_OFFERS' => 'Here are exclusive offers for You from :{business_name}:',
    'BACKEND_MY_BALANCE' => 'My Balance',
    'OFFER_STARTS' => 'Starts',
    'BACKEND_EXCLUSIVE_OFFER_FROM' => 'EXCLUSIVE OFFER FROM',
    'BACKEND_MKTCAMPAIGN_RECEIVE_NOT_SELECTED' => 'Please select who should receive the promotion',
    'BACKEND_MKTCAMPAIGN_OFFER_NOT_SELECTED' => 'No offer selected. Please select an offer',
    'BACKEND_MKTCAMPAIGN_BUSINESS_NAME_NOT_IN_TEXT' => 'Your Business name ":{business_name}", must be included in all your messages in order to comply with the carriers regulations.',
    'BACKEND_MKTCAMPAIGN_ZERO_CUSTOMERS' => 'There are no customers yet.',
    'BACKEND_MKTCAMPAIGN_ACTIVE_CAMPAIGN_CANNOT_EDIT' => 'An active and scheduled campaign cannot be edited.',
    'BACKEND_MKTCAMPAIGN_NOT_ENOUGH_CREDIT' => 'Not enough credit to start the campaign',
    'BACKEND_MKTCAMPAIGN_NOT_ENOUGH_CREDIT_SMS_EMAIL' => 'Only :{totUsersAllowSms} of :{totUsersSms} users will receive this message by SMS and :{totUsersAllowEmail} out of :{totUsersEmail} users will receive it by email!',
    'BACKEND_MKTCAMPAIGN_NOT_ENOUGH_CREDIT_SMS' => 'Only :{totUsers} of :{totUsersAllow} users will receive this message by SMS!',
    'BACKEND_MKTCAMPAIGN_NOT_ENOUGH_CREDIT_EMAIL' => 'Only :{totUsers} of :{totUsersAllow} users will receive this message by Email!',
    'BACKEND_MKTCAMPAIGN_WRONG_SCHEDULE_TIME' => 'Please choose a valid delivery time',
    'BACKEND_MKTCAMPAIGN_WRONG_SCHEDULE_DATE' => 'Please choose a valid delivery date',
    'BACKEND_MKTCAMPAIGN_START_TIME_PAST_ERROR' => 'Campaigns cannot be scheduled in the past, Please modify the date/time',
    'BACKEND_MKTCAMPAIGN_NEW_DATE_TIME_MESSAGE' => 'To respect the customers privacy, Marketing Campaigns must be launched between 8AM and 9PM.<br><br>It will be launched at :{new_date}. <br><br>Is it Ok?',
    'BACKEND_MKTCAMPAIGN_ACTIVE_CAMPAIGN_EXISTS' => 'A campaign with the same name already exists.',
    'BACKEND_MKTCAMPAIGN_PURCHASE_PRODUCT_TAG_REQUIRED' => 'At lease one tag for products is required',
    'BACKEND_MKTCAMPAIGN_LS_RETAIL_BRANDS_REQUIRED' => 'Brands and day of purchase are required',
    'BACKEND_MKTCAMPAIGN_LS_RETAIL_PRODUCTS_REQUIRED' => 'Products(Lightspeed retail), day of purchase, branches are required',
    'BACKEND_MKTCAMPAIGN_INTEGRATION_ACCOUNT_REQUIRED' => 'Integration account is required',
    'BACKEND_MKTCAMPAIGN_SHOPIFY_CATEGORY_REQUIRED' => 'Collections(Shopify) and day of purchase are required',
    'BACKEND_MKTCAMPAIGN_ECOM_CATEGORY_REQUIRED' => 'Categories(:{posName}) and day of purchase are required',
    'BACKEND_MKTCAMPAIGN_ECOM_PRODUCTS_REQUIRED' => 'Products(:{posName}), day of purchase are required',
    'BACKEND_MKTCAMPAIGN_ECOM_VENDOR_REQUIRED' => 'Vendors(:{posName}), day of purchase are required',
    'BACKEND_MKTCAMPAIGN_ECOM_TAG_REQUIRED' => 'Tags(:{posName}), day of purchase are required',
    'BACKEND_MKTCAMPAIGN_ECOM_BRAND_REQUIRED' => 'Brands(:{posName}), day of purchase are required',
    'BACKEND_MKTCAMPAIGN_CUSTOMER_TYPES_REQUIRED' => 'Customer types are required',
    'UNKNOWN_ERROR'=>'Ooops! Something went wrong! Relax then try again!', //commom
    'BACKEND_MKTCAMPAIGN_DAYS_BEFORE_EXPIRATION_ERROR' => 'days before expiration is required and it must be greater than 1',
    'BACKEND_MKTCAMPAIGN_CLIENTS_DAYS_FIRST_VISIT_REQUIRED' => 'Days number after first visit is required',
    'BACKEND_MKTCAMPAIGN_NUM_REWARDS_REDEMPTIONS' => 'The number of earnings/redemptions is required',
    'BACKEND_MKTCAMPAIGN_LAST_PURCHASE_DAYS_REQUIRED' => 'The last purchases days number is required',
    'BACKEND_MKTCAMPAIGN_LAST_PURCHASE_DAYS_RANGE_START_REQUIRED' => 'The last purchases days range start number is required',
    'BACKEND_MKTCAMPAIGN_LAST_PURCHASE_DAYS_RANGE_END_REQUIRED' => 'The last purchases days range end number is required',
    'BACKEND_MKTCAMPAIGN_LAST_PURCHASE_DAYS_RANGE_ERROR' => 'The last purchases days range start number must be smaller than the range end number',
    'BACKEND_MKTCAMPAIGN_REGISTER_NO_TRANSACTION_DAYS_REQUIRED' => 'Customers who registered, but didn\'t make a purchase days is required',
    'BACKEND_MKTCAMPAIGN_REGISTERED_START_DATE_REQUIRED' => 'Registration start date is required',
    'BACKEND_MKTCAMPAIGN_REGISTERED_END_DATE_REQUIRED' => 'Registration end date is required',
    'BACKEND_MKTCAMPAIGN_TIER_CHANGED_FROM_REQUIRED' => 'Start tier is required',
    'BACKEND_MKTCAMPAIGN_TIER_CHANGED_TO_REQUIRED' => 'Last tier is required',
    'BACKEND_MKTCAMPAIGN_STORE_CREDIT_REQUIRED' => 'The store credit amount is required',
    'BACKEND_MKTCAMPAIGN_END_STORE_CREDIT_REQUIRED' => 'The end store credit amount is required',
    'BACKEND_MKTCAMPAIGN_INFORMATION_EMAIL_REQUIRED' => 'Email information required',
    'BACKEND_MKTCAMPAIGN_DEFAULT_MESSAGE_REQUIRED' => 'Message is required',
    'BACKEND_MKTCAMPAIGN_NOT_ALLOW_SEND_TEXT' => 'No allowed to send Text',
    'BACKEND_MKTCAMPAIGN_POS_ACCOUNTING_GROUPS_REQUIRED' => 'Accounting groups(:{posName}) and day of purchase are required',
    'BACKEND_MKTCAMPAIGN_POS_PRODUCTS_REQUIRED' => 'Products(:{posName}), number of products, day of purchase are required',
    'BACKEND_MKTCAMPAIGN_INTEGRATION_PRODUCTS_REQUIRED' => 'Products(:{posName}), day of purchase are required',
    'BACKEND_MKTCAMPAIGN_CAMPAIGNED_OFFERS_REDEMPTIONS_CAMPAIGN_IDS' => 'At least one campaign must be selected with Campaigned Offers/Redemptions',
    'BACKEND_MKTCAMPAIGN_CAMPAIGNED_OFFERS_REDEMPTIONS_DAYS' => 'Campaigned Offers/Redemptions days is required',
    'BACKEND_MKTCAMPAIGN_CAMPAIGNED_OFFERS_REDEMPTIONS_BRANCHES' => 'Campaigned Offers/Redemptions branches is required',
    'BACKEND_MKTCAMPAIGN_CAMPAIGNED_OFFERS_REDEMPTIONS_LISTS' => 'At least one offer or redemption must be selected with Campaigned Offers/Redemptions',
    'BACKEND_MKTCAMPAIGN_PREV_REG_CAMP' => 'To exclude customers reached in previous campaigns, you need to select one day/hour, not zero',
    'BACKEND_MKTCAMPAIGN_PREV_AUTO_CAMP' => 'To exclude customers reached in automated campaigns, you need to select one day/hour, not zero',
    'BACKEND_MKTCAMPAIGN_LAST_PURCHASES_BRANCHES_ARRAY_REQUIRED' => 'The last purchases branches list is required and must be an array.',
    'BACKEND_MKTCAMPAIGN_LAST_PURCHASES_BRANCHES_ID_REQUIRED' => 'Branch ID is required when last purchases branches list is present.',
    'BACKEND_MKTCAMPAIGN_LAST_PURCHASES_BRANCHES_ID_INTEGER_REQUIRED' => 'Branch ID must be an integer.',
    'BACKEND_MKTCAMPAIGN_EMAIL_BODY_REQUIRED_WITHOUT_OFFERS_SELECTED' => 'The email body field is required when no offers are selected.',
    'BACKEND_MKTCAMPAIGN_REVIEW_THIS_ITEM' => 'Review this item',
    'BACKEND_MKTCAMPAIGN_DISCOUNT_AMOUNT_OFF' => ':{currency}:{discount_value} off',
    'BACKEND_MKTCAMPAIGN_DISCOUNT_PERCENTAGE_OFF' => ':{discount_value}% off',
    'BACKEND_MKTCAMPAIGN_DISCOUNT_FREE_SHIPPING' => 'free shipping on',
    'BACKEND_MKTCAMPAIGN_REVIEW_REPLY_SUBJECT' => 'Review Reply',
    'BACKEND_MKTCAMPAIGN_PRODUCTS_REQUIRED' => 'Products, day of purchase are required',
    'BACKEND_MKTCAMPAIGN_REVIEW_ORDER_RANGE_REQUIRED' => 'Date range of purchase are required',
    'BACKEND_MKTCAMPAIGN_REVIEW_LAST_X_ORDER_REQUIRED' => 'Number of last purchase are required',
];