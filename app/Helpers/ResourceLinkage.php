<?php

namespace App\Helpers;

use App\Http\Resources\AddressResource;
use App\Http\Resources\Admin\UserConsentResource;
use App\Http\Resources\BannerCollection;
use App\Http\Resources\BranchReferralRuleCollection;
use App\Http\Resources\BusinessCtaResource;
use App\Http\Resources\BusinessProfileResource;
use App\Http\Resources\CatalogItemCollection;
use App\Http\Resources\DrawCollection;
use App\Http\Resources\FrequentBuyerProgramCollection;
use App\Http\Resources\ProductRewardCollection;
use App\Http\Resources\SurveyCollection;
use App\Http\Resources\TierCollection;
use App\Http\Resources\UserBusinessNoteResource;
use App\Http\Resources\UserBusinessProfileResource;
use App\Http\Resources\UserTypeResource;
use App\Models\Banner;
use App\Models\V1\Address;
use App\Models\V1\BranchReferralRule;
use App\Models\V1\Business;
use App\Models\V1\BusinessBranch;
use App\Models\V1\BusinessProfile;
use App\Models\V1\BusinessRules;
use App\Models\V1\BusinessTierSetup;
use App\Models\V1\CatalogItem;
use App\Models\V1\Customer;
use App\Models\V1\Language;
use App\Models\V1\Product;
use App\Models\V1\SocialMedia;
use App\Models\V1\Translation;
use App\Models\V1\UserOfferStory;
use App\Models\V1\UserStatusManager;
use App\Repositories\OfferSearcher;
use App\Services\UserTierLevelService;
use App\Traits\BannerTrait;
use App\Traits\CallToActionTrait;
use App\Traits\DrawTrait;
use App\Traits\PredictionTrait;
use App\Traits\RelationTrait;
use App\Traits\SurveyTrait;
use App\Traits\UserBusinessProfileTrait;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use KangarooRewards\Common\Traits\BusinessBranchTrait;
use KangarooRewards\Common\Traits\BusinessTier as BusinessTierTrait;
use KangarooRewards\Common\Traits\BusinessTrait;
use KangarooRewards\Common\Traits\FrequentBuyerTrait;
use KangarooRewards\Common\Traits\UserBusinessNoteTrait;
use KangarooRewards\Common\Traits\UserTagTrait;
use App\Http\Resources\UserTagCollection;
use App\Http\Resources\CouponCollection;
use App\Http\Resources\CouponResource;
use App\Models\V1\UserBusinessNotification;

class ResourceLinkage
{
    use BannerTrait;
    use BusinessBranchTrait;
    use CallToActionTrait;
    use DrawTrait;
    use UserBusinessProfileTrait;
    use UserBusinessNoteTrait;

    private $request;
    private $user;
    private $business;
    private $lang;
    private $langId;

    public function __construct($request, $user, $business)
    {
        $this->request = $request;
        $this->user = $user;
        $this->business = $business;
        $this->lang = App::getLocale();
        if (isset($this->lang)) {
            $this->langId = Language::getLanguageIdByAbbreviation($this->lang);
        }

        Log::info(__METHOD__, [
            'businessId' => $this->business->business_pk,
            'conglomerate' => $this->business->conglomerate,
            'userId' => $this->user->id,
        ]);
    }

    /**
     * @param array $included - by reference
     * @return void
     */
    public function includeCustomerTier(array &$included): void
    {
        $userTier = BusinessTierTrait::getTierForUser(
            $this->business->business_pk,
            $this->user->id
        );

        if ($userTier) {
            $tierTypeName = $userTier->type_name;
            // Get translation if exist
            if (isset($this->langId)) {
                $translation = Translation::getBusinessTierTranslation($this->langId, $this->business->business_pk);
                if (isset($translation[$userTier->business_tiers_pk]['type_name'])) {
                    $tierTypeName = $translation[$userTier->business_tiers_pk]['type_name'];
                }
            }

            $included = array_merge($included, [
                'tier_level' => [
                    'id' => $userTier->business_tiers_pk,
                    'name' => $tierTypeName,
                    'reach_spend' => (float) $userTier->reach_spend,
                    'reach_visits' => (int) $userTier->reach_visits,
                    'reach_points' => (int) $userTier->reach_points,
                    //'earning'=> Business::transformEarningRatio($rule, $userTier->business_tiers_pk)
                ],
            ]);
        } else {
            $included = array_merge($included, [
                'tier_level' => null,
            ]);
        }
    }

    /**
     * @param array $included - by reference
     * @return void
     */
    public function includeTierProgress(array &$included): void
    {
        $tierProgress = null;
        try {
            //Check if tier setup exists and enabled for business
            $tierSetup = BusinessTierSetup::where('business_fk', $this->business->business_pk)
                ->where('enabled', 1)
                ->first();

            if (!empty($tierSetup) && $tierSetup->base_previous_period == 0) {
                $tierLevelService = new UserTierLevelService($this->user, $this->business, $this->langId, $tierSetup);
                $tierProgress = $tierLevelService->getTierProgress();

                if (empty($tierProgress)) {
                    $tierProgress = null;
                }
            }
        } catch (\Exception $e) {
            \Log::warning($e, ['businessId' => $this->business->business_pk, 'userId' => $this->user->id]);
        }

        $included = array_merge($included, [
            'tier_progress' => $tierProgress
        ]);
    }

    public function includeUserConsent(array &$included){
        $consent = UserBusinessNotification::forUserBusiness($this->user->id, $this->business)->first();
        // dd($consent);

        $userConsent = null;
        if(!empty($consent)){
            $userConsent = (array) (new UserConsentResource($consent))->response()->getData()->data;
            unset($userConsent['id']);
        }

        $included = array_merge($included, [
            'user_consent' => $userConsent
        ]);
    }
    
    public function includeUserNote(array &$included){
        $note = $this->getLastNoteForUser($this->user->id, $this->business->business_pk);
        // dd($consent);

        $userNote = null;
        if(!empty($note)){
            $userNote = (array) (new UserBusinessNoteResource($note))->response()->getData()->data;
            unset($userNote['id']);
        }

        $included = array_merge($included, [
            'user_note' => $userNote
        ]);
    }

    /**
     * @param array $included - by reference
     * @return void
     */
    public function includeBusinessTiers(array &$included): void
    {
        $items = \App\Traits\BusinessTierTrait::forBusinessAdminPaginated(
            $this->business->business_pk,
            $relations = ['tier_levels']
        )->simplePaginate(10);

        $included = array_merge($included, [
            'tiers' => (new TierCollection($items))->response()->getData()->data
        ]);
    }

    /**
     * @param $data
     * @return void
     */
    public function relationBusinessRules(&$data, $business): void
    {
        $rules = BusinessRules::forBusinessWithTaxes($this->business->business_pk);
        $data = array_merge($data, [
            'rules' => BusinessRules::transformItem($rules, $business),
        ]);
    }

    public function includeCustomerEarningRatio(array &$included): void
    {
        //Fetch business rules to get the default & tiers earning ratio
        $rule = BusinessRules::forBusinessWithTaxes($this->business->business_pk);

        //Get customer tier from included if exists
        $customer_tier_id = "";
        if (isset($included["tier_level"]["id"])) {
            $customer_tier_id = $included["tier_level"]["id"];
        } else {
            $userTier = BusinessTierTrait::getTierForUser(
                $this->business->business_pk,
                $this->user->id
            );
            if ($userTier) {
                $customer_tier_id = $userTier->business_tiers_pk;
            }
        }

        //Get & transform earning rule data
        $earning_rules = BusinessRules::transformEarningRatio($rule, $customer_tier_id);

        //Add the earning rule to the included data
        $included = array_merge($included, ['earning_rules' => $earning_rules]);
    }

    /**
     * @param $data
     * @return void
     */
    public function relationUserAddress(array &$data): void
    {
        $address = Address::forUser($this->user->id)->first();

        $addressResource = (new AddressResource($address))->response()->getData();

        $data = array_merge($data, [
            'address' => $addressResource->data,
        ]);
    }

    /**
     * @param $included
     * @return void
     */
    public function includeBusinessBranches(&$included, $includeEcomBranches = false): void
    {
        $branches = BusinessBranch::forBusiness($this->business->business_pk);

        if ($includeEcomBranches) {
            // Get ecom branches that are tied to an Ecom integration and with virtual flag enabled
            $ecomBranches = $this->getBusinessEcomBranches($this->business->business_pk);

            // Merge the physical branches with the ecom
            $branches = $branches->merge($ecomBranches);
        }

        $_branches = BusinessBranch::transformCollection($branches, $this->langId);
        $included = array_merge($included, ['branches' => $_branches]);
    }

    /**
     * Include ecom branches
     * @param $included
     * @return void
     */
    public function includeBusinessEcomBranches(array &$included): void
    {
        $branches = $this->getBusinessEcomBranches($this->business->business_pk);
        $_branches = BusinessBranch::transformCollection($branches, $this->langId);
        $included = array_merge($included, ['ecom_branches' => $_branches]);
    }

    /**
     * @param $included
     * @return void
     */
    public function includeUserEmails(array &$included): void
    {
        $userEmails = UserStatusManager::getEmailsForUser($this->user->id);
        $_userEmails = UserStatusManager::transformCollection($userEmails);
        $included = array_merge($included, ['user_emails' => $_userEmails]);
    }

    /**
     * @param $included
     * @return void
     */
    public function includeUserPhones(array &$included): void
    {
        $userPhones = UserStatusManager::getPhonesForUser($this->user->id);
        $_userPhones = UserStatusManager::transformCollection($userPhones);
        $included = array_merge($included, ['user_phone_numbers' => $_userPhones]);
    }

    /**
     * @param $included
     * @return void
     */
    public function includeSocialMedia(array &$included): void
    {
        $links = SocialMedia::forBranch($this->business->business_branch_pk);
        $conditions = [['type' => 'IN', 'column' => 'call_to_action_pk', 'value' => [
            CALL_TO_ACTION_FOLLOW_FACEBOOK,
            CALL_TO_ACTION_FOLLOW_TWITTER,
            CALL_TO_ACTION_FOLLOW_INSTAGRAM,
            CALL_TO_ACTION_FOLLOW_TIKTOK
        ]]];
        $businessCallToActions = $this->getBusinessCallToActionsForPagination($this->business, $conditions)->get();
        $businessCallToActionsArray = collect($businessCallToActions)->keyBy('call_to_action_fk');
        $_links = [];

        //Exclude the empty onces
        if ($links && $links->getAttributes()) {
            foreach ($links->getAttributes() as $key => $link) {
                if ($link) {
                    $action = null;
                    if (str_contains($key, 'facebook')) {
                        $action = $businessCallToActionsArray[CALL_TO_ACTION_FOLLOW_FACEBOOK] ?? null;
                    } elseif (str_contains($key, 'twitter')) {
                        $action = $businessCallToActionsArray[CALL_TO_ACTION_FOLLOW_TWITTER] ?? null;
                    } elseif (str_contains($key, 'instagram')) {
                        $action = $businessCallToActionsArray[CALL_TO_ACTION_FOLLOW_INSTAGRAM] ?? null;
                    } elseif (str_contains($key, 'tiktok')) {
                        $action = $businessCallToActionsArray[CALL_TO_ACTION_FOLLOW_TIKTOK] ?? null;
                    }

                    $isDone = false;
                    if ($action) {
                        $userActions = $this->getUserCallToActionsForPagination($action->call_to_action_fk, $this->user->id)
                            ->get();
                        if ($userActions->count() > 0) {
                            $isDone = true;
                        }
                    }

                    $_links[] = [
                        'icon' => config('app.server_url') . '/images/icons/' . $key . '.png?v=1',
                        'url' => $link,
                        'business_action' => isset($action) ? (new BusinessCtaResource($action))->response()->getData()->data : null,
                        'user_action_completed' => isset($action) ? $isDone : null
                    ];
                }
            }
        }

        //Adding website to social media links
        if ($this->business->web_site) {
            $_links[] = [
                'icon' => config('app.server_url') . '/images/icons/web_icon.png',
                'url' => $this->business->web_site,
            ];
        }

        $included = array_merge($included, ['social_media' => $_links]);
    }

    /**
     * @param $included
     * @return void
     */
    public function includeCatalogItems(array &$included): void
    {
        $catalogItems = collect(CatalogItem::forBusiness(
            $this->business->business_pk,
            $langId = 1,
            $this->user->id
        ));

        $_catalogItems = new CatalogItemCollection($catalogItems);
        $rewards = $_catalogItems->response()->getData();

        $included = array_merge($included, [
            'catalog_items' => $rewards->data,
        ]);
    }

    /**
     * @param $included
     * @param $conditions
     * @param $per_page
     * @return void
     */
    public function includeCoupons(&$included, &$conditions, $per_page): void
    {
        if (!empty($conditions) && $conditions[0]['value'] == 75) {
            $coupons = UserOfferStory::getRedeemedCouponsForUser($this->business, $this->user, $conditions)
                ->simplePaginate((int) $per_page);
        } else {
            $coupons = UserOfferStory::getAvailableCouponsForUser($this->business, $this->user, $conditions)
                ->simplePaginate((int) $per_page);
        }

        $_coupons = new CouponCollection($coupons);
        $rewards = $_coupons->response()->getData();

        $included = array_merge($included, [
            'coupons' => $rewards->data,
        ]);
    }

    /**
     * @param $included
     * @return void
     * @throws \Exception
     */
    public function includeGiftCards(array &$included): void
    {
        $offerSearcher = new OfferSearcher($this->request, $this->user, $this->business);
        $offerSearcher->setFilterBy([OFFER_TYPE_POINT_GIFTCARD_AMOUNT]);
        $_offers = $offerSearcher->search()->transformCollection();

        $included = array_merge($included, ['giftcards' => $_offers]);
    }

    /**
     * @param $included
     * @return void
     */
    public function includeOffers(&$included, $userOfferStatus): void
    {
        $offerSearcher = new OfferSearcher($this->request, $this->user, $this->business);
        $offerSearcher->setFilterBy([1,2,3,8,9,10,11]);
        $offerSearcher->setFilterByUserOfferStatus($userOfferStatus);
        $_offers = $offerSearcher->search()->getCollection()->response()->getData();

        $included = array_merge($included, ['offers' => $_offers->data]);
    }

    /**
     * @param $included
     * @return void
     */
//    public function includeConvertibleCoupons(array &$included)
//    {
//        $coupons = UserOfferStory::getConvertibleCouponsForUser($this->user->id, $this->business);
//        $_coupons = UserOfferStory::transformCouponsCollection($coupons);
//        $included = array_merge($included, ['convertible_coupons' => $_coupons]);
//    }

    /**
     * @param $included
     * @return void
     */
    public function includeProducts(array &$included): void
    {
        $products = Product::forBusiness($this->business->business_pk);
        $_products = Product::transformCollection($products, $langId = 1);

        $included = array_merge($included, ['products' => $_products]);
    }

    /**
     * @param $included
     * @return void
     */
    public function includeBusinessesWithBalance(array &$included): void
    {
        if ($this->business->coalition === 1) {
            $businesses = Business::forCoalitionWithBalance($this->business->business_pk, $this->user->id);
        } elseif ($this->business->conglomerate === 1) {
            $businesses = Business::forConglomerateWithBalance($this->business->business_pk, $this->user->id);
        } elseif ($this->business->business_pk === KNG_BUSINESS_ID) {
            $businesses = Business::userFollowingWithBalance($this->user->id);
        } else {
            $businesses = collect([$this->business]); //empty collection or tenant business
        }

        // dd($businesses);
        $included = array_merge($included, [
            'businesses' => Business::transformCollection($businesses, true),
        ]);
    }

    /**
     * @param $included
     * @return void
     */
    public function includeBusinesses(array &$included): void
    {
        if ($this->business->coalition == 1) {
            $businesses = Business::forCoalition($this->business->business_pk);
        } elseif ($this->business->conglomerate == 1) {
            $businesses = Business::forConglomerate($this->business->business_pk)->get();
        } elseif ($this->business->business_pk == KNG_BUSINESS_ID) {
            $businesses = Business::userFollowing($this->user->id);
        } else {
            $businesses = collect(); //empty collection or tenant business
        }

        $included = array_merge($included, [
            'businesses' => Business::transformCollection($businesses),
        ]);
    }

    /**
     * @param $included
     * @return void
     */
    public function includeReferralPrograms(array &$included): void
    {
        // The key in the following format: App.Models.V1.BranchReferralRule.15.1425
        // To make sure the key is unique for the same model, but different versions
        $userId = $this->user->id;
        $business = $this->business;

        $key = str_replace('\\', '.', BranchReferralRule::class) . '.' . __FUNCTION__
            . '.' . $business->business_pk . '.' . $userId;

        $referralPrograms = Cache::remember($key, 1, function () use ($business, $userId) {
            Log::info('Cache::remember retrieve BranchReferralRule from DB', [
                'BusinessId' => $business->business_pk,
                'userId' => $userId,
            ]);
            $referralItems = collect(BranchReferralRule::forBusiness(
                $business->business_pk,
                $business->business_branch_pk
            ));

            $_referralItems = new BranchReferralRuleCollection($referralItems);
            $_referralItems->setUserId($userId);
            $_referral_programs = $_referralItems->response()->getData()->data;
            if (count($_referral_programs) > 0) {
                return $_referral_programs;
            }
            return null;
        });

        if (!$referralPrograms) {
            $referralPrograms = [];
        }

        $this->logReferralPrograms($business, $userId, $referralPrograms);

        $included = array_merge($included, [
            'referral_programs' => $referralPrograms,
        ]);
    }

    public function includeProductRewards(array &$included): void
    {
        $items = \App\Models\V1\ProductReward::forBusinessAdminPaginated(
            $this->business->business_pk,
            $relations = ['product']
        )->limit(50)->get();

        $included = array_merge($included, [
            'product_rewards' => (new ProductRewardCollection($items))->response()->getData()->data,
        ]);
    }

    public function includeBanners(&$included, $archived): void
    {
        if (isset($this->user->id)) {
            $userIds = [$this->user->id];
        } else {
            $userIds = [];
        }

        $banners = $this->BannersForBusiness($this->business, [], $userIds, $archived)->get();

        $included = array_merge($included, [
            'banners' => (new BannerCollection($banners))->response()->getData()->data,
        ]);
    }

    public function includeMemberAccount(&$included, $appUser): void
    {
        if (!isset($this->user->id) || $this->user->id == $appUser->id) {
            $included = array_merge($included, [
                'member_account' => null
            ]);
            return;
        }

        $included = array_merge($included, [
            'member_account' => Customer::transformItem($this->user)
        ]);
    }

    public function relationUserProfile(array &$data): void
    {
        if ($this->business->conglomerate_id) {
            $profileResource = null; //TODO: handle for conglomerate admin and member
        } else {
            $profile = UserBusinessProfileTrait::getUserBusinessProfile($this->business->business_pk, $this->user->id);

            if (!$profile) {
                $profile = UserBusinessProfileTrait::createAndSetDefaultBusiness($this->business, $this->user);
            }
            $profileResource = (new UserBusinessProfileResource($profile))->response()->getData()->data;
        }

        $data = array_merge($data, [
            'user_profile' => $profileResource,
        ]);
    }

    public function relationUserType(array &$data): void
    {
        $userTypeResource = null;
        if ($this->business->conglomerate_id) {
            $userTypeResource = null; //TODO: handle for conglomerate admin and member
        } else {
            $profile = UserBusinessProfileTrait::getUserBusinessProfile($this->business->business_pk, $this->user->id);

            if (!$profile) {
                $profile = UserBusinessProfileTrait::createAndSetDefaultBusiness($this->business, $this->user);
            }

            if ($profile->userType) {
                $userTypeResource = (new UserTypeResource($profile->userType))->response()->getData()->data;
            }
        }

        $data = array_merge($data, [
            'user_type' => $userTypeResource,
        ]);
    }

    public function includeSurveys(array &$included): void
    {
        $items = SurveyTrait::forBusinessAdminPaginated(
            $this->business->business_pk,
            $relations = []
        )->limit(10)->get();

        $included = array_merge($included, [
            'surveys' => (new SurveyCollection($items, $this->user))->response()->getData()->data,
        ]);
    }

    public function includeFrequentBuyerPrograms(array &$included): void
    {
        $frequentBuyer = FrequentBuyerTrait::forUser(
            $this->business,
            $this->user,
            $relations = ['frequent_buyer_users']
        )->limit(10)->get();

        $colResource = new FrequentBuyerProgramCollection($frequentBuyer);

        $included = array_merge($included, [
            'frequent_buyer_programs' => $colResource->response()->getData()->data,
        ]);
    }

    public function includeBusinessProfile(array &$included): void
    {
        $settings = BusinessProfile::getPublicProfile($this->business->business_pk);
        $colResource = new BusinessProfileResource($settings);
        $included = array_merge($included, [
            'business_profile' => $colResource->response()->getData()->data
        ]);
    }

    public function includeUserTags(array &$included): void
    {
        $userTags = UserTagTrait::getTagsForUser($this->business->business_pk, $this->user->id);
        $colResource = new UserTagCollection($userTags);
        $tags = $colResource->response()->getData()->data;
        $tagIds = array_column($tags, 'id');
        $included = array_merge($included, [
            'user_tags' => $tagIds,
            'user_tag_details' => $tags
        ]);
    }

    public function includeUserSegment(array &$data): void
    {
        if ($this->business->conglomerate_id) {
            $profileResource = null; //TODO: handle for conglomerate admin and member
        } else {
            $profile = UserBusinessProfileTrait::getUserBusinessProfile($this->business->business_pk, $this->user->id);

            if (!$profile) {
                $profile = UserBusinessProfileTrait::createAndSetDefaultBusiness($this->business, $this->user);
            }
            $profileResource = (new UserBusinessProfileResource($profile))->response()->getData()->data;
        }

        $data = array_merge($data, [
            'user_segment' => $profileResource->segment ?? null,
        ]);
    }

    public function includeUserDraws(array &$data): void
    {

        if ($this->business->conglomerate_id) {
            $draws = null; //TODO: handle for conglomerate admin and member
        } else {
            $allDraws = DrawTrait::allDrawsForBusiness($this->business, $this->user);

            $draws = new DrawCollection($allDraws);
        }

        $data = array_merge($data, [
            'user_draws' => $draws ?? null,
        ]);
    }

    public function includeUserMetrics(array &$data): void
    {
        $rules = BusinessRules::select(['predictions_ml_flag'])
            ->where('business_fk', $this->business->business_pk)
            ->first();

        $metrics = null;
        if ($rules->predictions_ml_flag) {
            $metrics = self::getMetricsForUserProfile($this->business->business_pk, $this->user->id);
        }

        $data = array_merge($data, [
            'user_metrics' => $metrics,
        ]);
    }

    private function logReferralPrograms($business, $userId, $referralPrograms)
    {
        $appUser = $this->request->user();

        Log::info('ResourceLinkage::includeReferralPrograms', [
            'appUser' => [
                'id' => $appUser->id ?? null,
                'email' => $appUser->email ?? null,
                'phone' => $appUser->phone ?? null,
            ],
            'userId' => $userId,
            'businessId' => $business->business_pk,
            'referralPrograms' => $referralPrograms ? array_map(function ($program) {
//                return (array) $program;
                return [
                    'id' => $program->id,
                    'link_referral_enabled' => $program->link_referral_enabled,
                    'referral_link' => $program->referral_link,
                ];
            }, $referralPrograms) : [],
        ]);
    }
}
