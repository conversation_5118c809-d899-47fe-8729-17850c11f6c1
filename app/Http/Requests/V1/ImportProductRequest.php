<?php
namespace App\Http\Requests\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Collection;

class ImportProductRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'branch_id' => 'required|exists:tenant.business_branch,business_branch_pk',
            'file_path' => 'required|string',
            'file_name' => 'required|string'
        ];
    }
}