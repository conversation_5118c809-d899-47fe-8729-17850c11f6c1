<?php

namespace KangarooRewards\Reviews\Models;

use App\Models\V1\Business;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReviewRequest extends Model
{
    protected $table = 'review_requests';
    protected $connection = 'tenant';

    protected $primaryKey = 'review_request_pk';

    public const CREATED_AT = 'created_at';
    public const UPDATED_AT = 'updated_at';

    protected $guarded = [];
    protected $casts = [
        'sale_product_ids' => 'array',
        'sale_variant_ids' => 'array',
        'customer' => 'array'
    ];

    public function business():BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_fk', 'business_pk');
    }
}