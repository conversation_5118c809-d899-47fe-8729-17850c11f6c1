<?php

namespace App\Models\V1;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;

class UserPunchItem extends Model
{
    protected $table = 'user_punch_item';
    protected $primaryKey = 'user_punch_item_pk';
    protected $connection = 'tenant';

    /**
     * The name of the "created at" column.
     *
     * @var string
     */
    const CREATED_AT = 'utc_created';

    /**
     * The name of the "updated at" column.
     *
     * @var string
     */
    const UPDATED_AT = null;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_fk', 'punch_item_fk', 'transaction_fk', 'timezone_mysql_fk', 'utc_created',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
    ];


    /**
     * Transform a Collection
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function transformCollection($items)
    {
        $_items = [];

        foreach ($items as $item) {
            $_items[] = self::transformItem($item);
        }

        return $_items;
    }

    /**
     * Transform User model attributes
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function transformItem($item, $langId = 1)
    {
    }
}
