<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\GptChat;
use App\Models\GptChatCompletion;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use JsonException;
use RuntimeException;

class GoogleGeminiService implements GenAIServiceInterface
{
    private const DEFAULT_TEMPERATURE = 0.3;
    public function __construct()
    {
        //
    }

    /**
     * @param GptChat $gptChat
     * @param array $validated
     * @param int $maxTokens
     * @return GptChatCompletion|null
     * @throws RuntimeException
     */
    public function chatCompletions(GptChat $gptChat, array $validated, int $maxTokens = 4096): ?GptChatCompletion
    {
        $data = $this->prepareChatCompletionData($validated, $maxTokens);

        $model = $this->getAIModel($validated);

        try {
            $response = $this->sendRequest($data, $model);
            $message = $this->prepareMessage($gptChat, $validated, $response);
            return GptChatCompletion::create($message);
        } catch (Exception $e) {
            Log::error($e, ['businessId' => $gptChat->business->business_pk] + $validated);
            throw new RuntimeException('The server encountered an unexpected condition which prevented it from fulfilling the request');
        }
    }

    /**
     * @param array $validated
     * @param int $maxTokens
     * @return array
     */
    private function prepareChatCompletionData(array $validated, int $maxTokens): array
    {
        $messages = $this->buildMessages($validated);
        return [
            "contents" => $messages,
            "generationConfig" => [
                "temperature" => $validated['temperature'] ?? self::DEFAULT_TEMPERATURE,
                "topK" => 40,
                "topP" => 0.95,
                "maxOutputTokens" => $maxTokens,
                "responseMimeType" => "text/plain",
            ]
        ];
    }

    /**
     * @param array $validated
     * @return array
     */
    private function buildMessages(array $validated): array
    {
        $messages = [
            [
                "role" => "user",
                "parts" => [
                    [
                        "text" => $validated['prompt'],
                    ],
                ],
            ],
        ];

        if (!empty($validated['context'])) {
            $messages = [
                [
                    "role" => "user",
                    "parts" => [
                        [
                            "text" => $validated['context'],
                        ],
                    ],
                ],
                [
                    "role" => "user",
                    "parts" => [
                        [
                            "text" => $validated['prompt'],
                        ],
                    ],
                ],
            ];
        }
        return $messages;
    }

    /**
     * @param GptChat $gptChat
     * @param array $validated
     * @param array $response
     * @return array
     */
    private function prepareMessage(GptChat $gptChat, array $validated, array $response): array
    {
        $reply = $this->extractReply($response);
        return [
            'gpt_chat_id' => $gptChat->id,
            'prompt' => filter_var($validated['prompt'], FILTER_SANITIZE_FULL_SPECIAL_CHARS),
            'context' => filter_var($validated['context'] ?? '', FILTER_SANITIZE_FULL_SPECIAL_CHARS),
            'message' => $reply,
            'usage_prompt_tokens' => $response['usageMetadata']['promptTokenCount'] ?? 0,
            'usage_completion_tokens' => $response['usageMetadata']['candidatesTokenCount'] ?? 0,
            'usage_total_tokens' => $response['usageMetadata']['totalTokenCount'] ?? 0,
            'message_info' => [
                'id' => null, // google doesn't provide an id
                'object' => 'chat.completion',
                'model' => $response['modelVersion'],
                'provider' => 'google',
            ]
         ];
    }

    /**
     * @param array $data
     * @param string $model
     * @return array
     * @throws JsonException
     */
    private function sendRequest(array $data, string $model): array
    {
        $apiKey = config('app.GOOGLE_GEMINI_API_KEY');
        $url = "https://generativelanguage.googleapis.com/v1beta/models/$model:generateContent?key=$apiKey";
        $headers = [
            'Content-Type: application/json',
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($httpCode !== 200) {
            throw new RuntimeException("API request failed with HTTP code: $httpCode");
        }
        return json_decode($response, true, 512, JSON_THROW_ON_ERROR);
    }

    private function extractReply(array $response): string
    {
        if (!isset($response['candidates'][0]['content']['parts'][0]['text'])) {
            Log::error('Gemini response format error: ', $response);
            throw new RuntimeException("API response did not include the expected content.");
        }
        return (string) $response['candidates'][0]['content']['parts'][0]['text'];
    }

    /**
     * @todo Not production ready - WIP
     * @throws GuzzleException
     * @throws JsonException
     */
    public function imageGeneration(GptChat $gptChat, array $validated): ?array
    {
        //TODO: Use dev/prod credentials
        $projectId = 'kangaroo-dev-5223a';
        $accessToken = '**************************************************************************************************************************************************************************************************************************';
        $url = "https://us-central1-aiplatform.googleapis.com/v1/projects/{$projectId}/locations/us-central1/publishers/google/models/imagegeneration:predict";

        $imageCount = $validated['n'] ?? 1;
        $client = new Client();

        $requestBody = [
            'instances' => [
                [
                    'prompt' => $validated['prompt'],
                ],
            ],
            'parameters' => [
                'sampleCount' => $imageCount,
            ],
        ];

        $response = $client->post($url, [
            'json' => $requestBody,
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ],
        ]);

        $res = json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);
        $images = [];
        foreach ($res['predictions'] as $base64ImgInfo) {
            $imagePath = $this->saveBase64Image($base64ImgInfo);
            $images[] = ['url' => config('app.url') . $imagePath];
        }

        return $images;
    }

    /**
     * @param array $base64ImgInfo
     * @return string
     */
    private function saveBase64Image(array $base64ImgInfo): string
    {
        $base64Image = $base64ImgInfo['bytesBase64Encoded'];
        // Remove the data URI scheme part (e.g., "data:image/jpeg;base64,") if it exists
        $base64Image = preg_replace('/^data:image\/(jpeg|jpg|png);base64,/', '', $base64Image);

        // Decode the base64 string
        $imageData = base64_decode($base64Image);

        if ($imageData === false) {
            throw new RuntimeException("Base64 decoding failed.");
        }

        $imagePath = '/photos/business/' . $this->business->business_pk . '/' . time() . '.png';
        $filePath = public_path() . $imagePath;
        try {
            File::put($filePath, $imageData);
            //TODO: upload to cloud
            return $imagePath;
        } catch (Exception $e) {
            throw new RuntimeException("Failed to save the image: " . $e->getMessage());
        }
    }

    private function getAIModel(array $validated): string
    {
        $allowedModels = ['gemini-2.0-flash', 'gemini-2.0-flash-lite', 'gemini-2.0-flash-thinking-exp-01-21'];

        if (isset($validated['model']) && in_array(trim($validated['model']), $allowedModels, true)) {
            return trim($validated['model']);
        }

        return 'gemini-2.0-flash';
    }
}
