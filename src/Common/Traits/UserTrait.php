<?php

namespace KangarooRewards\Common\Traits;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use KangarooRewards\Common\Helpers\Utils;

trait UserTrait
{
    /**
     * Getting the user ids who have made between balance
     * Used in : Website (BusinessAdmin - Marketing Campaign)
     *
     * @param int $entityTypeFk
     * @param string $startPts
     * @param $endPts
     * @return mixed
     * @adaptation aviEL
     * <AUTHOR>
     * @access public
     */
    public static function campaignCustomersBetweenBalance($entityTypeFk, $startPts, $endPts)
    {
        return DB::table('user_entity_points as uep')
            ->select(array(
                'uep.user_fk',
                'uep.current_active_points',
            ))
            ->join('user as u', 'u.id', '=', 'uep.user_fk')
            ->where('u.enabled', 1)
            ->where('uep.entity_fk', $entityTypeFk)
            ->havingRaw("uep.current_active_points BETWEEN '$startPts' AND '$endPts'")
            ->get();
    }

    /**
     * Getting the user ids who have made more/less balance
     * CASL Flag per business implementation (3.4.0)
     * Used in : Website (BusinessAdmin - Marketing Campaign)
     *
     * @param int $entityTypeFk
     * @param string $trxCon
     * @return mixed
     * @adaptation aviEL
     * <AUTHOR> David
     * @access public
     */
    public static function campaignCustomersBalance($entityTypeFk, $trxCon = '>=1')
    {
        return DB::table('user_entity_points as uep')
            ->select(array(
                'uep.user_fk',
                'uep.current_active_points',
            ))
            ->join('user as u', 'u.id', '=', 'uep.user_fk')
            ->where('u.enabled', 1)
            ->where('uep.entity_fk', $entityTypeFk)
            ->havingRaw('uep.current_active_points' . $trxCon)
            ->get();
    }

    /**
     * Getting the user ids who have made more/less lifetime balance
     * Used in : Website (BusinessAdmin - Marketing Campaign)
     *
     * @param int $entityTypeFk
     * @param string $trxCon
     * @return mixed
     * @adaptation aviEL
     * <AUTHOR> David
     * @access public
     */
    public static function campaignCustomersLifetimeBalance($entityTypeFk, $trxCon = '>=1')
    {
        return DB::table('user_entity_points as uep')
            ->select(array(
                'uep.user_fk',
                'uep.lifetime_balance',
            ))
            ->join('user as u', 'u.id', '=', 'uep.user_fk')
            ->where('u.enabled', 1)
            ->where('uep.entity_fk', $entityTypeFk)
            ->havingRaw('lifetime_balance' . $trxCon)
            ->get();
    }

    /**
     * Getting the user ids who have record in user business notifications
     * Used in : Website (BusinessAdmin - Marketing Campaign)
     *
     * @param array $customers
     * @param string $businessId
     * @return mixed
     * @adaptation aviEL
     * <AUTHOR> David
     * @access public
     */
    public static function campaignCustomersWithNotifications($customers, $businessId)
    {
        $result = DB::table('user as u')
            ->select(['u.id'])
            ->join('user_business_notifications as ubn', 'ubn.user_fk', '=', 'u.id')
            ->where('ubn.business_fk', $businessId)
            ->where('u.enabled', 1)
            ->whereIn('u.id', $customers)
            ->get();

        return Utils::listDataKeys($result, 'id', 'last_trx');
    }

    /**
     * Get users by preferred languages
     * Used in: Marketing Audience
     *
     * <AUTHOR>
     * @since 24/08/2022
     * @param int $businessId
     * @param array $languageIds
     * @return array
     */
    public static function getCustomerByLanguages(int $businessId, array $languageIds) : array
    {
        return User::where('enabled', 1)
            ->where('business_fk', $businessId)
            ->whereIn('language', $languageIds)
            ->pluck('user.id')
            ->toArray();
    }

    /**
     *
     * @param int $businessId
     * @param string $date
     * @param int $customerCriteria
     * @param array $customerTypes
     * @return mixed
     * @since 12/09/2022
     * <AUTHOR>
     */
    public static function getCustomersWithUpcomingBirthday(int $businessId, string $date, int $customerCriteria = 1, array $customerTypes = []): mixed
    {
        $query = User::from('user as u')
            ->join('followers as f', 'f.user_fk', '=', 'u.id');

        if ($customerCriteria == 2) {
            //Customers without tiers
            $query->join('user_business_profile as ubp', function ($join) {
                $join->on('ubp.user_fk', '=', 'u.id')
                    ->whereRaw('(ubp.customer_type is null or ubp.customer_type=0)');
            });

        } elseif ($customerCriteria == 3) {
            // Customers in specific tiers
            $query->join('user_business_profile as ubp', 'ubp.user_fk', '=', 'u.id');
            $query->whereIn('ubp.customer_type', $customerTypes);
        }

        $query->where('u.enabled', 1);
        $query->where('f.entity_fk', $businessId);
        $query->whereRaw('DATE_FORMAT(u.birth_date, "%m-%d") = DATE_FORMAT("'.$date.'", "%m-%d")');

       return $query->pluck('u.id')
           ->toArray();
    }

    /**
     * Check if the customer by either phone or email if already exists
     *
     * @param string $field
     * @param string $value
     * @param int $businessId
     * @return mixed
     */
    public function isExistingCustomer(string $field, string $value, int $businessId): mixed
    {
        return \KangarooRewards\Common\Models\User::where([
            $field => $value,
            'enabled' => 1,
            'business_fk' => $businessId
        ])->exists();
    }

    /**
     * @param int $userId
     * @param array $fields
     * @param int|null $businessId
     * @return mixed
     */
    public function getUserById(int $userId, ?int $businessId = null, array $fields = []): mixed
    {
        if (empty($fields)) {
            $fields = [
                'id', 'username', 'first', 'last', 'phone', 'email', 'business_fk', 'gcm_device_token', 'device_token', 'utc_created'
            ];
        }

        $query = User::select($fields)
            ->where('id', $userId)
            ->where('enabled', 1);

        if ($businessId) {
            $query->where('business_fk', $businessId);
        }

        return $query->first();
    }
}
