<?php

namespace KangarooRewards\Geofencing\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;
use KangarooRewards\Common\Http\Controllers\Controller;
use KangarooRewards\Geofencing\Http\Requests\ListBusinessGeofenceRequest;
use KangarooRewards\Geofencing\Http\Requests\StoreBusinessGeofenceRequest;
use KangarooRewards\Geofencing\Http\Requests\UpdateBusinessGeofenceRequest;
use KangarooRewards\Geofencing\Http\Resources\BusinessGeofenceResource;
use KangarooRewards\Geofencing\Models\BusinessGeofence;
use KangarooRewards\Geofencing\Services\BusinessGeofenceService;

class BusinessGeofenceController extends Controller
{
    public function __construct(protected BusinessGeofenceService $service) {}

    /**
     * @param ListBusinessGeofenceRequest $request
     * @return AnonymousResourceCollection
     */
    public function index(ListBusinessGeofenceRequest $request): AnonymousResourceCollection
    {
        $businessId = $request->getBusiness()->business_pk;
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15);

        $geofences = $this->service->listPaginated($businessId, $perPage, $page);

        return BusinessGeofenceResource::collection($geofences);
    }

    /**
     * Show geofence for business
     * The geofence data will be returned from the request after validation and processing, with the ID retrieved from the route
     *
     * @param ListBusinessGeofenceRequest $request
     * @return BusinessGeofenceResource
     */
    public function show(ListBusinessGeofenceRequest $request): BusinessGeofenceResource
    {
        $geofence = $request->fetchGeofence();
        return new BusinessGeofenceResource($geofence);
    }

    /**
     * @param StoreBusinessGeofenceRequest $request
     * @return BusinessGeofenceResource|JsonResponse
     */
    public function store(StoreBusinessGeofenceRequest $request): BusinessGeofenceResource|JsonResponse
    {
        $business = $request->getBusiness();

        try {
            $geofence = $this->service->create($request->validated(), $business->business_pk);
            return new BusinessGeofenceResource($geofence);
        }  catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e->getMessage(), ['business_id' => $business->business_pk, 'payload' => $request->all()]);
            return $this->errorInternalError();
        }
    }

    /**
     * @param UpdateBusinessGeofenceRequest $request
     * @return BusinessGeofenceResource|JsonResponse
     */
    public function update(UpdateBusinessGeofenceRequest $request): BusinessGeofenceResource|JsonResponse
    {
        $business = $request->getBusiness();

        try {
            $geofence = $this->service->update($request->fetchGeofence(), $request->validated());
            return new BusinessGeofenceResource($geofence);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e->getMessage(), ['business_id' => $business->business_pk, 'payload' => $request->all()]);
            return $this->errorInternalError();
        }
    }

    /**
     * Enable / disable geofence
     * @param UpdateBusinessGeofenceRequest $request
     * @return JsonResponse
     */
    public function toggle(UpdateBusinessGeofenceRequest $request): JsonResponse
    {
        $this->service->toggleActiveStatus($request->fetchGeofence());
        return $this->respondNoContent();
    }
}