<?php

namespace App\Http\Resources;

class CategoryCollection extends ResourceCollection
{
    /**
     * Transform the resource into a JSON array.
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            'data' => $this->collection->transform(function ($item) {
                return (new CategoryResource($item))->response()->getData()->data;
            }),
            // 'links' => [
            //     'self' => 'link-value',
            // ],
        ];
    }
}
