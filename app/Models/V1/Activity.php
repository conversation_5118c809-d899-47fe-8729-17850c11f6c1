<?php

namespace App\Models\V1;

use Illuminate\Database\Eloquent\Model;

/**
 * @property string description
 * @property string utc_publish
 * @property int transaction_fk
 * @property int hidden
 * @property int units_exchange
 * @property int|null punch_offer_fk
 * @property int timezone_mysql_fk
 */
class Activity extends Model
{
    protected $table = 'activity';
    protected $connection = 'tenant';

    /**
     * The name of the "created at" column.
     *
     * @var string
     */
    const CREATED_AT = 'utc_created';

    /**
     * The name of the "updated at" column.
     *
     * @var string
     */
    const UPDATED_AT = 'utc_updated';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'description', 'units_exchange', 'transaction_fk', 'hidden',
        'timezone_mysql_fk', 'punch_offer_fk', 'amount',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
    ];



    /**
     * Transform Item
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function transformItem($social)
    {
        return [

        ];
    }
}
