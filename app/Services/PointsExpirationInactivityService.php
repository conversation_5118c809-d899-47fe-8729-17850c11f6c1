<?php

namespace App\Services;

use App\Helpers\Campaign\PointsExpirationCampaignBuilder;
use App\Helpers\Dto\CampaignData;
use App\Models\PointsExpiration;
use App\Models\PointsExpirationJob;
use App\Models\PointsExpirationTransaction;
use App\Models\V1\Activity;
use App\Models\V1\Business;
use App\Models\V1\BusinessBranch;
use App\Models\V1\BusinessRules;
use App\Models\V1\Campaign;
use App\Models\V1\Notification;
use App\Models\V1\Transaction;
use App\Models\V1\TransactionDetails;
use App\Models\V1\UserEntityPoints;
use App\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use KangarooRewards\Common\Models\UserEntityPermission;

class PointsExpirationInactivityService
{
    private $rules;

    /**
     * @var string
     */
    private $period;

    /**
     * @var Business
     */
    private $business;
    /**
     * @var bool
     */
    private $writeMode;

    /**
     * @var mixed
     */
    private $ruleConfig;

    public function __construct(BusinessRules $rules, bool $writeMode = false)
    {
        $this->rules = $rules;
        $this->writeMode = $writeMode;

        $ruleConfig = $rules->getPointsExpiryPolicyInactivityConfig();

        if (!$ruleConfig) {
            throw new \RuntimeException('Points Expiration Not Configured');
        }

        $this->period = $ruleConfig['period']['value'] . $ruleConfig['period']['designator']; // 1D, 1M, 1Y

        $this->ruleConfig = $ruleConfig;

        $this->business = Business::findByPk($rules->business_fk);
    }

    public function handle()
    {
        $branchIds = BusinessBranch::allowedBranchIdsForBusiness($this->business->business_pk);

        list($periodStart, $periodEnd) = $this->getPeriod($this->period);

        $expirePointsRecords = Transaction::customersNoTransactionsInPeriod(
            $this->business->business_pk,
            $branchIds,
            $periodStart,
            $periodEnd
        );

        if (!$this->writeMode) {
            return;
        }

        foreach ($expirePointsRecords as $expirePointsRecord) {
            try {
                $this->expirePointsForUser($expirePointsRecord);
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' ' . $e, [
                    'businessId' => $this->business->business_pk,
                ]);
            }
        }
    }

    /**
     * @param $daysBefore
     * @param $internal
     * @param $includeCustomer
     * @return array
     * @throws \Exception
     */
    public function getPtsExpireXDaysBefore($daysBefore, bool $internal = false, $includeCustomer = false): array
    {
        $branchIds = BusinessBranch::idsForBusiness($this->business->business_pk, true);

        $blockAndPeriod = $this->getPeriod($this->period, $daysBefore);

        list($periodStart, $periodEnd) = $blockAndPeriod;

        array_unshift($blockAndPeriod, '');// prepend empty string to array, there is no block values
        array_unshift($blockAndPeriod, '');

        $datetime1 =  clone $periodEnd;
        $datetime2 = new \DateTime();
        $interval = $datetime1->diff($datetime2);

        $usersNoTrx = Transaction::customersNoTransactionsInPeriod(
            $this->business->business_pk,
            $branchIds,
            $periodStart,
            $periodEnd,
            $includeCustomer
        );

        // Transform for response
        $expirePointsRecords = [];
        foreach ($usersNoTrx as $item) {
            $expirePointsRecord = [
                'customer_id' => bin2hex($item->user_uid),
                'expired_points' => $item->current_active_points,
            ];
            if ($internal) {
                // Do not expose externally the internal user ID
                $expirePointsRecord['_user_id'] = $item->user_fk;
            }
            if ($includeCustomer) {
                $expirePointsRecord['customer'] = [
                    'email' => $item->email,
                    'phone' => $item->phone,
                    'first_name' => $item->first,
                    'last_name' => $item->last,
                ];
            }

            $expirePointsRecords[] = $expirePointsRecord;
        }

        return [
            'period' => $blockAndPeriod,
            'interval_days' => $interval->days,
            '_interval' => $interval,
            '_config' => ['period' => $this->period],
            'results' => $expirePointsRecords,
        ];
    }

    /**
     * @param $period
     * @param int $daysBefore
     * @return \DateTime[]
     * @throws \Exception
     */
    private function getPeriod($period, int $daysBefore = 0): array
    {
        $periodEnd = new \DateTime();

        if ($daysBefore > 0) {
            // We want to know if no activity x days before the expiration, this will be in the future
            $periodEnd = (new \DateTime())->add(new \DateInterval('P' . $daysBefore . 'D'));
        }

        $periodStart = clone $periodEnd;
        $periodStart->sub(new \DateInterval('P' . $period));

        //dd($periodStart, $periodEnd, $period);

        return [$periodStart, $periodEnd];
    }


    /**
     * Main business logic to expire points for a user
     *
     * @param object $expirePointsRecord
     * @return void
     * @throws \Exception
     */
    private function expirePointsForUser($expirePointsRecord)
    {
        if (!$this->writeMode) {
            return;
        }

        $defaultBranch = $this->business->defaultBranch();

        if (!$defaultBranch) {
            throw new \RuntimeException('Branch not found');
        }

        $userId = $expirePointsRecord->user_fk;

        $userModel = User::find($userId);

        $userBalance = UserEntityPoints::forBusiness(
            $userModel->id,
            $this->business->business_pk,
            $this->business->entity_type_fk
        );

        if (!$userBalance) {
            return;
        }

        $expirePoints = (int) $userBalance->current_active_points;
        if ($expirePoints <= 0) {
            return;
        }

        DB::connection('tenant')->beginTransaction();
        try {
            $transaction = new Transaction();
            $transaction->user_fk = $userId;
            $transaction->entity_fk = $defaultBranch->business_branch_pk;
            $transaction->utc_created = date('Y-m-d H:i:s');
            $transaction->transaction_type_fk = TRX_TYPE_POINTS_PURCHASE;
            $transaction->transaction_sub_type_fk = TRX_SUBTYPE_POINTS_EXPIRATION;
            $transaction->entity_type_fk = BRANCH_POINTS;
            $transaction->timezone_mysql_fk = $defaultBranch->branch_timezone_fk;
            $transaction->save();

            $transactionId = $transaction->transaction_pk;

            $balance = UserEntityPoints::decreaseBalance(
                $userModel,
                $this->business,
                $expirePoints
            );

            TransactionDetails::create([
                'transaction_fk' => $transactionId,
                'status' => 1,
                'points' => -1 * $expirePoints,
                'amount' => 0,
                'points_balance' => (int) $balance->current_active_points,
                'giftcard_balance' => (float) $balance->giftcard,
            ]);

            Activity::create([
                'description' => 'ACTIVITY_POINTS_EXPIRATION',
                'units_exchange' => $expirePoints,
                'transaction_fk' => $transactionId,
                'hidden' => $this->rules->silo_flag,
                'timezone_mysql_fk' => $defaultBranch->branch_timezone_fk,
            ]);

            Notification::create([
                'notificationRequester' => NOTIF_REQ_POINTS_EXPIRATION_TITLE,
                'notification_text' => 'NOTIF_TEXT_POINTS_EXPIRATION',
                'notificationType' => NOTIFICATION_TYPE_NOTIFICATION,
                'units_exchange' => $expirePoints,
                'user_receiver_fk' => $userId,
                'user_sender_fk' => $userId,
                'business_fk' => $this->business->business_pk,
                'business_branch_fk' => $defaultBranch->business_branch_pk,
                'timezone_mysql_fk' => $defaultBranch->branch_timezone_fk,
            ]);

            DB::connection('tenant')->commit();
        } catch (\LogicException | \Exception $e) {
            DB::connection('tenant')->rollBack();
            Log::error(__METHOD__ . ' ' . $e, [
                'businessId' => $this->business->business_pk,
                'userId' => $userId,
            ]);
        }

        Log::info(__METHOD__, [
            'businessId' => $this->business->business_pk,
            'userId' => $userId,
            'expirePoints' => $expirePoints,
        ]);
    }

    private function createPointsExpirationCampaign($expirePointsJob)
    {
        try {
            $defaultBranch = $this->business->defaultBranch();

            if (!$defaultBranch) {
                throw new \RuntimeException('Branch not found');
            }
            $businessId = $this->business->business_pk;

            $emailBody = "<p>{POINTS_EXPIRED} points have expired - {$this->business->name}.</p>";
            $emailBody .= "<p>Your new point balance is {CUSTOMER_BALANCE} </p>";

            $smsBody = "{POINTS_EXPIRED} points have expired - {$this->business->name}.";
            $smsBody .= "Your new point balance is {CUSTOMER_BALANCE}";

            $emailSubject = 'Points Expiration';

            $data = [
                'name' => 'Points Expiration Campaign ' . ' - ' . date('d/m/Y'),
                'send_by' => 1, //email_sms:1, push:2, email:3, sms:4
                'send_by_priority' => 'email,sms',
                'targeted_customer_criteria' => [
                    [
                        'id' => 54, // points expiry, but not auto
                        'days' => 0,
                        'branch_id' => null,
                    ]
                ],
                'auto' => false,
                'silent' => false,
                'exclude_dormants' => true,
                'exclude_customer_in_previous_campaign' => false,
                'email_subject' => $emailSubject,
                'email_body' => $emailBody,
                'sms_body' => $smsBody,
                'push_title' => $emailSubject,
                'push_text' => '',
                'push_link' => null,
                'delivery_date' => date('Y-m-d H:i:s'),
                'expiry_date' => null,
                'send_to_invalid_phone_number' => false,
                'include_offer_link_in_sms' => false,
                'include_referral_link_in_sms' => false,
                'offers' => [],
                'options' => ['expire_points_job_id' => $expirePointsJob->id],
            ];

            $userEntity = UserEntityPermission::select('entity_fk', 'entity_type_fk', 'user_fk')
                ->where('entity_fk', $businessId)
                ->where('entity_type_fk', 1)
                ->first();

            $employeeUser = User::find($userEntity->user_fk);

            $campaignData = new CampaignData($data);
            $campaignBuilder = new PointsExpirationCampaignBuilder($campaignData, $this->business, $employeeUser);
            $campaignBuilder->validate();

            return $campaignBuilder->saveCampaign()
                ->generateShortUrl()
                ->getCampaign();
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, [
                'business_id' => $this->business->business_pk,
            ]);
        }
        return null;
    }
}
