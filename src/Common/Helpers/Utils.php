<?php /** @noinspection Annotator */
namespace KangarooRewards\Common\Helpers;

use DateTime;
use DateTimeZone;
use DOMDocument;
use KangarooRewards\Common\Models\Country;
use KangarooRewards\Common\Models\TimezoneMysql;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberUtil;

/**
 * This is the Utils class that holds common functions that are not related to a specific model
 *
 *
 */
class Utils
{
    public const RESELLER_KANGAROO = 'KANGAROO_REWARDS';
    public const RESELLER_WL1 = 'WL1_LOYALTEE_CLUB';

    public static function generateUserAgent(string $extension): string
    {
        $system = php_uname('s'); // System information (e.g., Linux, Windows, MacOS, iPhone etc.)
        $platform = 'Members API/1.0'; //(e.g., Members API, Business Portal, etc.)
        $platformDetails = 'Laravel/' . app()->version(); // (e.g., <PERSON><PERSON>, <PERSON><PERSON>, etc.)
        //$platformDetails .= '; PHP/' . phpversion();
        $platformDetails .= '; ID 10';

        // Base format for the user agent
        $userAgentFormat = "KangarooRewards/1.0 (%s) %s (%s) %s";

        // Create the user agent string with the passed parameters
        return sprintf(
            $userAgentFormat,
            $system,
            $platform,
            $platformDetails,
            $extension // Console, Job, API/1.0, NBP/1.27.3, EcomWidget/1.0, Mobile/1.0, Web/1.0
        );
    }

    /**
     * Sanitize HTML content to prevent XSS attacks. Removes script, iframe, object, embed tags and event attributes.
     *
     *
     * @param string $html
     * @return string
     * @throws \Random\RandomException
     */
    public static function sanitizeHtmlContent(string $html): string
    {
        if (trim($html) === '') {
            return '';
        }

        // Create DOMDocument with UTF-8 encoding
        $dom = new DOMDocument('1.0', 'UTF-8');
        $dom->strictErrorChecking = false;
        $dom->formatOutput = false;

        // Create a wrapper with unique ID
        $wrapperId = 'sanitize_wrapper_' . bin2hex(random_bytes(8));
        $wrappedHtml = '<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><div id="'
            . $wrapperId . '">' . $html . '</div>';

        // Load HTML with error suppression
        @$dom->loadHTML($wrappedHtml, LIBXML_NOERROR | LIBXML_NOWARNING | LIBXML_HTML_NODEFDTD);

        // Remove script elements
        while (($scripts = $dom->getElementsByTagName('script')) && $scripts->length > 0) {
            $scripts->item(0)->parentNode->removeChild($scripts->item(0));
        }

        // Remove iframe elements
        while (($iframes = $dom->getElementsByTagName('iframe')) && $iframes->length > 0) {
            $iframes->item(0)->parentNode->removeChild($iframes->item(0));
        }

        // Remove object elements
        while (($objects = $dom->getElementsByTagName('object')) && $objects->length > 0) {
            $objects->item(0)->parentNode->removeChild($objects->item(0));
        }

        // Remove embed elements
        while (($embeds = $dom->getElementsByTagName('embed')) && $embeds->length > 0) {
            $embeds->item(0)->parentNode->removeChild($embeds->item(0));
        }

        // Remove event attributes and javascript: URIs
        $allElements = $dom->getElementsByTagName('*');
        for ($i = 0; $i < $allElements->length; $i++) {
            $element = $allElements->item($i);
            $attributes = $element->attributes;

            // Collect attributes to remove
            $toRemove = [];
            for ($j = 0; $j < $attributes->length; $j++) {
                $attr = $attributes->item($j);
                $name = strtolower($attr->nodeName);
                $value = trim($attr->nodeValue);

                // Remove event attributes (onclick, onload, etc.)
                if (str_starts_with($name, 'on')) {
                    $toRemove[] = $name;
                } elseif (preg_match('/^\s*javascript:/i', $value)) {
                    // Remove attributes with javascript: URIs
                    $toRemove[] = $name;
                }
            }

            // Remove flagged attributes
            foreach ($toRemove as $name) {
                $element->removeAttribute($name);
            }
        }

        // Extract sanitized content from wrapper
        $container = $dom->getElementById($wrapperId);
        if (!$container) {
            return '';
        }

        $sanitized = '';
        foreach ($container->childNodes as $child) {
            $sanitized .= $dom->saveHTML($child);
        }

        // Replace http:// with https://
        $sanitized = preg_replace('#http://([\w\d\.]+)#i', 'https://$1', $sanitized);

        return $sanitized;
    }

    /**
     * Clean a string and keep digits only
     *
     * Used in: Oauth(Login)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function normalizeDigitsOnly($string)
    {
        return preg_replace('/[^0-9]+/', '', $string);
    }

    /**
     * Log a message to the console or log file
     *
     * @version January 2017 release V4.6.0
     * @since 4.6.0
     * <AUTHOR>
     * @access public
     * @param string $message
     * @param string $category
     * @param string $level
     * @return void
     */
    public static function logMessage($message, $category = '', $level = 'MESSAGE_LEVEL_INFO')
    {
        if ($level == 'MESSAGE_LEVEL_INFO') {
            \Log::info($message);
        }
        if ($level == 'MESSAGE_LEVEL_ERROR') {
            \Log::error($message);
        }
        if ($level == 'MESSAGE_LEVEL_WARNING') {
            \Log::warning($message);
        }
    }

    /**
     * TIMEZONE - Globalization implementation
     * Getting phone number formatted for a specific country
     * INTERNATIONAL - +****************
     * NATIONAL (*************
     * E164  +41446681800
     * RFC3966 is as per INTERNATIONAL format, but with all spaces and other separating symbols
     *
     * Used in : Website(Display phone, change phone)
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @since 3.1.1
     * @access public
     * @param string $phone - the phone number in any format with + or without
     * @param int|string $countryFk - country ID or Country ISO 2 Code
     * @param string $format - NATIONAL, INTERNATIONAL, E164, RFC3966
     * @return string
     * @throws NumberParseException
     */
    public static function getFormattedPhoneNumber($phone, $countryFk, $format = 'NATIONAL')
    {
        if (!$countryFk || !$phone) {
            return $phone;
        }

        $countryCode2 = $countryFk;
        if (is_numeric($countryFk)) {
            $countryCode2 = Country::find($countryFk)->code;
        }

        $phoneUtil = PhoneNumberUtil::getInstance();
        $phonenumberFormatted = $phoneUtil->parse($phone, $countryCode2);

        if ($format == 'NATIONAL') {
            $format = \libphonenumber\PhoneNumberFormat::NATIONAL;
        } elseif ($format == 'INTERNATIONAL') {
            $format = \libphonenumber\PhoneNumberFormat::INTERNATIONAL;
        } elseif ($format == 'E164') {
            $format = \libphonenumber\PhoneNumberFormat::E164;
        } elseif ($format == 'RFC3966') {
            $format = \libphonenumber\PhoneNumberFormat::RFC3966;
        } else {
            $format = \libphonenumber\PhoneNumberFormat::INTERNATIONAL;
        }

        $phonenumberFormatted = $phoneUtil->format($phonenumberFormatted, $format);

        return $phonenumberFormatted;
    }

    /**
     * Validate phone number with libphonenumber library
     *
     * Used in : Website(Login)
     *
     * @param string $phone
     * @param string $code2 - country code ISO 2
     * @return bool
     * <AUTHOR>
     * @access public
     * @version 1.0
     */
    public static function isValidPhone($phone, $code2)
    {
        $trace = debug_backtrace();

        if (!$phone) {
            return false;
        }

        $calledFrom = $trace[1]['function'];

        try {
            $phoneUtil = PhoneNumberUtil::getInstance();

            $code2 = strtoupper($code2);
            $phoneNumberProto = $phoneUtil->parse($phone, $code2);
            $type = $phoneUtil->getNumberType($phoneNumberProto);

            $isValidPhone = $phoneUtil->isValidNumber($phoneNumberProto);

            if (!$isValidPhone) {
                \Log::warning(__METHOD__, ['message' => 'Invalid Phone', 'phone' => $phone, 'code2' => $code2, 'calledFrom' => $calledFrom]);
            }

            return $isValidPhone;
        } catch (NumberParseException $e) {
            \Log::warning(__METHOD__, ['message' => 'Phone parse exception' . $e->getMessage(), 'phone' => $phone, 'code2' => $code2, 'calledFrom' => $calledFrom]);
        }

        return false;
    }

    /**
     * Converts a Timestamp from a time zone to UTC
     *
     * Used in : Website(Business Admin)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @param string $dateTimeToConvert
     * @param int $timezoneId
     * @param string $format
     * @return string
     * @throws \Exception
     */
    public static function utcDateTime($dateTimeToConvert, $timezoneId, $format = 'Y-m-d H:i:s')
    {
        $serverTimeZone = date_default_timezone_get();
        $timeZoneName = TimezoneMysql::findByPk($timezoneId)->name;
        $dateTime = new DateTime($dateTimeToConvert, new DateTimeZone($timeZoneName));
        $dateTime->setTimezone(new DateTimeZone($serverTimeZone));

        //print_r($dateTime); die;
        return $dateTime->format($format);
    }

    /**
     * Converts a Timestamp from UTC to the Tmz UTC of the front-end user
     * The Tmz UTC of the user is received as a param
     *
     * Used in : Merchant Android app - iPhone/android App - Website
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @param string $timeZoneName
     * @param string $dateTimeToConvert
     * @param string $format
     * @return string
     * @throws \Exception
     */
    public static function convertDateTimeToUserTmz($timeZoneName, $dateTimeToConvert, $format = 'Y-m-d H:i:s')
    {
        $serverTimeZone = date_default_timezone_get();
        $dateTime = new DateTime($dateTimeToConvert, new DateTimeZone($serverTimeZone));
        $dateTime->setTimezone(new DateTimeZone($timeZoneName));
        return $dateTime->format($format);
    }

    /**
     * Short url with custom domain
     * First it will short using own solution, in case it failed, will short with Google and if limit exceded or throws an error than short with bitly
     * Update: 2015-05-20 (Valentin), Additional release V3.2.5.1, using shortUrlKg as a main function for shortening
     * Used in : Website(Marketing Campaign) / CRON Job (Sending Business Contacts Invitations) / ALL platforms
     *
     * @version Mar - Apr 2015 release V3.2.5
     * <AUTHOR>
     * @access public
     * @param string $longUrl - the url to short
     * @param string $platformName
     * @param string $title - (optional) page title when redirecting from short url to long url
     * @param string $keyword - (optional) keyword instead of random string
     * @return array
     * @throws \Exception
     */
    public static function shortUrlKg($longUrl, $platformName = "WEBSITE", $title = 'Kangaroo Rewards Survey', $keyword = null)
    {
        $result = [];
        try {
            $username = config('app.SHORT_URL_USERNAME');
            $password = config('app.SHORT_URL_PASSWORD');
            $api_url = config('app.SHORT_URL_API_URL');
            $format = 'json';// output format: 'json', 'xml' or 'simple'
            // Init the CURL session
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url);
            curl_setopt($ch, CURLOPT_HEADER, 0);// No header in the result
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);// Return, do not echo result
            curl_setopt($ch, CURLOPT_POST, 1);// This is a POST request
            curl_setopt($ch, CURLOPT_POSTFIELDS, array( // Data to POST
                'url' => "$longUrl",
                'keyword' => "$keyword",
                'title' => "$title",
                'format' => "$format",
                'action' => 'shorturl',
                'username' => "$username",
                'password' => "$password",
            ));// Fetch and return content
            $data = curl_exec($ch);
            if (curl_errno($ch)) {
                \Log::error('Failed to short url with custom domain CURL error=' . curl_error($ch));
                throw new \Exception(curl_error($ch));
            }
            curl_close($ch);
            $result = (array)json_decode($data);//print_r($data); die;
            if (isset($result['status']) && $result['status'] == 'success') {
                return ['status' => 'OK', 'shortUrl' => $result['shorturl']];
            } else {
                throw new \Exception('Unable to short URL');
            }
        } catch (\Exception $e) {
            \Log::error('Error Common Helpers Utils::shortUrlKg ', ["error"=>$e, "info"=>json_encode($result)]);
        }
    }

    public static function listDataKeys($array, $from, $to = null, $group = null)
    {
        $result = [];
        foreach ($array as $element) {
            $key = static::getValue($element, $from);
            $result[] = $key;
        }
        return $result;
    }


    public static function getValue($array, $key, $default = null)
    {
        if ($key instanceof \Closure) {
            return $key($array, $default);
        }
        if (is_array($key)) {
            $lastKey = array_pop($key);
            foreach ($key as $keyPart) {
                $array = static::getValue($array, $keyPart);
            }
            $key = $lastKey;
        }
        if (is_array($array) && (isset($array[$key]) || array_key_exists($key, $array))) {
            return $array[$key];
        }
        if (($pos = strrpos($key, '.')) !== false) {
            $array = static::getValue($array, substr($key, 0, $pos), $default);
            $key = substr($key, $pos + 1);
        }
        if (is_object($array)) {
            // this is expected to fail if the property does not exist, or __get() is not implemented
            // it is not reliably possible to check whether a property is accessible beforehand
            return $array->$key;
        } elseif (is_array($array)) {
            return (isset($array[$key]) || array_key_exists($key, $array)) ? $array[$key] : $default;
        }
        return $default;
    }

    public static function getPhotosBucketName(): string
    {
        $reseller = config('app.RESELLER_ENV_ID', 'KANGAROO_REWARDS');

        if ($reseller === self::RESELLER_KANGAROO) {
            $bucketName = 'kng-pic';
            if (config('app.env') === 'staging') {
                $bucketName = 'kng-stg-pic';
            }
            return $bucketName;
        }

        if ($reseller === self::RESELLER_WL1) {
            $bucketName = 'kng-wl1-pic';
            if (config('app.env') === 'staging') {
                $bucketName = 'kng-wl1-stg-pic';
            }
            return $bucketName;
        }

        throw new \RuntimeException('Undefined Reseller Environment Identifier');
    }

    /**
     * Get the national phone number with the country code after parsing it
     *
     * @param $phone
     * @param null $phoneCountryCode
     * @return array
     * <AUTHOR>
     */
    public static function getNationalPhoneWithCode($phone, $phoneCountryCode = null): array
    {
        $trace = debug_backtrace();
        $calledFrom = $trace[1]['function'] ?? null;

        $phoneCountryCode = empty($phoneCountryCode) ? null : $phoneCountryCode;

        try {
            if (empty($phoneCountryCode)) {
                // Normalize the phone number by ensuring it starts with '+'
                $phone = Utils::normalizePhoneNumber($phone);
            } else {
                $phoneCountryCode = strtoupper($phoneCountryCode);
            }

            $phoneUtil = PhoneNumberUtil::getInstance();
            $phoneNumberFormatted = $phoneUtil->parse($phone, $phoneCountryCode);

            if ($phoneUtil->isValidNumber($phoneNumberFormatted) && $phoneNumberFormatted->hasCountryCode()) {
                return [
                    'country_code' => $phoneNumberFormatted->getCountryCode(),
                    'national_number' => $phoneNumberFormatted->getNationalNumber()
                ];
            }

            return [];
        } catch (NumberParseException|\Exception $e) {
             \Log::warning(__METHOD__, ['message' => 'Phone parse exception' . $e->getMessage(), 'phone' => $phone, 'code2' => $phoneCountryCode, 'calledFrom' => $calledFrom]);

             return [];
        }
    }

    /**
     * Normalize a phone number by ensuring it starts with a '+'.
     *
     * If the phone number starts with '00' but does not include a '+', replace '00' with '+'.
     * If the phone number does not start with a '+', add '+' at the beginning.
     *
     * @param string $phoneNumber
     * @return string
     */
    public static function normalizePhoneNumber(string $phoneNumber): string
    {
        // Remove any whitespace from the phone number
        $phoneNumber = trim($phoneNumber);

        // Check if the phone number starts with '00' but does not include '+'
        if (str_starts_with($phoneNumber, '00') && !str_starts_with($phoneNumber, '+')) {
            // Replace '00' at the beginning with '+'
            $phoneNumber = '+' . substr($phoneNumber, 2);
        } elseif (!str_starts_with($phoneNumber, '+')) {
            // Add '+' at the beginning if it doesn't include '+'
            $phoneNumber = '+' . $phoneNumber;
        }

        return $phoneNumber;
    }

    /**
     * Check if the given value is a valid hexadecimal string that contains both letters and numbers
     * @param $value
     * @return bool
     */
    public static function isValidHexaDecimal($value): bool
    {
        return preg_match('/^(?=.*[a-fA-F])(?=.*\d)[a-fA-F0-9]+$/', $value) === 1;
    }

    /**
     * @param int $digits
     * @return string
     */
    public static function randomNumberStr(int $digits = 4): string
    {
        $number = '';
        for ($i = 0; $i < $digits; $i++) {
            $number .= mt_rand(0, 9);
        }
        return $number;
    }

    /**
     * Generate random number
     * @param int $digits
     * @return string
     */
    public static function randomNumber(int $digits = 4): string
    {
        return abs(mt_rand(pow(10, $digits - 1), pow(10, $digits) - 1));
    }
}
