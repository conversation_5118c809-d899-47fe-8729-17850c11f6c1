<?php

/**
 * Created by <PERSON><PERSON><PERSON>
 * Date: 11/08/2023
 */

namespace KangarooRewards\RecommendationFeed\Triggers;

use KangarooRewards\RecommendationFeed\Exceptions\TriggerHandleException;
use Kang<PERSON>Rewards\RecommendationFeed\Services\{
    RecommendationService
};
use KangarooRewards\Common\Services\UserService;
use KangarooRewards\RecommendationFeed\Contracts\TriggerInterface;

class UpdateIfBlogsExist implements TriggerInterface
{
    const NAME = 'Update If Blog exists';
    const RECOMMENDATION_TYPE = RECOMMENDATION_TYPE_BLOG;

    protected $businessId;
    protected $recommendation;

    public function __construct(RecommendationService $recommendationService)
    {
        return $this->recommendation = $recommendationService;
    }
        
    public function setBusinessId(int $businessId): void
    {
        $this->businessId = $businessId;
    }

    public function getName(): string
    {
        return self::NAME;
    }

    public function getType(): int
    {
        return self::RECOMMENDATION_TYPE;
    }

    public function shouldRun()
    {
        //if new blog posts exists
        //if user has no prior set recommendation for each blog post.
        $recommendationIdsCount = $this->recommendation
            ->getPerType(RECOMMENDATION_TYPE_BLOG)
            ->where('publication_start_date', '<', now())
            ->where('publication_end_date', '>', now())
            ->pluck('id')
            ->count();

        return $recommendationIdsCount > 0;
    }
    
    public function handle($userIds)
    {
        try {

            $blogRecommendationId = $this->recommendation
                ->getPerType(RECOMMENDATION_TYPE_BLOG)
                ->where('publication_start_date', '<', now())
                ->where('publication_end_date', '>', now())
                ->pluck('id');

            foreach($blogRecommendationId as $recommendationId) {
                foreach($userIds as $userId) {
                    $this->recommendation
                    ->attachToUser($userId, $recommendationId);
                }
            }
        } catch (\Exception $e) {

                \Log::error(__METHOD__, [
                    'data' => [
                        'recommendation_name' => $this->getName(),
                        'user_id' => $userIds
                    ],
                    'e' => $e
                ]);

            throw new TriggerHandleException('Error in handle: ' . $e->getMessage());
        }
    }
}

