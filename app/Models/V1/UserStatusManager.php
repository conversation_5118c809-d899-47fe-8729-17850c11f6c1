<?php

namespace App\Models\V1;

use App\Jobs\TransactionalMailMessage;
use App\Jobs\TransactionalTextMessage;
use App\Models\MessageQueue;
use App\Traits\BusinessRulesTrait;
use DB;
use Eloquent;
use App\Mail\Email;
use App\Helpers\V1\Utils;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class UserStatusManager extends Eloquent
{
    protected $table = 'user_status_manager';
    protected $connection = 'tenant';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['current_status', 'new_email_phone', 'user_fk', 'status_manager_fk',
        'platform_solution_fk', 'country_fk', 'timezone_mysql_fk', 'old_email_phone'
    ];

    public static function createForUser($user)
    {
        // User Status Manager Records
        $statusAttributes = [
            'new_email_phone' => '',
            'user_fk' => $user->id,
            'current_status' => 1,
            'status_manager_fk' => STATUS_MANAGER_PIN_CODE_AUTO_GENERATED,
            'platform_solution_fk' => $user->platform_solution_fk,
            'timezone_mysql_fk' => $user->timezone_mysql_fk,
            'country_fk' => $user->country_phone_fk,
            'utc_created' => (new \DateTime())->format('Y-m-d H:i:s'),
        ];

        //create user status manager
//        UserStatusManager::create($statusAttributes);

        //IMPORTANT this creates deadlocks
        //changing the previous status for a user
//        UserStatusManager::where('user_fk', $user->id)
//            ->update(['current_status' => 0]);

        if ($user->email && $user->phone) {
            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_EMAIL_NOT_VERIFIED;
            $statusAttributes['new_email_phone'] = $user->email;
            UserStatusManager::create($statusAttributes);
            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_PHONE_NOT_VERIFIED;
            $statusAttributes['new_email_phone'] = $user->phone;
            UserStatusManager::create($statusAttributes);
        } elseif ($user->email) {
            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_EMAIL_NOT_VERIFIED;
            $statusAttributes['new_email_phone'] = $user->email;
            UserStatusManager::create($statusAttributes);
        } elseif ($user->phone) {
            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_PHONE_NOT_VERIFIED;
            $statusAttributes['new_email_phone'] = $user->phone;
            UserStatusManager::create($statusAttributes);
        } else {
            throw new \Exception("No phone or email provided", 1);
        }
    }


    /**
     * 32 - EMAIL_NOT_VERIFIED -
     * 33 - EMAIL_VERIFIED
     * 40 - ADD_EMAIL_REQUEST
     * 42 - ADD_EMAIL_VERIFIED
     * 44 - ADD_EMAIL_REQUEST_CANCEL
     * 46 - ADD_EMAIL_REQUEST_RESEND
     * 48 - CHANGE_EMAIL_REQUEST -
     * 50 - CHANGED_EMAIL_VERIFIED - verified
     * 52 - CHANGE_EMAIL_REQUEST_CANCEL
     * 54 - CHANGE_EMAIL_REQUEST_RESEND
     * 58 - EMAIL_NOT_VERIFIED_INVITED_BYSTORE
     * 59 - EMAIL_VERIFIED_INVITED_BYSTORE
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function getEmailsForUser($userId)
    {
        $r = UserStatusManager::from('user_status_manager as usm')
            ->select(['usm.new_email_phone', 'usm.utc_created', 'sm.status_name',
                'status_manager_fk', 'country_fk',
            ])
            ->join('status_manager as sm', 'sm.status_manager_pk', 'usm.status_manager_fk')
            ->where('user_fk', $userId)
            ->whereIn('status_manager_fk', [32, 33, 40, 42, 44, 46, 48, 50, 52, 54, 58, 59])
            ->whereNotNull('usm.new_email_phone')
            ->where('usm.new_email_phone', '<>', '')
            ->orderBy('usm.user_status_manager_pk', 'DESC')
            ->get();

        // dd($r);
        return $r;
    }

    /**
     * 30 - PHONE_NOT_VERIFIED
     * 31 - PHONE_VERIFIED
     * 39 - ADD_PHONE_REQUEST
     * 41 - ADD_PHONE_VERIFIED
     * 43 - ADD_PHONE_REQUEST_CANCEL
     * 45 - ADD_PHONE_REQUEST_RESEND
     * 47 - CHANGE_PHONE_REQUEST
     * 49 - CHANGED_PHONE_VERIFIED
     * 51 - CHANGE_PHONE_REQUEST_CANCEL
     * 53 - CHANGE_PHONE_REQUEST_RESEND
     * 56 - PHONE_NOT_VERIFIED_INVITED_BYSTORE
     * 57 - PHONE_VERIFIED_INVITED_BYSTORE
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function getPhonesForUser($userId)
    {
        $r = UserStatusManager::from('user_status_manager as usm')
            ->select(['usm.new_email_phone', 'usm.utc_created', 'sm.status_name',
                'status_manager_fk', 'country_fk',
            ])
            ->join('status_manager as sm', 'sm.status_manager_pk', 'usm.status_manager_fk')
            ->where('user_fk', $userId)
            ->whereIn('status_manager_fk', [30, 31, 39, 41, 43, 45, 47, 49, 51, 53, 56, 57])
            ->whereNotNull('usm.new_email_phone')
            ->where('usm.new_email_phone', '<>', '')
            ->orderBy('usm.user_status_manager_pk', 'DESC')
            ->get();

        // dd($r);
        return $r;
    }

    public static function getLastStatusForEmail($email, $userId)
    {
        $result = UserStatusManager::where('new_email_phone', $email)
            ->where('user_fk', $userId)
            ->select(['new_email_phone', 'utc_created', 'status_manager_fk'])
            ->orderBy('user_status_manager_pk', 'DESC')
            ->first();

        if (!$result) {
            throw (new ModelNotFoundException)->setModel(__CLASS__);
        }

        return $result;
    }

    public static function getLastStatusForPhone($phone, $userId)
    {
        $result = UserStatusManager::where('new_email_phone', $phone)
            ->where('user_fk', $userId)
            ->select(['new_email_phone', 'utc_created', 'status_manager_fk'])
            ->orderBy('user_status_manager_pk', 'DESC')
            ->first();

        if (!$result) {
            throw (new ModelNotFoundException)->setModel(__CLASS__);
        }

        return $result;
    }

    public static function verifyCredentials($attributes, $user, $business)
    {
        $statusAttributes = [
            'user_fk' => $user->id,
            'current_status' => 1,
            'platform_solution_fk' => config('app.platform_id'),
            'timezone_mysql_fk' => $business->branch_timezone_fk,
            'utc_created' => \Carbon\Carbon::now(),
        ];

        if (!empty($attributes['email'])) {
            $lastStatus = self::getLastStatusForEmail($attributes['email'], $user->id);
        } elseif (!empty($attributes['phone'])) {
            $lastStatus = self::getLastStatusForPhone($attributes['phone'], $user->id);
        } else {
            throw new \RuntimeException("Error Processing Request", 1);
        }

        // dd($lastStatus);
        Log::info(__METHOD__, ['lastStatus' => $lastStatus]);

        if (isset($attributes['phone'], $attributes['country_code'])) {
            $country = Country::where('code', $attributes['country_code'])->first();
        }

        if ($lastStatus->status_manager_fk == 32) {
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);
            // EMAIL_NOT_VERIFIED
            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_EMAIL_VERIFIED; //33
            $statusAttributes['old_email_phone'] = (string) $user->email;
            $statusAttributes['new_email_phone'] = $attributes['email'];
            UserStatusManager::create($statusAttributes);
        } elseif ($lastStatus->status_manager_fk == 30) {
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);
            // PHONE_NOT_VERIFIED
            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_PHONE_VERIFIED; //31
            $statusAttributes['old_email_phone'] = (string) $user->phone;
            $statusAttributes['new_email_phone'] = $attributes['phone'];
            $statusAttributes['country_fk'] = $country->country_pk;
            UserStatusManager::create($statusAttributes);
        } elseif (in_array($lastStatus->status_manager_fk, [39,45])) {
            // ADD_PHONE_REQUEST, ADD_PHONE_REQUEST_RESEND
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);

            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_ADD_PHONE_VERIFIED; //41
            $statusAttributes['old_email_phone'] = (string) $user->phone;
            $statusAttributes['new_email_phone'] = $attributes['phone'];
            $statusAttributes['country_fk'] = $country->country_pk;
            UserStatusManager::create($statusAttributes);
        } elseif (in_array($lastStatus->status_manager_fk, [40,46])) {
            // ADD_EMAIL_REQUEST, ADD_EMAIL_REQUEST_RESEND
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);

            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_ADD_EMAIL_VERIFIED; //42
            $statusAttributes['old_email_phone'] = (string) $user->email;
            $statusAttributes['new_email_phone'] = $attributes['email'];
            UserStatusManager::create($statusAttributes);
        } elseif (in_array($lastStatus->status_manager_fk, [47,53])) {
            // CHANGE_PHONE_REQUEST, CHANGE_PHONE_REQUEST_RESEND
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);

            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_CHANGED_PHONE_VERIFIED; //49
            $statusAttributes['old_email_phone'] = (string) $user->phone;
            $statusAttributes['new_email_phone'] = $attributes['phone'];
            $statusAttributes['country_fk'] = $country->country_pk;
            UserStatusManager::create($statusAttributes);
        } elseif (in_array($lastStatus->status_manager_fk, [48,54])) {
            // CHANGE_EMAIL_REQUEST, CHANGE_EMAIL_REQUEST_RESEND
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);

            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_CHANGED_EMAIL_VERIFIED; //50
            $statusAttributes['old_email_phone'] = (string) $user->email;
            $statusAttributes['new_email_phone'] = $attributes['email'];
            UserStatusManager::create($statusAttributes);
        } elseif ($lastStatus->status_manager_fk == 56) {
            // PHONE_NOT_VERIFIED_INVITED_BYSTORE
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);

            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_PHONE_VERIFIED_INVITED_BYSTORE; //57
            $statusAttributes['old_email_phone'] = (string) $user->phone;
            $statusAttributes['new_email_phone'] = $attributes['phone'];
            $statusAttributes['country_fk'] = $country->country_pk;
            UserStatusManager::create($statusAttributes);
        } elseif ($lastStatus->status_manager_fk == 58) {
            // EMAIL_NOT_VERIFIED_INVITED_BYSTORE
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);

            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_EMAIL_VERIFIED_INVITED_BYSTORE; //59
            $statusAttributes['old_email_phone'] = (string) $user->email;
            $statusAttributes['new_email_phone'] = $attributes['email'];
            UserStatusManager::create($statusAttributes);
        } elseif ($lastStatus->status_manager_fk == 64) {
            // PHONE_NOT_VERIFIED_UNITS_TRANSFER
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);

            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_PHONE_VERIFIED_UNITS_TRANSFER; //65
            $statusAttributes['old_email_phone'] = (string) $user->phone;
            $statusAttributes['new_email_phone'] = $attributes['phone'];
            $statusAttributes['country_fk'] = $country->country_pk;
            UserStatusManager::create($statusAttributes);
        } elseif ($lastStatus->status_manager_fk == 66) {
            // EMAIL_NOT_VERIFIED_UNITS_TRANSFER
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);

            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_EMAIL_VERIFIED_UNITS_TRANSFER; //66
            $statusAttributes['old_email_phone'] = (string) $user->email;
            $statusAttributes['new_email_phone'] = $attributes['email'];
            UserStatusManager::create($statusAttributes);
        }
    }

    public static function updateProfile($attributes, $user, $business)
    {
        $statusAttributes = [
            'user_fk' => $user->id,
            'current_status' => 1,
            'platform_solution_fk' => config('app.platform_id'),
            'timezone_mysql_fk' => $attributes['timezone_mysql_fk'],
            'utc_created' => \Carbon\Carbon::now(),
        ];

        $rules = BusinessRulesTrait::getRulesForBusiness($business->business_pk);
        $emailUpdated = false;
        $phoneUpdated = false;

        \Log::info(__METHOD__ . ' -> Initial', [ 'user_id' => $user->id, 'status' => 'Before change/add', 'attributes' => $statusAttributes,]);

        //user has an email and new email exists in request - CHANGE
        if ($user->email && isset($attributes['email']) && $user->email != $attributes['email']) {
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);

            \Log::info(__METHOD__ . ' -> After update current_status', ['user_id' => $user->id, 'status'=>'STATUS_MANAGER_CHANGE_EMAIL_REQUEST', 'attributes' => $statusAttributes]);

            //Change email
            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_CHANGE_EMAIL_REQUEST;
            $statusAttributes['old_email_phone'] = (string) $user->email;
            $statusAttributes['new_email_phone'] = $attributes['email'];
            UserStatusManager::create($statusAttributes);

            \Log::info(__METHOD__ . ' -> After create', [
                'user_id' => $user->id, 'status'=>'STATUS_MANAGER_CHANGE_EMAIL_REQUEST',
                'attributes' => $statusAttributes
            ]);

            $emailUpdated = true;
        } elseif (!$user->email && !empty($attributes['email'])) {
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);
            // ADD Email
            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_ADD_EMAIL_REQUEST;
            $statusAttributes['old_email_phone'] = (string) $user->email;
            $statusAttributes['new_email_phone'] = $attributes['email'];
            UserStatusManager::create($statusAttributes);

            \Log::info(__METHOD__, ['user_id' => $user->id, 'status'=>'STATUS_MANAGER_ADD_EMAIL_REQUEST', 'attributes' => $statusAttributes]);

            $emailUpdated = true;
        }

        //user has an phone and new phone exists in request - CHANGE
        if ($user->phone && isset($attributes['phone']) && $user->phone != $attributes['phone']) {
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);

            //Change phone
            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_CHANGE_PHONE_REQUEST;
            $statusAttributes['old_email_phone'] = (string) $user->phone;
            $statusAttributes['new_email_phone'] = $attributes['phone'];
            $statusAttributes['country_fk'] = $attributes['country_phone_fk'];
            UserStatusManager::create($statusAttributes);

            $phoneUpdated = true;
            \Log::info(__METHOD__, ['user_id' => $user->id, 'status'=>'STATUS_MANAGER_CHANGE_PHONE_REQUEST','attributes' => $statusAttributes]);
        } elseif (!$user->phone && !empty($attributes['phone'])) {
            // UserStatusManager::where('user_fk', $user->id)->update(['current_status' => 0]);

            //Add phone
            $statusAttributes['status_manager_fk'] = STATUS_MANAGER_ADD_PHONE_REQUEST;
            $statusAttributes['old_email_phone'] = (string) $user->phone;
            $statusAttributes['new_email_phone'] = $attributes['phone'];
            $statusAttributes['country_fk'] = $attributes['country_phone_fk'];
            UserStatusManager::create($statusAttributes);

            $phoneUpdated = true;
            \Log::info(__METHOD__, ['user_id' => $user->id, 'status'=>'STATUS_MANAGER_ADD_PHONE_REQUEST', 'attributes' => $statusAttributes]);
        }

        $businessModel = Business::findOrFail($business->business_pk);
        if ($emailUpdated && $rules->notify_change_credential) {
            self::sendChangeEmailMessage($user, $business, $attributes['email']);
        }

        if ($phoneUpdated && $rules->notify_change_credential && !$businessModel->industryProhibitedSms()) {
            self::sendChangePhoneMessage($user, $business, $attributes['phone'], $attributes['country_phone_fk']);
        }
    }

    /**
     * Sends a Welcome Email or SMS
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     * @return void
     */
    public static function sendChangePhoneMessage($user, $business, $phone, $countryCodeId)
    {
        $locale = 'en';

        $textProvider = SmsProvider::where('business_fk', $business->business_pk)->first();

        // Not users have signed up at
        $branch = BusinessBranch::find($user->signedupat_fk);

        $token = substr(microtime(true) . 'CH' . Str::random(60), 0, 60); //make sure is 60 characters

        $country = Country::findOrFail($countryCodeId);

        ActivationToken::create([
            'id' => $token,
            'user_fk' => $user->id,
            'utc_link_expires' => date('Y-m-d H:i:s', strtotime(ACTIVATION_LINK_EXPIRES)),
            'phone' => $phone,
            'email' => null,
            'country_code' => $country->code,
            'intent' => 'change_phone',
        ]);

        $businessProfile = BusinessProfile::where('business_fk', $business->business_pk)->first();

        if ($businessProfile && $businessProfile->web_app_uri) {
            # http://webapp.local/site/verify?token=44&email=<EMAIL>
            $uri = url(rtrim($businessProfile->web_app_uri, '/') . '/site/verify');
        } else {
            $uri = url(rtrim(config('app.members_webapp_url'), '/')  . '/site/verify');
        }

        $link = $uri . '?' . http_build_query([
            'token' => $token, 'phone' => $phone, 'action' => 'change',
        ]);

        $shortLink = Utils::shortUrl($link, config('app.platform_name'));

        $fromName = $branch ? $branch->name : $business->name;

        $smsBody = __('api.FRONTEND_CHANGE_PHONE_SMS', [
            'business_name' => $fromName, 'short_link' => $shortLink
        ], $locale);


        $intlPhone = Utils::getFormattedPhoneNumber($phone, $country->code, 'E164');

        dispatch((new TransactionalTextMessage($user->id, $business->business_pk, $textProvider))
            ->content($smsBody)
            ->to($intlPhone));
    }

    /**
     * Sends a Welcome Email or SMS
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     * @return void
     */
    public static function sendChangeEmailMessage($user, $business, $email)
    {
        $locale = 'en';

        // Not users have signed up at
        $branch = BusinessBranch::find($user->signedupat_fk);

        $token = substr(microtime(true) . 'CH' . Str::random(60), 0, 60); //make sure is 60 characters

        ActivationToken::create([
            'id' => $token,
            'user_fk' => $user->id,
            'utc_link_expires' => date('Y-m-d H:i:s', strtotime(ACTIVATION_LINK_EXPIRES)),
            'phone' => null,
            'email' => $email,
            'country_code' => null,
            'intent' => 'change_email',
        ]);

        $businessProfile = BusinessProfile::where('business_fk', $business->business_pk)->first();

        if ($businessProfile && $businessProfile->web_app_uri) {
            # http://webapp.local/site/verify?token=44&email=<EMAIL>
            $uri = url(rtrim($businessProfile->web_app_uri, '/') . '/site/verify');
        } else {
            $uri = url(rtrim(config('app.members_webapp_url'), '/')  . '/site/verify');
        }

        $link = $uri . '?' . http_build_query([
            'token' => $token,  'email' => $email, 'action' => 'change',
        ]);

        $emailProvider = EmailProvider::where('business_fk', $business->business_pk)->first();

        $fromName = $branch ? $branch->name : $business->name;

        if ($emailProvider) {
            $subject = $emailProvider->from_name . ': Change email verification';
        } else {
            $subject = $fromName . ': Change email verification';
        }

        $welcomeEmail = new Email($emailProvider);

        $welcomeEmail
            ->subject($subject)
            ->view('emails.user.changeEmail.en')
            ->with([
                'link' => $link,
                'email' => $email,
                'businessName' => $fromName,
                'businessLogo' => $branch ? $branch->logo_image_medium : $business->logo_image_medium,
            ]);

        dispatch((new TransactionalMailMessage($user->id, $business->business_pk, $emailProvider))->with($welcomeEmail)->to($email));
    }

    /**
     * 32 - EMAIL_NOT_VERIFIED -
     * 33 - EMAIL_VERIFIED
     * 40 - ADD_EMAIL_REQUEST
     * 42 - ADD_EMAIL_VERIFIED
     * 44 - ADD_EMAIL_REQUEST_CANCEL
     * 46 - ADD_EMAIL_REQUEST_RESEND
     * 48 - CHANGE_EMAIL_REQUEST -
     * 50 - CHANGED_EMAIL_VERIFIED - verified
     * 52 - CHANGE_EMAIL_REQUEST_CANCEL
     * 54 - CHANGE_EMAIL_REQUEST_RESEND
     * 58 - EMAIL_NOT_VERIFIED_INVITED_BYSTORE
     * 59 - EMAIL_VERIFIED_INVITED_BYSTORE
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function getStatusText($status = null)
    {
        $statusList = [
            32 => 'EMAIL_NOT_VERIFIED',
            33 => 'EMAIL_VERIFIED',
            40 => 'ADD_EMAIL_REQUEST',
            42 => 'ADD_EMAIL_VERIFIED',
            44 => 'ADD_EMAIL_REQUEST_CANCEL',
            46 => 'ADD_EMAIL_REQUEST_RESEND',
            48 => 'CHANGE_EMAIL_REQUEST',
            50 => 'CHANGED_EMAIL_VERIFIED',
            52 => 'CHANGE_EMAIL_REQUEST_CANCEL',
            54 => 'CHANGE_EMAIL_REQUEST_RESEND',
            58 => 'EMAIL_NOT_VERIFIED_INVITED_BYSTORE',
            59 => 'EMAIL_VERIFIED_INVITED_BYSTORE',

            30 => 'PHONE_NOT_VERIFIED',
            31 => 'PHONE_VERIFIED',
            39 => 'ADD_PHONE_REQUEST',
            41 => 'ADD_PHONE_VERIFIED',
            43 => 'ADD_PHONE_REQUEST_CANCEL',
            45 => 'ADD_PHONE_REQUEST_RESEND',
            47 => 'CHANGE_PHONE_REQUEST',
            49 => 'CHANGED_PHONE_VERIFIED',
            51 => 'CHANGE_PHONE_REQUEST_CANCEL',
            53 => 'CHANGE_PHONE_REQUEST_RESEND',
            56 => 'PHONE_NOT_VERIFIED_INVITED_BYSTORE',
            57 => 'PHONE_VERIFIED_INVITED_BYSTORE',
        ];

        return $status ? $statusList[$status] : $statusList;
    }

    /**
     * Transforms a collection
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function transformCollection($items)
    {
        $_items = [];

        foreach ($items as $key => $item) {
            $_items[] = self::transformItem($item);
        }

        return $_items;
    }

    /**
     * Transforms model attributes
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function transformItem($item)
    {
        $common = [
            'status_code' => $item->status_manager_fk,
            'status_text' => self::getStatusText($item->status_manager_fk),
            'created_at' => (new \Carbon\Carbon($item->utc_created))->toIso8601String(),
        ];

        if (strpos($item->new_email_phone, '@') === false) {
            $country = Country::find($item->country_fk);
            $countryCode = $country ? strtoupper($country->code) : null;
            $phone = ['mobile' => $item->new_email_phone, 'country_code' => $countryCode];
            $result = array_merge($common, $phone);
        } else {
            $email = ['email' => $item->new_email_phone];
            $result = array_merge($common, $email);
        }

        return $result;
    }
}
