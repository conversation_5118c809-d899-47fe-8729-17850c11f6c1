<?php

use application\models\AuditUserLoginAttempt;

class MainController extends Controller
{
    const STATUS_ENABLED = 1;
    const TMZ_MONTREAL_NAME = 'America/Montreal';

    private $_model;
    private $_platformName = PlatformSolution::PLATFORM_NAME_SYSTEM;

    // public $layout='//layouts/column2';
    public $layout = 'admin2';

    /**
     * @return array action filters
     */
    public function filters()
    {
        return array(
            'accessControl', // perform access control for CRUD operations
            'postOnly + delete', // we only allow deletion via POST request
            // array(
            //     'COutputCache',
            //     'duration'=> 5*60,
            // ),
        );
    }

    /**
     * Specifies the access control rules.
     * This method is used by the 'accessControl' filter.
     * @return array access control rules
     */
    public function accessRules()
    {
        return array(
//            array('allow',
//                'expression' => function(){
//                    $headers = Utils::getallheaders();
//                    return isset($headers['Authorization']);
//                },
//            ),
            array('deny',
                'users' => array('?'), //deny all non authenticated users
            ),
            array('allow',
                'roles' => array('superadmin', 'merchantsetup'),
            ),
            array('deny', // deny all users
                'users' => array('*'),
            ),

        );
    }

    public function actionIndex()
    {
        if (Yii::app()->user->checkAccess('merchantsetup')) {
            $this->redirect(Yii::app()->createUrl('/systemAdmin/business/impersonate'));
        }

        /*$timeZoneFk = Utils::getTimezoneSession();*/
        $systemStat = Utils::getSystemStat(null, true);

        $stat = AuditUserLogin::systemLoginStats();
        $stat = array_merge($stat, $systemStat);


        $this->render('index', array('stat' => $stat));
    }

    public function actionGetLockedAccounts()
    {
        $lockedAccounts = AuditUserLoginAttempt::getRecentLockedLoginAttempts();
        Utils::sendResponse(200, CJSON::encode(['locked_accounts' => $lockedAccounts]));
    }

    public function actionUnlockAccount()
    {
        $id = (int) ($_POST['id'] ?? 0);
        if (!$id) {
            Utils::sendResponse(400, CJSON::encode(['message' => 'Invalid request']));
        }

        $account = AuditUserLoginAttempt::model()->findByPk($id);
        if (!$account) {
            Utils::sendResponse(404, CJSON::encode(['message' => 'Account not found']));
        }

        $hashedUsername = hash('sha512', $account->credential);
        Yii::app()->cache->delete("login_attempts:{$hashedUsername}");

        Utils::sendResponse(200, CJSON::encode(['message' => 'Account unlocked successfully']));
    }

    public function actionOpcache()
    {
        $dataModel = new OpCacheDataModel();

        if (isset($_GET['clear']) && $_GET['clear'] == 1) {
            $dataModel->clearCache();
            // $this->redirect('/main/opCache');
        }

        $this->render('opcache',array( 'dataModel' => $dataModel ));
    }

    public function actionEmployees()
    {
        $employees = [];
        $businesses = BusinessBranch::model()->findAllByAttributes(['enabled'=>1, 'default_branch' =>1]);

        foreach ($businesses as $business) {
            // $ems = UserEn
        }
    }

    public function actionRestartService()
    {
        // exec (VESTA_CMD."v-restart-service ".$v_service, $output, $return_var);
        // if ($return_var != 0) {
        //     $error = implode('<br>', $output);
        //     if (empty($error)) $error =  __('SERVICE_ACTION_FAILED',__('restart'),$v_service);
        //         $_SESSION['error_msg'] = $error;
        // }
        // unset($output);
    }

    public function actionCalc()
    {
        $this->render('calculator');
    }

    public function actionTest()
    {
        $businessId = 15;
        $currentLanguage = 'en';
        $defaultBranch = BusinessBranch::getDefaultBranch($businessId);
        $businessLogo = Yii::app()->getBaseUrl(true) . $defaultBranch['logo_image_medium'];

        $employeeModel = User::model()->findByPk(70);

        $random = Utils::generateRandomString(60);
        $link = $this->createAbsoluteUrl('merchant/activate', array(
            'q' => $random,
        ));

        $emailData = ['username' => $employeeModel->first . ' ' . $employeeModel->last,
            'businessLogo' => $businessLogo,
            'link' => $link,
            'lang' => $currentLanguage];
        $emailSubject = Yii::t('frontend', 'USR_REG', array(), null, $currentLanguage);
        $sendTo = $employeeModel->email;

        $r= Utils::sendEmailWithTemplate($emailView = 'employeeActivateBilingual', $layout = 'bilingual', $emailSubject, $sendTo, $emailData, $viewPath = null,
            null, null, $businessId, $employeeModel->id, null, true);

        echo '<pre>';
        print_r($r);
        echo '</pre>';
    }

    public function actionMaps()
    {
        $branches = Yii::app()->db->createCommand()
            ->select([
                'bb.business_branch_pk',
                'bb.name as branch_name',
                //'IF (b.entity_type_fk = 1, "Point", IF (b.entity_type_fk = 99, "Free", "Punch")) AS business_type',
                //'bb.contact_name',
                //'bb.phone',
                //'bb.email',
                //'ad.street as address',
                'ad.geo_lat',
                'ad.geo_long',
                //'bb.country_phone_fk',
                //'tz.name as timezone',
                //'c.name as city_name',
                //'r.name as region_name',
                //'co.name as country_name',
                //'co.code as country_code',
                //'co.code_iso3 as country_code3',
                //'co.phone_code as country_pone_code',
            ])
            ->from('business b')
            ->join('business_branch bb', 'b.business_pk=bb.business_fk')
            ->join('timezone_mysql tz', 'tz.timezone_mysql_pk=bb.branch_timezone_fk')
            ->leftjoin('address ad', 'ad.entity_fk=bb.business_branch_pk AND ad.entity_type_fk IN (2,8)')
            ->leftjoin('cities c', 'c.city_pk=ad.city_fk')
            ->leftjoin('regions r', 'r.region_pk=c.region_fk')
            ->leftjoin('countries co', 'co.country_pk=r.country_fk')
            ->where('bb.enabled = 1 AND b.enabled = 1 AND b.entity_type_fk IN (1,7)')
            ->queryAll();

        foreach ($branches as &$branch) {
            $branch['branch_name'] = stripslashes((string)$branch['branch_name']??'');
            //$branch['phone'] = Utils::getFormattedPhoneNumber($branch['phone'], $branch['country_phone_fk'], 'INTERNATIONAL' );
            //$branch['time_now'] = Utils::convertDateTimeToUserTmz($branch['timezone'], date('Y-m-d H:i:s'), $format = 'D, d - H:i');
        }
        $this->render('maps', array('branches' => json_encode($branches), 'totalLocations' => count($branches)));
    }

    public function actionCampaigns()
    {
        $campaignsCriteria = new CDbCriteria;
        $campaignsCriteria->select = 't.*';
        $campaignsCriteria->condition = 't.enabled=0 AND t.locked = 0';
        $campaignsCriteria->order = 't.entity_campaign_pk DESC';
        $campaignsAll = EntityCampaign::model()->findAll($campaignsCriteria);

        foreach ($campaignsAll as $campaign) {
            $offersList = CJSON::decode(stripslashes((string)$campaign->offers_campaigned??''));

            $entityPackageCredit = EntityMktgPackageCredit::model()->findByPk($campaign->entity_mktg_package_credit_fk);
            $entityPackage = EntityMktgPackage::model()->findByPk($entityPackageCredit->entity_mktg_package_fk);
            $branch = BusinessBranch::model()->findByAttributes(['business_fk' => $entityPackage->entity_fk, 'enabled' => 1]);

            $type = $campaign->campaign_type;
            $subType = $campaign->email_body ? 'Announcement' : 'Offer';

            if (empty($offersList)) {
                echo $campaign->entity_campaign_pk .' Name: ' . $campaign->campaign_name . '<BR>';
                echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Business: ' . $branch->name .'<BR>';
                echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Type: ' . $type . '<BR>';
                echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; SubType: ' . $subType .'<BR>';
                echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Comment: No offers' . '<BR>';
            } else {
                echo $campaign->entity_campaign_pk .' Name: ' . $campaign->campaign_name . '<BR>';
                echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Business: ' . $branch->name .'<BR>';
                echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Type: ' . $type . '<BR>';
                echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; SubType: ' . $subType .'<BR>';
                echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Comment: Comment: Has offers' . '<BR>';
                $offersCampaigned = []; #will store enabled offers
                foreach ($offersList as $offer) {
                    if (in_array($offer['offerType'], [1, 2, 3, 4, 8, 9, 10, 11, 12, 13])) {
                        $offerModel = Offer::model()->findByPk($offer['offerId']);
                        $isExpired = new DateTime($offerModel->utc_expires) <= new DateTime('now');

                        $expiredText = '';

                        if ($isExpired) {
                            $expiredText = '<span style="color: red; font-weight: bold;"> Expired </span>';
                        }

                        if ($offerModel) {
                            $offerLanguage = OfferLanguage::model()->findByAttributes(['offer_fk' => $offer['offerId'], 'language_fk' => 1]);
                            echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Ofer: <b>' . $offerLanguage->offer_title . '</b> Enabled: <b>' . $offerModel->enabled . '</b> Expires: <b>' .$offerModel->utc_expires . '</b> ' . $expiredText. '<BR>';
                        } else {
                            echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Ofer: No offer model found' . '<BR>';
                        }
                    } elseif (in_array($offer['offerType'], [15, 16, 17])) {
                        $punchItemModel = PunchItem::model()->findByPk($offer['offerId']);

                        $isExpired = new DateTime($punchItemModel->utc_expires) <= new DateTime('now');

                        $expiredText = '';

                        if ($isExpired) {
                            $expiredText = '<span style="color: red; font-weight: bold;"> Expired </span>';
                        }

                        if ($punchItemModel) {
                            $punchLanguage = PunchItemLanguage::model()->findByAttributes(['punch_item_fk' => $offer['offerId'], 'language_fk' => 1]);
                            echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Catalogue Item: <b>' . $punchLanguage->title . '</b> Enabled: <b>' . $punchItemModel->enabled . '</b> Expires: <b>' .$punchItemModel->utc_expires . '</b> ' . $expiredText. '<BR>';
                        } else {
                            echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Catalogue Item: No PunchItem model found' . '<BR>';
                        }
                    }
                }
            }
        }
    }

    /**
     * @throws CDbException
     */
    public function actionUpdateCampaign()
    {
        $campaignId = $_POST['Campaign']['entity_campaign_pk'];
        $action = $_POST['Campaign']['action'];

        $campaign = EntityCampaign::model()->findByPk($campaignId);

        if ($action === 'resume') {
            $campaign->enabled = 1;
            $campaign->locked = 0;
            $campaign->save();

            $queueItems = ResqueQueue::model()->findAllByAttributes([
                'queue' => 'campaigns',
                'task' => 'run_campaign',
                'active' => 1,
            ]);

            foreach ($queueItems as $queueItem) {
                $args = json_decode($queueItem->args ?? '');
                if ($args->entity_campaign_pk == $campaign->entity_campaign_pk) {
                    $queueItem->active = 0;
                    $queueItem->save();
                }
            }

        } elseif ($action === 'cancel') {
            $campaign->enabled = 0;
            $campaign->locked = 0;
            $campaign->canceled = 1;
            $campaign->save();
        } elseif ($action === 'approve') {
            $userId = Yii::app()->user->id;
            $loggedInUser = User::model()->findByPk($userId);
            $service = new CampaignApprovalService($loggedInUser, $campaign);
            $service->approveCampaign();
        } elseif ($action === 'reject') {
            $userId = Yii::app()->user->id;
            $loggedInUser = User::model()->findByPk($userId);
            $service = new CampaignApprovalService($loggedInUser, $campaign);
            $service->rejectCampaign();
        }

        Utils::sendResponse(200, CJSON::encode([
            'status' => 'OK',
        ]));
    }

    /**
     * List of transactions (page)
     *
     * Used in : Website()
     *
     * @version January 2016 release V3.5.0
     * @since 3.5.0
     * <AUTHOR>
     * @access public
     * @return html
     */
    public function actionTransactions()
    {
        //$this->layout='//layouts/adminContentLayout';

        $timeZoneFk = Utils::getTimezoneSession();
        $userTimeZone = TimezoneMysql::model()->findByPk($timeZoneFk)->name;

        $date = new DateTime();
        //$date->sub(new DateInterval('P1D'));//1 days ago
        $xDaysAgo = $date->format('Y-m-d H:i:s');

        // $firstDate = Utils::convertDateTimeToUserTmz($userTimeZone, date('Y-m-d H:i:s'), 'Y-m-01');
        $firstDate = Utils::convertDateTimeToUserTmz($userTimeZone, $xDaysAgo, 'Y-m-d');
        $lastDate = Utils::convertDateTimeToUserTmz($userTimeZone, date('Y-m-d H:i:s'), 'Y-m-d');
        $this->render('transactions', array('firstDate' => $firstDate, 'lastDate' => $lastDate));
    }

    /**
     * Getting data for charts
     * system_reports_flag=1 (3.5.0)
     * Used in : Website(SystemAdmin)
     *
     * @version January release V3.5.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public function actionAjaxCharts()
    {
        $timeZoneFk = Utils::getTimezoneSession();
        $userTimeZone = TimezoneMysql::model()->findByPk($timeZoneFk)->name;

        $results = Utils::systemStatsCharts($userTimeZone);

        echo CJSON::encode($results);
    }

    /**
     * Insert undelivered sms in DB log
     *
     * Used in : Website()
     *
     * @version July - August release V 2.1.1
     * @access public
     */

    /*public function actionSMSStatusLog()
    {
        $userId = Yii::app()->user->id;
        if ($userId == null) {
            throw new Exception("Error Processing Request", 1);
        }

        // $accessAllowed = array( 8, 10);

        // if (!in_array($userId, $accessAllowed)  )
        //      throw new CHttpException(403,Yii::t('backend','EX_ACCESS_DENIED'));

        // Step 1: Include Twillio
        spl_autoload_unregister(array('YiiBase', 'autoload'));
        require Yii::app()->params['TwillioIncludePath'];
        spl_autoload_register(array('YiiBase', 'autoload'));

        // Step 2: set our AccountSid and AuthToken from www.twilio.com/user/account
        $AccountSid = Yii::app()->params['TwillioAccountSid'];
        $AuthToken = Yii::app()->params['TwillioAuthToken'];

        // Step 3: instantiate a new Twilio Rest Client
        $client = new Services_Twilio($AccountSid, $AuthToken);

        // echo '<pre>';
        //  echo 'Sim:                        '; echo 'To:             ';  echo 'Data:             ';  echo 'Status:             ';  echo '<br>';

        $lastRunDate = SmsLogs::getLastRunDate();
        $lastRunDate = new DateTime($lastRunDate);
        $lastRunDate = $lastRunDate->format('Y-m-d');

        $today = new DateTime();
        $today = $today->format('Y-m-d');

        $messages = $client->account->messages->getIterator(0, 1000, array(
            //'DateSent<' => "2014-07-22",
            //'DateSent>' => "2014-07-20",
            'DateSent<' => $today,
            'DateSent>' => $lastRunDate,
        ));

        // Loop over the list of messages and echo a property for each one
        foreach ($messages as $message) {

            $date_updated = new DateTime($message->date_updated);

            if ($message->status == 'undelivered' || $message->status == 'failed') {

                $results = Yii::app()->db->createCommand()
                    ->select(array('phone'))
                    ->from('sms_logs')
                    ->where('phone=:phone', array(':phone' => $message->to))
                    ->queryAll();

                if (empty($results)) {

                    //echo $message->sid;  echo '     '; echo $message->to;  echo '     ';  echo $message->date_updated;  echo '     '; echo $message->status;  echo '<br>';

                    $Smslogs = new SmsLogs;
                    $Smslogs->isNewRecord = true;
                    $Smslogs->sms_id = $message->sid;
                    $Smslogs->phone = $message->to;
                    $Smslogs->datetime_tt_executed = date("Y-m-d H:i:s");
                    //$Smslogs->datetime_tt_executed = date("2014-07-22");
                    $Smslogs->datetime_twilio_updated = $date_updated->format('Y-m-d H:i:s');
                    $Smslogs->twilio_status = $message->status;
                    $Smslogs->twilio_errorcode = $message->error_code;
                    $Smslogs->twilio_errormessage = $message->error_message;
                    $Smslogs->user_fk = $userId;
                    $Smslogs->save(false);

                }
            } //undelivered
            elseif ($message->status == 'delivered') {
                //echo $message->sid;  echo '     '; echo $message->to;  echo '     ';  echo $message->date_updated;  echo '     '; echo $message->status;  echo '<br>';

                $results = Yii::app()->db->createCommand()
                    ->select(array('phone'))
                    ->from('sms_logs')
                    ->where('phone=:phone', array(':phone' => $message->to))
                    ->queryRow();

                if (!empty($results)) {
                    $Smslogs = new SmsLogs;
                    $Smslogs->isNewRecord = true;
                    $Smslogs->sms_id = $message->sid;
                    $Smslogs->phone = $message->to;
                    $Smslogs->datetime_tt_executed = date("Y-m-d H:i:s");
                    $Smslogs->datetime_twilio_updated = $date_updated->format('Y-m-d H:i:s');
                    $Smslogs->twilio_status = $message->status;
                    $Smslogs->twilio_errorcode = $message->error_code;
                    $Smslogs->twilio_errormessage = $message->error_message;
                    $Smslogs->user_fk = $userId;
                    $Smslogs->save(false);
                }

            }
        } //foreach
        echo 'done';
    }*/

    /**
     * Insert undelivered sms in DB log, First run
     *
     * Used in : Website()
     *
     * @version July - August release V 2.1.1
     * @access public
     */

    /*public function actionSMSStatusLogFirst()
    {
        $userId = Yii::app()->user->id;
        if ($userId == null) {
            throw new Exception("Error Processing Request", 1);
        }

        // $accessAllowed = array( 8, 10);

        // if (!in_array($userId, $accessAllowed)  )
        //      throw new CHttpException(403,Yii::t('backend','EX_ACCESS_DENIED'));

        // Step 1: Include Twillio
        spl_autoload_unregister(array('YiiBase', 'autoload'));
        require Yii::app()->params['TwillioIncludePath'];
        spl_autoload_register(array('YiiBase', 'autoload'));

        // Step 2: set our AccountSid and AuthToken from www.twilio.com/user/account
        $AccountSid = Yii::app()->params['TwillioAccountSid'];
        $AuthToken = Yii::app()->params['TwillioAuthToken'];

        // Step 3: instantiate a new Twilio Rest Client
        $client = new Services_Twilio($AccountSid, $AuthToken);

        // echo '<pre>';
        //  echo 'Sim:                        '; echo 'To:             ';  echo 'Data:             ';  echo 'Status:             ';  echo '<br>';

        $lastRunDate = SmsLogs::getLastRunDate();
        $lastRunDate = new DateTime($lastRunDate);
        $lastRunDate = $lastRunDate->format('Y-m-d');

        $today = new DateTime();
        $today = $today->format('Y-m-d');

        $messages = $client->account->messages->getIterator(0, 1000, array(
            'DateSent<' => $today,
        ));

        // Loop over the list of messages and echo a property for each one
        foreach ($messages as $message) {

            if ($message->status == 'undelivered' || $message->status == 'failed') {
                //echo $message->sid;  echo '     '; echo $message->to;  echo '     ';  echo $message->date_updated;  echo '     '; echo $message->status;  echo '<br>';

                $results = Yii::app()->db->createCommand()
                    ->select(array('phone'))
                    ->from('sms_logs')
                    ->where('phone=:phone', array(':phone' => $message->to))
                    ->queryAll();

                if (empty($results)) {
                    $date_updated = new DateTime($message->date_updated);

                    $Smslogs = new SmsLogs;
                    $Smslogs->isNewRecord = true;
                    $Smslogs->sms_id = $message->sid;
                    $Smslogs->phone = $message->to;
                    $Smslogs->datetime_tt_executed = date("Y-m-d H:i:s");
                    $Smslogs->datetime_twilio_updated = $date_updated->format('Y-m-d H:i:s');
                    $Smslogs->twilio_status = $message->status;
                    $Smslogs->twilio_errorcode = $message->error_code;
                    $Smslogs->twilio_errormessage = $message->error_message;
                    $Smslogs->user_fk = $userId;
                    $Smslogs->save(false);

                }
            }
        } //foreach

        echo 'done';
    }*/

    /**
     * Lists all models.
     */
    public function actionUndefinedBusinessList()
    {
        $sql = Yii::app()->db->createCommand()
            ->select('bb.business_branch_pk, bb.name as business_name, bb.address')
            ->from('business_branch bb')
            ->join('business b', 'b.business_pk = bb.business_fk')
            ->where('b.enabled = 1 and bb.enabled = 1 AND default_branch = 1 AND b.hidden = 0')
            ->getText();

        $count = Yii::app()->db->createCommand($sql)->queryScalar();

        $dataProvider = new CSqlDataProvider($sql, array(
            'totalItemCount' => $count,
            'sort' => array(
                'attributes' => array(
                    'business_branch_pk',
                    'business_name',
                    'address',
                ),
            ),
            'pagination' => array(
                'pageSize' => 10,
            ),
        ));

        //print_r($dataProvider->getData()); die();

        $this->render('undefinedBusinessList', array(
            'dataProvider' => $dataProvider,
        ));
    }

    /**
     * Soon Expire Offers list and Expired offers in the last 3 weeks
     * Handle timezone
     * Used in : Website(SystemAdmin)
     *
     * @version May - June 2015 V3.3.0
     * <AUTHOR>
     * @access public
     */

    public function actionSoonExpireOffers()
    {
        $timeZoneFk = Utils::getTimezoneSession();
        $userTimeZone = TimezoneMysql::model()->findByPk($timeZoneFk)->name;

        $offers = Offer::getSoonExpireOfferPunchesListCron($userTimeZone);
        $expiredOffers = Offer::getExpiredOfferPunchesListCron($userTimeZone);

        $this->render('soonExpireOffers', array(
            'offers' => $offers,
            'expiredOffers' => $expiredOffers,
        ));
    }

    /**
     * Including Gift Card Purchase
     * Getting transaction list between two dates
     * Used for Website (Business Admin)
     * @version July - Aug 2014 V3.3.1
     * @since 3.2.5
     * @param string listType
     * @param string date1
     * @param string date2
     * @return JSON
     */
    public function actionAjaxTransactionList()
    {
        $listType = $_GET['listType'];

        $date1 = isset($_GET['date1']) ? $_GET['date1'] : date('Y-m-d');
        $date2 = isset($_GET['date2']) ? $_GET['date2'] : date('Y-m-d');

        $timezoneId = Utils::getTimezoneSession();
        $timezoneName = TimezoneMysql::model()->findByPk($timezoneId)->name;

        // First day of the month.
        $firstDate = date('Y-m-d 00:00:00', strtotime((string)$date1)); // Utils::utcDateTime(date('Y-m-d 00:00:00', strtotime((string)$date1)), $timezoneId);
        // Last day of the month.
        $lastDate = date('Y-m-d 23:59:59', strtotime((string)$date2)); //Utils::utcDateTime(date('Y-m-d 23:59:59', strtotime((string)$date2)), $timezoneId);

        $utcFirstDate = Utils::utcDateTime($firstDate, $timezoneId);
        $utcLastDate = Utils::utcDateTime($lastDate, $timezoneId);

        // Yii::log('date1='.$firstDate, CLogger::LEVEL_INFO, $this->_platformName.'_'.__FUNCTION__);
        // Yii::log('date2='.$lastDate, CLogger::LEVEL_INFO, $this->_platformName.'_'.__FUNCTION__);
        // Yii::log('firstDate='.$utcFirstDate, CLogger::LEVEL_INFO, $this->_platformName.'_'.__FUNCTION__);
        // Yii::log('lastDate='.$utcLastDate, CLogger::LEVEL_INFO, $this->_platformName.'_'.__FUNCTION__);

        if ($listType == 'point_rewards') //transactions list; system_reports_flag=1
        {
            $trxSybType = [2, 4, 5, 15, 16, 17, 18, 19, 24, 25, 29, 30, 31, 32, 33, 37, 38, 41, 42, 43, 44, 45, 52, 53, 55, 56, 57, 58, 59, 62, 63, 64, 65,
                    67, 68, 69, 70, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 90, 91, 92, 109];
            $list = Transaction::systemTrxListBetweenDates($utcFirstDate, $utcLastDate, $trxSybType, $timezoneName);

            foreach ($list as $key => $item) {
                $list[$key]['username'] = User::getUserDisplayName($item['user_fk']);
                $list[$key]['image'] = '';
                $list[$key]['clerk_username'] = User::getUserDisplayName($item['user_clerk_fk']);
                $list[$key]['trx_subtype_name'] = Yii::t('backend', $item['trx_subtype_name']);
            }
            echo CJSON::encode(array('status' => 'OK', 'message' => Yii::t('backend', 'BACKEND_DASHBOARD_TRX_LIST_EMPTY_MESSAGE'), 'aaData' => $list));
            Yii::app()->end();
        } elseif ($listType == 'store_zero_trx') {
            $trxSybType = [4, 5, 17, 18, 43, 55, 62, 63, 67, 68, 84, 85];

            $sign = (isset($_GET['sign'])) ? $_GET['sign'] : '=';
            $totalTrx = (isset($_GET['totalTrx'])) ? $_GET['totalTrx'] : 0;

            $totalTrx = $sign . $totalTrx;

            $trx = Transaction::storesWithNoTrxBetweenDates($utcFirstDate, $utcLastDate, $trxSybType, $totalTrx);

            echo CJSON::encode(['trx' => $trx]);
            Yii::app()->end();
        } elseif ($listType == 'monthly_stores_trx') {
            $sign = (isset($_GET['sign'])) ? $_GET['sign'] : '=';
            $totalTrx = (isset($_GET['totalTrx'])) ? $_GET['totalTrx'] : 0;
            $trxSubtype = (isset($_GET['trxSubtype'])) ? (int) $_GET['trxSubtype'] : 1;

            $totalTrx = $sign . $totalTrx;

            if ($trxSubtype == 1) //all trx
            {
                $trxSybType = [4, 5, 17, 18, 43, 62, 63, 65, 67, 68, 84, 85];
            } elseif ($trxSubtype == 2) //rewards
            {
                $trxSybType = [4, 17, 67, 68];
            } elseif ($trxSubtype == 3) //redemptions
            {
                $trxSybType = [5, 18, 43, 62, 63, 65];
            }

            if (in_array($trxSubtype, [1, 2, 3])) {
                $trx = Transaction::reportTotalMonthlyTrxByBusiness($utcFirstDate, $utcLastDate, $trxSybType, $totalTrx, $timezoneName);
            } elseif ($trxSubtype == 4) //redemptions
            {
                $trx = Transaction::reportTotalMonthlySignupByBusiness($utcFirstDate, $utcLastDate, $totalTrx, $timezoneName);
            }

            echo CJSON::encode(['trx' => $trx]);
            Yii::app()->end();
        } elseif ($listType == 'users_at_store') {

            $sign = (isset($_GET['sign'])) ? $_GET['sign'] : '=';
            $total = (isset($_GET['total'])) ? $_GET['total'] : 0;

            $total = $sign . $total;

            $trx = BusinessBranch::systemUsersSignedUpAtStore($utcFirstDate, $utcLastDate, $total);

            echo CJSON::encode(['trx' => $trx]);
            Yii::app()->end();
        } elseif ($listType == 'weekly_report') {
            $businessId = (isset($_GET['businessId'])) ? (int) $_GET['businessId'] : null;
            $firstDate = date('Y-m-d', strtotime((string)$date1)); // Utils::utcDateTime(date('Y-m-d 00:00:00', strtotime((string)$date1)), $timezoneId);
            $lastDate = date('Y-m-d', strtotime((string)$date2)); //Utils::utcDateTime(date('Y-m-d 23:59:59', strtotime((string)$date2)), $timezoneId);

            $trx = BusinessReport::systemWeeklyReportBetweenDates($firstDate, $lastDate, $businessId);

            echo CJSON::encode(['trx' => $trx]);
            Yii::app()->end();
        } elseif ($listType == 'inactivity_report') {
            $subject = (isset($_GET['subject'])) ? (int) $_GET['subject'] : 1;
            $strict = (isset($_GET['strict'])) ? $_GET['strict'] : true;
            $weeks1 = (int) $_GET['weeks1'];
            $weeks2 = (int) $_GET['weeks2'];
            $branchId = null;

            $weeks1 = ($weeks1<2) ? 2 : $weeks1;

            if(date('D', strtotime((string)'now')) === 'Mon') {
                $firstDate     = new DateTime('previous monday -'.($weeks1+1).' week'); //2015-10-05, previous monday
                $lastDate     = new DateTime('previous monday'); //2015-10-12, previous monday, 2016-01-18
            } else {
                $firstDate = new DateTime('previous monday -'.($weeks1+1).' week');
                $lastDate = new DateTime('last monday -1 week');
            }

            $businesses = BusinessReport::systemInactivityReport($firstDate->format('Y-m-d'), $lastDate->format('Y-m-d'), $branchId);
            $results = BusinessReport::buildInactivityReport($subject, $businesses, $firstDate, $lastDate, $weeks1, $strict);
            // echo '<pre>';print_r($results); die;
            echo CJSON::encode(['trx' => $results]);
            Yii::app()->end();
        }  elseif ($listType == 'inactivity_chart') {
            $branchId = (isset($_GET['branchId'])) ? (int) $_GET['branchId'] : null;

            if(date('D', strtotime((string)'now')) === 'Mon') {
                $firstDate = new DateTime('last monday -10 week');
                $lastDate = new DateTime('last monday');
            } else {
                $firstDate = new DateTime('last monday -10 week');
                $lastDate = new DateTime('last monday -1 week');
            }

            $results = BusinessReport::systemInactivityReport($firstDate->format('Y-m-d'), $lastDate->format('Y-m-d'), $branchId);
            // echo '<pre>';print_r($businesses); die;
            Utils::sendResponse(200, CJSON::encode(['results' => $results]));
        }
    }

    /**
     * Totals, transactions for each store per month between dates
     *
     * Used in : Website(SystemAdmin)
     *
     * @version Aug - Sept 2015 release V3.4.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public function actionInactivityReport()
    {
        //$this->layout='//layouts/adminContentLayout';

        $timeZoneFk = Utils::getTimezoneSession();
        $userTimeZone = TimezoneMysql::model()->findByPk($timeZoneFk)->name;

        $firstDate = date('Y-m-d', strtotime((string)'last monday -4 week')); //in UTC
        $lastDate = Utils::convertDateTimeToUserTmz($userTimeZone, date('Y-m-d 23:59:59'), 'Y-m-d');
        $this->render('inactivityReport', array('firstDate' => $firstDate, 'lastDate' => $lastDate));
    }

    /**
     * Totals, transactions for each store per month between dates
     *
     * Used in : Website(SystemAdmin)
     *
     * @version Aug - Sept 2015 release V3.4.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public function actionWeeklyReport()
    {
        //$this->layout='//layouts/adminContentLayout';

        $timeZoneFk = Utils::getTimezoneSession();
        $userTimeZone = TimezoneMysql::model()->findByPk($timeZoneFk)->name;

        $firstDate = date('Y-m-d', strtotime((string)'last monday -4 week')); //in UTC
        $lastDate = Utils::convertDateTimeToUserTmz($userTimeZone, date('Y-m-d 23:59:59'), 'Y-m-d');
        $this->render('weeklyReport', array('firstDate' => $firstDate, 'lastDate' => $lastDate));
    }

    /**
     * Totals, transactions for each store per month between dates
     *
     * Used in : Website(SystemAdmin)
     *
     * @version Aug - Sept 2015 release V3.4.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public function actionReportsTransactions()
    {
        //$this->layout='//layouts/adminContentLayout';

        $timeZoneFk = Utils::getTimezoneSession();
        $userTimeZone = TimezoneMysql::model()->findByPk($timeZoneFk)->name;

        $firstDate = Utils::convertDateTimeToUserTmz($userTimeZone, date('Y-m-d H:i:s'), 'Y-m-01');
        $lastDate = Utils::convertDateTimeToUserTmz($userTimeZone, date('Y-m-d H:i:s'), 'Y-m-d');
        $this->render('reportsTransactions', array('firstDate' => $firstDate, 'lastDate' => $lastDate));

    }

    public function actionStoresZeroTrx()
    {
        //$this->layout='//layouts/adminContentLayout';

        $timeZoneFk = Utils::getTimezoneSession();
        $userTimeZone = TimezoneMysql::model()->findByPk($timeZoneFk)->name;

        $firstDate = Utils::convertDateTimeToUserTmz($userTimeZone, date('Y-m-d H:i:s'), 'Y-m-01');
        $lastDate = Utils::convertDateTimeToUserTmz($userTimeZone, date('Y-m-d H:i:s'), 'Y-m-d');
        $this->render('storesZeroTrx', array('firstDate' => $firstDate, 'lastDate' => $lastDate));

    }

    public function actionActiveUsersCharts()
    {
        // $this->layout='//layouts/adminContentLayout';

        $timeZoneFk = Utils::getTimezoneSession();
        $userTimeZone = TimezoneMysql::model()->findByPk($timeZoneFk)->name;

        $firstDate = Utils::convertDateTimeToUserTmz($userTimeZone, date('Y-m-d H:i:s'), 'Y-m-01');
        $lastDate = Utils::convertDateTimeToUserTmz($userTimeZone, date('Y-m-d H:i:s'), 'Y-m-d');
        $this->render('activeUsersCharts', array('firstDate' => $firstDate, 'lastDate' => $lastDate));

    }

    public function actionUsersAtStore()
    {
        //$this->layout='//layouts/adminContentLayout';

        $timeZoneFk = Utils::getTimezoneSession();
        $userTimeZone = TimezoneMysql::model()->findByPk($timeZoneFk)->name;

        $firstDate = Utils::convertDateTimeToUserTmz($userTimeZone, date('Y-m-d H:i:s'), 'Y-m-01');
        $lastDate = Utils::convertDateTimeToUserTmz($userTimeZone, date('Y-m-d H:i:s'), 'Y-m-d');
        $this->render('usersAtStore', array('firstDate' => $firstDate, 'lastDate' => $lastDate));

    }

    /**
     * Getting data for charts
     *
     * Used in : Website(SystemAdmin)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return json
     */
    public function actionAjaxTrxCharts()
    {
        $chartType = $_GET['chartType'];

        if ($chartType == 'activeUsers') {
            $trxSybTypes = [4, 5, 15, 16, 17, 18, 24, 25, 29, 30, 31, 32, 33, 37, 38, 41, 42, 43, 44, 45, 52, 53, 55, 56, 57, 58, 59, 62, 63, 64, 65, 67, 68, 69, 70];

            $trx = Transaction::chartActiveUsers($trxSybTypes);

            echo CJSON::encode(['trx' => $trx]);
        }
    }

    /**
     * TIMEZONE - Globalization implementation
     *
     * Used in : Website(SystemAdmin)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public function actionCampaignsList()
    {
        $auto = (int) isset($_GET['auto']) ? $_GET['auto'] : 0;
        $timezoneId = Utils::getTimezoneSession();
        $timezoneName = TimezoneMysql::model()->findByPk($timezoneId)->name;
        $campaigns = EntityCampaign::getAllCampaigns($limit = 500, $auto);
        $pendingCampaigns = EntityCampaign::getPendingApprovalCampaigns();

        $this->render('campaignList', [
            'campaigns' => $campaigns,
            'pendingCampaigns' => $pendingCampaigns,
            'timezoneName' => $timezoneName,
        ]);
    }

    public function actionViewCampaign()
    {
        $id = (int) $_GET['id'];
        $campaign = EntityCampaign::model()->findByPk($id);

        $queueStats = EmailSmsQueue::queueRealTimeStatsForCampaign($id);
        $processingItems = EmailSmsQueue::queueProcessingItemsForCampaign($id);
        $scheduledItems = EmailSmsQueue::queueScheduledStuckItemsForCampaign($id);

        $data = [
            'campaign' => $campaign,
            'queueStats' => $queueStats,
            'scheduledItems' => $scheduledItems,
            'processingItems' => $processingItems,
        ];

        if (isset($_GET['ajax'])) {
            Utils::sendResponse(200, CJSON::encode([
                '_viewCampaignQueueStats' => $this->renderPartial('_viewCampaignQueueStats', $data, true),
                '_viewCampaignProcessing' => $this->renderPartial('_viewCampaignProcessing', $data, true),
            ]));
        }

        $this->render('viewCampaign', $data);
    }

    public function actionUpdateCampaignQueueItems()
    {
        $id = (int) ($_POST['id'] ?? 0);
        $campId = (int) ($_POST['campId'] ?? 0);
        $type = $_POST['type'] ?? '';

        if ($id > 0) {
            $model = EmailSmsQueue::model()->findByPk($id);
            $model->saveAttributes([
                'email_sms_status_fk' => 5,
                'utc_lastattempt' => date('Y-m-d H:i:s'),
                //'error' => null,
            ]);
        } elseif($type === 'scheduled') {
            $criteria = new CDbCriteria;
            $criteria->condition = "entity_campaign_fk={$campId} AND process_time IS NOT NULL AND email_sms_status_fk=1" ;
            EmailSmsQueue::model()->updateAll(['process_time' => null, 'attempts' => 0],
                $criteria);
        } else {
            $criteria = new CDbCriteria;
            $criteria->condition = "entity_campaign_fk={$campId} AND process_time IS NOT NULL AND email_sms_status_fk=8";
            EmailSmsQueue::model()->updateAll(['process_time' => null, 'email_sms_status_fk' => 1, 'attempts' => 0],
                $criteria);
        }
    }

    public function actionCampaignsProgress()
    {
        $campaigns =  Yii::app()->db->createCommand()
            ->select([
                'ec.entity_campaign_pk',
                'COUNT(CASE WHEN q.email_sms_status_fk = 1 THEN 1 END) AS scheduled',
                'COUNT(CASE WHEN q.email_sms_status_fk = 2 THEN 1 END) AS queued',
                'COUNT(CASE WHEN q.email_sms_status_fk = 3 THEN 1 END) AS sent',
                'COUNT(CASE WHEN q.email_sms_status_fk = 4 THEN 1 END) AS delivered',
                'COUNT(CASE WHEN q.email_sms_status_fk = 5 THEN 1 END) AS failed',
                'COUNT(CASE WHEN q.email_sms_status_fk > 1 THEN 1 END) AS processed',
                'count(q.email_sms_queue_pk) as total',
            ])
            ->from('entity_campaign ec')
            ->leftJoin('email_sms_queue q', 'q.entity_campaign_fk = ec.entity_campaign_pk')
            ->where('ec.locked=1 AND ec.enabled=1')
            ->group('ec.entity_campaign_pk')
            ->queryAll();

        Utils::sendResponse(200, CJSON::encode(['campaings' => $campaigns]));
    }

    /**
     * Generate a QRcode and insert it to already existing users in the DB
     * Important: This fucntion is NOT YET implemented in the UI
     * @version Mar - Apr 2015 V3.2.5
     * <AUTHOR>
     * @return string QRcode
     */
    public function actionAddQRcodeToExistingUsers()
    {
        User::updateQRCodesForExistingUsers();
        echo 'AddBarcodeToExistingUsers - Completed';
    }

    /**
     * Giving welcome points
     * Validate and insert the Validated user in the database
     * used on website system admin pages
     * <AUTHOR> N.
     * @version September 2015 V3.4.0
     * @since August september 2015 V3.3.1
     */
    public function actionValidateAndInsertUsers()
    {
        $selectAll = false;
        $ids = [];
        $action = 'import_users';
        $welcomePts = (isset($_POST['welcomePts'])) ? (int) $_POST['welcomePts'] : 0;
        $followFlag = true;
//        $followFlag = (isset($_POST['follow']) && $_POST['follow'] === 'true') ? true : false;
        //Flag in case we need to insert status verified for phone
        $verifyAccount = (isset($_POST['verified']) && $_POST['verified'] === 'true') ? true : false;
        $pinCode = isset($_POST['pin_code']) ? trim((string)$_POST['pin_code']) : '';

        if(isset($_POST['selectedIds_all']) && $_POST['selectedIds_all'] == 1) {
            $selectAll = true;
        } elseif (isset($_POST['selectedIds'])) {
            $ids = $_POST['selectedIds'];
            $selectAll = false;
        }

        if (!$selectAll && count($ids) == 0) {
            $response = ['status' => 'NOT_OK', 'message' => 'No contacts selected. Please select and try again'];
            Utils::sendResponse(200, CJSON::encode($response));
        }

        if ($pinCode && strlen((string)$pinCode) > 0 && strlen((string)$pinCode) < 4) {
            Utils::sendResponse(200, CJSON::encode(['status' => 'NOT_OK', 'message' => 'The PIN Code must be 4 digits']));
        }

        if ($pinCode && !is_numeric($pinCode)) {
            Utils::sendResponse(200, CJSON::encode(['status' => 'NOT_OK', 'message' => 'The PIN Code must be a number']));
        }

        $platformName = PlatformSolution::model()->findByPk(5)->name;
        $timeZoneId = TimezoneMysql::getTMZPkByTMZName(self::TMZ_MONTREAL_NAME, $platformName);

        ImportContacts::validateAndInsertUsers($_POST, $selectAll, $ids, $action, $welcomePts, $followFlag, $verifyAccount, $timeZoneId);

        Yii::app()->user->setFlash('success', 'Contacts successfully imported');
        $response = ['status' => 'redirect', 'message' => 'Contacts successfully imported', 'redirect' => '/systemAdmin/importContacts/admin?hidebttn=1'];
        Utils::sendResponse(200, CJSON::encode($response));

        // Get first record from import table
        // $firstContact = ImportContacts::model()->find();

        // // Hash the task and business branch - to make sure the job executes once
        // $task = 'import.contacts.create';
        // $worker = 'Worker_ImportContacts';
        // $hash = hash('sha256', $task.'-'.$firstContact->business_branch_fk);

        // //Check if the record exists in the queue by hash(TASK,ID) and get the last record
        // $queue = ResqueQueue::model()->findByAttributes(['hash' => $hash, 'active' => 1], ['order' => 'id DESC']);

        // if ($queue) {
        //     Yii::log("{worker} {$task} still running branchId={$firstContact->business_branch_fk} queueId={$queue->id}", CLogger::LEVEL_ERROR, __METHOD__);
        //     Yii::app()->user->setFlash('error', 'Import Job is still running');
        //     Utils::sendResponse(200, CJSON::encode([
        //         'status' => 'NOT_OK',
        //         'message' => 'Import Job is still running',
        //         'redirect' => '/systemAdmin/importContacts/admin',
        //     ]));
        //     return;
        // }

        // //Create a new record
        // $queue = new ResqueQueue;
        // $queue->hash = $hash;
        // $queue->name = $worker;
        // $queue->queue = 'import_contacts';
        // $queue->task = $task;
        // $queue->save();

        // $jobArgs = [
        //     'queueId' => $queue->id,
        //     'task' => $task,
        //     'db' => Yii::app()->params['dbname'],
        //     'action' => $action,
        //     'selectAll' => $selectAll,
        //     'welcomePts' => $welcomePts,
        //     'followFlag' => $followFlag,
        //     'verifyAccount' => $verifyAccount,
        //     'timeZoneId' => $timeZoneId,
        //     'post' => $_POST,
        //     'ids' => $ids,
        // ];

        // // Create the job that will import contacts
        // $jobId = Yii::app()->resque->createJob('import_contacts', 'Worker_ImportContacts', $jobArgs, true);

        // // Update the queue model with job id and arguments
        // $queue->args = json_encode($jobArgs);
        // $queue->job_id = $jobId;
        // $queue->save();

        // Yii::log("{worker} {$task} Started for branchId={$firstContact->business_branch_fk}", CLogger::LEVEL_INFO, __METHOD__);

        // //message
        // Yii::app()->user->setFlash('success', 'Import Contacts Job successfully started');
        // $response = ['status' => 'redirect', 'message' => 'Import Contacts Job successfully started', 'redirect' => '/systemAdmin/importContacts/admin?hidebttn=1'];
        // Utils::sendResponse(200, CJSON::encode($response));
    }

    /**
     * generate qrcode for all the users that where imported to the database with status 5,6,7,8 (check import status table for more details)
     * used in website system admin
     * <AUTHOR> N.
     * @version August september 2015 V3.3.1
     *
     */
    public function actionGenerateQRCodesForUsers()
    {
        $platformName = PlatformSolution::PLATFORM_NAME_SYSTEM;
        $selectAll = false;
        $ids = [];
        $action = 'generate_qrcodes';

        if(isset($_POST['selectedIds_all']) && $_POST['selectedIds_all'] == 1) {
            $selectAll = true;
        } elseif (isset($_POST['selectedIds'])) {
            $ids = $_POST['selectedIds'];
            $selectAll = false;
        }

        if (!$selectAll && count($ids) == 0) {
            $response = ['status' => 'NOT_OK', 'message' => 'No contacts selected. Please select and try again'];
            Utils::sendResponse(200, CJSON::encode($response));
        }

        try {
            $model = new ImportContacts('search');
            $model->unsetAttributes(); // clear any default values
            if (isset($_POST['ImportContacts'])) {
                $model->attributes = $_POST['ImportContacts'];
            }

            $dataProvider = $model->search($selectAll, $ids, $action);

            $importedUsers = $dataProvider->getData();

            //$importedUsers = ImportContacts::model()->findAll(array("condition"=>"import_status_fk in (5,6,7,8) and qrcode_generated = 0"));

            foreach ($importedUsers as $importedUser) {
                $user = User::model()->findByPk($importedUser->user_fk);
                if ($user->first == null || $user->first == '' || $user->last == null || $user->last == '') {
                    $fullName = ($user->email == '' || $user->email == null) ? $user->phone : $user->email;
                } else {
                    $fullName = $user->last . ' ' . $user->first;
                }

//                Utils::generateQrCode($user->qrcode, $fullName, $user->signedupat_fk);
                $importedUser->qrcode_generated = 1;
                $importedUser->save();
            }
            $response = ['status' => 'OK', 'message' => 'QR codes generated successfully!'];
            Utils::sendResponse(200, CJSON::encode($response));
        } catch (Exception $ex) {
            Yii::log('Error = ' . $ex->getMessage(), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            $response = ['status' => 'NOT_OK', 'message' => $ex->getMessage()];
            Utils::sendResponse(500, CJSON::encode($response));
        }
    }

    /**
     * Marketing campaign credit for businesses that have a package
     * Handle timezone
     * Used in : Website(SystemAdmin)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return html
     */
    public function actionMarketingCampaignCredit()
    {
        $credit = EntityMktgPackageCredit::getEntitiesCreditCurrentMonth();
        $timezoneId = Utils::getTimezoneSession();
        $timezoneName = TimezoneMysql::model()->findByPk($timezoneId)->name;

        $this->render('marketingCampaignCredit', ['credit' => $credit, 'timezoneName' => $timezoneName]);
    }
}
