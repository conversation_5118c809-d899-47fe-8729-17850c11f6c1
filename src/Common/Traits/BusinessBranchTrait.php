<?php

namespace KangarooRewards\Common\Traits;

use App\Models\V1\BusinessBranch;
use App\Models\V1\PosBranch;
use KangarooRewards\Common\Models\BusinessBranch as ModelBusinessBranch;

use KangarooRewards\Common\Helpers\Utils;
use Illuminate\Support\Facades\DB;

trait BusinessBranchTrait
{

    /**
     * Getting the business default branch
     *
     * Used in : BusinessAdmin /smartphone (android/iphone) / Merchant Tablet app
     * @access public
     * @return Array()
     */
    public static function getDefaultBranch($businessId)
    {
        $results = \DB::table('business_branch')
            ->select('*')
            ->where('business_branch.default_branch', '=', 1)
            ->where('business_branch.business_fk', '=', $businessId)
            ->where('business_branch.enabled', '=', 1)
            ->get();

        return $results;
    }

    /**
     * Getting all branche IDs for a specific business
     * To check the allowed branches
     * Used in : Website,  (Business Admin, add Employee), Android Merchant Tablet
     *
     * @param int $businessId
     * @param bool $count
     * @return array|integer
     * @adaptation AviEL
     * @version Mar - Apr 2015 release V3.2.5
     * @since 3.1.1
     * <AUTHOR>
     * @access public
     */
    public static function getAllowedBranchesByBusiness($businessId, $count = false)
    {
        if ($count==true) {
            $results = \DB::table('business_branch')
                ->select('business_branch.business_branch_pk')
                ->where('business_branch.business_fk', '=', $businessId)
                ->where('business_branch.enabled', '=', 1)
                ->get();

            return count($results);
        } elseif ($count == false) {
            $results = \DB::table('business_branch')
                ->select('business_branch.business_branch_pk', 'business_branch.default_branch')
                ->where('business_branch.business_fk', '=', $businessId)
                ->where('business_branch.enabled', '=', 1)
                ->get();

            $results = Utils::listDataKeys($results, 'business_branch_pk', 'type');
            return $results;
        }
    }

    /**
     * Get a list / count of enabled business branches
     * @param $businessId
     * @param bool $count
     * @return mixed
     */
    public function getEnabledBusinessBranches($businessId, bool $count = false): mixed
    {
        $branches = BusinessBranch::select('business_branch_pk', 'branch_uid', 'timezone_mysql_fk')
            ->where('business_fk', $businessId)
            ->where('enabled', 1);

        if ($count) {
            return $branches->count();
        } else {
            return $branches->get();
        }
    }

    /**
     * get all the enabled branches (name, street geolocation) to add them to passbook
     * @version Sept - October 2016 V4.2
     * <AUTHOR> N.
     * @return Ambigous <multitype:, mixed>
     * <AUTHOR>
     */
    public static function getDefaultPhysicalBranchId($id)
    {
        try {
            $businessBranch = ModelBusinessBranch::find($id);
            if ($businessBranch->virtual_branch_group_flag ==1) {
                //virtual branch
                $defaultBranch = ModelBusinessBranch::findByAttributes([["business_fk",'=',$businessBranch->business_fk],['default_branch','=',1]]);
                return $defaultBranch->business_branch_pk;
            } else {
                return $id;
            }
        } catch (\Exception $e) {
            return $id;
        }
    }

    /**
     * Gets the TMZ Name by passing the Branch Id
     *
     * Used in : Merchant Android app, Website(BusinessAdmin)
     * <AUTHOR> Patricia
     * @version : May - Jun 2015 release V3.3.0
     * @param int $branchId
     * @return string
     * @adaptation AviEL
     */
    public static function getTmzNamebyBranchId($branchId)
    {
        $results =  DB::table('business_branch as bb')
            ->select('tmz.name')
            ->join('timezone_mysql as tmz', 'tmz.timezone_mysql_pk', '=', 'bb.branch_timezone_fk')
            ->where('bb.business_branch_pk', $branchId)
            ->first();

        return $results;
    }

    public static function getBranchEntityTypeByBusinessEntityType($entity_type_fk)
    {
        $branch_entity_type_fk = null;

        switch ($entity_type_fk) {
            case 1:
                $branch_entity_type_fk = 2;
                break;
            case 7:
                $branch_entity_type_fk = 8;
                break;
            case 99:
                $branch_entity_type_fk = 100;
                break;
            default:
                $branch_entity_type_fk = null;
                break;
        }

        return $branch_entity_type_fk;
    }

    /**
     * Get business allowed branches V2
     *
     * @note  Enhanced non-static method; It should replicate the use of getAllowedBranchesByBusiness function
     *
     * @param int $businessId
     * @param bool $count
     * @return mixed
     */
    public function getAllowedBranchesByBusinessV2(int $businessId, bool $count = false): mixed
    {
        if ($count) {
            return BusinessBranch::select('business_branch_pk')
                ->where('business_fk', $businessId)
                ->where('enabled', 1)
                ->count();
        } else {
            return BusinessBranch::select('business_branch_pk')
                ->where('business_fk', $businessId)
                ->where('enabled', 1)
                ->pluck('business_branch_pk')
                ->toArray();
        }
    }

    /**
     * Get branch entity type for business
     *
     * @note Enhanced non-static method; t should replicate the use of getBranchEntityTypeByBusinessEntityType function
     *
     * @param $businessEntityTypeId
     * @return int|null
     */
    public function getBranchEntityTypeForBusiness($businessEntityTypeId): ?int
    {
        return match ($businessEntityTypeId) {
            1 => 2,
            7 => 8,
            99 => 100,
            default => null,
        };
    }

    /**
     * Get the hexadecimal branch IDs
     * @param $branchIds
     * @return mixed
     */
    public function getHexBranchIds($branchIds): mixed
    {
        return BusinessBranch::selectRaw('hex(branch_uid) as hex_branch_id')
            ->whereIn('business_branch_pk', $branchIds)
            ->pluck('hex_branch_id')
            ->toArray();
    }

    /**
     *  Get the hexadecimal branch ID (single ID)
     * @param $branchId
     * @return mixed
     */
    public function getHexBranchId($branchId): mixed
    {
        return BusinessBranch::selectRaw('hex(branch_uid) as hex_branch_id')
            ->where('business_branch_pk', $branchId)
            ->pluck('hex_branch_id')
            ->first();
    }

    /**
     * Get distinct ecom branches
     * @param $businessId
     * @param array $branchUids
     * @return mixed
     */
    public function getBusinessEcomBranches($businessId, array $branchUids = []): mixed
    {
        $query = BusinessBranch::from('business_branch as bb')
            ->select('bb.*', 'ps.name as pos_system_name', 'ps.pos_system_pk as pos_system_id')
            ->join('pos_branch as pb', 'pb.business_branch_fk', '=', 'bb.business_branch_pk')
            ->join('pos_system as ps', 'ps.pos_system_pk', '=', 'pb.pos_system_fk')
            ->where('bb.business_fk', $businessId)
            ->where('pb.enabled', 1)
            ->where('ps.enabled', 1)
            ->where('bb.enabled', 1)
            ->where('bb.virtual_branch_group_flag', 1)
            ->whereIn('pb.pos_system_fk', [
                POS_SYSTEM_SHOPIFY,
                POS_SYSTEM_LIGHTSPEED_ECOM,
                POS_SYSTEM_WOOCOMMERCE_ECOM,
                POS_SYSTEM_MAGENTO,
                POS_SYSTEM_BIGCOMMERCE,
                POS_SYSTEM_ECOMZ,
                POS_SYSTEM_ECWID
            ]);

        if(!empty($branchUids)) {
            $query->whereIn('bb.branch_uid', $branchUids);
        }

        $query->distinct('bb.business_branch_pk');

        return $query->get();
    }

    /**
     * Get the e-com pos system ID by branch UIDs
     * @param array $branchUids
     * @return mixed
     */
    public function getEcomPosSystemByBranchUniqueIds(array $branchUids): mixed
    {
        return PosBranch::select(['pos_system_fk'])
            ->rightJoin('business_branch', function ($join) {
                $join->on('business_branch.business_branch_pk', '=', 'pos_branch.business_branch_fk')
                    ->where('virtual_branch_group_flag', 1)
                    ->where('pos_branch.enabled', 1)
                    ->where('business_branch.enabled', 1)
                    ->whereIn('pos_system_fk', [
                        POS_SYSTEM_SHOPIFY,
                        POS_SYSTEM_LIGHTSPEED_ECOM,
                        POS_SYSTEM_WOOCOMMERCE_ECOM,
                        POS_SYSTEM_MAGENTO,
                        POS_SYSTEM_BIGCOMMERCE,
                        POS_SYSTEM_ECOMZ,
                        POS_SYSTEM_ECWID
                    ]); //ecom branches
            })
            ->whereIn('business_branch.branch_uid', $branchUids)
            ->get();
    }

    /**
     * Get default business branch ID
     * @param int $businessId
     * @return int|null
     */
    public function getDefaultBranchId(int $businessId): ?int
    {
        return BusinessBranch::where([
            'business_fk' => $businessId,
            'default_branch' => 1
        ])->value('business_branch_pk');
    }

    /**
     * Get branch timezone name
     * @param int $branchId
     * @return string|null
     */
    public function getBranchTimezoneName(int $branchId): ?string
    {
        return DB::table('business_branch as bb')
            ->join('timezone_mysql as tmz', 'tmz.timezone_mysql_pk', '=', 'bb.branch_timezone_fk')
            ->where('bb.business_branch_pk', $branchId)
            ->value('tmz.name');
    }
}
