<?php

namespace KangarooRewards\Common\Traits;

use KangarooRewards\Common\Models\FeatureFlag;

trait HasFeatureFlag
{
    /**
     * @param $name
     * @return bool
     */
    public static function feature($name)
    {
//        $key = str_replace('\\', '.',FeatureFlag::class) . '.' . __FUNCTION__ . '.' . implode('.', func_get_args());
//
//        $feature = Cache::remember($key, $minutes = 60, function () use ($name) {
//            return FeatureFlag::where(['name' => $name, 'enabled' => 1])->first();
//        });
        $feature = FeatureFlag::where(['name' => $name, 'enabled' => 1])->first();

        if ($feature) {
            return true;
        }
        return false;
    }
}
