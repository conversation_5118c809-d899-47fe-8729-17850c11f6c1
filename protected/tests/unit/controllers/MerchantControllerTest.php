<?php

Yii::import('application.controllers.MerchantController');
Yii::import('application.models.User');
Yii::import('application.models.AuditUserLogin');
Yii::import('application.models.AuditUserLoginNotification');

use PHPUnit\Framework\TestCase;

/**
 * cd protected/tests
 * ../vendor/bin/phpunit unit/MerchantControllerTest.php
 */
class MerchantControllerTest extends TestCase
{
    /** @var MerchantController */
    private $controller;

    protected function setUp(): void
    {
        parent::setUp();
        $this->controller = new MerchantController('merchant');
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        AuditUserLogin::model()->deleteAll('user_id=:uid', [':uid' => 1]);
        AuditUserLogin::model()->deleteAll('user_id=:uid', [':uid' => 2]);
        AuditUserLogin::model()->deleteAll('user_id=:uid', [':uid' => 3]);
        AuditUserLoginNotification::model()->deleteAll('user_id=:uid', [':uid' => 1]);
        AuditUserLoginNotification::model()->deleteAll('user_id=:uid', [':uid' => 2]);
        AuditUserLoginNotification::model()->deleteAll('user_id=:uid', [':uid' => 3]);
    }

    public function testNonProdEnvReturnsFalse()
    {
        Yii::app()->params['envParam'] = 'DEV';
        $user = new User();
        $user->id = 1;
        $location = (object)['countryCode' => 'US', 'city' => 'NY'];
        $this->assertFalse($this->invokeShouldForceMfa($user, $location));
    }

    public function testNoPreviousLoginReturnsTrue()
    {
        Yii::app()->params['envParam'] = 'PROD';
        $user = new User();
        $user->id = 999;
        AuditUserLogin::model()->deleteAll('user_id=:uid', [':uid' => $user->id]);
        $_SERVER['REMOTE_ADDR'] = '127.0.0.1';
        $_SERVER['HTTP_USER_AGENT'] = 'UA';
        $location = (object)['countryCode' => 'US', 'city' => 'NY'];
        $this->assertTrue($this->invokeShouldForceMfa($user, $location));
    }

    public function testSameIpAndUserAgentReturnsFalse()
    {
        Yii::app()->params['envParam'] = 'PROD';
        $user = new User();
        $user->id = 2;
        AuditUserLogin::model()->deleteAll('user_id=:uid', [':uid' => $user->id]);
        $login = new AuditUserLogin();
        $login->user_id = $user->id;
        $login->ip = '********';
        $login->user_agent = 'TestAgent';
        $login->platform_id = PLATFORM_MERCHANT_PORTAL;
        $login->created_at = date('Y-m-d H:i:s');
        $login->details = null;
        $login->save(false);
        $_SERVER['REMOTE_ADDR'] = '********';
        $_SERVER['HTTP_USER_AGENT'] = 'TestAgent';
        $location = (object)['countryCode' => 'US', 'city' => 'NY'];
        $this->assertFalse($this->invokeShouldForceMfa($user, $location));
    }

    public function testDifferentLocationReturnsTrue()
    {
        Yii::app()->params['envParam'] = 'PROD';
        $user = new User();
        $user->id = 3;
        AuditUserLogin::model()->deleteAll('user_id=:uid', [':uid' => $user->id]);
        $prev = new AuditUserLogin();
        $prev->user_id = $user->id;
        $prev->ip = '********';
        $prev->user_agent = 'AgentX';
        $prev->platform_id = PLATFORM_MERCHANT_PORTAL;
        $prev->created_at = date('Y-m-d H:i:s', time() - 3600);
        $prev->details = json_encode(['location' => ['countryCode' => 'CA', 'city' => 'Toronto']]);
        $prev->save(false);
        $_SERVER['REMOTE_ADDR'] = '********';
        $_SERVER['HTTP_USER_AGENT'] = 'AgentY';
        $location = (object)['countryCode' => 'US', 'city' => 'New York'];
        $this->assertTrue($this->invokeShouldForceMfa($user, $location));
    }

    public function testSameLocationDifferentUserAgentReturnsTrue()
    {
        Yii::app()->params['envParam'] = 'PROD';
        $user = new User();
        $user->id = 3;
        AuditUserLogin::model()->deleteAll('user_id=:uid', [':uid' => $user->id]);
        $prev = new AuditUserLogin();
        $prev->user_id = $user->id;
        $prev->ip = '********';
        $prev->user_agent = 'AgentX';
        $prev->platform_id = PLATFORM_MERCHANT_PORTAL;
        $prev->created_at = date('Y-m-d H:i:s', time() - 3600);
        $prev->details = json_encode(['location' => ['countryCode' => 'US', 'city' => 'New York']]);
        $prev->save(false);
        $_SERVER['REMOTE_ADDR'] = '********';
        $_SERVER['HTTP_USER_AGENT'] = 'AgentY';
        $location = (object)['countryCode' => 'US', 'city' => 'New York'];
        $this->assertTrue($this->invokeShouldForceMfa($user, $location));
    }

    public function testSameLocationSameUserAgentReturnsFalse()
    {
        Yii::app()->params['envParam'] = 'PROD';
        $user = new User();
        $user->id = 3;
        AuditUserLogin::model()->deleteAll('user_id=:uid', [':uid' => $user->id]);
        $prev = new AuditUserLogin();
        $prev->user_id = $user->id;
        $prev->ip = '********';
        $prev->user_agent = 'AgentX';
        $prev->platform_id = PLATFORM_MERCHANT_PORTAL;
        $prev->created_at = date('Y-m-d H:i:s', time() - 3600);
        $prev->details = json_encode(['location' => ['countryCode' => 'US', 'city' => 'New York']]);
        $prev->save(false);
        $_SERVER['REMOTE_ADDR'] = '********';
        $_SERVER['HTTP_USER_AGENT'] = 'AgentX';
        $location = (object)['countryCode' => 'US', 'city' => 'New York'];
        $this->assertFalse($this->invokeShouldForceMfa($user, $location));  
    }

    public function testSameIpDifferentUserAgentReturnsTrue()
    {
        Yii::app()->params['envParam'] = 'PROD';
        $user = new User();
        $user->id = 3;
        AuditUserLogin::model()->deleteAll('user_id=:uid', [':uid' => $user->id]);
        $prev = new AuditUserLogin();
        $prev->user_id = $user->id;
        $prev->ip = '********';
        $prev->user_agent = 'AgentX';
        $prev->platform_id = PLATFORM_MERCHANT_PORTAL;
        $prev->created_at = date('Y-m-d H:i:s', time() - 3600);
        $prev->details = json_encode(['location' => ['countryCode' => 'US', 'city' => 'New York']]);
        $prev->save(false);
        $_SERVER['REMOTE_ADDR'] = '********';
        $_SERVER['HTTP_USER_AGENT'] = 'AgentY';
        $location = (object)['countryCode' => 'US', 'city' => 'New York'];
        $this->assertTrue($this->invokeShouldForceMfa($user, $location));  
    }

    public function testSameUserAgentDifferentIpReturnsFalse()
    {
        Yii::app()->params['envParam'] = 'PROD';
        $user = new User();
        $user->id = 3;
        AuditUserLogin::model()->deleteAll('user_id=:uid', [':uid' => $user->id]);
        $prev = new AuditUserLogin();
        $prev->user_id = $user->id;
        $prev->ip = '********';
        $prev->user_agent = 'AgentX';
        $prev->platform_id = PLATFORM_MERCHANT_PORTAL;
        $prev->created_at = date('Y-m-d H:i:s', time() - 3600);
        $prev->details = json_encode(['location' => ['countryCode' => 'US', 'city' => 'New York']]);
        $prev->save(false);
        $_SERVER['REMOTE_ADDR'] = '********';
        $_SERVER['HTTP_USER_AGENT'] = 'AgentX';
        $location = (object)['countryCode' => 'US', 'city' => 'New York'];
        $this->assertFalse($this->invokeShouldForceMfa($user, $location));  
    }

    /**
     * Helper to invoke private shouldForceMfa method
     */
    private function invokeShouldForceMfa($user, $location)
    {
        $ref = new ReflectionMethod($this->controller, 'shouldForceMfa');
        $ref->setAccessible(true);
        return $ref->invoke($this->controller, $user, $location);
    }
}
