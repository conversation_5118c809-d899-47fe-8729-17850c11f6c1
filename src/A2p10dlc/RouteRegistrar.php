<?php

namespace KangarooRewards\A2p10dlc;

use Illuminate\Contracts\Routing\Registrar as Router;

class RouteRegistrar
{
    /**
     * The router implementation.
     *
     * @var \Illuminate\Contracts\Routing\Registrar
     */
    protected $router;

    /**
     * Create a new route registrar instance.
     *
     * @param  \Illuminate\Contracts\Routing\Registrar  $router
     * @return void
     */
    public function __construct(Router $router)
    {
        $this->router = $router;
    }

    /**
     * Register routes
     *
     * @return void
     */
    public function all()
    {
        // $this->forA2P10DLC();
    }

    /**
     * Register the routes needed for Survey.
     *
     * @return void
     */
    public function forA2P10DLC()
    {
        /*$this->router->group(['middleware' => ['api']], function ($router) {
            $router->get('/a2p', [
                'uses' => 'ExampleRepoController@index',
            ]);
        });*/
    }
}
