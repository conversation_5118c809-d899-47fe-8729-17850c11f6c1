<?php
/* @var $this PosSaleController */
/* @var $model PosSale */

$this->breadcrumbs=array(
	'Pos Sales'=>array('index'),
	$model->pos_sale_pk,
);

$this->menu=array(
	array('label'=>'Manage PosSale', 'url'=>array('admin')),
);
?>

<h1>View PosSale #<?php echo $model->pos_sale_pk; ?></h1>

<p>
	Completed: <span class="badge"><?= $sale['completed'] ?></span>
	Canceled: <span class="badge"><?= $sale['archived'] ?></span>
	Voided: <span class="badge"><?= $sale['voided'] ?></span>
</p>

<?php $this->widget('zii.widgets.CDetailView', array(
	'data'=>$model,
	'attributes'=>array(
		'pos_sale_pk',
		'processed',
		'redeem_amount',
		'sale_id',
		'sale_time_stamp',
		'sale_completed',
		'sale_archived',
		'sale_voided',
		'sale_enable_promotions',
		'sale_tax_inclusive',
		'sale_reference_number',
		'sale_tax1_rate',
		'sale_tax2_rate',
		'sale_change',
		'sale_ticket_number',
		'sale_calc_discount',
		'sale_calc_total',
		'sale_calc_sub_total',
		'sale_calc_taxable',
		'sale_calc_non_taxable',
		'sale_calc_avg_cost',
		'sale_calc_fifo_cost',
		'sale_calc_tax1',
		'sale_calc_tax2',
		'sale_calc_payments',
		'sale_total',
		'sale_total_due',
		'sale_balance',
		'sale_customer_id',
		'sale_discount_id',
		'sale_employee_id',
		'sale_quote_id',
		'sale_register_id',
		'sale_shop_id',
		'sale_tax_category_id',
		'sale_tax_total',
		'account_id',
		'utc_updated',
		'utc_created',
		'user_fk',
		'pos_system_fk',
		'pos_branch_fk',
		'transaction_sub_type_fk',
	),
)); ?>

<br>

<p>x-ls-api-bucket-level: <?= $xLimit ?></p>

<p><a href="/systemAdmin/posSale/fbp?id=<?php echo $model->pos_sale_pk; ?>"> Create FBP</a></p>

Sale:
<?php
	if (isset($sale['SaleLines']['SaleLine'][0])) {
        $saleLines = $sale['SaleLines']['SaleLine']; //multiple sale lines
    } elseif (isset($sale['SaleLines']['SaleLine'])) {
        $saleLines = [$sale['SaleLines']['SaleLine']]; //one sale line
    } else {
        $saleLines = [];
    }
?>
<table class="table">
	<tr>
		<th>saleID</th>
		<th>completed</th>
		<th>archived</th>
		<th>createTime</th>
		<th>completeTime</th>
		<th>customerID</th>
		<th>employeeID</th>
	</tr>
	<tr>
		<td><?= $sale['saleID'] ?></td>
		<td><?= $sale['completed']?></td>
		<td><?= $sale['archived'] ?></td>
		<td><?= $sale['createTime'] ?></td>
		<td><?= isset($sale['completeTime']) ? $sale['completeTime'] : '' ?></td>
		<td><?= $sale['customerID'] ?></td>
		<td><?= $sale['employeeID'] ?></td>
	</tr>
</table>
Sale Lines:
<table class="table">
	<tr>
		<th></th>
		<th>saleLineID</th>
		<th>description</th>
		<th>unitQuantity</th>
		<th>unitPrice</th>
		<th>calcTotal</th>
		<th>calcSubtotal</th>
		<th>itemID</th>
		<th>systemSku</th>
		<th>createTime</th>
		<th>FBP</th>
	</tr>
<?php foreach ($saleLines as $sl): ?>

	<?php
    $itemSystemSku = $sl['Item']['systemSku'] ?? '';
    $fbProduct = PosLsSaleLine::getFBProgramForItem($model->account_id, $itemSystemSku) ?>

	<tr>
		<td></td>
		<td><?= $sl['saleLineID'] ?></td>
		<td>
			<?= $sl['Item']['description'] ?? '' ?>
			<?php if ($sl['discountID'] > 0): ?>
				<div><small class="text-muted">Discount: Amount $<?= $sl['discountAmount'] ?> Line: $<?= $sl['calcLineDiscount'] ?></small></div>
			<?php endif ?>
		</td>
		<td><?= $sl['unitQuantity'] ?></td>
		<td><?= $sl['unitPrice'] ?></td>
		<td><?= $sl['calcTotal'] ?></td>
		<td><?= $sl['calcSubtotal'] ?></td>
		<td><?= $sl['itemID'] ?></td>
		<td><a href="/systemAdmin/posSale/view?id=<?= $model->primaryKey; ?>&itemId=<?= $sl['itemID']; ?>"><?= $itemSystemSku ?></a></td>
		<td><?= $sl['createTime'] ?></td>
		<td><?= $fbProduct ? $fbProduct['frequent_buyer_pk']. '-' . $fbProduct['title'] : ''; ?></td>
	</tr>
<?php endforeach ?>
</table>

<table class="table">
	<tr>
		<td align="right">
			<b>calcTotal:</b> <?= $sale['calcTotal'] ?> <br>
			<b>calcSubtotal:</b> <?= $sale['calcSubtotal'] ?> <br>
			<b>calcDiscount:</b> <?= $sale['calcDiscount'] ?> <br>
		</td>
	</tr>
</table>

<?php if ($itemSaleLines): ?>
<h3>Last 100 sales for item: <?= $_GET['itemId'] ?></h3>
<table class="table">
	<tr>
		<th></th>
		<th>saleLineID</th>
		<th>saleID</th>
		<th>discount</th>
		<th>unitQuantity</th>
		<th>unitPrice</th>
		<th>calcTotal</th>
		<th>calcSubtotal</th>
		<th>createTime</th>
	</tr>
<?php foreach ($itemSaleLines as $sl): ?>
	<tr>
		<td></td>
		<td><?= $sl['saleLineID'] ?></td>
		<td><?= $sl['saleID'] ?></td>
		<td>
			<?php if ($sl['discountID'] > 0): ?>
				<div><small class="text-muted">Discount: Amount $<?= $sl['discountAmount'] ?> Line: $<?= $sl['calcLineDiscount'] ?></small></div>
			<?php endif ?>
		</td>
		<td><?= $sl['unitQuantity'] ?></td>
		<td><?= $sl['unitPrice'] ?></td>
		<td><?= $sl['calcTotal'] ?></td>
		<td><?= $sl['calcSubtotal'] ?></td>
		<td><?= $sl['createTime'] ?></td>
	</tr>
<?php endforeach ?>
</table>
<?php endif ?>

Sale:
<pre>
<?php print_r($sale) ?>
</pre>

Items:
<pre>
<?php print_r($items) ?>
</pre>

Headers:
<pre>
<?php print_r($headers) ?>
</pre>