<?php

/**
 * Created by <PERSON><PERSON><PERSON>
 * Date: 10/26/2023
 */

namespace KangarooRewards\RecommendationFeed\Triggers;

use KangarooRewards\RecommendationFeed\Exceptions\TriggerHandleException;
use KangarooRewards\RecommendationFeed\Services\RecommendationService;
use KangarooRewards\RecommendationFeed\Contracts\TriggerInterface;
use Illuminate\Support\Facades\Log;
use App\Traits\CampaignTrait;


class UpdateIfNoRetentionCampaignConfigured implements TriggerInterface
{
    use CampaignTrait;

    const NAME = 'Update If No Retention Campaigns Have Been Sent';
    const RECOMMENDATION_TYPE = RECOMMENDATION_TYPE_CAMPAIGN;
    const RECOMMENDATION_ACTION = ACTION_NO_RETENTION_CAMPAIGN;


    protected $businessId;
    protected $recommendation;

    public function __construct(RecommendationService $recommendationService)
    {
        return $this->recommendation = $recommendationService;
    }

    public function setBusinessId(int $businessId): void
    {
        $this->businessId = $businessId;
    }

    public function getName(): string
    {
        return self::NAME;
    }

    public function getType(): int
    {
        return self::RECOMMENDATION_TYPE;
    }

    public function getRecommendationId(): string
    {
        return $this->recommendation
            ->getByAction(self::RECOMMENDATION_ACTION)
            ->pluck('id')[0];
    }

    public function shouldRun()
    {
        //checks if retention campaigns are configured
        $count = $this->listCampaignsForBusinessAll($this->businessId)
        ->whereJsonContains('options->whoWillReceive', '32')
        ->where('draft', 0)
        ->get()
        ->count();

        return ($count == 0);
    }

    public function handle($userIds)
    {
        try {
            foreach($userIds as $userId) {
                $this->recommendation
                ->attachToUser($userId, $this->getRecommendationId());
            }
        } catch (\Exception $e) {

                \Log::error(__METHOD__, [
                    'data' => [
                        'recommendation_name' => $this->getName(),
                        'user_id' => $userIds,
                        'recommendation_id' => $this->getRecommendationId()
                    ],
                    'e' => $e
                ]);

            throw new TriggerHandleException('Error in handle: ' . $e->getMessage());
        }
    }
}
