/*
|
|---------------------------------------------------------------------------------------
|
| FEATURE:
| VERSION: 6.9.0
| Deployment:  19/05/2025
|---------------------------------------------------------------------------------------
|
*/

-- Val: 2025-05-07
ALTER TABLE merchant_profile ADD COLUMN mfa_channels JSON NULL DEFAULT NULL AFTER `mfa_preferred_channel`;

ALTER TABLE merchant_profile 
MODIFY COLUMN `utc_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Globalization - UTC (0)' AFTER `merchant_profile_pk`,
MODIFY COLUMN `mfa_preferred_channel` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'email' COMMENT 'Preferred mfa channel for user' AFTER `twilio_totp_sid`,
MODIFY COLUMN `favorite_modules` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'Pinned & unpinned modules for the BP mobile app'; -- AFTER `mfa_sms`;
-- End Val: 2025-05-07

-- Mohammad START 2025-05-09
ALTER TABLE business_geofences
    CHANGE linked_campaigns campaign_fk INT UNSIGNED DEFAULT NULL;

ALTER TABLE business_geofences
    ADD INDEX idx_campaign_fk (campaign_fk),
    ADD CONSTRAINT fk_campaign FOREIGN KEY (campaign_fk) REFERENCES entity_campaign(entity_campaign_pk)
        ON DELETE RESTRICT ON UPDATE RESTRICT;
-- Mohammad END 2025-05-09

-- Mohammad START 2025-05-13
ALTER TABLE entity_campaign
    ADD COLUMN trigger_schedule_timing JSON DEFAULT NULL AFTER audience_type;
ALTER TABLE entity_campaign
    MODIFY COLUMN trigger_schedule_timing JSON COMMENT 'Stores active days/time for geofence and auto campaigns';
-- Mohammad END 2025-05-13


-- Lei Start 2025-05-08
ALTER TABLE `email_templates`
    ADD COLUMN `storage_url` VARCHAR(255) DEFAULT NULL;

CREATE TABLE `workflows`.`messages_queue` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `send_to` varchar(100) DEFAULT NULL,
  `params` json DEFAULT NULL COMMENT 'json object with list of params',
  `max_attempts` tinyint(3) unsigned NOT NULL DEFAULT '2' COMMENT 'By detault is 2 BUT for push_notif is has to be ALWAYS = 1',
  `cell_email` tinyint(1) unsigned DEFAULT '1' COMMENT 'It has to be read like this: email=1, cell=2, push_notif=3',
  `error_details` text,
  `attempts` tinyint(3) unsigned DEFAULT '0',
  `external_message_id` varchar(100) DEFAULT NULL,
  `utc_last_attempt` timestamp NULL DEFAULT NULL COMMENT 'Globalization - UTC (0)',
  `utc_start` timestamp NULL DEFAULT NULL COMMENT 'Globalization - UTC (0)',
  `utc_sent` timestamp NULL DEFAULT NULL COMMENT 'Globalization - UTC (0)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `user_fk` int(11) unsigned DEFAULT NULL COMMENT 'Saving user id to track campaign by user - in case he chages email-phonenb',
  `country_phone_fk` int(11) DEFAULT NULL COMMENT 'This is current country FK associated to the User phone nb code',
  `email_sms_status_fk` int(11) unsigned NOT NULL DEFAULT '1' COMMENT 'Email sms status',
  `smsvendor_fk` int(11) unsigned DEFAULT NULL COMMENT 'SMS Vendor FK used to send SMS',
  `workflow_action_id` bigint(20) unsigned NOT NULL,
  `workflow_event_id` bigint(20) unsigned NOT NULL,
  `business_fk` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `messages_queue_ibfk_1` FOREIGN KEY (`workflow_action_id`) REFERENCES `workflow_actions` (`id`),
  CONSTRAINT `messages_queue_ibfk_2` FOREIGN KEY (`workflow_event_id`) REFERENCES `workflow_events` (`id`),
  INDEX `messages_queue_ix_1` (`business_fk`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE `marketing_credit_history`
    Modify COLUMN `type` TINYINT(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '1: marketing; 2: survey broadcasting; 3: transactional;4: new credit based on package; 5: Owned credit from previous month 6: Unused credit; 7: Workflow',
    ADD COLUMN `workflow_message_queue_fk` INT(11) UNSIGNED DEFAULT NULL,
    ADD INDEX `mkt_credit_history_ibfk5` (`workflow_message_queue_fk`) USING BTREE ;

INSERT INTO `workflows`.`main_actions`(`id`,`action_name`, `type_id`, `dsl_call`, `dsl_retry_steps`) VALUES (7, 'Send Push', 7, 'http.post', '{"max_retries":10,"backoff":{"initial_delay":3,"max_delay":90,"multiplier":3}}');

ALTER TABLE `workflows`.`ecom_abandoned_carts`
    ADD COLUMN `subtotal` decimal(10,2) DEFAULT NULL,
    ADD COLUMN `total` decimal(10,2) DEFAULT NULL;
-- Lei End 2025-05-08

