
#run all tests
php vendor/phpunit/phpunit/phpunit --debug

#run specific folder
php vendor/phpunit/phpunit/phpunit tests\ApiUsers\V1

#run tests for one method
php vendor/phpunit/phpunit/phpunit --filter testGetCustomer tests/Api/V1/ApiCustomersTest.php
php vendor/phpunit/phpunit/phpunit --filter testAwsEmailDelivery tests/Feature/AwsEmailFeedbackTest.php
php vendor/phpunit/phpunit/phpunit --filter testReplaceWelcomeEmailBlocks tests/Traits/TemplateTraitTest.php

# get access token from
http://oauth2-kngapi.dev/kangaroo-demo-app/index.php

#and replace in phpunit.xml

// if (!$request->user()->tokenCan('manage_transactions')) {
        //     return Utils::fail(403, 1);
        // }
        
    /**
     * Create a new Customer
     * Returns JSON representation of a new created customer
     *
     * POST /customer
     *
     * @Post("/customer")
     * @Versions({"v1"})
     * @Response(200, body={"data":[{"id":12345678,"first":"Sara","last":"Silverman","email":"<EMAIL>"},{"id":12345679,"first":"victoria","last":"Leblanc","email":"<EMAIL>"}],"cursor": {"next": 2,"next_uri": "/customers?page=2"}})
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     */
    // public function store(Request $request)
    // {
    //     $user = Auth::user();//merchant user id
    //     $business = Business::forUser($user->id);
    //     $mainBranch = BusinessBranch::getInfo($business->business_branch_pk);

    //     if ($request->isJson()) {
    //         $data = $request->json()->all();
    //     } else {
    //         $data = $request->all();
    //     }

    //     $this->validateCreate($data)->validate();

    //     if (!empty($data['email'])) {

    //     } elseif(!empty($data['phone'])) {
    //         //TODO Validate Phone with LibphoneNumber
    //         $data['phone'] = Utils::normalizeDigitsOnly($data['phone']);
    //     }

    //     $data['country_phone_fk'] = $mainBranch->country_pk;
    //     $data['timezone_mysql_fk'] = $business->branch_timezone_fk;
    //     $data['signedupat_fk'] = $business->business_branch_pk;
    //     $data['platform_solution_fk'] = config('app.platform_id');
    //     $data['ip'] = '';

    //     // $data['username'] = mt_rand(1000000000, 9999999999);

    //     // $transaction = \Illuminate\Support\Facades\DB::transaction(function() use ($data) {
    //     //     $newCustomer = Customer::create($data);
    //     //     $customer = Customer::find($newCustomer->id, ['user_uid', 'id', 'first', 'last', 'email', 'phone']);
    //     //     return Customer::transformItem($customer);
    //     // });
    //     // return $this->respondWithArray(['data' => $transaction]);

    //     try {
    //         //prepare for SmartPhone API
    //         $data['appToken'] = config('app.api_app_token');
    //         $data['countryCodeIso'] = isset($data['country_code']) ? $data['country_code'] : null;
    //         $data['timeZoneName'] = $business->time_zone;

    //         $http = new \GuzzleHttp\Client(['base_uri' => config('app.server_url')]);
    //         $response = $http->request('POST', '/api/smartPhone/signUp', [
    //             'form_params' => $data,
    //         ]);

    //         $body = (string) $response->getBody();
    //         $result = json_decode($body, true);

    //         if ($result['status'] == 'NOT_OK') {
    //             Log::warning(__METHOD__, ['message' =>'NOT_OK from SmartPhone API', 'result' => $result, 'business'=> $business->name]);
    //             return Utils::fail(400, null, $result['message']);
    //         }

    //         if ($result['status'] == 'OK' && $result['results']['user_id']) {
    //             $userId = (int) $result['results']['user_id'];

    //             $customer = Customer::findOrFail($userId);

    //             $customer = Customer::saveOptionalAttributes($customer, $data);

    //             // TODO update first name
    //             $_customer = Customer::transformItem($customer);
    //             return $this->respondWithArray(['data' => $_customer]);
    //         } else {
    //             Log::warning(__METHOD__, ['message' =>'NO User Id from SmartPhone API', 'result' => $result, 'business'=> $business->name]);
    //             return $this->errorInternalError();
    //         }

    //     } catch (\GuzzleHttp\Exception\RequestException $e) {
    //         Log::error(__METHOD__, ['message' => $e->getResponse(), 'business'=> $business->name]);
    //         return $this->errorInternalError();
    //         // return $this->respondWithArray([
    //         //     'code' => $e->getResponse()->getStatusCode(),
    //         //     'error'=>$e->getResponse()->getReasonPhrase(),
    //         //     'message'=>$e->getMessage(),
    //         // ], $e->getResponse()->getStatusCode());
    //     } catch (\Exception $e) {
    //         Log::error(__METHOD__, ['message' => $e, 'business'=> $business->name]);
    //         // var_dump(htmlentities($e));die;
    //         return $this->errorInternalError();
    //     }
    // }