<?php

declare(strict_types=1);

namespace App\Services;

use App\Notifications\SystemLoginAlert;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use KangarooRewards\Common\Models\AuditUserLogin;
use KangarooRewards\Common\Models\AuditUserLoginAttempt;
use KangarooRewards\Common\Helpers\Utils;

class LoginMonitoringService
{
    private const WINDOW_MINUTES = 15;
    private const THRESHOLD_MULTIPLIER = 3;
    private const PER_USER_FAILURE_THRESHOLD = 5;
    private const LOCATION_HISTORY_DAYS = 30;
    private const PLATFORM_IDS = [1, 25]; // Old and New Portal for now
    private const IP_CACHE_TTL = 86400; // 24 hours in seconds

    /**
     * Timestamp when monitoring started.
     */
    private Carbon $monitoringTime;

    /**
     * Store alerts generated during monitoring
     */
    private array $alerts = [];

    /**
     * Recipients for alert notifications
     */
    private array $recipients = [];

    /**
     * Monitor login patterns and collect alerts
     *
     * @return self
     */
    public function monitorLoginPatterns(): self
    {
        $this->recipients = [config('app.MAIL_TECH_SUPPORT')];
        $this->monitoringTime = Carbon::now(); // Use consistent 'now' for the entire monitoring operation
        $since = $this->monitoringTime->copy()->subMinutes(self::WINDOW_MINUTES);
        $this->alerts = [];

        // Check for login volume spikes
        $this->collectVolumeSpikes($since);

        // Check for per-user failed login spikes
        $this->collectPerUserFailures($since);

        // Check for geolocation anomalies
        $this->collectLocationAnomalies($since);

        return $this;
    }

    /**
     * Set recipients for alert notifications
     *
     * @param array $recipients
     * @return self
     */
    public function setRecipients(array $recipients): self
    {
        $this->recipients = $recipients;
        return $this;
    }

    /**
     * Send alert notifications if any alerts were generated
     *
     * @return bool Whether any alerts were sent
     */
    public function sendAlertNotifications(): bool
    {
        if (empty($this->alerts)) {
            return false;
        }

        $message = $this->formatAlertMessage();

        try {
            foreach ($this->recipients as $recipient) {
                Notification::route('mail', $recipient)
                    ->notify(new SystemLoginAlert($message));
            }

            Log::info('Login monitoring alerts sent', [
                'alertCount' => count($this->alerts),
                'recipients' => $this->recipients
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Failed to send login monitoring alerts', [
                'error' => $e->getMessage(),
                'alertCount' => count($this->alerts)
            ]);

            return false;
        }
    }

    /**
     * Get all collected alerts
     *
     * @return array
     */
    public function getAlerts(): array
    {
        return $this->alerts;
    }

    /**
     * Format alert message for notification
     *
     * @return string
     */
    private function formatAlertMessage(): string
    {
        $message = "The following login anomalies were detected:\n\n";

        foreach ($this->alerts as $index => $alert) {
            $message .= ($index + 1) . ". {$alert}\n";
        }

        $message .= "\nDetected at: " . $this->monitoringTime->toDateTimeString() . " UTC";

        return $message;
    }

    /**
     * Check for volume spikes in successful and failed logins
     */
    private function collectVolumeSpikes(Carbon $since): void
    {
        // Get current window counts
        $successfulCount = $this->getSuccessfulLoginCount($since);
        $failedCount = $this->getFailedLoginCount($since);

        // Calculate baselines
        $avgSuccessfulPerWindow = $this->calculateBaseline('user_logins', $this->monitoringTime);
        $avgFailedPerWindow = $this->calculateBaseline('user_login_attempts', $this->monitoringTime);

        // Calculate thresholds
        $successfulThreshold = max(1, (int) ceil($avgSuccessfulPerWindow * self::THRESHOLD_MULTIPLIER));
        $failedThreshold = max(1, (int) ceil($avgFailedPerWindow * self::THRESHOLD_MULTIPLIER));

        // Check for spikes
        if ($successfulCount >= $successfulThreshold) {
            $this->alerts[] = "High volume of successful logins ({$successfulCount} in last " . self::WINDOW_MINUTES
                . " min, threshold: {$successfulThreshold}).";
        }

        if ($failedCount >= $failedThreshold) {
            $this->alerts[] = "Spike of failed login attempts ({$failedCount} in last " . self::WINDOW_MINUTES
                . " min, threshold: {$failedThreshold}).";
        }
    }

    /**
     * Check for per-user failed login spikes
     */
    private function collectPerUserFailures(Carbon $since): void
    {
        $perUserFailures = AuditUserLoginAttempt::query()
            ->select('username_hash', DB::raw('COUNT(*) as cnt'))
            ->where('created_at', '>=', $since)
            ->whereIn('platform_id', self::PLATFORM_IDS)
            ->groupBy('username_hash')
            ->having('cnt', '>=', self::PER_USER_FAILURE_THRESHOLD)
            ->get();

        foreach ($perUserFailures as $row) {
            $this->alerts[] = "Username hash {$row->username_hash} had {$row->cnt} failed attempts in last "
                . self::WINDOW_MINUTES . " min.";
        }
    }

    /**
     * Check for geolocation anomalies by detecting logins from new locations.
     */
    private function collectLocationAnomalies(Carbon $since): void
    {
        $newLocationLogins = $this->detectNewLocations($since);

        foreach ($newLocationLogins as $login) {
            $locationInfo = $this->formatLocationInfo($login);
            $this->alerts[] = "User {$login->user_id} logged in from a new location {$locationInfo}"
                . " at {$login->created_at}.";
        }
    }

    /**
     * Format location information for display in alerts
     *
     * @param object $login Login record with location data
     * @return string Formatted location string
     */
    private function formatLocationInfo(object $login): string
    {
        $country = $login->countryName ?? $login->country ?? 'Unknown';
        $region = $login->regionName ?? $login->region ?? 'Unknown';
        $city = $login->city ?? '';

        $locationParts = [];

        if (!empty($country) && $country !== 'Unknown') {
            $locationParts[] = $country;
        }

        if (!empty($region) && $region !== 'Unknown') {
            $locationParts[] = $region;
        }

        if (!empty($city)) {
            $locationParts[] = $city;
        }

        if (empty($locationParts)) {
            return "IP: {$login->ip}";
        }

        return implode('/', $locationParts);
    }

    /**
     * Get a successful login count for the given time period
     */
    private function getSuccessfulLoginCount(Carbon $since): int
    {
        return AuditUserLogin::query()
            ->where('created_at', '>=', $since)
            ->whereIn('platform_id', self::PLATFORM_IDS)
            ->count();
    }

    /**
     * Get failed login count for the given time period
     */
    private function getFailedLoginCount(Carbon $since): int
    {
        return AuditUserLoginAttempt::query()
            ->where('created_at', '>=', $since)
            ->whereIn('platform_id', self::PLATFORM_IDS)
            ->count();
    }

    /**
     * Calculate a baseline average count for a given table over 15-minute windows in the last day.
     */
    private function calculateBaseline(string $tableName, Carbon $now): float
    {
        $windowMinutes = self::WINDOW_MINUTES;

        $baselineWindowCounts = DB::connection('audit_external')->table($tableName)
            ->selectRaw('COUNT(*) as cnt')
            // Group by date, hour, and N-minute window to avoid merging windows across days
            ->whereBetween('created_at', [$now->copy()->subDay(), $now])
            ->whereIn('platform_id', self::PLATFORM_IDS)
            ->groupByRaw("DATE(created_at), HOUR(created_at), FLOOR(MINUTE(created_at)/{$windowMinutes})")
            ->pluck('cnt');

        return $baselineWindowCounts->avg() ?? 0.0;
    }

    /**
     * Detect logins from new countries/regions for each user within the last window.
     * This method is optimized to avoid N+1 queries and handle JSON location data.
     *
     * @param Carbon $since Start time for the monitoring window
     * @return array Array of login records with location data
     */
    private function detectNewLocations(Carbon $since): array
    {
        // Fetch all successful logins in the current monitoring window
        $recentLogins = DB::connection('audit_external')->table('user_logins')
            ->select('id', 'user_id', 'ip', 'created_at', 'details')
            ->where('created_at', '>=', $since)
            ->whereIn('platform_id', self::PLATFORM_IDS)
            ->orderBy('user_id')
            ->orderBy('created_at')
            ->get();

        if ($recentLogins->isEmpty()) {
            return [];
        }

        // Extract and validate location data for each login
        $processedLogins = $this->processLocationData($recentLogins);

        // Get unique user IDs from the processed logins
        $userIds = collect($processedLogins)->pluck('user_id')->unique()->values()->all();

        // Fetch historical locations for these users
        $historicalLocationsData = $this->getHistoricalLocations($userIds, $since);

        // Organize historical locations for a quick lookup: [userId => [locationKey => true]]
        $knownUserLocations = [];
        foreach ($historicalLocationsData as $loc) {
            if (!isset($loc->country) || !isset($loc->region)) {
                continue; // Skip entries with missing location data
            }

            $locationKey = $this->getLocationKey($loc->country, $loc->region);
            $knownUserLocations[$loc->user_id][$locationKey] = true;
        }

        // Find logins from new locations
        $newLocationEntries = [];
        foreach ($processedLogins as $currentLogin) {
            // Skip if we couldn't extract valid location data
            if (!isset($currentLogin->country) || !isset($currentLogin->region)) {
                continue;
            }

            $locationKey = $this->getLocationKey($currentLogin->country, $currentLogin->region);

            // If this user has no known locations or this location is new for this user
            if (
                !isset($knownUserLocations[$currentLogin->user_id]) ||
                !isset($knownUserLocations[$currentLogin->user_id][$locationKey])
            ) {
                $newLocationEntries[] = $currentLogin;

                // Add this location to known locations to avoid duplicate alerts
                $knownUserLocations[$currentLogin->user_id][$locationKey] = true;
            }
        }

        return $newLocationEntries;
    }

    /**
     * Process and validate location data from login records
     *
     * @param Collection $logins Collection of login records
     * @return array Processed login records with extracted location data
     */
    private function processLocationData(Collection $logins): array
    {
        $processedLogins = [];

        foreach ($logins as $login) {
            $loginWithLocation = clone $login;

            try {
                // Extract location data from the details JSON column
                if (!empty($login->details)) {
                    $details = json_decode($login->details);

                    if (json_last_error() === JSON_ERROR_NONE && isset($details->location)) {
                        // Extract location data
                        $location = $details->location;

                        // Set location properties on the login object
                        $loginWithLocation->country = $location->countryCode ?? null;
                        $loginWithLocation->region = $location->region ?? $location->regionName ?? null;
                        $loginWithLocation->city = $location->city ?? null;

                        // If we have a valid country and region, add to processed logins
                        if (!empty($loginWithLocation->country) && !empty($loginWithLocation->region)) {
                            $processedLogins[] = $loginWithLocation;
                            continue;
                        }
                    }
                }

                // If we couldn't extract location from details, try to get it from IP
                $ipLocation = $this->getLocationFromIp($login->ip);
                if ($ipLocation) {
                    $loginWithLocation->country = $ipLocation->country ?? null;
                    $loginWithLocation->region = $ipLocation->region ?? null;
                    $loginWithLocation->city = $ipLocation->city ?? null;
                }

                $processedLogins[] = $loginWithLocation;
            } catch (Exception $e) {
                // Log error and continue with the next login
                Log::error('Error processing location data', [
                    'login_id' => $login->id ?? $login->user_id ?? null,
                    'error' => $e->getMessage(),
                ]);

                // Still add the login, but without location data
                $processedLogins[] = $loginWithLocation;
            }
        }

        return $processedLogins;
    }

    /**
     * Get location data from IP address using Utils::getGeoInfoFromIpWhoIs
     * with caching to avoid repeated lookups
     *
     * @param string|null $ip IP address
     * @return object|null Location data object or null if not found
     */
    private function getLocationFromIp(?string $ip): ?object
    {
        if (empty($ip)) {
            Log::debug('Empty IP address provided to getLocationFromIp');
            return null;
        }

        // Generate a cache key based on the IP
        $cacheKey = 'ip_location_' . md5($ip);

        // Try to get location from cache first
        $cachedLocation = Cache::get($cacheKey);
        if ($cachedLocation !== null) {
            Log::debug('IP location retrieved from cache', ['ip' => $ip]);
            return (object)$cachedLocation;
        }

        try {
            // Use the existing Utils::getGeoInfoFromIpWhoIs method to get location data
            $locationInfo = Utils::getGeoInfoFromIpWhoIs($ip);

            if (!$locationInfo) {
                Log::warning('Failed to get location data for IP', ['ip' => $ip]);
                return null;
            }

            // Map the location data to our expected structure
            $locationData = [
                'country' => $locationInfo['countryCode'] ?? null,
                'region' => $locationInfo['region'] ?? null,
                'city' => $locationInfo['city'] ?? null,
                'latitude' => $locationInfo['latitude'] ?? 0,
                'longitude' => $locationInfo['longitude'] ?? 0,
                'countryName' => $locationInfo['countryName'] ?? null,
                'regionName' => $locationInfo['regionName'] ?? null,
            ];

            // Only cache if we have at least country data
            if (!empty($locationData['country'])) {
                Cache::put($cacheKey, $locationData, self::IP_CACHE_TTL);
                Log::debug('IP location cached', ['ip' => $ip, 'country' => $locationData['country']]);
            }

            return (object)$locationData;
        } catch (Exception $e) {
            Log::error('Error getting location from IP', [
                'ip' => $ip,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Get historical locations for a set of users
     *
     * @param array $userIds User IDs to get historical locations for
     * @param Carbon $since Current monitoring window start time
     * @return array Array of historical location records
     */
    private function getHistoricalLocations(array $userIds, Carbon $since): array
    {
        // Fetch raw historical login records
        $historicalLogins = DB::connection('audit_external')->table('user_logins')
            ->select('user_id', 'ip', 'details')
            ->whereIn('user_id', $userIds)
            ->where('created_at', '<', $since)
            ->where('created_at', '>=', $this->monitoringTime->copy()->subDays(self::LOCATION_HISTORY_DAYS))
            ->whereIn('platform_id', self::PLATFORM_IDS)
            ->get();

        // Process and extract location data
        return $this->processLocationData($historicalLogins);
    }

    /**
     * Create a unique key for a location
     */
    private function getLocationKey(string $country, string $region): string
    {
        return "{$country}|{$region}";
    }
}
