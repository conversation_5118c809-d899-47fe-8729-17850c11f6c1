<?php

namespace App\Models\V1;

use DB;
use Eloquent;

class BatchTransaction extends Eloquent
{
    const STATUS_READY = 0;
    const STATUS_SUCCESS = 1;
    const STATUS_FAILED = 2;

    protected $table = 'batch_transactions';
    protected $connection = 'tenant';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'processed', 'status', 'logs', 'user_fk', 'transaction_fk', 'branch_fk', 'pos_system_fk', 'batch_import_fk',
        'x_account_id', 'x_points', 'x_amount',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
    ];

    /**
     * Process reward transaction
     * Create transaction, activity, purchase and increase user balance
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return void
     */
    public function processPointsReward()
    {
        $branch = BusinessBranch::findOrFail($this->branch_fk);
        $rules = BusinessRules::where(['business_fk' => $branch->business_fk])->firstOrFail();
        $timezoneId = $branch->branch_timezone_fk;
        $businessId = $branch->business_fk;
        $business = Business::findOrFail($businessId);
        $user = Customer::findOrFail($this->user_fk);
        $userEntity = UserEntityPermission::forEntity($businessId, $business->entity_type_fk);

        //Clerk that will be attached to the transaction
        $clerkId = ($userEntity && isset($userEntity[0])) ? $userEntity[0]->user_fk : null;

        $points = $this->x_points;

        /**
         * Validate before processing
         */
        if (!$this->validatePointsReward()) {
            $this->update([
                'processed' => 1,
                'status' => self::STATUS_FAILED,
            ]);
            return;
        }

        /**
         * Creating the Transaction
         *
         */
        $trxModel = new Transaction();
        $trxModel->user_fk = $user->id;
        $trxModel->transaction_type_fk = TRX_TYPE_POINTS_PURCHASE;
        $trxModel->transaction_sub_type_fk = TRX_SUBTYPE_REWARD_POINTS;
        $trxModel->entity_fk = $this->branch_fk;
        $trxModel->entity_type_fk = BRANCH_POINTS;
        $trxModel->timezone_mysql_fk = $timezoneId;
        $trxModel->save();
        $trxId = $trxModel->transaction_pk;
        $this->transaction_fk = $trxId;

        /**
         * Creating the Purchase
         */
        $purchaseModel = new Purchase();
        $purchaseModel->transaction_fk = $trxId;
        $purchaseModel->user_clerk_fk = $clerkId;
        $purchaseModel->amount = $this->x_amount;
        $purchaseModel->punch_value = $points;
        $purchaseModel->reward_ratio_numerator = $rules->reward_ratio_numerator;
        $purchaseModel->reward_ratio_denominator = $rules->reward_ratio_denominator;
        $purchaseModel->timezone_mysql_fk = $timezoneId;
        $purchaseModel->business_taxes_fk = $rules->business_taxes_fk;
        $purchaseModel->amount_orig = $this->x_amount;
        $purchaseModel->save();
        $purchaseId = $purchaseModel->getKey();

        /**
         * Update Balance
         */
        $balance = UserEntityPoints::increaseBalance($user, $business, $points);

        /**
         * Transaction Details
         */
        TransactionDetails::create([
            'transaction_fk' => $trxId,
            'points' => $points,
            'amount' => 0,
            'points_balance' => (int) $balance->current_active_points,
            'giftcard_balance' => (float) $balance->giftcard,
        ]);

        /**
         * Create Activity
         */
        $activity = Activity::create([
            'description' => 'ACTIVITY_WELCOME_POINT_PLURAL',
            'transaction_fk' => $trxId,
            'units_exchange' => $points,
            'hidden' => $rules->silo_flag,
            'timezone_mysql_fk' => $timezoneId,
        ]);

        //create Follower record
        $follower = Follower::where([
            'user_fk' => $user->id,
            'entity_fk' => $business->business_pk,
            'entity_type_fk' => $business->entity_type_fk,
        ])->first();

        if (!$follower) {
            Follower::updateOrCreate([
                'utc_created' => (new \DateTime())->format('Y-m-d H:i:s'),
                'user_fk' => $user->id,
                'entity_fk' => $business->business_pk,
                'entity_type_fk' => $business->entity_type_fk,
                'timezone_mysql_fk' => $timezoneId,
            ]);
        }

        //update the status and commit transaction
        $this->status = self::STATUS_SUCCESS;
        $this->processed = 1;
        $this->save();

        $this->logMessage('Processing Done', __METHOD__);
    }

    /**
     * Validate points value
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return boolean
     */
    private function validatePointsReward()
    {
        if ($this->x_points == 0 || empty($this->x_points)) {
            $this->logMessage("Amount is empty or invalid: {$this->x_points}", __METHOD__);
            return false;
        }
        return true;
    }

    /**
     * Log message for model
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return void
     */
    private function logMessage($message, $category, $time = null)
    {
        $time = $time ? $time : microtime(true);

        $this->update([
            'logs' => $this->logs . self::formatLogMsg($message, $category, $time),
        ]);
    }

    /**
     * Format log message
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function formatLogMsg($message, $category, $time)
    {
        return @date('Y-m-d H:i:s', $time) . " [$category] $message\n";
    }

    /**
     * Transform Item
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function transformItem($social)
    {
        return [

        ];
    }
}
