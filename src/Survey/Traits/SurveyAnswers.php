<?php

namespace KangarooRewards\Survey\Traits;

use KangarooRewards\Common\Helpers\Utils;
use KangarooRewards\Common\Models\SurveyAnswer as ModelSurveyAnswer;
use KangarooRewards\Survey\Helpers\Constants;
use KangarooRewards\Common\Models\SurveyQuestionAnswers as ModelSurveyQuestionAnswers;

trait SurveyAnswers
{
    /*
     *Returns the List of Users Who answered the Survey
     * <AUTHOR>
     */
    public static function getListOfUsersWhoAnsweredSurvey($surveyId = 0)
    {
        $listUsers = ModelSurveyQuestionAnswers::from('survey_question_answers as sqa')
            ->select('r.user_fk')
            ->join('survey_responses as r', 'sqa.survey_question_answer_pk', '=', 'r.survey_question_answer_relation_child_fk')
            ->where('sqa.survey_fk', $surveyId)
            ->where('sqa.enabled', 1)
            ->groupBy('r.user_fk')
            ->get();

        return Utils::listDataKeys($listUsers, 'user_fk');
    }
}
