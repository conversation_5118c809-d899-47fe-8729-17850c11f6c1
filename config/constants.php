<?php

/*
|--------------------------------------------------------------------------
| Global Constants
|--------------------------------------------------------------------------
|
| This file is for storing the global constants that could be used anywhere
|
 */

defined('APP_TOKEN_WEBSITE') or define('APP_TOKEN_WEBSITE', '1070155650.VHoL6m0Ey7hgz6pYrL7ptKXqSPM1M42FzpE49f0E');
defined('APP_TOKEN_WEBAPP') or define('APP_TOKEN_WEBAPP', '1245974802.19841SiPxTh9k0Lf8H2tZVGdwlEpauUQOqX6J3mrjg4INyMBA');

defined('ENABLED') or define('ENABLED', 1);
defined('DISABLED') or define('DISABLED', 0);
defined('GIFTCARD_QUEUE_MAX_ATTEMPTS') or define('GIFTCARD_QUEUE_MAX_ATTEMPTS', 3);
defined('CAMPAIGN_QUEUE_MAX_ATTEMPTS') or define('CAMPAIGN_QUEUE_MAX_ATTEMPTS', 3);// MUST be > 1
defined('CAMPAIGN_QUEUE_CHUNK_SIZE') or define('CAMPAIGN_QUEUE_CHUNK_SIZE', 1000);
defined('CAMPAIGN_QUEUE_FINISH_AFTER_MINUTES') or define('CAMPAIGN_QUEUE_FINISH_AFTER_MINUTES', 180);
// 64k Placeholders Limit https://bugs.mysql.com/bug.php?id=101630
defined('CAMPAIGN_PLACEHOLDERS_CHUNK_SIZE') or define('CAMPAIGN_PLACEHOLDERS_CHUNK_SIZE', 10000);
defined('CAMPAIGN_CHUNK_INSERT_SIZE') or define('CAMPAIGN_CHUNK_INSERT_SIZE', 4000);
defined('CAMPAIGN_CHUNK_UPDATE_SIZE') or define('CAMPAIGN_CHUNK_UPDATE_SIZE', 2000);

//used for slug hash
defined('OFFER_TABLE_NAME') or define('OFFER_TABLE_NAME', 'offer');
defined('PUNCH_TABLE_NAME') or define('PUNCH_TABLE_NAME', 'punch_item');
defined('PRODUCT_TABLE_NAME') or define('PRODUCT_TABLE_NAME', 'products');

//Values from notificationtypes table
defined('NOTIFICATION_TYPE_MESSAGE') or define('NOTIFICATION_TYPE_MESSAGE', 1);
defined('NOTIFICATION_TYPE_NOTIFICATION') or define('NOTIFICATION_TYPE_NOTIFICATION', 2);

//values from status_manager table
defined('STATUS_MANAGER_PIN_CODE_AUTO_GENERATED') or define('STATUS_MANAGER_PIN_CODE_AUTO_GENERATED', 35);
defined('STATUS_MANAGER_PHONE_NOT_VERIFIED') or define('STATUS_MANAGER_PHONE_NOT_VERIFIED', 30);
defined('STATUS_MANAGER_PHONE_VERIFIED') or define('STATUS_MANAGER_PHONE_VERIFIED', 31);
defined('STATUS_MANAGER_EMAIL_NOT_VERIFIED') or define('STATUS_MANAGER_EMAIL_NOT_VERIFIED', 32);
defined('STATUS_MANAGER_EMAIL_VERIFIED') or define('STATUS_MANAGER_EMAIL_VERIFIED', 33);
defined('STATUS_MANAGER_CHANGE_EMAIL_REQUEST') or define('STATUS_MANAGER_CHANGE_EMAIL_REQUEST', 48);
defined('STATUS_MANAGER_ADD_EMAIL_REQUEST') or define('STATUS_MANAGER_ADD_EMAIL_REQUEST', 40);
defined('STATUS_MANAGER_ADD_PHONE_VERIFIED') or define('STATUS_MANAGER_ADD_PHONE_VERIFIED', 41);
defined('STATUS_MANAGER_ADD_EMAIL_VERIFIED') or define('STATUS_MANAGER_ADD_EMAIL_VERIFIED', 42);
defined('STATUS_MANAGER_CHANGE_PHONE_REQUEST') or define('STATUS_MANAGER_CHANGE_PHONE_REQUEST', 47);
defined('STATUS_MANAGER_ADD_PHONE_REQUEST') or define('STATUS_MANAGER_ADD_PHONE_REQUEST', 39);
defined('STATUS_MANAGER_CHANGED_PHONE_VERIFIED') or define('STATUS_MANAGER_CHANGED_PHONE_VERIFIED', 49);
defined('STATUS_MANAGER_CHANGED_EMAIL_VERIFIED') or define('STATUS_MANAGER_CHANGED_EMAIL_VERIFIED', 50);
defined('STATUS_MANAGER_PHONE_NOT_VERIFIED_INVITED_BYSTORE') or define('STATUS_MANAGER_PHONE_NOT_VERIFIED_INVITED_BYSTORE', 56);
defined('STATUS_MANAGER_PHONE_VERIFIED_INVITED_BYSTORE') or define('STATUS_MANAGER_PHONE_VERIFIED_INVITED_BYSTORE', 57);
defined('STATUS_MANAGER_EMAIL_NOT_VERIFIED_INVITED_BYSTORE') or define('STATUS_MANAGER_EMAIL_NOT_VERIFIED_INVITED_BYSTORE', 58);
defined('STATUS_MANAGER_EMAIL_VERIFIED_INVITED_BYSTORE') or define('STATUS_MANAGER_EMAIL_VERIFIED_INVITED_BYSTORE', 59);
defined('STATUS_MANAGER_PHONE_NOT_VERIFIED_UNITS_TRANSFER') or define('STATUS_MANAGER_PHONE_NOT_VERIFIED_UNITS_TRANSFER', 64);
defined('STATUS_MANAGER_PHONE_VERIFIED_UNITS_TRANSFER') or define('STATUS_MANAGER_PHONE_VERIFIED_UNITS_TRANSFER', 65);
defined('STATUS_MANAGER_EMAIL_NOT_VERIFIED_UNITS_TRANSFER') or define('STATUS_MANAGER_EMAIL_NOT_VERIFIED_UNITS_TRANSFER', 66);
defined('STATUS_MANAGER_EMAIL_VERIFIED_UNITS_TRANSFER') or define('STATUS_MANAGER_EMAIL_VERIFIED_UNITS_TRANSFER', 67);
defined('ADD_PHONE_REQUEST_CANCEL') or define('ADD_PHONE_REQUEST_CANCEL', 43);
defined('CHANGE_PHONE_REQUEST_CANCEL') or define('CHANGE_PHONE_REQUEST_CANCEL', 51);
defined('CHANGE_EMAIL_REQUEST_CANCEL') or define('CHANGE_EMAIL_REQUEST_CANCEL', 52);
defined('ADD_EMAIL_REQUEST_CANCEL') or define('ADD_EMAIL_REQUEST_CANCEL', 44);

//values from email_sms_status table
defined('EMAIL_SMS_STATUS_SCHEDULED') or define('EMAIL_SMS_STATUS_SCHEDULED', 1);
defined('EMAIL_SMS_STATUS_QUEUED') or define('EMAIL_SMS_STATUS_QUEUED', 2);
defined('EMAIL_SMS_STATUS_SENT') or define('EMAIL_SMS_STATUS_SENT', 3);
defined('EMAIL_SMS_STATUS_DELIVERED') or define('EMAIL_SMS_STATUS_DELIVERED', 4);
defined('EMAIL_SMS_STATUS_FAILED') or define('EMAIL_SMS_STATUS_FAILED', 5);
defined('EMAIL_SMS_STATUS_SENDER_CLAIMED_BACK') or define('EMAIL_SMS_STATUS_SENDER_CLAIMED_BACK', 6);
defined('EMAIL_SMS_STATUS_RECEIVER_ACCEPTED') or define('EMAIL_SMS_STATUS_RECEIVER_ACCEPTED', 7);
defined('EMAIL_SMS_STATUS_PROCESSING') or define('EMAIL_SMS_STATUS_PROCESSING', 8);

//values from language table
defined('LANG_EN') or define('LANG_EN', 1);
defined('LANG_FR') or define('LANG_FR', 2);
defined('LANG_ES') or define('LANG_ES', 3);
defined('LANG_AR') or define('LANG_AR', 4);
defined('LANG_PT') or define('LANG_PT', 6);
defined('LANG_NL') or define('LANG_NL', 7);
defined('LANG_KU') or define('LANG_KU', 8);
defined('LANG_IT') or define('LANG_IT', 9);

//values from notification_requester table
defined('NOTIF_REQ_WELCOME_PUNCH_TITLE') or define('NOTIF_REQ_WELCOME_PUNCH_TITLE', 13);
defined('NOTIF_REQ_WELCOME_POINTS_TITLE') or define('NOTIF_REQ_WELCOME_POINTS_TITLE', 14);
defined('NOTIF_REQ_NEW_MKTCAMPAIGN_OFFER_TITLE') or define('NOTIF_REQ_NEW_MKTCAMPAIGN_OFFER_TITLE', 22);
defined('NOTIF_REQ_GIFTCARD_PURCHASE_TITLE') or define('NOTIF_REQ_GIFTCARD_PURCHASE_TITLE', 26);
defined('NOTIF_REQ_TRANSFER_POINTS_RECEIVER_TITLE') or define('NOTIF_REQ_TRANSFER_POINTS_RECEIVER_TITLE', 29);
defined('NOTIF_REQ_GIFTCARD_TRANSFER_POINTS_RECEIVER_TITLE') or define('NOTIF_REQ_GIFTCARD_TRANSFER_POINTS_RECEIVER_TITLE', 30);
defined('NOTIF_REQ_POINT_PURCHASE_REWARD_TITLE') or define('NOTIF_REQ_POINT_PURCHASE_REWARD_TITLE', 31);
defined('NOTIF_REQ_PUNCH_PURCHASE_REWARD_TITLE') or define('NOTIF_REQ_PUNCH_PURCHASE_REWARD_TITLE', 32);
defined('NOTIF_REQ_POINT_PURCHASE_REDEMPTION_TITLE') or define('NOTIF_REQ_POINT_PURCHASE_REDEMPTION_TITLE', 33);
defined('NOTIF_REQ_PUNCH_PURCHASE_REDEMPTION_TITLE') or define('NOTIF_REQ_PUNCH_PURCHASE_REDEMPTION_TITLE', 34);
defined('NOTIF_REQ_POINT_PURCHASE_REFUND_TITLE') or define('NOTIF_REQ_POINT_PURCHASE_REFUND_TITLE', 35);
defined('NOTIF_REQ_GIFTCARD_AMOUNT_PURCHASE_TITLE') or define('NOTIF_REQ_GIFTCARD_AMOUNT_PURCHASE_TITLE', 42);
defined('NOTIF_REQ_GIFTCARD_AMOUNT_TRANSFER_RECEIVER_TITLE') or define('NOTIF_REQ_GIFTCARD_AMOUNT_TRANSFER_RECEIVER_TITLE', 43);
defined('NOTIF_REQ_GIFTCARD_AMOUNT_PAYMENT_TITLE') or define('NOTIF_REQ_GIFTCARD_AMOUNT_PAYMENT_TITLE', 44);
defined('NOTIF_REQ_NEW_POINT_CANCEL_TITLE') or define('NOTIF_REQ_NEW_POINT_CANCEL_TITLE', 45);
defined('NOTIF_REQ_CONVERTIBLE_GIFT_CARD_POINTS_TITLE') or define('NOTIF_REQ_CONVERTIBLE_GIFT_CARD_POINTS_TITLE', 46);
defined('NOTIF_REQ_CONVERTIBLE_GIFT_CARD_AMOUNT_TITLE') or define('NOTIF_REQ_CONVERTIBLE_GIFT_CARD_AMOUNT_TITLE', 47);
defined("NOTIF_REQ_CHECKIN_POINTS_TITLE") or define("NOTIF_REQ_CHECKIN_POINTS_TITLE", 50);
defined("NOTIF_REQ_POINTS_EXPIRATION_TITLE") or define("NOTIF_REQ_POINTS_EXPIRATION_TITLE", 51);
defined("NOTIF_REQ_COMPLETE_PROFILE") or define("NOTIF_REQ_COMPLETE_PROFILE", 53);
defined("NOTIF_REQ_CAMPAIGN_STATS_COMPLETED") or define("NOTIF_REQ_CAMPAIGN_STATS_COMPLETED", 57);
defined("NOTIF_REQ_TRENDING_OFFER") or define("NOTIF_REQ_TRENDING_OFFER", 58);
defined("NOTIF_REQ_TRENDING_REWARD") or define("NOTIF_REQ_TRENDING_REWARD", 59);

defined("REQUESTER_CAMPAIGN_CATEGORY_ID") or define("REQUESTER_CAMPAIGN_CATEGORY_ID", 1);
defined("REQUESTER_OFFER_CATEGORY_ID") or define("REQUESTER_OFFER_CATEGORY_ID", 2);
defined("REQUESTER_REWARD_CATEGORY_ID") or define("REQUESTER_REWARD_CATEGORY_ID", 3);

//values for gift card schedule, min and max hours
defined('GIFTCARD_SCHEDULE_MIN_HOURS') or define('GIFTCARD_SCHEDULE_MIN_HOURS', 11);
defined('GIFTCARD_SCHEDULE_MAX_HOURS') or define('GIFTCARD_SCHEDULE_MAX_HOURS', 19);

//values from transaction_type table
defined('TRX_TYPE_OFFER_POINTS') or define('TRX_TYPE_OFFER_POINTS', 2);
defined('TRX_TYPE_CHECKIN') or define('TRX_TYPE_CHECKIN', 3);
defined('TRX_TYPE_POINTS_PURCHASE') or define('TRX_TYPE_POINTS_PURCHASE', 5);
defined('TRX_TYPE_PUNCH_PURCHASE') or define('TRX_TYPE_PUNCH_PURCHASE', 7);
defined('TRX_TYPE_GIFTCARD_POINTS') or define('TRX_TYPE_GIFTCARD_POINTS', 12);
defined('TRX_TYPE_GIFTCARD_AMOUNT') or define('TRX_TYPE_GIFTCARD_AMOUNT', 12);
defined('TRX_TYPE_TRANSFER_POINTS') or define('TRX_TYPE_TRANSFER_POINTS', 13);
defined('TRX_TYPE_TRANSFER_PUNCHES') or define('TRX_TYPE_TRANSFER_PUNCHES', 14);
defined('RESET_BALANCE') or define('RESET_BALANCE', 16);
defined('REDEMPTION_CATALOG_PUNCH') or define('REDEMPTION_CATALOG_PUNCH', 8);
defined('REDEMPTION_CATALOG_POINTS') or define('REDEMPTION_CATALOG_POINTS', 11);
defined('TRX_TYPE_ADJUST_BALANCE') or define('TRX_TYPE_ADJUST_BALANCE', 16);
defined('TRX_TYPE_TRANSFER_AMOUNT') or define('TRX_TYPE_TRANSFER_AMOUNT', 17);

//values from transaction_sub_type table
defined('TRX_SUBTYPE_CLAIMED_POINTS') or define('TRX_SUBTYPE_CLAIMED_POINTS', 2);
defined('TRX_SUBTYPE_REWARD_POINTS') or define('TRX_SUBTYPE_REWARD_POINTS', 4);
defined('TRX_SUBTYPE_BOOKING_POINTS') or define('TRX_SUBTYPE_BOOKING_POINTS', 111);
defined('TRX_SUBTYPE_REWARD_PUNCH') or define('TRX_SUBTYPE_REWARD_PUNCH', 17);
defined('TRX_SUBTYPE_PRODUCT_REDEMPTION_PUNCH') or define('TRX_SUBTYPE_PRODUCT_REDEMPTION_PUNCH', 18);
defined('TRX_SUBTYPE_REDEMPTION_PTS') or define('TRX_SUBTYPE_REDEMPTION_PTS', 5);
defined('TRX_SUBTYPE_SIGNUP_REWARD_PUNCH') or define('TRX_SUBTYPE_SIGNUP_REWARD_PUNCH', 32);//Welcome punches
defined('TRX_SUBTYPE_SIGNUP_REWARD_POINTS') or define('TRX_SUBTYPE_SIGNUP_REWARD_POINTS', 33);//Welcome points
defined('TRX_SUBTYPE_MULTI_PUNCHES') or define('TRX_SUBTYPE_MULTI_PUNCHES', 57);
defined('TRX_SUBTYPE_TRANSFER_POINTS_SENDER') or define('TRX_SUBTYPE_TRANSFER_POINTS_SENDER', 58);
defined('TRX_SUBTYPE_TRANSFER_POINTS_RECEIVER') or define('TRX_SUBTYPE_TRANSFER_POINTS_RECEIVER', 59);
defined('TRX_SUBTYPE_TRANSFER_PUNCHES_SENDER') or define('TRX_SUBTYPE_TRANSFER_PUNCHES_SENDER', 60);
defined('TRX_SUBTYPE_TRANSFER_PUNCHES_RECEIVER') or define('TRX_SUBTYPE_TRANSFER_PUNCHES_RECEIVER', 61);
defined('TRX_SUBTYPE_GIFTCARD_POINTS_PURCHASED_MERCHANT') or define('TRX_SUBTYPE_GIFTCARD_POINTS_PURCHASED_MERCHANT', 70);
defined('TRX_SUBTYPE_REFERRAL_REFEREE_REWARD_PUNCH') or define('TRX_SUBTYPE_REFERRAL_REFEREE_REWARD_PUNCH', 78);
defined('TRX_SUBTYPE_REFERRAL_REFERER_REWARD_PUNCH') or define('TRX_SUBTYPE_REFERRAL_REFERER_REWARD_PUNCH', 79);
defined('TRX_SUBTYPE_CLERK_REDEMPTION') or define('TRX_SUBTYPE_CLERK_REDEMPTION', 62);
defined('TRX_SUBTYPE_PRODUCT_REDEMPTION_POINTS') or define('TRX_SUBTYPE_PRODUCT_REDEMPTION_POINTS', 43);
defined('TRX_SUBTYPE_CLERK_PRODUCT_REDEMPTION_POINTS') or define('TRX_SUBTYPE_CLERK_PRODUCT_REDEMPTION_POINTS', 63);
defined('TRX_SUBTYPE_POINTS_REFUND') or define('TRX_SUBTYPE_POINTS_REFUND', 64);
defined('TRX_SUBTYPE_CLAIMED_POINTS_REWARD') or define('TRX_SUBTYPE_CLAIMED_POINTS_REWARD', 67);
defined('TRX_SUBTYPE_AGGREGATED_CLAIMED_POINTS_REWARD') or define('TRX_SUBTYPE_AGGREGATED_CLAIMED_POINTS_REWARD', 68);
defined('TRX_SUBTYPE_TRANSFER_POINTS_SENDER_RECALL') or define('TRX_SUBTYPE_TRANSFER_POINTS_SENDER_RECALL', 71);
defined('TRX_SUBTYPE_REWARD_REDEEM_BUNDLE') or define('TRX_SUBTYPE_REWARD_REDEEM_BUNDLE', 72);
defined('TRX_SUBTYPE_LEAVE_MEMBERSHIP') or define('TRX_SUBTYPE_LEAVE_MEMBERSHIP', 73);
defined('TRX_SUBTYPE_FACEBOOK_SHARED_POINTS') or define('TRX_SUBTYPE_FACEBOOK_SHARED_POINTS', 15);
defined('TRX_SUBTYPE_TWITTER_SHARED_POINTS') or define('TRX_SUBTYPE_TWITTER_SHARED_POINTS', 16);
defined('TRX_SUBTYPE_FACEBOOK_SHARED_PUNCH') or define('TRX_SUBTYPE_FACEBOOK_SHARED_PUNCH', 30);
defined('TRX_SUBTYPE_TWITTER_SHARED_PUNCH') or define('TRX_SUBTYPE_TWITTER_SHARED_PUNCH', 31);
defined('TRX_SUBTYPE_FACEBOOK_SHARED_BASIC') or define('TRX_SUBTYPE_FACEBOOK_SHARED_BASIC', 37);
defined('TRX_SUBTYPE_TWITTER_SHARED_BASIC') or define('TRX_SUBTYPE_TWITTER_SHARED_BASIC', 38);
defined('TRX_SUBTYPE_FACEBOOK_SHARED_GIFTCARD') or define('TRX_SUBTYPE_FACEBOOK_SHARED_GIFTCARD', 52);
defined('TRX_SUBTYPE_TWITTER_SHARED_GIFTCARD') or define('TRX_SUBTYPE_TWITTER_SHARED_GIFTCARD', 53);
defined('TRX_SUBTYPE_FACEBOOK_SHARED_RED_CATALOG_PUNCH') or define('TRX_SUBTYPE_FACEBOOK_SHARED_RED_CATALOG_PUNCH', 24);
defined('TRX_SUBTYPE_FACEBOOK_SHARED_RED_CATALOG_POINTS') or define('TRX_SUBTYPE_FACEBOOK_SHARED_RED_CATALOG_POINTS', 41);
defined('TRX_SUBTYPE_TWITTER_SHARED_RED_CATALOG_PUNCH') or define('TRX_SUBTYPE_TWITTER_SHARED_RED_CATALOG_PUNCH', 25);
defined('TRX_SUBTYPE_TWITTER_SHARED_RED_CATALOG_POINTS') or define('TRX_SUBTYPE_TWITTER_SHARED_RED_CATALOG_POINTS', 42);
defined('TRX_SUBTYPE_COUPON_GET_COUPON') or define('TRX_SUBTYPE_COUPON_GET_COUPON', 74);
defined('TRX_SUBTYPE_COUPON_CLAIMED') or define('TRX_SUBTYPE_COUPON_CLAIMED', 75);
defined('TRX_SUBTYPE_REFERRAL_REFEREE_REWARD_POINTS') or define('TRX_SUBTYPE_REFERRAL_REFEREE_REWARD_POINTS', 76);
defined('TRX_SUBTYPE_REFERRAL_REFERER_REWARD_POINTS') or define('TRX_SUBTYPE_REFERRAL_REFERER_REWARD_POINTS', 77);
defined('TRX_SUBTYPE_ADJUST_BALANCE_POINTS_INCREASE') or define('TRX_SUBTYPE_ADJUST_BALANCE_POINTS_INCREASE', 80);
defined('TRX_SUBTYPE_ADJUST_BALANCE_PUNCH_INCREASE') or define('TRX_SUBTYPE_ADJUST_BALANCE_PUNCH_INCREASE', 81);
defined('TRX_SUBTYPE_ADJUST_BALANCE_POINTS_DECREASE') or define('TRX_SUBTYPE_ADJUST_BALANCE_POINTS_DECREASE', 82);
defined('TRX_SUBTYPE_ADJUST_BALANCE_PUNCH_DECREASE') or define('TRX_SUBTYPE_ADJUST_BALANCE_PUNCH_DECREASE', 83);
defined('TRX_SUBTYPE_GIFTCARD_AMOUNT_PURCHASED') or define('TRX_SUBTYPE_GIFTCARD_AMOUNT_PURCHASED', 84);
defined('TRX_SUBTYPE_GIFTCARD_AMOUNT_PURCHASED_MERCHANT') or define('TRX_SUBTYPE_GIFTCARD_AMOUNT_PURCHASED_MERCHANT', 85);
defined('TRX_SUBTYPE_TRANSFER_AMOUNT_SENDER') or define('TRX_SUBTYPE_TRANSFER_AMOUNT_SENDER', 86);
defined('TRX_SUBTYPE_TRANSFER_AMOUNT_RECEIVER') or define('TRX_SUBTYPE_TRANSFER_AMOUNT_RECEIVER', 87);
defined('TRX_SUBTYPE_GIFTCARD_AMOUNT_PAYMENT') or define('TRX_SUBTYPE_GIFTCARD_AMOUNT_PAYMENT', 88);
defined('TRX_SUBTYPE_TRANSFER_AMOUNT_SENDER_RECALL') or define('TRX_SUBTYPE_TRANSFER_AMOUNT_SENDER_RECALL', 89);
defined('TRX_SUBTYPE_TRANSFER_PUNCHES_SENDER_RECALL') or define('TRX_SUBTYPE_TRANSFER_PUNCHES_SENDER_RECALL', 90);
defined('TRX_SUBTYPE_COUPON_CONVERTIBLE_CREATED') or define('TRX_SUBTYPE_COUPON_CONVERTIBLE_CREATED', 91);
defined('TRX_SUBTYPE_COUPON_CONVERTIBLE_CLAIMED') or define('TRX_SUBTYPE_COUPON_CONVERTIBLE_CLAIMED', 92);
defined('TRX_SUBTYPE_INITIAL_POINTS_IMPORT') or define('TRX_SUBTYPE_INITIAL_POINTS_IMPORT', 93);
defined('TRX_SUBTYPE_INITIAL_PUNCHES_IMPORT') or define('TRX_SUBTYPE_INITIAL_PUNCHES_IMPORT', 94);
defined('TRX_SUBTYPE_CONVERTIBLE_PRODUCT_REDEMPTION_POINTS') or define('TRX_SUBTYPE_CONVERTIBLE_PRODUCT_REDEMPTION_POINTS', 95);
defined('TRX_SUBTYPE_CONVERTIBLE_GIFTCARD_AMOUNT_PURCHASED') or define('TRX_SUBTYPE_CONVERTIBLE_GIFTCARD_AMOUNT_PURCHASED', 96);
defined('TRX_SUBTYPE_SPIN_DRAW_FREE_POINTS') or define('TRX_SUBTYPE_SPIN_DRAW_FREE_POINTS', 97);
defined('TRX_SUBTYPE_SPIN_DRAW_FREE_PRODUCT') or define('TRX_SUBTYPE_SPIN_DRAW_FREE_PRODUCT', 98);
defined('TRX_SUBTYPE_SPIN_DRAW_PORCENTAGE_OFF') or define('TRX_SUBTYPE_SPIN_DRAW_PORCENTAGE_OFF', 99);
defined('TRX_SUBTYPE_BONUS_POINTS_ASSIGNED') or define('TRX_SUBTYPE_BONUS_POINTS_ASSIGNED', 100);
defined('TRX_SUBTYPE_SPIN_DRAW_OFFER') or define('TRX_SUBTYPE_SPIN_DRAW_OFFER', 119);
defined('TRX_SUBTYPE_SURVEY_COMPLETED') or define('TRX_SUBTYPE_SURVEY_COMPLETED', 101);
defined('TRX_SUBTYPE_GIFTCARD_AMOUNT_STORE_CREDIT') or define('TRX_SUBTYPE_GIFTCARD_AMOUNT_STORE_CREDIT', 102);
defined('TRX_SUBTYPE_GIFTCARD_AMOUNT_REFUND_STORE_CREDIT') or define('TRX_SUBTYPE_GIFTCARD_AMOUNT_REFUND_STORE_CREDIT', 103);
defined('TRX_SUBTYPE_CHECKIN') or define('TRX_SUBTYPE_CHECKIN', 19);
defined('TRX_SUBTYPE_REFUND_REDEEMED_POINTS_EXCLUDE_TIERS') or define('TRX_SUBTYPE_REFUND_REDEEMED_POINTS_EXCLUDE_TIERS', 107);
defined('TRX_SUBTYPE_OFFER_REFUND_POINTS') or define('TRX_SUBTYPE_OFFER_REFUND_POINTS', 104);
defined("TRX_SUBTYPE_REDEMPTION_PTS_REFUND") or define("TRX_SUBTYPE_REDEMPTION_PTS_REFUND", 105);
defined("TRX_SUBTYPE_REWARD_RECEIPT_SCAN_QR_CODE") or define("TRX_SUBTYPE_REWARD_RECEIPT_SCAN_QR_CODE", 109);
defined("TRX_SUBTYPE_POINTS_EXPIRATION") or define("TRX_SUBTYPE_POINTS_EXPIRATION", 110);
defined("TRX_SUBTYPE_REFERRAL_REFEREE_REWARD_POINTS_BY_PURCHASE_RATIO") or define("TRX_SUBTYPE_REFERRAL_REFEREE_REWARD_POINTS_BY_PURCHASE_RATIO", 117);
defined("TRX_SUBTYPE_REFERRAL_REFERER_REWARD_POINTS_BY_PURCHASE_RATIO") or define("TRX_SUBTYPE_REFERRAL_REFERER_REWARD_POINTS_BY_PURCHASE_RATIO", 118);
defined("TRX_SUBTYPE_COMPLETE_PROFILE") or define("TRX_SUBTYPE_COMPLETE_PROFILE", 113);

//values from call_to_action table
defined('CALL_TO_ACTION_SIGNUP') or define('CALL_TO_ACTION_SIGNUP', 1);
defined('CALL_TO_ACTION_FOLLOW') or define('CALL_TO_ACTION_FOLLOW', 2);
defined('CALL_TO_ACTION_CHECKIN') or define('CALL_TO_ACTION_CHECKIN', 3);

//values for Kangaroo business
defined('KNG_BUSINESS_ID') or define('KNG_BUSINESS_ID', 125);
defined('KNG_BUSINESS_BRANCH_ID') or define('KNG_BUSINESS_BRANCH_ID', 164);
defined('KNG_SYS_ADMIN_ID') or define('KNG_SYS_ADMIN_ID', 768);
defined('KNG_SYS_ADMIN_NAME') or define('KNG_SYS_ADMIN_NAME', 'kangaroo');
defined('NASSPAY_BUSINESS_ID') or define('NASSPAY_BUSINESS_ID', 1742);

//Reward expiry date
defined('DEFAULT_REWARD_EXPIRY_DATE') or define('DEFAULT_REWARD_EXPIRY_DATE', '2030-12-31');
defined('TIMESTAMP_RANGE_MAX') or define('TIMESTAMP_RANGE_MAX', '2038-01-19 00:00:00');

//values from countries
defined('CANADA_COUNTRY_FK') or define('CANADA_COUNTRY_FK', 2);

//values from Smartphone Login
defined('LOGIN_TYPE_REGULAR') or define('LOGIN_TYPE_REGULAR', 1);
defined('LOGIN_TYPE_FACEBOOK') or define('LOGIN_TYPE_FACEBOOK', 2);
defined('GENDER_FEMALE_FK') or define('GENDER_FEMALE_FK', 1);
defined('GENDER_MALE_FK') or define('GENDER_MALE_FK', 2);

//values from pos_system table
defined('POS_SYSTEM_LIGHTSPEED') or define('POS_SYSTEM_LIGHTSPEED', 1);
defined('POS_SYSTEM_VEND') or define('POS_SYSTEM_VEND', 2);
defined('POS_SYSTEM_INFOSYS') or define('POS_SYSTEM_INFOSYS', 3);
defined('POS_SYSTEM_SEDONA') or define('POS_SYSTEM_SEDONA', 4);
defined('POS_SYSTEM_SHOPIFY') or define('POS_SYSTEM_SHOPIFY', 5);
defined('POS_SYSTEM_LIGHTSPEED_ECOM') or define('POS_SYSTEM_LIGHTSPEED_ECOM', 6);
defined('POS_SYSTEM_WOOCOMMERCE_ECOM') or define('POS_SYSTEM_WOOCOMMERCE_ECOM', 7);
defined('POS_SYSTEM_MAGENTO') or define('POS_SYSTEM_MAGENTO', 8);
defined('POS_SYSTEM_LIGHTSPEED_RESTO') or define('POS_SYSTEM_LIGHTSPEED_RESTO', 9);
defined('POS_SYSTEM_CUSTOM_QR_CODE') or define('POS_SYSTEM_CUSTOM_QR_CODE', 14);
defined('POS_SYSTEM_BOOKING') or define('POS_SYSTEM_BOOKING', 15);
defined('POS_SYSTEM_BIGCOMMERCE') or define('POS_SYSTEM_BIGCOMMERCE', 10);
defined('POS_SYSTEM_ECOMZ') or define('POS_SYSTEM_ECOMZ', 16);
defined('POS_SYSTEM_HEARTLAND') or define('POS_SYSTEM_HEARTLAND', 17);
defined('POS_SYSTEM_LIGHTSPEED_RESTO_K_SERIES') or define('POS_SYSTEM_LIGHTSPEED_RESTO_K_SERIES', 19);
defined('POS_SYSTEM_UBER_VOUCHER') or define('POS_SYSTEM_UBER_VOUCHER', 20);
defined('POS_SYSTEM_AMAZON_GIFT_CARD') or define('POS_SYSTEM_AMAZON_GIFT_CARD', 21);
defined('POS_SYSTEM_ECWID') or define('POS_SYSTEM_ECWID', 22);
defined('POS_SYSTEM_KANGAROO_REVIEWS') or define('POS_SYSTEM_KANGAROO_REVIEWS', 24);
//app version
defined('POS_SYSTEM_APP_VERSION') or define('POS_SYSTEM_APP_VERSION', '4.6.0');
defined('MERCHANT_WEB_APP_VERSION') or define('MERCHANT_WEB_APP_VERSION', '4.6.0');

//values from platform_solution table
defined('PLATFORM_WEBSITE') or define('PLATFORM_WEBSITE', 1);
defined('PLATFORM_MERCHANT_ANDROID_APP') or define('PLATFORM_MERCHANT_ANDROID_APP', 2);
defined('PLATFORM_IOS_SMARTPHONE_APP') or define('PLATFORM_IOS_SMARTPHONE_APP', 3);
defined('PLATFORM_ANDROID_SMARTPHONE_APP') or define('PLATFORM_ANDROID_SMARTPHONE_APP', 4);
defined('PLATFORM_WEBSITE_CRONJOB') or define('PLATFORM_WEBSITE_CRONJOB', 5);
defined('PLATFORM_MERCHANT_WEB_APP') or define('PLATFORM_MERCHANT_WEB_APP', 6);
defined('PLATFORM_POS_LIGHTSPEED') or define('PLATFORM_POS_LIGHTSPEED', 7);
defined('PLATFORM_POS_VEND') or define('PLATFORM_POS_VEND', 8);
defined('PLATFORM_IMPORT_CUSTOMERS_MODULE') or define('PLATFORM_IMPORT_CUSTOMERS_MODULE', 33);
defined('PLATFORM_IMPORT_PRODUCTS_MODULE') or define('PLATFORM_IMPORT_PRODUCTS_MODULE', 34);
defined('PLATFORM_IMPORT_TRANSACTIONS_MODULE') or define('PLATFORM_IMPORT_TRANSACTIONS_MODULE', 35);

//Marketing campaign customers came to the store during 3 days the campaign finished
defined('MKT_CAMPAIGN_CAME_TO_STORE_DAYS') or define('MKT_CAMPAIGN_CAME_TO_STORE_DAYS', 7);
defined('MKT_CAMPAIGN_EXPIRY_DATE') or define('MKT_CAMPAIGN_EXPIRY_DATE', '2050-12-31 23:59:59');
defined('MKT_CAMPAIGN_UPDATE_INTERVAL') or define('MKT_CAMPAIGN_UPDATE_INTERVAL', 1800); //seconds after campaign finished - 30 min

//values from entity_type table
defined('BRANCH_POINTS') or define('BRANCH_POINTS', 2);
defined('BRANCH_PUNCH') or define('BRANCH_PUNCH', 8);
defined('BRANCH_UNDEFINED') or define('BRANCH_UNDEFINED', 100);
defined('BUSINESS_POINTS') or define('BUSINESS_POINTS', 1);
defined('BUSINESS_PUNCH') or define('BUSINESS_PUNCH', 7);
defined('BUSINESS_UNDEFINED') or define('BUSINESS_UNDEFINED', 99);
defined('CHARITY') or define('CHARITY', 3);
defined('TRAKTROK') or define('TRAKTROK', 5);
defined('USER') or define('USER', 4);
defined('WHITELABEL') or define('WHITELABEL', 6);

//values for user settings
defined('IMERIAL') or define('IMERIAL', 1);
defined('METRIC') or define('METRIC', 0);

defined('UPLOAD_MAX_FILESIZE') or define('UPLOAD_MAX_FILESIZE', 10485760); //10 MB (size in bytes)
defined('CROP_IMAGE_MIN_WIDTH') or define('CROP_IMAGE_MIN_WIDTH', 450); //offer image
defined('CROP_IMAGE_MIN_HEIGHT') or define('CROP_IMAGE_MIN_HEIGHT', 264); //offer image
defined('CROP_IMAGE_BUS_LOGO_MIN_WIDTH') or define('CROP_IMAGE_BUS_LOGO_MIN_WIDTH', 450); //business logo
defined('CROP_IMAGE_BUS_LOGO_MIN_HEIGHT') or define('CROP_IMAGE_BUS_LOGO_MIN_HEIGHT', 273); //business logo
defined('CROP_IMAGE_BUS_COVER_MIN_WIDTH') or define('CROP_IMAGE_BUS_COVER_MIN_WIDTH', 640); //cover photo
defined('CROP_IMAGE_BUS_COVER_MIN_HEIGHT') or define('CROP_IMAGE_BUS_COVER_MIN_HEIGHT', 528); //cover photo
defined('CROP_IMAGE_OFFER_LARGE_WIDTH') or define('CROP_IMAGE_OFFER_LARGE_WIDTH', 450); //offer image LARGE
defined('CROP_IMAGE_OFFER_LARGE_HEIGHT') or define('CROP_IMAGE_OFFER_LARGE_HEIGHT', 264); //offer image LARGE
defined('CROP_IMAGE_OFFER_MEDIUM_WIDTH') or define('CROP_IMAGE_OFFER_MEDIUM_WIDTH', 290); //offer image MEDIUM
defined('CROP_IMAGE_OFFER_MEDIUM_HEIGHT') or define('CROP_IMAGE_OFFER_MEDIUM_HEIGHT', 170); //offer image MEDIUM
defined('CROP_IMAGE_OFFER_SMALL_WIDTH') or define('CROP_IMAGE_OFFER_SMALL_WIDTH', 150); //offer image THUMBNAIL
defined('CROP_IMAGE_OFFER_SMALL_HEIGHT') or define('CROP_IMAGE_OFFER_SMALL_HEIGHT', 88); //offer image THUMBNAIL

//slug
defined('SLUG_PRODUCT_TABLE_NAME') or define('SLUG_PRODUCT_TABLE_NAME', 'products');
defined('SLUG_DRAW_TABLE_NAME') or define('SLUG_DRAW_TABLE_NAME', 'draw');
defined('SLUG_PRIZES_TABLE_NAME') or define('SLUG_PRIZES_TABLE_NAME', 'prizes');

defined('BRANCH_LANGUAGEX_MAX') or define('BRANCH_LANGUAGEX_MAX', 3); //Max number of languages

//Kangaroo default colors
defined('APP_COLOR_PRIMARY') or define('APP_COLOR_PRIMARY', '#41afd7'); //blue
defined('APP_COLOR_SECONDARY') or define('APP_COLOR_SECONDARY', '#ea581d'); //orange
defined('APP_COLOR_TERTIARY') or define('APP_COLOR_TERTIARY', '#a5abb1'); //grey
defined('APP_COLOR_BACKGROUND') or define('APP_COLOR_BACKGROUND', '#000000'); //dark grey
defined('APP_COLOR_PRIMARY_RGB') or define('APP_COLOR_PRIMARY_RGB', 'rgba(65,175,215,0.6)'); //blue
defined('APP_COLOR_SECONDARY_RGB') or define('APP_COLOR_SECONDARY_RGB', 'rgba(234,88,29,0.6)'); //orange
defined('APP_COLOR_TERTIARY_RGB') or define('APP_COLOR_TERTIARY_RGB', 'rgba(165,171,177,0.6)'); //grey
defined('APP_COLOR_BACKGROUND_RGB') or define('APP_COLOR_BACKGROUND_RGB', 'rgba(0, 0, 0, 1)'); //grey

defined('APP_COLOR_SLIDE_H1') or define('APP_COLOR_SLIDE_H1', '#ffffff'); //White
defined('APP_COLOR_SLIDE_H2') or define('APP_COLOR_SLIDE_H2', '#ea581d'); //Orange
defined('APP_COLOR_SLIDE_H3') or define('APP_COLOR_SLIDE_H3', '#ffffff'); //White
defined('APP_COLOR_SLIDE_BG') or define('APP_COLOR_SLIDE_BG', '#000000'); //Dark gray
defined('APP_COLOR_TITLES') or define('APP_COLOR_TITLES', '#41afd7'); //Blue Kng
defined('APP_COLOR_TEXT') or define('APP_COLOR_TEXT', '#9ea2a3'); //Light gray
defined('APP_COLOR_BUTTONS') or define('APP_COLOR_BUTTONS', '#41afd7'); //Blue Kng
defined('APP_COLOR_BUTTONS_REDEEM') or define('APP_COLOR_BUTTONS_REDEEM', '#41afd7'); //Blue Kng
defined('APP_COLOR_KEYPAD_PHONE') or define('APP_COLOR_KEYPAD_PHONE', '#41afd7'); //Blue Kng
defined('APP_COLOR_KEYPAD_EMPLOYEE') or define('APP_COLOR_KEYPAD_EMPLOYEE', '#a5abb1'); //Gray
defined('APP_COLOR_KEYPAD_USER') or define('APP_COLOR_KEYPAD_USER', '#a5abb1'); //Gray
defined('APP_COLOR_KEYPAD_REDEEM') or define('APP_COLOR_KEYPAD_REDEEM', '#ea581d'); //Orange

defined('MOBILE_APP_BACKGROUND_COLOR') or define('MOBILE_APP_BACKGROUND_COLOR', '#000000'); //dark grey
defined('MOBILE_COLOR_BUTTONS') or define('MOBILE_COLOR_BUTTONS', '#41afd7');
defined('MOBILE_COLOR_TEXT_BUTTON') or define('MOBILE_COLOR_TEXT_BUTTON', '#ffffff');
defined('MOBILE_COLOR_TEXT') or define('MOBILE_COLOR_TEXT', '#9ea2a3');
defined('MOBILE_COLOR_TITLES') or define('MOBILE_COLOR_TITLES', '#41afd7');
defined('MOBILE_COLOR_TOOLBAR_BACKGROUND') or define('MOBILE_COLOR_TOOLBAR_BACKGROUND', '#41afd7');
defined('MOBILE_COLOR_TOOLBAR_ICONS') or define('MOBILE_COLOR_TOOLBAR_ICONS', '#ffffff');
defined('MOBILE_COLOR_TOOLBAR_TEXT') or define('MOBILE_COLOR_TOOLBAR_TEXT', '#ffffff');
defined('MOBILE_COLOR_BACKGROUND_COLOR') or define('MOBILE_COLOR_BACKGROUND_COLOR', '#41afd7');
defined('MOBILE_COLOR_TAB_INDICATOR') or define('MOBILE_COLOR_TAB_INDICATOR', '#ea581d');
defined('MOBILE_COLOR_TAB_TEXT') or define('MOBILE_COLOR_TAB_TEXT', '#ea581d');
defined('MOBILE_COLOR_INACTIVE_INPUTS') or define('MOBILE_COLOR_INACTIVE_INPUTS', '#41afd7');
defined('MOBILE_COLOR_ACTIVE_INPUTS') or define('MOBILE_COLOR_ACTIVE_INPUTS', '#ea581d');
defined('MOBILE_COLOR_CARDS') or define('MOBILE_COLOR_CARDS', '#a5abb1');
defined('MOBILE_COLOR_REWARDS_FILL') or define('MOBILE_COLOR_REWARDS_FILL', '#41afd7');
defined('MOBILE_COLOR_DRAWER_BACKGROUND') or define('MOBILE_COLOR_DRAWER_BACKGROUND', '#a5abb1');
defined('DEFAULT_BRAND_COLOR_100') or define('DEFAULT_BRAND_COLOR_100', '#f4fafc');
defined('DEFAULT_BRAND_COLOR_200') or define('DEFAULT_BRAND_COLOR_200', '#c0e5f0');
defined('DEFAULT_BRAND_COLOR_300') or define('DEFAULT_BRAND_COLOR_300', '#58b9d9');
defined('DEFAULT_BRAND_COLOR_400') or define('DEFAULT_BRAND_COLOR_400', '#3baed3');

//Business Menu ID values from main_menu table
defined('MAIN_MENU_BUSINESS_PROFILE_POINT') or define('MAIN_MENU_BUSINESS_PROFILE_POINT', 23);
defined('MAIN_MENU_BUSINESS_PROFILE_PUNCH') or define('MAIN_MENU_BUSINESS_PROFILE_PUNCH', 24);
defined('MAIN_MENU_BUSINESS_REFERRAL_POINT') or define('MAIN_MENU_BUSINESS_REFERRAL_POINT', 27);
defined('MAIN_MENU_BUSINESS_REFERRAL_PUNCH') or define('MAIN_MENU_BUSINESS_REFERRAL_PUNCH', 28);

//local server timezone
defined('SERVER_TIME_ZONE') or define('SERVER_TIME_ZONE', 'America/Toronto');
defined('SERVER_TIME_ZONE_ID') or define('SERVER_TIME_ZONE_ID', 211);

//Status Manager
defined('STATUS_MANAGER_SIGNUP') or define('STATUS_MANAGER_SIGNUP', 1);
defined('STATUS_MANAGER_WIZARD') or define('STATUS_MANAGER_WIZARD', 2);
defined('STATUS_MANAGER_MERCHANTAPPVERIFIED') or define('STATUS_MANAGER_MERCHANTAPPVERIFIED', 3);
defined('STATUS_MANAGER_VERIFIED') or define('STATUS_MANAGER_VERIFIED', 4);

//used in MessagesQueue
defined('MESSAGE_SEND_BY_EMAIL') or define('MESSAGE_SEND_BY_EMAIL', 1);
defined('MESSAGE_SEND_BY_SMS') or define('MESSAGE_SEND_BY_SMS', 2);
defined('MESSAGE_SEND_BY_PUSH') or define('MESSAGE_SEND_BY_PUSH', 3);

//values from business_rules table
defined('RULES_BALANCE_AMOUNT_PTS') or define('RULES_BALANCE_AMOUNT_PTS', 1); //Display amount and points
defined('RULES_BALANCE_AMOUNT') or define('RULES_BALANCE_AMOUNT', 2); //Display amount only
defined('RULES_BALANCE_POINTS') or define('RULES_BALANCE_POINTS', 3); //Display points only

defined('MERCHANT_APP_TIMER_SECONDS') or define('MERCHANT_APP_TIMER_SECONDS', 90);

//CONFIG flags
defined('CONFIG_SHOULD_QUEUE') or define('CONFIG_SHOULD_QUEUE', true); //send emails and SMS in background
defined('CONFIG_CACHE_APP_VERSION') or define('CONFIG_CACHE_APP_VERSION', '4.6.6'); //invalidate cache app version

defined('LIGHTSPEED_LOGIN_URL') or define('LIGHTSPEED_LOGIN_URL', 'https://cloud.merchantos.com/login.html');

//values from offer_type table
defined('OFFER_TYPE_POINT_MULTIPLIER') or define('OFFER_TYPE_POINT_MULTIPLIER', 1); //Multiply points
defined('OFFER_TYPE_POINT_FREE_PRODUCT') or define('OFFER_TYPE_POINT_FREE_PRODUCT', 2); //Free product
defined('OFFER_TYPE_POINT_BONUS_POINTS') or define('OFFER_TYPE_POINT_BONUS_POINTS', 3); //Bonus points, points give away
defined('OFFER_TYPE_PUNCH_MULTIPLIER') or define('OFFER_TYPE_PUNCH_MULTIPLIER', 4); //punch multiplier - promotion
defined('OFFER_TYPE_OTHER_BASIC_FREE_PRODUCT') or define('OFFER_TYPE_OTHER_BASIC_FREE_PRODUCT', 5);
defined('OFFER_TYPE_OTHER_BASIC_DISCOUNT_PERCENT') or define('OFFER_TYPE_OTHER_BASIC_DISCOUNT_PERCENT', 6);
defined('OFFER_TYPE_OTHER_BASIC_DISCOUNT_AMOUNT') or define('OFFER_TYPE_OTHER_BASIC_DISCOUNT_AMOUNT', 7);
defined('OFFER_TYPE_POINT_BASIC_FREE_PRODUCT') or define('OFFER_TYPE_POINT_BASIC_FREE_PRODUCT', 8);
defined('OFFER_TYPE_POINT_BASIC_DISCOUNT_PERCENT') or define('OFFER_TYPE_POINT_BASIC_DISCOUNT_PERCENT', 9);
defined('OFFER_TYPE_POINT_BASIC_DISCOUNT_AMOUNT') or define('OFFER_TYPE_POINT_BASIC_DISCOUNT_AMOUNT', 10);
defined('OFFER_TYPE_PUNCH_BASIC_FREE_PRODUCT') or define('OFFER_TYPE_PUNCH_BASIC_FREE_PRODUCT', 11);
defined('OFFER_TYPE_PUNCH_BASIC_DISCOUNT_PERCENT') or define('OFFER_TYPE_PUNCH_BASIC_DISCOUNT_PERCENT', 12);
defined('OFFER_TYPE_PUNCH_BASIC_DISCOUNT_AMOUNT') or define('OFFER_TYPE_PUNCH_BASIC_DISCOUNT_AMOUNT', 13);
defined('OFFER_TYPE_POINT_GIFTCARD_AMOUNT') or define('OFFER_TYPE_POINT_GIFTCARD_AMOUNT', 14);
defined('OFFER_TYPE_POINT_REDEEM_FREE_PRODUCT') or define('OFFER_TYPE_POINT_REDEEM_FREE_PRODUCT', 15);//REDEMPTION CATALOG item
defined('OFFER_TYPE_POINT_REDEEM_CASH_BACK') or define('OFFER_TYPE_POINT_REDEEM_CASH_BACK', 16);//REDEMPTION CATALOG item
defined('OFFER_TYPE_PUNCH_REDEEM_FREE_PRODUCT') or define('OFFER_TYPE_PUNCH_REDEEM_FREE_PRODUCT', 17);//REDEMPTION CATALOG item
defined('OFFER_TYPE_POINT_REDEEM_DISCOUNT_PERCENT') or define('OFFER_TYPE_POINT_REDEEM_DISCOUNT_PERCENT', 18);//REDEMPTION CATALOG item

//user_entity_points table fields
defined('BALANCE_TYPE_ACTIVE_POINTS') or define('BALANCE_TYPE_ACTIVE_POINTS', 'active_points');//field: current_active_points
defined('BALANCE_TYPE_PENDING_POINTS') or define('BALANCE_TYPE_PENDING_POINTS', 'pending_points');//field: current_pending_points
defined('BALANCE_TYPE_GIFTCARD') or define('BALANCE_TYPE_GIFTCARD', 'giftcard');//field: giftcard
defined('BALANCE_TYPE_ACTIVE_PUNCHES') or define('BALANCE_TYPE_ACTIVE_PUNCHES', 'active_punches');//field: current_punches

defined('CAMPAIGN_IMPACT_DEFAULT_PERIOD') or define('CAMPAIGN_IMPACT_DEFAULT_PERIOD', 5);
defined('OFFER_REWARD_IMPACT_DEFAULT_PERIOD') or define('OFFER_REWARD_IMPACT_DEFAULT_PERIOD', 5);

//values from oauth_provider_tokens table
defined('OAUTH_PROVIDER_KANGAROO_API') or define('OAUTH_PROVIDER_KANGAROO_API', 1);

//values from pos_temp_items table
defined('POS_TEMP_ITEM_CREATED_FOR_PROMO') or define('POS_TEMP_ITEM_CREATED_FOR_PROMO', 1); // LS promotions
defined('POS_TEMP_ITEM_CREATED_FOR_FBP') or define('POS_TEMP_ITEM_CREATED_FOR_FBP', 2); // Frequent buyer program discount

// offer_type field value in branch_offer table
defined('BRANCH_OFFER_TYPE_OFFER') or define('BRANCH_OFFER_TYPE_OFFER', 1);
defined('BRANCH_OFFER_TYPE_PUNCH_ITEM') or define('BRANCH_OFFER_TYPE_PUNCH_ITEM', 2);
defined('BRANCH_OFFER_TYPE_PRODUCT') or define('BRANCH_OFFER_TYPE_PRODUCT', 3);

// referral utm_source field value in referrals table
defined('REFERRAL_TYPE_IN_STORE') or define('REFERRAL_TYPE_IN_STORE', 1);
defined('REFERRAL_TYPE_REFERRAL_LINK') or define('REFERRAL_TYPE_REFERRAL_LINK', 2);
defined('REFERRAL_TYPE_API') or define('REFERRAL_TYPE_API', 3);
// return [

// ];



//Survey
defined('TRX_TYPE_SURVEY') or define('TRX_TYPE_SURVEY', 4);
defined("TRX_TYPE_BASIC_OFFER") or define("TRX_TYPE_BASIC_OFFER", 10);
defined('TRX_SUBTYPE_SURVEY_COMPLETED') or define('TRX_SUBTYPE_SURVEY_COMPLETED', 101);
defined("TRX_SUBTYPE_TARGETED_BASIC") or define("TRX_SUBTYPE_TARGETED_BASIC", 49);
defined("TRX_SUBTYPE_TARGETED_POINTS") or define("TRX_SUBTYPE_TARGETED_POINTS", 46);
defined('NOTIF_REQ_REFERRAL_REFEREE_REWARD') or define('NOTIF_REQ_REFERRAL_REFEREE_REWARD', 39);
defined('NOTIF_REQ_REFERRAL_REFERER_REWARD') or define('NOTIF_REQ_REFERRAL_REFERER_REWARD', 40);

//values from phone_number_types table
defined('PHONE_NUMBER_MOBILE') or define('PHONE_NUMBER_MOBILE', 1);
defined('PHONE_NUMBER_LANDLINE') or define('PHONE_NUMBER_LANDLINE', 2);
defined('PHONE_NUMBER_VOIP') or define('PHONE_NUMBER_VOIP', 3);
defined('PHONE_NUMBER_INVALID') or define('PHONE_NUMBER_INVALID', 4);


defined('PLATFORM_NAME_WEBSITE') or define('PLATFORM_NAME_WEBSITE', 'WEBSITE');
defined('PLATFORM_NAME_CRONJOB') or define('PLATFORM_NAME_CRONJOB', 'WEBSITE_CRONJOB');
defined('PLATFORM_NAME_SYSTEM') or define('PLATFORM_NAME_SYSTEM', 'WEBSITE_SYSTEM');
defined('PLATFORM_NAME_MERCHANT_WEB') or define('PLATFORM_NAME_MERCHANT_WEB', 'MERCHANT_WEB_APP');
defined('PLATFORM_NAME_POS_LIGHTSPEED') or define('PLATFORM_NAME_POS_LIGHTSPEED', 'POS_LIGHTSPEED');
defined('PLATFORM_NAME_POS_VEND') or define('PLATFORM_NAME_POS_VEND', 'POS_VEND');
defined('PLATFORM_NAME_SHOPIFY_SYSTEM') or define('PLATFORM_NAME_SHOPIFY_SYSTEM', 'POS_SHOPIFY');
defined('PLATFORM_NAME_MKT_LS') or define('PLATFORM_NAME_MKT_LS', 'MKT_POS_LIGHTSPEED');
defined('PLATFORM_NAME_BATCH_IMPORT') or define('PLATFORM_NAME_BATCH_IMPORT', 'BATCH_IMPORT');
defined('PLATFORM_NAME_SHOPIFY') or define('PLATFORM_NAME_SHOPIFY', 'Shopify');
defined('PLATFORM_NAME_ECOM_LIGHTSPEED') or define('PLATFORM_NAME_ECOM_LIGHTSPEED', 'LightSpeed eCom');
defined('PLATFORM_NAME_WOOCOMMERCE') or define('PLATFORM_NAME_WOOCOMMERCE', 'WooCommerce');
defined('PLATFORM_NAME_MAGENTO') or define('PLATFORM_NAME_MAGENTO', 'MAGENTO');
defined('PLATFORM_NAME_LIGHTSPEED_RESTO') or define('PLATFORM_NAME_LIGHTSPEED_RESTO', 'Lightspeed Resto');

//values from smsvendor table
defined('SMS_VENDOR_TWILIO_US') or define('SMS_VENDOR_TWILIO_US', 1);
defined('SMS_VENDOR_TWILIO_CA') or define('SMS_VENDOR_TWILIO_CA', 2);
defined('SMS_VENDOR_NEXMO_US') or define('SMS_VENDOR_NEXMO_US', 3);
defined('SMS_VENDOR_NEXMO_CA') or define('SMS_VENDOR_NEXMO_CA', 4);
defined('SMS_VENDOR_BROADNET_US') or define('SMS_VENDOR_BROADNET_US', 5);
defined('SMS_VENDOR_BROADNET_CA') or define('SMS_VENDOR_BROADNET_CA', 6);
defined('SMS_VENDOR_TWILIO_ROCKY_M') or define('SMS_VENDOR_TWILIO_ROCKY_M', 7);
defined('SMS_VENDOR_TWILIO_DOG_PERFECT') or define('SMS_VENDOR_TWILIO_DOG_PERFECT', 8);
defined('SMS_VENDOR_TWILIO_CBD_PLUS') or define('SMS_VENDOR_TWILIO_CBD_PLUS', 9);
defined('SMS_VENDOR_BANDWITH') or define('SMS_VENDOR_BANDWITH', 10);
defined('SMS_VENDOR_PLIVO') or define('SMS_VENDOR_PLIVO', 11);
defined('SMS_VENDOR_TWILIO_CBD_WORLD') or define('SMS_VENDOR_TWILIO_CBD_WORLD', 12);
defined('SMS_VENDOR_PLIVO_CBD_PLUS') or define('SMS_VENDOR_PLIVO_CBD_PLUS', 13);

//values from smsvendor table, sms_provider field
defined('SMS_PROVIDER_TWILIO') or define('SMS_PROVIDER_TWILIO', 'twilio');
defined('SMS_PROVIDER_NEXMO') or define('SMS_PROVIDER_NEXMO', 'nexmo');
defined('SMS_PROVIDER_BROADNET') or define('SMS_PROVIDER_BROADNET', 'broadnet');
defined('SMS_PROVIDER_BROADNET2') or define('SMS_PROVIDER_BROADNET2', 'broadnet2');
defined('SMS_PROVIDER_BROADNET3') or define('SMS_PROVIDER_BROADNET3', 'broadnet3');
defined('SMS_PROVIDER_BANDWITH') or define('SMS_PROVIDER_BANDWITH', 'bandwith');
defined('SMS_PROVIDER_PLIVO') or define('SMS_PROVIDER_PLIVO', 'plivo');


defined('MESSAGE_LEVEL_WARNING') or define('MESSAGE_LEVEL_WARNING', 'MESSAGE_LEVEL_WARNING');
defined('MESSAGE_LEVEL_ERROR') or define('MESSAGE_LEVEL_ERROR', 'MESSAGE_LEVEL_ERROR');
defined('MESSAGE_LEVEL_INFO') or define('MESSAGE_LEVEL_INFO', 'MESSAGE_LEVEL_INFO');


defined('SURVEY_TYPE_REGULAR') or define('SURVEY_TYPE_REGULAR', 1);
defined('SURVEY_TYPE_SATISFACTION') or define('SURVEY_TYPE_SATISFACTION', 2);

defined('SURVEY_ANSWER_TEXT') or define('SURVEY_ANSWER_TEXT', 1);
defined('SURVEY_ANSWER_STAR_1') or define('SURVEY_ANSWER_STAR_1', 2);
defined('SURVEY_ANSWER_STAR_2') or define('SURVEY_ANSWER_STAR_2', 3);
defined('SURVEY_ANSWER_STAR_3') or define('SURVEY_ANSWER_STAR_3', 4);
defined('SURVEY_ANSWER_STAR_4') or define('SURVEY_ANSWER_STAR_4', 5);
defined('SURVEY_ANSWER_STAR_5') or define('SURVEY_ANSWER_STAR_5', 6);
defined('SURVEY_ANSWER_NO') or define('SURVEY_ANSWER_NO', 7);
defined('SURVEY_ANSWER_YES') or define('SURVEY_ANSWER_YES', 8);
defined('SURVEY_ANSWER_FACE_1') or define('SURVEY_ANSWER_FACE_1', 9);
defined('SURVEY_ANSWER_FACE_2') or define('SURVEY_ANSWER_FACE_2', 10);
defined('SURVEY_ANSWER_FACE_3') or define('SURVEY_ANSWER_FACE_3', 11);
defined('SURVEY_ANSWER_FACE_4') or define('SURVEY_ANSWER_FACE_4', 12);
defined('SURVEY_ANSWER_NOT_INTERESTED') or define('SURVEY_ANSWER_NOT_INTERESTED', 13);
defined('SURVEY_ANSWER_EMAIL') or  define('SURVEY_ANSWER_EMAIL', 14);
defined('SURVEY_ANSWER_PHONE') or define('SURVEY_ANSWER_PHONE', 15);
defined('ACTIVATION_LINK_EXPIRES') or define('ACTIVATION_LINK_EXPIRES', '+48 hours');
defined('STRIPE_API_VERSION') or define('STRIPE_API_VERSION', '2020-08-27');
defined('TRD_COUPONS_STRIPE_API_VERSION') or define('TRD_COUPONS_STRIPE_API_VERSION', '2022-11-15');
defined('SYSTEM_ROLES_LIST') or define('SYSTEM_ROLES_LIST', ['business', 'admin', 'branch', 'clerk', 'businessgroupmanager', 'coalition', 'superadmin']);

defined('PACKAGE_BASIC') or define('PACKAGE_BASIC', 1);

//Set the gated sub-features names
defined('GATED_EMPLOYEES_SUB_FEATURE') or define('GATED_EMPLOYEES_SUB_FEATURE', 'employees');
defined('GATED_OFFERS_SUB_FEATURE') or define('GATED_OFFERS_SUB_FEATURE', 'offers');
defined('GATED_REDEMPTIONS_SUB_FEATURE') or define('GATED_REDEMPTIONS_SUB_FEATURE', 'redemptions');
defined('GATED_TRANSACTIONS_SUB_FEATURE') or define('GATED_TRANSACTIONS_SUB_FEATURE', 'transactions');

// CampaignData
defined('CAMPAIGN_SEND_BY_EMAIL_SMS') or define('CAMPAIGN_SEND_BY_EMAIL_SMS', 1);
defined('CAMPAIGN_SEND_BY_PUSH') or define('CAMPAIGN_SEND_BY_PUSH', 2);
defined('CAMPAIGN_SEND_BY_EMAIL') or define('CAMPAIGN_SEND_BY_EMAIL', 3);
defined('CAMPAIGN_SEND_BY_SMS') or define('CAMPAIGN_SEND_BY_SMS', 4);
defined('CAMPAIGN_SEND_BY_SMS_PUSH') or define('CAMPAIGN_SEND_BY_SMS_PUSH', 5);

defined('CAMPAIGN_AUDIENCE_ALL_CUSTOMERS_TYPE') or define('CAMPAIGN_AUDIENCE_ALL_CUSTOMERS_TYPE', 1);
defined('CAMPAIGN_AUDIENCE_TARGETED_CUSTOMERS_TYPE') or define('CAMPAIGN_AUDIENCE_TARGETED_CUSTOMERS_TYPE', 2);
defined('CAMPAIGN_AUDIENCE_GEOFENCE_CUSTOMERS_TYPE') or define('CAMPAIGN_AUDIENCE_GEOFENCE_CUSTOMERS_TYPE', 3);

defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WEEKS_PURCHASES') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WEEKS_PURCHASES', 1);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_FOLLOW_BUSINESS_AND_NEVER_CAME') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_FOLLOW_BUSINESS_AND_NEVER_CAME', 3);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_TARGETED_IN_PREVIOUS_CAMPAIGNS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_TARGETED_IN_PREVIOUS_CAMPAIGNS', 4);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_CLICKED_PROMOTION_FROM_PREVIOUS_CAMPAIGNS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_CLICKED_PROMOTION_FROM_PREVIOUS_CAMPAIGNS', 5);
defined('CAMPAIGN_WHO_WILL_RECEIVE_TOP_HIGHEST_CUSTOMERS') or define('CAMPAIGN_WHO_WILL_RECEIVE_TOP_HIGHEST_CUSTOMERS', 6);
defined('CAMPAIGN_WHO_WILL_RECEIVE_ALL_CUSTOMERS_CLOSED_TO_REDEEMING_POINTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_ALL_CUSTOMERS_CLOSED_TO_REDEEMING_POINTS', 7);
defined('CAMPAIGN_WHO_WILL_RECEIVE_ALL_CUSTOMERS') or define('CAMPAIGN_WHO_WILL_RECEIVE_ALL_CUSTOMERS', 8);
defined('CAMPAIGN_WHO_WILL_RECEIVE_UPCOMING_BIRTHDAYS') or define('CAMPAIGN_WHO_WILL_RECEIVE_UPCOMING_BIRTHDAYS', 9);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_LAST_PURCHASE') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_LAST_PURCHASE', 10);
defined('CAMPAIGN_WHO_WILL_RECEIVE_ALL_CUSTOMERS_HAVING_POSTAL_CODE') or define('CAMPAIGN_WHO_WILL_RECEIVE_ALL_CUSTOMERS_HAVING_POSTAL_CODE', 11);
defined('CAMPAIGN_WHO_WILL_RECEIVE_ALL_CUSTOMERS_BRANCH') or define('CAMPAIGN_WHO_WILL_RECEIVE_ALL_CUSTOMERS_BRANCH', 12);
defined('CAMPAIGN_WHO_WILL_RECEIVE_NEW_CLIENTS_AFTER_FIRST_VISIT') or define('CAMPAIGN_WHO_WILL_RECEIVE_NEW_CLIENTS_AFTER_FIRST_VISIT', 13);
defined('CAMPAIGN_WHO_WILL_RECEIVE_SPECIFIC_CUSTOMER') or define('CAMPAIGN_WHO_WILL_RECEIVE_SPECIFIC_CUSTOMER', 14);
defined('CAMPAIGN_WHO_WILL_RECEIVE_BOUGHT_FROM_A_LA_CARTE') or define('CAMPAIGN_WHO_WILL_RECEIVE_BOUGHT_FROM_A_LA_CARTE', 15);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_POINTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_POINTS', 16);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WITH_FIRST_VISIT') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WITH_FIRST_VISIT', 18);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WEEKS_SPENDING') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_WEEKS_SPENDING', 17);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_TIERS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_TIERS', 19);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_SEGMENTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_SEGMENTS', 20);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_LIFETIME_BALANCE') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_LIFETIME_BALANCE', 21);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_TAGS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_TAGS', 22);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_NOT_MADE_PURCHASES') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_NOT_MADE_PURCHASES', 23);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_MADE_EARN_REWARD') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_MADE_EARN_REWARD', 24);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LS_PRODUCT_TAGS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LS_PRODUCT_TAGS', 25);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REFERRALS_SIGNED_UP') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REFERRALS_SIGNED_UP', 26);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LS_PRODUCT_CATEGORIES') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LS_PRODUCT_CATEGORIES', 27);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_MAGENTO_PRODUCT_CATEGORIES') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_MAGENTO_PRODUCT_CATEGORIES', 28);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LS_PRODUCT_BRANDS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LS_PRODUCT_BRANDS', 29);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LS_PRODUCTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LS_PRODUCTS', 30);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_PREFERRED_LANGUAGE') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_PREFERRED_LANGUAGE', 31);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REGISTER_NO_TRANSACTION') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REGISTER_NO_TRANSACTION', 32);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_CATEGORIES') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_CATEGORIES', 33);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCTS', 34);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_VENDORS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_VENDORS', 35);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_TAGS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_SHOPIFY_PRODUCT_TAGS', 36);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_CATEGORIES') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_CATEGORIES', 37);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_VENDORS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_VENDORS', 38);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_BRANDS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_BRANDS', 39);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCTS', 40);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_TAGS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSECOM_PRODUCT_TAGS', 41);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_WOO_PRODUCT_CATEGORIES') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_WOO_PRODUCT_CATEGORIES', 42);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_WOO_PRODUCTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_WOO_PRODUCTS', 43);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_WOO_PRODUCT_TAGS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_WOO_PRODUCT_TAGS', 44);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_BC_PRODUCT_CATEGORIES') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_BC_PRODUCT_CATEGORIES', 45);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_BC_PRODUCT_BRANDS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_BC_PRODUCT_BRANDS', 46);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_BC_PRODUCTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_BC_PRODUCTS', 47);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_MAGENTO_PRODUCTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_MAGENTO_PRODUCTS', 48);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_PRODUCT_REVIEWS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_PRODUCT_REVIEWS', 49);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REDEEMED_OFFERS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REDEEMED_OFFERS', 50);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REDEEMED_REDEMPTION') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REDEEMED_REDEMPTION', 51);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_STORE_CREDIT') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_STORE_CREDIT', 52);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REFERRED') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REFERRED', 53);
defined('CAMPAIGN_WHO_WILL_RECEIVE_POINT_EXPIRE') or define('CAMPAIGN_WHO_WILL_RECEIVE_POINT_EXPIRE', 54);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_BOOKED_APPOINTMENTS_BETWEEN_DATES') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_BOOKED_APPOINTMENTS_BETWEEN_DATES', 55);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_BOOKED_SPECIFIC_APPOINTMENTS_PER_DAYS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_BOOKED_SPECIFIC_APPOINTMENTS_PER_DAYS', 57);
defined('CAMPAIGN_WHO_WILL_RECEIVE_POINT_EXPIRE_FOR_INACTIVITY') or define('CAMPAIGN_WHO_WILL_RECEIVE_POINT_EXPIRE_FOR_INACTIVITY', 59);
defined('CAMPAIGN_WHO_WILL_CUSTOMERS_REACHED_SPECIFIC_TIER') or define('CAMPAIGN_WHO_WILL_CUSTOMERS_REACHED_SPECIFIC_TIER', 60);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCT_CATEGORIES') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCT_CATEGORIES', 61);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCT_BRANDS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCT_BRANDS', 62);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCTS', 63);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCT_TAGS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECOMZ_PRODUCT_TAGS', 64);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REGISTERED_BETWEEN_DATES') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REGISTERED_BETWEEN_DATES', 65);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCT_VENDORS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCT_VENDORS', 66);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCT_BRANDS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCT_BRANDS', 67);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCTS', 68);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCT_TAGS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_VEND_PRODUCT_TAGS', 69);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_HEARTLAND_PRODUCTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_HEARTLAND_PRODUCTS', 70);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_HEARTLAND_CATEGORIES') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_HEARTLAND_CATEGORIES', 71);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_HEARTLAND_VENDORS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_HEARTLAND_VENDORS', 72);
defined('CAMPAIGN_WHO_WILL_RECEIVE_SPECIFIC_CUSTOMERS_LIST') or define('CAMPAIGN_WHO_WILL_RECEIVE_SPECIFIC_CUSTOMERS_LIST', 73);
defined('CAMPAIGN_WHO_WILL_RECEIVE_REFERRED_CUSTOMERS_NUMBER') or define('CAMPAIGN_WHO_WILL_RECEIVE_REFERRED_CUSTOMERS_NUMBER', 74);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSK_PRODUCT_ACCOUNTING_GROUPS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSK_PRODUCT_ACCOUNTING_GROUPS', 75);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSK_PRODUCTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_LSK_PRODUCTS', 76);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_QRCODE_PRODUCTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_QRCODE_PRODUCTS', 77);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_QRCODE_BRANDS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_QRCODE_BRANDS', 78);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_CAMPAIGNED_OFFERS_REDEMPTIONS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_CAMPAIGNED_OFFERS_REDEMPTIONS', 79);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECWID_CATEGORIES') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECWID_CATEGORIES', 80);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECWID_BRANDS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECWID_BRANDS', 81);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECWID_PRODUCTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_HAVE_PURCHASES_ECWID_PRODUCTS', 82);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REGULAR_LAST_PURCHASE') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_REGULAR_LAST_PURCHASE', 83);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_PREDICT_CHURN') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_PREDICT_CHURN', 84);
defined('CAMPAIGN_WHO_WILL_RECEIVE_REVIEW_CUSTOMERS_HAVE_PURCHASES') or define('CAMPAIGN_WHO_WILL_RECEIVE_REVIEW_CUSTOMERS_HAVE_PURCHASES', 85);
defined('CAMPAIGN_WHO_WILL_RECEIVE_REVIEW_CUSTOMERS_HAVE_PURCHASES_PRODUCTS') or define('CAMPAIGN_WHO_WILL_RECEIVE_REVIEW_CUSTOMERS_HAVE_PURCHASES_PRODUCTS', 86);
defined('CAMPAIGN_WHO_WILL_RECEIVE_REVIEW_CUSTOMERS_LAST_PURCHASES') or define('CAMPAIGN_WHO_WILL_RECEIVE_REVIEW_CUSTOMERS_LAST_PURCHASES', 87);
defined('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_CASL_CONSENT') or define('CAMPAIGN_WHO_WILL_RECEIVE_CUSTOMERS_CASL_CONSENT', 99);
defined('CAMPAIGN_WHO_WILL_RECEIVE_RE_OPT_IN_NO_MARKETING') or define('CAMPAIGN_WHO_WILL_RECEIVE_RE_OPT_IN_NO_MARKETING', 10001);
defined('CAMPAIGN_WHO_WILL_RECEIVE_RE_OPT_IN_NO_TRANSACTIONS') or define('CAMPAIGN_WHO_WILL_RECEIVE_RE_OPT_IN_NO_TRANSACTIONS', 10002);

// Draw
defined('TRX_TYPE_DRAW') or define('TRX_TYPE_DRAW', 18);


// define string constant for IDE autocompletion
defined("FEATURE_MENU_DASHBOARD") or define("FEATURE_MENU_DASHBOARD", 'FEATURE_MENU_DASHBOARD');
defined("FEATURE_MENU_MARKETING") or define("FEATURE_MENU_MARKETING", 'FEATURE_MENU_MARKETING');
defined("FEATURE_MENU_RULES") or define("FEATURE_MENU_RULES", 'FEATURE_MENU_RULES');
defined("FEATURE_MENU_REDEMPTION") or define("FEATURE_MENU_REDEMPTION", 'FEATURE_MENU_REDEMPTION');
defined("FEATURE_MENU_PRODUCT_REWARD") or define("FEATURE_MENU_PRODUCT_REWARD", 'FEATURE_MENU_PRODUCT_REWARD');
defined("FEATURE_MENU_OFFERS") or define("FEATURE_MENU_OFFERS", 'FEATURE_MENU_OFFERS');
defined("FEATURE_MENU_GIFT_CARDS") or define("FEATURE_MENU_GIFT_CARDS", 'FEATURE_MENU_GIFT_CARDS');
defined("FEATURE_MENU_SLIDESHOW") or define("FEATURE_MENU_SLIDESHOW", 'FEATURE_MENU_SLIDESHOW');
defined("FEATURE_MENU_BRANCHES") or define("FEATURE_MENU_BRANCHES", 'FEATURE_MENU_BRANCHES');
defined("FEATURE_MENU_SOCIAL_MEDIA") or define("FEATURE_MENU_SOCIAL_MEDIA", 'FEATURE_MENU_SOCIAL_MEDIA');
defined("FEATURE_MENU_PRODUCTS") or define("FEATURE_MENU_PRODUCTS", 'FEATURE_MENU_PRODUCTS');
defined("FEATURE_MENU_PAYMENTS") or define("FEATURE_MENU_PAYMENTS", 'FEATURE_MENU_PAYMENTS');
defined("FEATURE_MENU_INVITE_CONTACTS") or define("FEATURE_MENU_INVITE_CONTACTS", 'FEATURE_MENU_INVITE_CONTACTS');
defined("FEATURE_MENU_INTEGRATIONS") or define("FEATURE_MENU_INTEGRATIONS", 'FEATURE_MENU_INTEGRATIONS');
defined("FEATURE_MENU_BUSINESS_PROFILE") or define("FEATURE_MENU_BUSINESS_PROFILE", 'FEATURE_MENU_BUSINESS_PROFILE');
defined("FEATURE_MENU_BUSINESS_DRAW") or define("FEATURE_MENU_BUSINESS_DRAW", 'FEATURE_MENU_BUSINESS_DRAW');
defined("FEATURE_MENU_REFERRALS") or define("FEATURE_MENU_REFERRALS", 'FEATURE_MENU_REFERRALS');
defined("FEATURE_MENU_FREQUENT_BUYER") or define("FEATURE_MENU_FREQUENT_BUYER", 'FEATURE_MENU_FREQUENT_BUYER');
defined("FEATURE_MENU_CUSTOMERS") or define("FEATURE_MENU_CUSTOMERS", 'FEATURE_MENU_CUSTOMERS');
defined("FEATURE_MENU_IMPORT_TRANSACTIONS") or define("FEATURE_MENU_IMPORT_TRANSACTIONS", 'FEATURE_MENU_IMPORT_TRANSACTIONS');
defined("FEATURE_MENU_SURVEY") or define("FEATURE_MENU_SURVEY", 'FEATURE_MENU_SURVEY');
defined("FEATURE_MENU_BANNERS") or define("FEATURE_MENU_BANNERS", 'FEATURE_MENU_BANNERS');
defined("FEATURE_MENU_REVIEWS") or define("FEATURE_MENU_REVIEWS", 'FEATURE_MENU_REVIEWS');
defined("FEATURE_MENU_ROLES") or define("FEATURE_MENU_ROLES", 'FEATURE_MENU_ROLES');
defined("FEATURE_MENU_AUDIT_LOGS") or define("FEATURE_MENU_AUDIT_LOGS", 'FEATURE_MENU_AUDIT_LOGS');
defined("DO_NOT_SEND_CREDENTIAL_CHANGE_NOTIFICATION") or define("DO_NOT_SEND_CREDENTIAL_CHANGE_NOTIFICATION", [
    1542, 1852, 1888, 1959, 2030, 2046, 2098, 2102, 2150, 2239, 2315, 2347, 2359, 2360, 2375, 2395, 2423, 2456, 2460,
    2538, 2666, 2667, 2690, 2724, 2812,
]);

defined("RESULT_CALL_TO_ACTION_TYPE_REWARD_POINTS") or define("RESULT_CALL_TO_ACTION_TYPE_REWARD_POINTS", 1);
defined("CALL_TO_ACTION_FOLLOW_FACEBOOK") or define("CALL_TO_ACTION_FOLLOW_FACEBOOK", 4);
defined("CALL_TO_ACTION_FOLLOW_TWITTER") or define("CALL_TO_ACTION_FOLLOW_TWITTER", 6);
defined("CALL_TO_ACTION_FOLLOW_INSTAGRAM") or define("CALL_TO_ACTION_FOLLOW_INSTAGRAM", 7);
defined("CALL_TO_ACTION_FOLLOW_TIKTOK") or define("CALL_TO_ACTION_FOLLOW_TIKTOK", 8);

defined("CONSENT_OFF") or define("CONSENT_OFF", 0);
defined("CONSENT_ON") or define("CONSENT_ON", 1);
defined("CONSENT_DOUBLE_OPT_IN") or define("CONSENT_DOUBLE_OPT_IN", 2);

//Batch import types
defined("BATCH_IMPORT_TRANSACTIONS_TYPE") or define("BATCH_IMPORT_TRANSACTIONS_TYPE", 2);
defined("BATCH_IMPORT_PRODUCTS_TYPE") or define("BATCH_IMPORT_PRODUCTS_TYPE", 4);
defined("BATCH_IMPORT_CUSTOMERS_TYPE") or define("BATCH_IMPORT_CUSTOMERS_TYPE", 5);
defined("BATCH_IMPORT_REQUEST_REVIEWS") or define("BATCH_IMPORT_REQUEST_REVIEWS", 6);
//Batch import statuses
defined("BATCH_IMPORT_STATUS_READY") or define("BATCH_IMPORT_STATUS_READY", 0);
defined("BATCH_IMPORT_STATUS_LOCKED_IMPORT") or define("BATCH_IMPORT_STATUS_LOCKED_IMPORT", 1);
defined("BATCH_IMPORT_STATUS_IMPORTED") or define("BATCH_IMPORT_STATUS_IMPORTED", 2);
defined("BATCH_IMPORT_STATUS_LOCKED_PROCESS") or define("BATCH_IMPORT_STATUS_LOCKED_PROCESS", 3);
defined("BATCH_IMPORT_STATUS_PROCESSED") or define("BATCH_IMPORT_STATUS_PROCESSED", 4);
defined("BATCH_IMPORT_STATUS_FAILED") or define("BATCH_IMPORT_STATUS_FAILED", 5);
defined("BATCH_IMPORT_STATUS_LOCKED_PRE_PROCESS") or define("BATCH_IMPORT_STATUS_LOCKED_PRE_PROCESS", 6);
defined("BATCH_IMPORT_STATUS_PRE_PROCESSED") or define("BATCH_IMPORT_STATUS_PRE_PROCESSED", 7);
defined("BATCH_IMPORT_STATUS_ITEM_DELETED") or define("BATCH_IMPORT_STATUS_ITEM_DELETED", 10);
defined("BATCH_IMPORT_STATUS_IN_PROGRESS") or define("BATCH_IMPORT_STATUS_IN_PROGRESS", 18);
defined("BATCH_IMPORT_STATUS_IMPORTED_WITH_ERRORS") or define("BATCH_IMPORT_STATUS_IMPORTED_WITH_ERRORS", 19);
defined("BATCH_IMPORT_STATUS_UPLOAD_FAILED") or define("BATCH_IMPORT_STATUS_UPLOAD_FAILED", 20);

defined("IMPORT_STATUS_INVALID_ID") or define("IMPORT_STATUS_INVALID_ID", 4);
defined("IMPORT_STATUS_USER_EXISTS_ID") or define("IMPORT_STATUS_USER_EXISTS_ID", 10);
defined("READY_TO_IMPORT_STATUS_ID") or define("READY_TO_IMPORT_STATUS_ID", 12);
defined("IMPORT_STATUS_READY_ID") or define("IMPORT_STATUS_READY_ID", 12);
defined("IMPORT_STATUS_INCOMPLETE_ID") or define("IMPORT_STATUS_INCOMPLETE_ID", 13);
defined("IMPORT_STATUS_IMPORTED_ID") or define("IMPORT_STATUS_IMPORTED_ID", 14);
defined("IMPORTED_STATUS_ID") or define("IMPORTED_STATUS_ID", 14);
defined("PRODUCT_ALREADY_IMPORTED_STATUS_ID") or define("PRODUCT_ALREADY_IMPORTED_STATUS_ID", 17);
defined("IMPORT_STATUS_DUPLICATE_ENTRY_ID") or define("IMPORT_STATUS_DUPLICATE_ENTRY_ID", 21);
defined("IMPORT_STATUS_FAILED_ENTRY_ID") or define("IMPORT_STATUS_FAILED_ENTRY_ID", 22);

//CRM Field IDs
defined('PHONE_CRM_FIELD_ID') or define('PHONE_CRM_FIELD_ID', -1);
defined('EMAIL_CRM_FIELD_ID') or define('EMAIL_CRM_FIELD_ID', -2);
defined('FIRST_NAME_CRM_FIELD_ID') or define('FIRST_NAME_CRM_FIELD_ID', 0);
defined('LAST_NAME_CRM_FIELD_ID') or define('LAST_NAME_CRM_FIELD_ID', 1);
defined('GENDER_CRM_FIELD_ID') or define('GENDER_CRM_FIELD_ID', 2);
defined('BIRTH_DATE_CRM_FIELD_ID') or define('BIRTH_DATE_CRM_FIELD_ID', 3);
defined('ADDRESS_CRM_FIELD_ID') or define('ADDRESS_CRM_FIELD_ID', 5);
defined('POSTAL_CODE_CRM_FIELD_ID') or define('POSTAL_CODE_CRM_FIELD_ID', 6);
defined('COUNTRY_CRM_FIELD_ID') or define('COUNTRY_CRM_FIELD_ID', 7);
defined('PROMOTIONS_MAIL_CRM_FIELD_ID') or define('PROMOTIONS_MAIL_CRM_FIELD_ID', 8);
defined('CUSTOM_FIELD_1_CRM_FIELD_ID') or define('CUSTOM_FIELD_1_CRM_FIELD_ID', 10);
defined('CUSTOM_FIELD_2_CRM_FIELD_ID') or define('CUSTOM_FIELD_2_CRM_FIELD_ID', 11);
defined('CUSTOM_FIELD_3_CRM_FIELD_ID') or define('CUSTOM_FIELD_3_CRM_FIELD_ID', 12);
defined('CUSTOM_FIELD_4_CRM_FIELD_ID') or define('CUSTOM_FIELD_4_CRM_FIELD_ID', 13);
defined('CUSTOM_FIELD_5_CRM_FIELD_ID') or define('CUSTOM_FIELD_5_CRM_FIELD_ID', 14);
defined('TERMS_CONDITIONS_CRM_FIELD_ID') or define('TERMS_CONDITIONS_CRM_FIELD_ID', 100);
defined('TERMS_CONDITIONS_CRM_INPUT_NAME') or define('TERMS_CONDITIONS_CRM_INPUT_NAME', "terms_and_conditions");

//Notification priority
defined('EMAIL_NOTIFICATION_PRIORITY') or define('EMAIL_NOTIFICATION_PRIORITY', 1);
defined('SMS_NOTIFICATION_PRIORITY') or define('SMS_NOTIFICATION_PRIORITY', 2);

//Recommendation Types
defined('RECOMMENDATION_TYPE_BLOG') or define('RECOMMENDATION_TYPE_BLOG', 1);
defined('RECOMMENDATION_TYPE_CAMPAIGN') or define('RECOMMENDATION_TYPE_CAMPAIGN', 2);
defined('RECOMMENDATION_TYPE_OFFER') or define('RECOMMENDATION_TYPE_OFFER', 3);
defined('RECOMMENDATION_TYPE_REWARD') or define('RECOMMENDATION_TYPE_REWARD', 4);
defined('RECOMMENDATION_TYPE_MISC') or define('RECOMMENDATION_TYPE_MISC', 5);

//Recommendations
defined('RECOMMENDATION_UPDATE_IF_UPDATE_IF_NO_AUTO_CAMPAIGN_CONFIGURED') or define('RECOMMENDATION_UPDATE_IF_UPDATE_IF_NO_AUTO_CAMPAIGN_CONFIGURED', 1);
defined('RECOMMENDATION_UPDATE_IF_NO_SIGNUP_BONUS_CONFIGURED_ID') or define('RECOMMENDATION_UPDATE_IF_NO_SIGNUP_BONUS_CONFIGURED_ID', 14);

//Recommendation Actions
defined('ACTION_TIER_PROGRAM_EXISTS_EARNING_RATIO_BLANK') or define('ACTION_TIER_PROGRAM_EXISTS_EARNING_RATIO_BLANK', 'tier_program_exists_earning_ratio_blank');
defined('ACTION_NO_BONUS_POINTS_OFFER') or define('ACTION_NO_BONUS_POINTS_OFFER', 'no_bonus_points_offer');
defined('ACTION_NO_ACTIVE_POINT_MULTIPLIER_OFFER') or define('ACTION_NO_ACTIVE_POINT_MULTIPLIER_OFFER', 'no_active_point_multiplier_offer');
defined('ACTION_NO_NEW_CUSTOMERS_CAMPAIGN') or define('ACTION_NO_NEW_CUSTOMERS_CAMPAIGN', 'no_new_customers_campaign');
defined('ACTION_NO_OFFERS') or define('ACTION_NO_OFFERS', 'no_offers');
defined('ACTION_NO_FOLLOW_BONUS_POINTS') or define('ACTION_NO_FOLLOW_BONUS_POINTS', 'no_follow_bonus_points');
defined('ACTION_NO_RETENTION_CAMPAIGN') or define('ACTION_NO_RETENTION_CAMPAIGN', 'no_retention_campaign');
defined('ACTION_NO_RETARGETING_CAMPAIGN') or define('ACTION_NO_RETARGETING_CAMPAIGN', 'no_retargeting_campaign');
defined('ACTION_NO_BIRTHDAY_CAMPAIGNS') or define('ACTION_NO_BIRTHDAY_CAMPAIGNS', 'no_birthday_campagins');
defined('ACTION_NO_AUTO_CAMPAIGNS') or define('ACTION_NO_AUTO_CAMPAIGNS', 'no_auto_campaigns');
defined('ACTION_NO_REWARDS_OTHER_THAN_FORCE_CREATED') or define('ACTION_NO_REWARDS_OTHER_THAN_FORCE_CREATED', 'no_rewards_other_than_force_created');
defined('ACTION_NO_SIGNUP_BONUS') or define('ACTION_NO_SIGNUP_BONUS', 'no_signup_bonus');
defined('ACTION_NO_APP_OFFERS') or define('ACTION_NO_APP_OFFERS', 'no_app_offers');

// Customized Messages/Emails
defined('CUSTOMIZED_MESSAGES_TYPE_TRANSACTIONAL') or define('CUSTOMIZED_MESSAGES_TYPE_TRANSACTIONAL', 1);
defined('CUSTOMIZED_MESSAGES_TYPE_MARKETING') or define('CUSTOMIZED_MESSAGES_TYPE_MARKETING', 2);
defined('CUSTOMIZED_MESSAGES_KEY_USER_FIRST') or define('CUSTOMIZED_MESSAGES_KEY_USER_FIRST', 'userFirst');
defined('CUSTOMIZED_MESSAGES_KEY_USER_LAST') or define('CUSTOMIZED_MESSAGES_KEY_USER_LAST', 'userLast');
defined('CUSTOMIZED_MESSAGES_KEY_ANDROID_APP_LINK') or define('CUSTOMIZED_MESSAGES_KEY_ANDROID_APP_LINK', 'androidAppLink');
defined('CUSTOMIZED_MESSAGES_KEY_IOS_APP_LINK') or define('CUSTOMIZED_MESSAGES_KEY_IOS_APP_LINK', 'iosAppLink');
defined('CUSTOMIZED_MESSAGES_KEY_CONGLOMERATE_NAME') or define('CUSTOMIZED_MESSAGES_KEY_CONGLOMERATE_NAME', 'conglomerateName');
defined('CUSTOMIZED_MESSAGES_KEY_BUTTON_COLOR') or define('CUSTOMIZED_MESSAGES_KEY_BUTTON_COLOR', 'buttonColor');
defined('CUSTOMIZED_MESSAGES_KEY_USER_BALANCE') or define('CUSTOMIZED_MESSAGES_KEY_USER_BALANCE', 'userBalance');
defined('CUSTOMIZED_MESSAGES_KEY_USER_REFERRAL_LINK') or define('CUSTOMIZED_MESSAGES_KEY_USER_REFERRAL_LINK', 'userReferralLink');
defined('CUSTOMIZED_MESSAGES_KEY_PIN_CODE') or define('CUSTOMIZED_MESSAGES_KEY_PIN_CODE', 'pinCode');
defined('CUSTOMIZED_MESSAGES_KEY_BUSINESS_NAME') or define('CUSTOMIZED_MESSAGES_KEY_BUSINESS_NAME', 'businessName');
defined('CUSTOMIZED_MESSAGES_KEY_USER_EMAIL') or define('CUSTOMIZED_MESSAGES_KEY_USER_EMAIL', 'userEmail');
defined('CUSTOMIZED_MESSAGES_KEY_WElCOME_LINK') or define('CUSTOMIZED_MESSAGES_KEY_WElCOME_LINK', 'welcomeLink');
defined('CUSTOMIZED_MESSAGES_KEY_ACTIVATION_LINK_EXPIRES') or define('CUSTOMIZED_MESSAGES_KEY_ACTIVATION_LINK_EXPIRES', 'activationLinkExpires');
defined('CUSTOMIZED_MESSAGES_KEY_NEW_TIER_NAME') or define('CUSTOMIZED_MESSAGES_KEY_NEW_TIER_NAME', 'newTierName');
defined('CUSTOMIZED_MESSAGES_KEY_ITUNES_PATH') or define('CUSTOMIZED_MESSAGES_KEY_ITUNES_PATH', 'itunesPath');
defined('CUSTOMIZED_MESSAGES_KEY_PLAY_STORE_PATH') or define('CUSTOMIZED_MESSAGES_KEY_PLAY_STORE_PATH', 'playStore');
defined('CUSTOMIZED_MESSAGES_KEY_BUSINESS_SUPP_EMAIL') or define('CUSTOMIZED_MESSAGES_KEY_BUSINESS_SUPP_EMAIL', 'businessSupportEmail');
defined('CUSTOMIZED_MESSAGES_KEY_REVIEW_ITEMS') or define('CUSTOMIZED_MESSAGES_KEY_REVIEW_ITEMS', 'reviewItemsBlock');
defined('CUSTOMIZED_MESSAGES_KEY_USER_FULL_NAME') or define('CUSTOMIZED_MESSAGES_KEY_USER_FULL_NAME', 'userFullName');
defined('CUSTOMIZED_MESSAGES_KEY_REVIEW_COUPON_CODE') or define('CUSTOMIZED_MESSAGES_KEY_REVIEW_COUPON_CODE', 'reviewCoupon');

// Spin To Win
defined('BACKEND_PRIZE_SPIN_FREE_PRODUCT') or define('BACKEND_PRIZE_SPIN_FREE_PRODUCT', 1);
defined('BACKEND_PRIZE_SPIN_FREE_POINTS') or define('BACKEND_PRIZE_SPIN_FREE_POINTS', 2);
defined('BACKEND_PRIZE_SPIN_PORCENTAGE_OFF') or define('BACKEND_PRIZE_SPIN_PORCENTAGE_OFF', 3);
defined('BACKEND_PRIZE_SPIN_OFFER') or define('BACKEND_PRIZE_SPIN_OFFER', 4);

defined('DEFAULT_TIER_RESET_YEAR') or define('DEFAULT_TIER_RESET_YEAR', 2019);

defined('ADMIN_SUPPORT_EMAIL') or define('ADMIN_SUPPORT_EMAIL', "<EMAIL>");
defined('ITUNES_PATH') or define('ITUNES_PATH', 'https://itunes.apple.com/ca/app/kangaroo-rewards/id673355611?mt=8');
defined('PLAY_STORE_PATH') or define('PLAY_STORE_PATH', 'https://play.google.com/store/apps/details?id=com.mobicept.kangaroo.rewards.smartphone');
// Import module
defined('IMPORT_CUSTOMERS_TYPE') or define('IMPORT_CUSTOMERS_TYPE', "customers");
defined('IMPORT_PRODUCTS_TYPE') or define('IMPORT_PRODUCTS_TYPE', "products");
defined('IMPORT_TRANSACTIONS_TYPE') or define('IMPORT_TRANSACTIONS_TYPE', "transactions");
defined('IMPORT_REQUEST_REVIEWS_TYPE') or define('IMPORT_REQUEST_REVIEWS_TYPE', "review_requests");
defined('IMPORT_MIN_ALLOWED_ROWS') or define('IMPORT_MIN_ALLOWED_ROWS', 1);
defined('IMPORT_MAX_VALIDATION_ROWS') or define('IMPORT_MAX_VALIDATION_ROWS', 500);
defined('IMPORT_CHUNK_SIZE') or define('IMPORT_CHUNK_SIZE', 1000);
