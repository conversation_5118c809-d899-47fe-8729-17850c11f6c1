<?php

namespace App\Http\Resources;

use App\Models\V1\Business;
// use Illuminate\Http\Resources\Json\JsonResource;

class CampaignMessageResource extends CampaignResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     *
     * @return array
     */
    public function toArray($request)
    {
        $include = explode(',', $request->include);

        $includeSettings = in_array('business_settings', $include, true);

        if ($includeSettings) {
            $newInclude = $this->excludeSettingsAttribute($include);
            // IMPORTANT: update the request before calling CampaignResource
            $request->merge(['include' => implode(',', $newInclude),]);
        }

        $campaign = (new CampaignResource($this->campaign))->response()->getData();

        $params = json_decode($this->params);
        $business = Business::findByPk($params->businessId);

        // \Log::info(__METHOD__, ['include' => $include, 'includeSettings' => $includeSettings]);

        return [
            $this::$wrap => [
                'id' => $this->email_sms_queue_pk,
                'campaign' => $campaign->data,
                'business' => Business::transformItem($business),
                $this->mergeWhen($includeSettings, [
                    'business_settings' => [
                        'app_context' => [
                            'points_label' => $this->getPointsLabel($request->hasHeader('Accept-Language')),
                        ],
                    ],
                ]),
            ]
        ];
    }

    private function excludeSettingsAttribute($include): array
    {
        $newInclude = [];
        // Exclude the key from include to avoid double inclusion
        foreach ($include as $incVal) {
            if ($incVal !== 'business_settings') {
                $newInclude[] = $incVal;
            }
        }
        return $newInclude;
    }
}
