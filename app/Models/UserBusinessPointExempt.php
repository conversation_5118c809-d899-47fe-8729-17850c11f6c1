<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class UserBusinessPointExempt extends Model
{
    /**
     * The name of the "created at" column.
     *
     * @var string
     */
    const CREATED_AT = 'created_at';

    /**
     * The name of the "updated at" column.
     *
     * @var string
     */
    const UPDATED_AT = 'updated_at';

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'tenant';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_business_point_exempt';

    /**
     * The primary key associated with the model.
     *
     * @var int
     */
    protected $primaryKey = 'user_business_point_exempt_pk';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
    ];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        static::addGlobalScope('enabled', function (Builder $builder) {
            $builder->where('enabled', 1);
        });
    }

    public static function findUserBusinessPointExempt($userId, $platformId)
    {
        if (!$platformId) {
            return null;
        }

        return UserBusinessPointExempt::where('user_fk', $userId)
            ->where('platform_fk', $platformId)
            ->first();
    }
}