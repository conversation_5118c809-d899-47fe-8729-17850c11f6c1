<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiHelper;
use App\Helpers\CollectionHelper;
use App\Helpers\DataTransferObject\DataTransferObjectError;
use App\Helpers\Dto\UserActions\RewardCalculatorData;
use App\Helpers\V1\Utils;
use App\Http\Requests\V1\ClaimableOffersRequest;
use App\Http\Requests\V1\CustomerActionRequest;
use App\Http\Requests\V1\CustomerGetTransactions;
use App\Http\Resources\CatalogItemCollection;
use App\Http\Resources\CouponCollection;
use App\Http\Resources\Admin\OfferCollection as AdminOfferCollection;
use App\Http\Resources\TransactionCollection;
use App\Jobs\SendDoubleOptInMessage;
use App\Models\ForgottenDataRequest;
use App\Models\User;
use App\Models\V1\AuthUserToken;
use App\Models\V1\Business;
use App\Models\V1\BusinessBranch;
use App\Models\V1\BusinessTierSetup;
use App\Models\V1\CatalogItem;
use App\Models\V1\Customer;
use App\Models\V1\Notification;
use App\Models\V1\Offer;
use App\Models\V1\Transaction;
use App\Models\V1\UserOfferStory;
use App\Repositories\OfferSearcher;
use App\Services\CreateUserCommand;
use App\Services\CreateUserCommandHandler;
use App\Services\RewardCalculator;
use App\Services\TransactionalMessageService;
use App\Services\UserTierLevelService;
use App\Traits\RelationTrait;
use App\Traits\UserAnonymizationTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use libphonenumber\NumberParseException;

/**
 * Customer resource representation.
 *
 * @Resource("Customers", uri="/customers")
 */
class CustomerController extends ApiController
{
    use UserAnonymizationTrait;

    /**
     * List Customers
     * Get a JSON representation of all customers for a business
     *
     * GET /customers
     *
     * @Get("/customers{?page}")
     * @Versions({"v1"})
     * @Response(200, body={"data":[{"id":12345678,"first":"Sara","last":"Silverman","email":"<EMAIL>"},{"id":12345679,"first":"victoria","last":"Leblanc","email":"<EMAIL>"}],"cursor": {"next": 2,"next_uri": "/customers?page=2"}})
     * @Parameters({
     *      @Parameter("page", type="integer", required=false description="The page of results to view.", default=1)
     * })
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request, $notSeparateInclude = true)
    {
        try {
            $user = Auth::user(); //merchant user id
            // $business = Business::forUser($user);

            $business = $request->appBusiness();

            $this->sanitizeInput(); # Always sanitize the input first

            $limit = config('app.pagination_limit');
            $page = (int) ($request->page ?? 1);
            $offset = ($page * $limit) - $limit;
            $next = ++$page;

            // Filter or retrieve all customers
            $conditions = Customer::getFilteringConditions($request);
            $include = $request->include ? array_map('trim', explode(',', $request->include)) : [];

            $search = $request->search ?? null;
            $orderFirst = $request->order_first ?? null;

            $customers = Customer::forBusiness($business->business_pk, $business->entity_type_fk, $limit, $offset, $conditions, $include, $search, $orderFirst);

            $_customers = Customer::transformCollection($customers, [], $include, $request, $business, $notSeparateInclude);

            // $total = Customer::getTotal($business->business_pk, $business->entity_type_fk, $conditions);

            $count = count($_customers);
        } catch (\InvalidArgumentException $e) {
            \Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\LogicException $e) {
            \Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \Log::error($e);
            return $this->errorInternalError();
        }

        return $this->respondWithArray([
            'data' => $_customers,
            'cursor' => [
                'count' => $count,
                'next' => $next,
                'next_uri' => 'customers?page=' . $next,
            ],
            // 'meta' => $total
        ]);
    }

    /**
     * 
     * Get list of customers with the same structure as the "show" endpoint / function
     * 
     */
    public function bpaIndex(Request $request)
    {
        return $this->index($request, false);
    }

    /**
     * Create a new Customer
     * Returns JSON representation of a new created customer
     *
     * POST /customers
     *
     * @Post("/customers")
     * @Versions({"v1"})
     * @Request({email": "<EMAIL>", "first_name":"Sara","last_name":"Silverman"}, headers={"Authorization": "Bearer eyJ0eXAiOiJKV1Q", "Accept": "application/vnd.kangaroorewards.api.v1+json;"})
     * @Response(200, body={"data":[{"id":12345678,"first_name":"Sara","last_name":"Silverman","email":"<EMAIL>"},{"id":12345679,"first":"victoria","last":"Leblanc","email":"<EMAIL>"}],"cursor": {"next": 2,"next_uri": "/customers?page=2"}})
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $user = Auth::user(); //merchant user id

            $business = $request->appBusiness();

            //$mainBranch = BusinessBranch::getInfo($business->business_branch_pk);

            $data = $request->isJson() ? $request->json()->all() : $request->all();
            $data['coalition'] = $business->coalition;
            $data['business_pk'] = $business->business_pk;
            $data['ip'] = $request->ip();
            $data['timezone_mysql_fk'] = $business->branch_timezone_fk;
            $data['clerk_id'] = $user->id;
        } catch (\InvalidArgumentException $e) {
            \Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\LogicException $e) {
            \Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \Log::error($e);
            return $this->errorInternalError();
        }

        Customer::validateOnCreate($data)->validate();

        $user = null;
        if (!empty($data['phone'])) {
            $data['phone'] = Utils::normalizeDigitsOnly($data['phone']);
            if (!Utils::isValidPhone($data['phone'], $data['country_code'])) {
                return $this->errorWrongArgs('Bad Request', 'Invalid phone number');
            }
        }

        $command = new CreateUserCommand($business, $data);
        $handler = new CreateUserCommandHandler($command);
        try {
            $customer = $handler->handle();
        } catch (\Exception $e) {
            \Log::error($e, ['business' => $business->name]);
            return $this->errorInternalError();
        }

        /*
        //New Customer - prepare for insert
        $data = Customer::prepareCustomerData($data, $mainBranch);

        \DB::connection('tenant')->beginTransaction();
        try {
            $customer = Customer::createForBusiness($data, $business);

            \DB::connection('tenant')->commit();
        } catch (\Exception $e) {
            \DB::connection('tenant')->rollBack();
            \Log::error(__METHOD__, ['exception' => $e, 'business' => $business->name]);
            return $this->errorInternalError();
        }*/

        $_customer = Customer::transformItem($customer);

        return $this->respondWithArray(['data' => $_customer]);
    }

    /**
     * Updates the details of an existing customer
     * Returns JSON representation of a customer
     *
     * PUT /customers/{customer_id}
     *
     * @Put("/customers/{customer_id}")
     * @Versions({"v1"})
     * @Response(200, body={"data":[{"id":12345678,"first":"Sara","last":"Silverman","email":"<EMAIL>"},{"id":12345679,"first":"victoria","last":"Leblanc","email":"<EMAIL>"}],"cursor": {"next": 2,"next_uri": "/customers?page=2"}})
     *
     * @param Request $request
     *
     * @param $customerUId
     * @return JsonResponse
     * @throws \Exception
     */
    public function update(Request $request, $customerUId)
    {
        try {
            $user = Auth::user(); //merchant user id
            // $business = Business::forUser($user);
            //            $business = $this->getBusinessForAppKey();
            $business = $request->appBusiness();

            $mainBranch = BusinessBranch::getInfo($business->business_branch_pk);

            $data = $request->isJson() ? $request->json()->all() : $request->all();
            $data['coalition'] = $business->coalition;
            $data['business_pk'] = $business->business_pk;
            $data['timezone_mysql_fk'] = $business->branch_timezone_fk;
            $data['ip'] = $request->ip();
            $data['clerk_id'] = $user->id;

            $customer = Customer::findUid($customerUId, ['user_uid', 'id', 'email', 'phone', 'first', 'last']);
            if (!$customer) {
                \Log::warning(__METHOD__, ['message' => 'Customer Not found', 'customerUId' => $customerUId, 'business' => $business->name]);
                return $this->errorNotFound("Customer $customerUId Not Found");
            }
        } catch (\InvalidArgumentException $e) {
            \Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\LogicException $e) {
            \Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \Log::error($e);
            return $this->errorInternalError();
        }

        Customer::validateOnUpdate($request, $business, $customer->id)->validate();

        if (!empty($data['phone'])) {
            $data['phone'] = Utils::normalizeDigitsOnly($data['phone']);

            if (!Utils::isValidPhone($data['phone'], $data['country_code'])) {
                return $this->errorWrongArgs('Bad Request', 'Invalid phone number');
            }
        }

        /**
         * Insert records in DB in a Transaction
         */
        \DB::connection('tenant')->beginTransaction();
        try {
            /** @var Customer $customer */
            $customer = Customer::updateProfile($data, $customer, $business);
            $customer = $customer->getProfile();

            \DB::connection('tenant')->commit();
        } catch (\Exception $e) {
            \DB::connection('tenant')->rollBack();
            \Log::error($e, ['business_name' => $business->name, 'customerUId' => $customerUId, 'input' => $request->all()]);
            return $this->errorInternalError();
        }

        $_customer = Customer::transformItem($customer);

        return $this->respondWithArray(['data' => $_customer]);




        // try {
        //     //prepare for SmartPhone API
        //     $data['appToken'] = config('app.api_app_token');
        //     $data['countryCodeIso'] = isset($data['country_code']) ? $data['country_code'] : null;
        //     $data['timeZoneName'] = $business->time_zone;

        //     $customer = Customer::saveOptionalAttributes($customer, $data);

        //     $_customer = Customer::transformItem($customer);
        //     return $this->respondWithArray(['data' => $_customer]);

        // } catch (\Exception $e) {
        //     \Log::warning(__METHOD__, ['message' => $e, 'customerUId' => $customerUId, 'business' => $business->name]);
        //     // var_dump(htmlentities($e));die;
        //     return $this->errorInternalError();
        // }
    }

    /**
     * Retrieve a Customer
     * Get a JSON representation of a specific customer
     *
     * GET /customers/{id}
     *
     * @Get("/customer/{id}{?include=balance}")
     * @Versions({"v1"})
     * @Transaction({
     *      @Response(200, body={"data":{"profile":{"user_id":12345678,"username":"************","email":"<EMAIL>","first_name":"John","last_name":"Doe"},"balance":{"active_points":600,"active_punches":0}}})
     *      @Response(404, body={"data":null,"code":404,"error":"Not found","message":"Customer 12345118 could not be found."})
     * })
     * @Parameters({
     *      @Parameter("include[]", type="array", required=false description="You can optionally request that the response include the customer's balance. Example: include=balance")
     * })
     *
     * @param Request $request
     * @param string $customerUId
     *
     * @return JsonResponse
     */
    public function show(Request $request, $customerUId)
    {
        $user = Auth::user(); //merchant user id
        // $business = Business::forUser($user);
        // $business = $this->getBusinessForAppKey();
        $business = $request->appBusiness();
        $customer = Customer::findByUIdForResponse($customerUId, Customer::PROFILE_FIELDS);

        $data = Customer::transformItem($customer);

        $response = Business::includeRelations($request, $data, $business, $customer);

        return $this->respondWithArray($response);
    }

    public function destroy(Request $request, Customer $customer)
    {
        $business = $request->appBusiness();

        if ((int) $customer->enabled === 0 || $customer->business_fk !== $business->business_pk) {
            return $this->errorNotFound("Customer Not Found");
        }

        DB::connection('tenant')->beginTransaction();
        try {
            $this->disableUserBusinessNotification($customer->id);
            $this->deleteFollower($customer->id);
            $this->deleteAddress($customer->id);
            $this->deleteUserBalance($customer->id, $business->business_pk);
            $this->anonymizeUser($customer);

            DB::connection('tenant')->commit();
        } catch (\Exception $e) {
            DB::connection('tenant')->rollBack();
            Log::warning($e, ['user_id' => $customer->id, 'business_fk' => $business->business_pk]);
            return $this->errorInternalError();
        }

        Log::info(__METHOD__, [
            'user_id' => $customer->id,
            'business_fk' => $business->business_pk,
            'message' => 'User Deleted',
        ]);

        return $this->respondNoContent();
    }

    /**
     * List Customer's offers with actions to be taken
     *
     * GET /customers/{id}/offers
     *
     * @param Request $request
     * @param $customerUId
     * @return JsonResponse|mixed
     */
    public function offers(Request $request, $customerUId)
    {
        $business = $request->appBusiness();

        $customer = Customer::findByUIdForResponse($customerUId, Customer::PROFILE_FIELDS);

        $this->validate($request, [
            'exclude_used_coupons' => 'sometimes|boolean|in:1'
        ]);

        $offerSearcher = new OfferSearcher($request, $customer, $business);
        $offerSearcher->setFilterBy([1, 2, 3, 8, 9, 10, 11]);
        $offerSearcher->setExcludeUsedCouponsFilter($request->exclude_used_coupons);

        return $offerSearcher->search((int) $request->per_page)->getCollection()->response();
    }

    /**
     * List Customer's rewards to redeem including targeted
     *
     * GET /customers/{id}/rewards
     *
     * @param Request $request
     * @param $customerUId
     * @return JsonResponse|mixed
     */
    public function rewards(Request $request, $customerUId)
    {
        $business = $request->appBusiness(); // resolved in business:employee middleware

        $customer = Customer::findByUIdForResponse($customerUId, Customer::PROFILE_FIELDS);

        $this->validate($request, [
            'per_page' => 'sometimes|integer|min:1|max:' . config('app.pagination_limit'),
            'branch_id' => 'sometimes|string',
        ]);

        $conditions = ApiHelper::buildQueryConditions(
            $request->only(['branch_id'])
        );

        $catalogItems = CatalogItem::forUserBusinessPaginated($business->business_pk, $customer->id, 1, $conditions)
            ->simplePaginate((int)$request->per_page);

        return new CatalogItemCollection($catalogItems);
    }

    /**
     * List Customer's available coupons: targeted and convertible
     *
     * GET /customers/{id}/available-coupons
     *
     * @param Request $request
     * @param $customerUId
     * @return JsonResponse|mixed
     */
    public function availableCoupons(Request $request, $customerUId)
    {
        $this->validate($request, [
            'per_page' => 'sometimes|integer|min:1|max:' . config('app.pagination_limit'),
        ]);
        $business = $request->appBusiness();

        $user = Customer::findByUIdForResponse($customerUId, Customer::PROFILE_FIELDS);

        $coupons = UserOfferStory::getAvailableCouponsForUser($business, $user)
            ->simplePaginate((int) $request->per_page);

        return new CouponCollection($coupons);
    }

    /**
     * List Customer's claimable offers
     *
     * GET /customers/{id}/claimable-offers
     *
     * @param ClaimableOffersRequest $request
     * @param $customerUId
     * @return JsonResponse|mixed
     */
    public function claimableOffers(ClaimableOffersRequest $request, $customerUId)
    {
        $data = $request->validated();

        $business = $request->appBusiness();

        $user = Customer::findByUIdForResponse($customerUId, Customer::PROFILE_FIELDS);

        $branch = $request->getBranch(); // We already have the branch and authorized
        if (!$branch) {
            $branch = BusinessBranch::getInfo($business->business_branch_pk); // Main Branch
        }

        $offers = Offer::getApplyingOffersForPurchase(
            $branch,
            $user->id,
            $data['amount'],
            $loginType = 1
        );

        $perPage = (int) ($data['per_page'] ?? 15);
        $offersPaginated = CollectionHelper::simplePaginate($offers, $perPage);

        return new AdminOfferCollection($offersPaginated);
        //        return new ResourceCollection($offersPaginated);
    }

    /**
     * List Customer's transactions
     *
     * GET /customers/{id}/transactions
     *
     * @Get("/customer/{id}/transactions{?page}")
     * @Versions({"v1"})
     * @Transaction({
     *      @Response(200, body={"data":{"profile":{"user_id":12345678,"username":"************","email":"<EMAIL>","first_name":"John","last_name":"Doe"},"balance":{"active_points":600,"active_punches":0}}})
     *      @Response(404, body={"data":null,"code":404,"error":"Not found","message":"Customer 12345118 could not be found."})
     * })
     * @Parameters({
     *      @Parameter("page", type="integer", required=false description="You can optionally request that the response include the customer's balance. Example: include=balance")
     * })
     *
     * @param Request $request
     * @param string $customerUId
     *
     * @return JsonResponse
     */
    public function transactions(CustomerGetTransactions $request, string $customerUId)
    {
        try {
            $user = Auth::user(); //merchant user id
            // $business = Business::forUser($user);
            //            $business = $this->getBusinessForAppKey();
            $business = $request->appBusiness();
            $customer = Customer::findUid(trim($customerUId), ['id']);

            if (!$customer) {
                Log::warning(__METHOD__, [
                    'message' => 'Customer Not found',
                    'customerUId' => $customerUId,
                    'business_pk' => $business->business_pk,
                    'business_name' => $business->name,
                ]);
                return $this->errorNotFound("Customer $customerUId Not Found");
            }

            $this->sanitizeInput();

            $relations = explode(',', $request->relationships);
            $include = explode(',', $request->include);

            $conditions = $this->getFilterTransactionsConditions($request);

            $transactions = Transaction::forCustomerWithPagination(
                $business,
                $business,
                $customer->id,
                $relations,
                $include,
                $conditions
            )->simplePaginate((int) $request->per_page);

            return (new TransactionCollection($transactions))->response();
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\LogicException $e) {
            Log::error($e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            Log::error($e);
            return $this->errorInternalError();
        }
    }

    /**
     * List Customer's notifications
     *
     * GET /customers/{id}/transactions
     *
     * @Get("/customer/{id}/transactions{?page}")
     * @Versions({"v1"})
     * @Transaction({
     *      @Response(200, body={"data":{"profile":{"user_id":12345678,"username":"************","email":"<EMAIL>","first_name":"John","last_name":"Doe"},"balance":{"active_points":600,"active_punches":0}}})
     *      @Response(404, body={"data":null,"code":404,"error":"Not found","message":"Customer 12345118 could not be found."})
     * })
     * @Parameters({
     *      @Parameter("page", type="integer", required=false description="You can optionally request that the response include the customer's balance. Example: include=balance")
     * })
     *
     * @param Request $request
     * @param string $customerUId
     *
     * @return \Illuminate\Http\Response
     */
    public function notifications(Request $request, $customerUId)
    {
        return $this->errorNotImplemented();

        $user = Auth::user(); //merchant user id
        // $business = Business::forUser($user);
        //        $business = $this->getBusinessForAppKey();
        $business = $request->appBusiness();
        $customer = Customer::findUid(trim($customerUId), ['id']);

        if (!$customer) {
            \Log::warning(__METHOD__, ['message' => 'Customer Not Found', 'customerUId' => $customerUId, 'business' => $business->name]);
            return $this->errorNotFound("Customer $customerUId Not Found");
        }

        $limit = 25;
        $page = ($request->page) ? (int) $request->page : 1;
        $offset = ($page * $limit) - $limit;
        $next = ++$page;

        try {
            $userToken = AuthUserToken::where('user_fk', $customer->id)
                ->where('enabled', 1)->first();

            //prepare for SmartPhone API
            $data['userToken'] = $userToken->ws_user_token;
            $data['platformSolutionFk'] = config('app.platform_id');

            $http = new \GuzzleHttp\Client(['base_uri' => config('app.server_url')]);
            $response = $http->request('GET', '/api/SmartPhone/GetUserNotificationsMessages', [
                'query' => $data,
            ]);

            $body = (string) $response->getBody();
            $result = json_decode($body, true);

            if ($result['status'] == 'NOT_OK') {
                \Log::warning(__METHOD__, ['message' => 'NOT_OK from SmartPhone API', 'customerUId' => $customerUId, 'business' => $business->name]);
                return $this->errorWrongArgs($result['message']);
            }

            if ($result['status'] == 'OK' && $result['results']) {
                $_notifications = Notification::normalizeApiCollection($result["results"]["notifications"]);

                return $this->respondWithArray(['data' => $_notifications]);
            } else {
                \Log::warning(__METHOD__, ['message' => 'Response from Smartphone API', 'result' => $result, 'customerUId' => $customerUId, 'business' => $business->name]);
                return $this->errorInternalError();
            }
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            \Log::error(__METHOD__, ['message' => $e->getResponse(), 'customerUId' => $customerUId, 'business' => $business->name]);
            return $this->errorInternalError();
            // return $this->respondWithArray([
            //     'code' => $e->getResponse()->getStatusCode(),
            //     'error'=>$e->getResponse()->getReasonPhrase(),
            //     'message'=>$e->getMessage(),
            // ], $e->getResponse()->getStatusCode());
        } catch (\Exception $e) {
            // var_dump(htmlentities($e));die;
            \Log::error(__METHOD__, ['message' => $e, 'customerUId' => $customerUId, 'business' => $business->name]);
            return $this->errorInternalError();
        }
    }

    public function createAction(CustomerActionRequest $request, $customerUId)
    {
        $employee = Auth::user(); //merchant user id
        $business = $request->appBusiness();
        $customer = Customer::findUid(
            trim($customerUId),
            ['id', 'email', 'phone', 'utc_created'],
            ['business_fk' => $business->business_pk]
        );
        // or  [['business_fk',$business->business_pk]]

        if (!$customer) {
            \Log::warning(__METHOD__, [
                'message' => 'Customer Not Found',
                'customerUId' => $customerUId,
                'business' => $business->name,
            ]);
            return $this->errorNotFound("Customer $customerUId Not Found");
        }

        try {
            if ($request->resource === 'tier_level' && $request->type === 'recalculate') {
                $tierProgress = null;

                //Check if business has the tier setup enabled
                $tierSetup = BusinessTierSetup::where('business_fk', $business->business_pk)
                    ->where('enabled', 1)
                    ->first();

                if (!empty($tierSetup) && $tierSetup->base_previous_period == 0) {
                    try {
                        $tierLevelService = new UserTierLevelService($customer, $business);
                        $tierProgress = $tierLevelService->updateTierProgress();
                    } catch (\LogicException $e) {
                        \Log::warning($e, ['businessId' => $business->business_pk, 'userId' => $customer->id]);
                    }
                }

                return $this->respondWithArray(['data' => [
                    'tier_progress' => $tierProgress
                ]]);
            } elseif ($request->resource === 'reward' && $request->type === 'calculate') {
                $data = new RewardCalculatorData($request->all());
                $rewardCalculator = new RewardCalculator($customer, $business, $employee, $data);

                return $this->respondWithArray(['data' => $rewardCalculator->getResponse()]);
            } elseif ($request->resource === 'reward' && $request->type === 'batch_calculate') {
                $items = $request->items;
                $results = [];
                foreach ($items as $item) {
                    $item['resource'] = $request->resource;
                    $item['type'] = $request->type;
                    $item['intent'] = $request->intent;
                    $data = new RewardCalculatorData($item);
                    $rewardCalculator = new RewardCalculator($customer, $business, $employee, $data);
                    $results[] = $rewardCalculator->getResponse();
                }
                return $this->respondWithArray(['data' => $results]);
            } else {
                return $this->errorWrongArgs(
                    'Bad Request',
                    'The request was unacceptable, often due to missing a required parameter. '
                        . 'Hint: resource or type parameter'
                );
            }
        } catch (\LogicException $e) {
            \Log::warning($e, ['businessId' => $business->business_pk, 'userId' => $customer->id]);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (DataTransferObjectError $e) {
            \Log::error($e, ['businessId' => $business->business_pk, 'userId' => $customer->id]);
            return $this->errorWrongArgs(
                'Bad Request',
                'The request was unacceptable, often due to missing a required parameter. '
                // .'Hint: resource or type parameter'
            );
        } catch (\Exception $e) {
            \Log::error($e, ['businessId' => $business->business_pk, 'userId' => $customer->id]);
            return $this->errorWrongArgs();
        }
    }

    /**
     * Prepare conditions for filtering by specific fields
     *
     * @param Request $request
     * @return array
     */
    private function getFilterTransactionsConditions(Request $request): array
    {
        $fields = $request->only([
            'created_at',
            'transaction_type',
            'branch_id',
            'branch_category_id', // . is replaced with _
        ]);

        $conditions = [];
        if (empty($fields)) {
            return $conditions;
        }

        $keys = array_keys($fields);
        $values = array_values($fields);

        // prepend table name to each key
        $keys = array_map(function ($key) {
            return 'transactions.' . $key;
        }, $keys);

        $conditions = ApiHelper::buildQueryConditions(array_combine($keys, $values));

        return $conditions;
    }

    public function anonymization(Request $request, $id)
    {
        try {
            $authUser = Customer::findUid($id, ['id', 'email', 'phone', 'business_fk']);
            if (!$authUser) {
                $this->errorNotFound();
            }
            $business = Business::findByPk($authUser->business_fk);
            //Find all child users
            $endUsers = RelationTrait::getEndUserIds($authUser, $business);

            $forgottenRequest = ForgottenDataRequest::findAndCreate([
                'email' => $authUser->email,
                'phone' => $authUser->phone,
                'user_fk' => $authUser->id,
                'platform_fk' => $request->platform_id ?? config('app.platform_id')
            ]);

            foreach ($endUsers as $userId) {
                if ($userId == $authUser->id) {
                    continue;
                }

                $user = User::find($userId);
                ForgottenDataRequest::findAndCreate([
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'user_fk' => $user->id,
                    'platform_fk' => $request->platform_id ?? config('app.platform_id'),
                    'original_forgotten_request_id' => $forgottenRequest->id
                ]);
                //cron job will be scheduled during the night to process the rest users.
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, [
                'user_id' => $authUser->id ?? null,
            ]);
            return $this->errorInternalError();
        }

        return $this->requestAccepted();
    }

    /**
     * Used internally with Client Grant Credentials
     *
     * @param Request $request
     * @param mixed $id
     * @return JsonResponse
     * @throws NumberParseException
     */
    public function sendMessages(Request $request, mixed $id): JsonResponse
    {
        $this->validate($request, [
            'type' => 'required|in:opt_in,account_lock',
            'business_id' => 'required|exists:tenant.business,business_pk',
            'lockout_until' => 'required_if:type,account_lock|date_format:Y-m-d H:i:s',
        ]);

        if ($request->type === 'opt_in') {
            // The user might not exist if created in a DB transaction, wait 30 sec
            dispatch(new SendDoubleOptInMessage(['user_id' => $id, 'business_id' => $request->business_id]))
                ->delay(30);
        }

        if ($request->type === 'account_lock') {
            $business = Business::findByPk($request->business_id);
            if (is_numeric($id)) {
                $user = \App\User::findOrFail((int) $id);
            } else {
                $user = \App\User::where('user_uid', hex2bin($id))->firstOrFail();
            }
            $service = new TransactionalMessageService($business, $user);
            $service->sendAccountLockNotification($request->lockout_until);
        }

        return $this->requestAccepted();
    }

    /**
     * List Customer's available offers: targeted and convertible
     *
     * GET /customers/{id}/targeted-offers
     *
     * @param Request $request
     * @param $customerUId
     * @return JsonResponse|mixed
     */
    public function targetedOffers(Request $request, $customerUId)
    {
        $this->validate($request, [
            'branch_id' => 'sometimes|string',
            'type_id' => 'sometimes|string',
            'category' => 'sometimes|string',
            'per_page' => 'sometimes|integer|min:1|max:' . config('app.pagination_limit'),
        ]);

        try {
            $business = $request->appBusiness();
            $user = Customer::findByUIdForResponse($customerUId, Customer::PROFILE_FIELDS);

            $conditions = ApiHelper::buildQueryConditions(
                $request->only(['type_id', 'category'])
            );

            $offerSearcher = new OfferSearcher($request, $user, $business);
            $offerSearcher->setFilterBy([1, 2, 3, 8, 9, 10, 11]);
            $offerSearcher->setFilterByUserOfferStatus([1]);
            $offerSearcher->setFilterByQueryConditions($conditions);
            return $offerSearcher->search((int)$request->per_page)->getCollection()->response();
        } catch (\Exception $e) {
            Log::error($e);
            return $this->errorInternalError();
        }
    }
}
