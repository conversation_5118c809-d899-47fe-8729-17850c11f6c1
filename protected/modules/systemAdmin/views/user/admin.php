<?php
/* @var $this UserController */
/* @var $model User */

$this->breadcrumbs=array(
	'Users'=>array('index'),
	'Manage',
);

$this->menu=array(
	array('label'=>'List User', 'url'=>array('index')),
	//array('label'=>'Create User', 'url'=>array('create')),
);

Yii::app()->clientScript->registerScript('search', "
$('.search-button').click(function(){
	$('.search-form').toggle();
	return false;
});
$('.search-form form').submit(function(){
	$('#user-grid').yiiGridView('update', {
		data: $(this).serialize()
	});
	return false;
});
");
?>


<h1>Manage Users</h1>

<p>
You may optionally enter a comparison operator (<b>&lt;</b>, <b>&lt;=</b>, <b>&gt;</b>, <b>&gt;=</b>, <b>&lt;&gt;</b>
or <b>=</b>) at the beginning of each of your search values to specify how the comparison should be done.
</p>

<?php echo CHtml::link('Advanced Search','#',array('class'=>'search-button')); ?>
<div class="search-form" style="display:none">
<?php $this->renderPartial('_search',array(
	'model'=>$model,
)); ?>
</div><!-- search-form -->

<div class="table-responsive">
<?php $this->widget('zii.widgets.grid.CGridView', array(
	'id'=>'user-grid',
	'dataProvider'=>$model->searchSystem(),
	'filter'=>$model,
	'columns'=>array(
		'id',
		'username',
		'email',
		'phone',
		array(
			'header' => 'Phone Type',
			'type' => 'raw',
            'filter'=>CHtml::dropDownList('User[phone_number_type_fk]', $model->phone_number_type_fk, [''=>'All', 1=>'Mobile', 2=>'Landline', 3=>'Voip', 4=>'Invalid', ], array('options'=>array('$data->phone_number_type_fk'=>'selected'))),
			'value' => function($data) {
				if ($data['phone_number_type_fk'] == PHONE_NUMBER_MOBILE) {
					return 'Mobile';
				} elseif ($data['phone_number_type_fk'] == PHONE_NUMBER_LANDLINE) {
					return 'Landline';
				} elseif ($data['phone_number_type_fk'] == PHONE_NUMBER_VOIP) {
					return 'Voip';
				} elseif ($data['phone_number_type_fk'] == PHONE_NUMBER_INVALID) {
					return 'Invalid';
				}
			},
		),
        'account_number',
		'first',
		'last',
		'enabled',
		'utc_created',
		'ip',
		array(
			'name' => 'platform_solution_fk',
			'value' => function ($data) {
				$platform = PlatformSolution::getNameById($data->platform_solution_fk);
				return $data->platform_solution_fk . '-' . $platform;
			},
		),
		'signedupat_fk',
		array(
			'header'=>'Signed Up At',
			'name'=>'signetdUpAt.name',
			'filter'=>CHtml::activeTextField($model,'business_name'),
		),
		'business_fk',
		array(
			'header'=>'Points Balance',
			'type'=>'raw',
			'value'=>function($data) {
				$balance = UserEntityPoints::getCurrentBalance($data->id);
				return number_format( (int) $balance['active_points'] + (int) $balance['punches']);
			},
		),
		array(
			'header'=>'Gift Card Balance',
			'type'=>'raw',
			'value'=>function($data) {
				$balance = UserEntityPoints::getCurrentBalance($data->id);
				return $balance['giftcard'];
			},
		),
		/*

		'phone',
		'enabled',
		'created',
		'modified',
		'gender',
		'birth_date',
		'twitter_id',
		'twitter_secret',
		'facebook_id',
		'facebook_token',
		'twitter_token',
		'tw_publish',
		'fb_publish',
		'lastLoginTime',
		'ip',
		'image',
		'language',
		'address',
		'city_fk',
		'region_fk',
		'country_fk',
		'pin_permission',
		'pin_enabled',
		'merchant_reset_psw',
		'sms_activation_code',
		'app_launched',
		'device_token',
		'hidden',
		'platform_solution_fk',
		'signedupat_fk',
		'display_facebook_image',
		'login_type_fk',
		'link_expires',
		'accounts_merge',
		'basic_profile_reminder',
		'activation_email_sent',
		'pin_email_sent',
		*/
		array(
			'class'=>'CButtonColumn',
			'afterDelete'=>'function(link,success,data){ if(success) $("#statusMsg").show().html(data).delay(3000).fadeOut(); }',
		),
	),
)); ?>
</div>