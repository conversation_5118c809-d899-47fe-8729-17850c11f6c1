<?php

namespace KangarooRewards\Geofencing\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use KangarooRewards\Geofencing\Models\BusinessGeofence;
use KangarooRewards\Geofencing\Repositories\BusinessGeofenceRepository;

class BusinessGeofenceService
{
    public function __construct(protected BusinessGeofenceRepository $repository) {}

    /**
     * @param array $data
     * @param $businessId
     * @return BusinessGeofence
     */
    public function create(array $data, $businessId): BusinessGeofence
    {
        $data ['business_fk'] = $businessId;
        $geofence = $this->repository->create($data);
        return $geofence->fresh();
    }

    /**
     * @param BusinessGeofence $geofence
     * @param array $data
     * @return BusinessGeofence
     */
    public function update(BusinessGeofence $geofence, array $data): BusinessGeofence
    {
        $geofence->fill($data);
        $geofence->save();

        return $geofence->fresh();
    }

    /**
     * Enable / disable geofence
     * @param BusinessGeofence $geofence
     * @return void
     */
    public function toggleActiveStatus(BusinessGeofence $geofence): void
    {
        $geofence->active = !$geofence->active;
        $geofence->save();
    }

    /**
     * @param array $filters
     * @return array|Builder[]|Collection
     */
    public function list(array $filters = []): Collection|array
    {
        return $this->repository->all($filters);
    }

    /**
     * @param int $businessId
     * @param int $perPage
     * @param int $page
     * @return LengthAwarePaginator
     */
    public function listPaginated(int $businessId, int $perPage = 15, int $page = 1): LengthAwarePaginator
    {
        return BusinessGeofence::where('business_fk', $businessId)
            ->where('active', true)
            ->orderByDesc('id')
            ->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * Used in ListBusinessGeofenceRequest
     * @param $id
     * @param $businessId
     * @return mixed
     */
    public function findForBusiness($id, $businessId): mixed
    {
        return BusinessGeofence::where([
            'id' => $id,
            'business_fk' => $businessId,
        ])->first();
    }
}