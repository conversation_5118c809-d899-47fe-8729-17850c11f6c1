<?php

declare(strict_types=1);

namespace KangarooRewards\Workflows\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use KangarooRewards\Workflows\Models\Workflow;
use KangarooRewards\Workflows\Models\WorkflowAction;
use KangarooRewards\Workflows\Models\WorkflowActionExecution;
use KangarooRewards\Workflows\Models\WorkflowEvent;

/**
 * Workflow Execution Started
 */
class WorkflowActivityStarted
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public string $uuid;
    private WorkflowAction $workflowAction;
    private WorkflowActionExecution $activity;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(WorkflowAction $workflowAction, WorkflowActionExecution $activity)
    {
        $this->workflowAction = $workflowAction;
        $this->activity = $activity;
        $this->uuid = Str::uuid()->toString();
        // Log::info(__METHOD__, ['uuid' => $this->uuid, 'workflow_id' => $this->workflow->id]);
    }

    public function getWorkflowAction(): WorkflowAction
    {
        return $this->workflowAction;
    }

    public function getWorkflowActionExecution(): WorkflowActionExecution
    {
        return $this->activity;
    }

    public function getUuid(): string
    {
        return $this->uuid;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    /*public function broadcastOn()
    {
        return new PrivateChannel('user-registered');
    }*/
}
