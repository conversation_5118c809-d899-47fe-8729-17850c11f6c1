<?php

namespace KangarooRewards\Common\Components;

class NotificationContentBuilder
{
    private string $type;
    private mixed $templateId;

    public function __construct(string $type, mixed $templateId)
    {
        $this->type = $type;
        $this->templateId = $templateId;
    }

    public function buildContentFromTemplate(): string
    {
        return '';
    }

    public function getSubject(): string
    {
        return '';
    }
}
