<?php

namespace App\Traits;

use App\Models\V1\ProductReward;

trait ProductRewardTrait
{
    /**
     * Check and get product reward details for business
     * @param $productId
     * @param $branchIds
     * @param array $fields
     * @return mixed
     */
    public function getProductRewardForBusiness($productId, $branchIds, array $fields = [])
    {
        if (empty($fields)) {
            $fields = ['pr.product_reward_pk', 'pr.point_value', 'p.product_name'];
        }

        return ProductReward::from('product_reward as pr')
            ->select($fields)
            ->join('products as p', 'pr.product_fk', '=', 'p.product_pk')
            ->join('product_branches as pb', 'pb.product_fk', '=', 'p.product_pk')
            ->where('p.enabled', 1)
            ->where('pr.enabled', 1)
            ->where('pr.product_fk', $productId)
            ->whereIn('pb.business_branch_fk', $branchIds)
            ->get();
    }

    public function createProductReward($data, $business)
    {
        $attributes = $this->normalizeProductRewardAttributes($data, $business);
        $productReward = ProductReward::create($attributes);

        return $productReward;
    }

    public function updateProductReward($data, $business, $productReward)
    {
        $attributes = $this->normalizeProductRewardAttributes($data, $business, $productReward);
        $productReward->update($attributes);
        return $productReward;
    }

    private function normalizeProductRewardAttributes($data, $business, $productReward = null)
    {
        return [
            'product_fk' => isset($data['product_id']) ? (int) $data['product_id'] : $productReward->product_fk,
            'point_value' => isset($data['points']) ? (int) $data['points'] : $productReward->point_value,
            'published' => isset($data['published']) ? (int) $data['published'] : $productReward->published,
            'enabled' => 1,
            'timezone_mysql_fk' => $business->branch_timezone_fk,
        ];
    }

    public function deleteProductReward(ProductReward $productReward)
    {
        $productReward->enabled = 0;
        $productReward->save();
    }
}
