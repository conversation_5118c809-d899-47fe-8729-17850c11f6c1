<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\GptChat;
use App\Models\GptChatCompletion;
use Illuminate\Support\Facades\Log;
use Exception;
use JsonException;
use RuntimeException;

/**
 * Handles AI services integrated with Cerebras API.
 */
class CerebrasAIService implements GenAIServiceInterface
{
    public function __construct()
    {
        //
    }

    /**
     * https://inference.cerebras.ai/
     * @todo Not production ready - To enable billing, get API key
     * @param GptChat $gptChat
     * @param array $validated
     * @param int $maxTokens
     * @return GptChatCompletion|null
     * @throws JsonException
     */
    public function chatCompletions(GptChat $gptChat, array $validated, int $maxTokens = 4096): ?GptChatCompletion
    {
        $messages = [["role" => "user", "content" => $validated['prompt']]];
        if (isset($validated['context']) && trim($validated['context'])) {
            array_unshift($messages, ["role" => "system", "content" => $validated['context']]);
        }

        try {
            $headers = [
                'authorization: Bearer ' . config('app.CEREBRAS_API_KEY'),
                'content-type: application/json'
            ];

            $data = [
                'model' => $this->getAIModel($validated),
                'stream' => false,
                'max_tokens' => $maxTokens,
                'temperature' => $validated['temperature'] ?? 0.3,
                'top_p' => 1,
                'messages' => $messages,
            ];

            $ch = curl_init('https://api.cerebras.ai/v1/chat/completions');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            curl_close($ch);

            if ($httpCode !== 200) {
                throw new RuntimeException("API request failed with HTTP code: $httpCode");
            }
            $body = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

            $reply = (string) $body['choices'][0]['message']['content'];

            $message['gpt_chat_id'] = $gptChat->id;
            $message['prompt'] = filter_var($validated['prompt'], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            $message['context'] = filter_var($validated['context'] ?? '', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            $message['message'] = $reply;
            $message['usage_prompt_tokens'] = $body['usage']['prompt_tokens'];
            $message['usage_completion_tokens'] = $body['usage']['completion_tokens'];
            $message['usage_total_tokens'] = $body['usage']['total_tokens'];
            $message['message_info'] = [
                'id' => $body['id'],
                'object' => $body['object'],
                'model' => $body['model'],
                'provider' => 'cerebras',
            ];

            return GptChatCompletion::create($message);
        } catch (Exception $e) {
            Log::error($e, ['businessId' => $gptChat->business->business_pk] + $validated);
        }

        throw new RuntimeException('The server encountered an unexpected condition which prevented it from fulfilling the request');
    }

    /**
     * @throws RuntimeException
     */
    public function imageGeneration(GptChat $gptChat, array $validated): ?array
    {
        throw new RuntimeException('Not implemented');
    }

    private function getAIModel(array $validated): string
    {
        $allowedModels = ['llama-4-scout-17b-16e-instruct','llama-3.3-70b', 'qwen-3-32b'];

        if (isset($validated['model']) && in_array(trim($validated['model']), $allowedModels, true)) {
            return trim($validated['model']);
        }

        // default model
        return 'llama-4-scout-17b-16e-instruct';
    }
}
