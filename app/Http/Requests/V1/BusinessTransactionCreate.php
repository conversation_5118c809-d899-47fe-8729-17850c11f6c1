<?php

namespace App\Http\Requests\V1;

use App\Helpers\ApiHelper;
use App\Helpers\V1\Utils;
use App\Models\UserPrizeWin;
use App\Models\V1\Business;
use App\Models\V1\BusinessBranch;
use App\Models\V1\BusinessRules;
use App\Models\V1\Customer;
use App\Models\V1\Employee;
use App\Models\V1\Offer;
use App\Models\V1\PosBranch;
use App\Models\V1\UserOffer;
use App\Models\V1\UserOfferStory;
use App\Services\EncryptorService;
use App\Traits\IntegrationMenuTrait;
use App\Traits\PosBranchTrait;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\V1\User;

class BusinessTransactionCreate extends FormRequest
{
    use PosBranchTrait;

    public $qrSale = [];

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     * @throws AuthorizationException
     */
    public function authorize(): bool
    {
        if ($this->intent === 'get_coupon') {
            return $this->authorizeGetCoupon();
        }

        if ($this->intent === 'claim_coupon' || $this->intent === 'claim_ecom_coupon') {
            return $this->authorizeClaimCoupon();
        }

        if ($this->intent === 'reward_store_credit') {
            return $this->authorizeRewardStoreCredit();
        }

        if ($this->intent === 'spin_to_win') {
            return $this->authorizeClaimSpinToWin();
        }

        if (isset($this->employee['id'])) {
            $this->authorizeEmployee();
        }

        return true;
    }

    private $business;

    public function getBusiness()
    {
        if (!$this->business) {
            if (!$this->user()) {
                $jwtToken = $this->header('X-Application-Key');
                $appKey = ApiHelper::decodeAppKey($jwtToken);

                $this->business = Business::findByPk($appKey->businessId);
            } else {
                $this->business = $this->appBusiness();
            }
        }
        return $this->business;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $commonRules = [
            'intent' => 'required|in:reward,redeem,return,get_coupon,claim_coupon,purchase_giftcard_prepaid,'
                . 'giftcard_payment,reward_store_credit,return_store_credit,adjust_points_balance,reward_referral,'
                . 'refund_redeemed_points,qr_code,redeem_ecom_coupon,claim_ecom_coupon,cancel_ecom_coupon,spin_to_win',
            'employee' => 'required_without_all:customer|array',
            'employee.id' => 'required_without_all:customer.id,customer.email,customer.phone|exists:tenant.user,user_uid',
            'employee.pin_code' => 'sometimes|required|digits:4',
            'receipt' => 'sometimes|required|array',
            'receipt.number' => 'sometimes|required|string|min:1|max:50|alpha_num',
            'branch' => 'sometimes|required|array',
            'branch.id' => 'sometimes|required|string|exists:tenant.business_branch,branch_uid',
            'comments' => 'sometimes|required|string|min:3|max:255',
            'created_at' => 'sometimes|nullable|string|max:60|date|date_format:' . \DateTime::ATOM . '|before_or_equal:now',
            'transaction_details' => 'sometimes|required|array',
            'transaction_details.external_order_id' => 'sometimes|string|min:1|max:100',
            'transaction_details.custom_field_1' => 'sometimes|string|min:1|max:255',
            'transaction_details.custom_field_2' => 'sometimes|string|min:1|max:255',
            'transaction_details.custom_field_3' => 'sometimes|string|min:1|max:255',
            'transaction_details.custom_field_4' => 'sometimes|string|min:1|max:255',
            'transaction_details.custom_field_5' => 'sometimes|string|min:1|max:255',
        ];

        $rewardRules = $this->getRewardRules();
        $redeemRules = $this->getRedeemRules();
        $returnRules = $this->getReturnRules();
        $getCouponRules = $this->getAssignCouponRules();
        $claimCouponRules = $this->getClaimCouponRules();
        $purchaseGiftCardPrepaidRules = $this->getPurchaseGiftCardPrepaidRules();
        $giftCardPaymentRules = $this->getGiftCardPaymentRules();
        $storeCreditRules = $this->getStoreCreditRules();
        $returnStoreCreditRules = $this->getReturnStoreCreditRules();
        $adjustBalanceRules = $this->getAdjustBalanceRules();
        $rewardReferralRules = $this->getRewardReferralRules();
        $refundRedeemedPtsRules = $this->getRefundRedeemedPtsRules();
        $qrCodeRules = $this->getQrCodeRules();
        $redeemEcomCouponRules = $this->getRedeemEcomCouponRules();
        $claimEcomCouponRules = $this->getClaimEcomCouponRules();
        $cancelEcomCouponRules = $this->getCancelEcomCouponRules();
        $claimSpinToWinRules = $this->getClaimSpinToWinPrizeRules();

        $businessRules = BusinessRules::forBusinessWithTaxes($this->getBusiness()->business_pk);

        $customerPinCode = [
            'customer.pin_code' => 'required|digits:4',
        ];

        if ($this->intent === 'reward') {
            if (isset($this->amount)
                && $businessRules->pinclerk_flag === 1
                && $this->amount >= $businessRules->pinclerk_min_amount) {
                $rules = array_merge($rewardRules, $customerPinCode);
            } else {
                $rules = array_merge($rewardRules, $commonRules);
            }
        } elseif ($this->intent === 'redeem') {
            $rules = array_merge($redeemRules, $commonRules);
        } elseif ($this->intent === 'return') {
            $rules = array_merge($returnRules, $commonRules);
        } elseif ($this->intent === 'get_coupon') {
            $rules = array_merge($getCouponRules, $commonRules);
        } elseif ($this->intent === 'claim_coupon') {
            $rules = array_merge($claimCouponRules, $commonRules);
        } elseif ($this->intent === 'purchase_giftcard_prepaid') {
            $rules = array_merge($purchaseGiftCardPrepaidRules, $commonRules);
        } elseif ($this->intent === 'giftcard_payment') {
            $rules = array_merge($giftCardPaymentRules, $commonRules);
        } elseif ($this->intent === 'reward_store_credit') {
            $rules = array_merge($storeCreditRules, $commonRules);
        } elseif ($this->intent === 'return_store_credit') {
            $rules = array_merge($returnStoreCreditRules, $commonRules);
        } elseif ($this->intent === 'adjust_points_balance') {
            $rules = array_merge($adjustBalanceRules, $commonRules);
        } elseif ($this->intent === 'reward_referral') {
            $rules = array_merge($rewardReferralRules, $commonRules);
        } elseif ($this->intent === 'refund_redeemed_points') {
            $rules = array_merge($refundRedeemedPtsRules, $commonRules);
        } elseif ($this->intent === 'qr_code') {
            $rules = array_merge($qrCodeRules, $commonRules);
        } elseif ($this->intent === 'redeem_ecom_coupon') {
            $rules = array_merge($commonRules, $redeemEcomCouponRules);
        } elseif ($this->intent === 'claim_ecom_coupon') {
            $rules = array_merge($commonRules, $claimEcomCouponRules);
        } elseif ($this->intent === 'cancel_ecom_coupon') {
            $rules = array_merge($commonRules, $cancelEcomCouponRules);
        } elseif ($this->intent === 'spin_to_win') {
            $rules = array_merge($commonRules, $claimSpinToWinRules);
        } else {
            $rules = $commonRules;
        }

        if ($this->intent === 'qr_code' && $this->is_user_qr_code) {
            $this->excludeUserValidationRules($rules);
        }

        return $rules;
    }

    public function prepareForValidation()
    {
        try {
            if (isset($this->customer['id'])) {
                $this->merge([
                    'customer' => array_merge($this->customer, [
                        'id' => hex2bin($this->customer['id']),
                    ]),
                ]);
            }

            if (isset($this->employee['id'])) {
                $this->merge([
                    'employee' => array_merge($this->employee, [
                        'id' => hex2bin($this->employee['id']),
                    ]),
                ]);
            }
            if (!empty($this->branch['id'])) {
                $this->merge([
                    'branch' => array_merge($this->branch, [
                        'id' => hex2bin($this->branch['id']),
                    ]),
                ]);
            }
        } catch (\Exception $e) {
        }
    }

    private function getRewardReferralRules(): array
    {
        return [
            'amount' => 'required|numeric|min:0',
            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone|email',
            'customer.phone' => 'required_without_all:customer.id,customer.email',
            'customer.country_code' => 'required_with:customer.phone|size:2|exists:tenant.countries,code',
            'product_rewards' => 'sometimes|required|array',
            'product_rewards.*.id' => 'required_with:product_rewards|integer|exists:tenant.product_reward,product_reward_pk',
            'product_rewards.*.quantity' => 'required_with:product_rewards|integer|min:1',
        ];
    }

    private function getQrCodeRules(): array
    {
        return [
            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone|email',
            'customer.phone' => 'required_without_all:customer.id,customer.email',
            'customer.country_code' => 'required_with:customer.phone|size:2|exists:tenant.countries,code',
            'qr_code_info' => 'required|string',// it contains the encrypted message
            'is_user_qr_code' => 'sometimes|bool'
        ];
    }

    private function getRefundRedeemedPtsRules(): array
    {
        return [
            'points' => 'required|integer|min:1',
            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone|email',
            'customer.phone' => 'required_without_all:customer.id,customer.email',
            'customer.country_code' => 'required_with:customer.phone|size:2|exists:tenant.countries,code',
        ];
    }

    private function getRewardRules(): array
    {
        return [
            'amount' => [
                'required_without_all:points,visits',
                'numeric',
                function ($attribute, $value, $fail) {
                    if (request()->has('product_rewards')) {
                        if ($value != 0) {
                            $fail('The '.$attribute.' must be 0 when product rewards are attached.');
                        }
                    } else {
                        if ($value < 1) {
                            $fail('The '.$attribute.' must be at least 1 when no product rewards are attached.');
                        }
                    }
                },
            ],
            'points' => 'required_without_all:amount,visits|integer|min:1',
            'visits' => 'required_without_all:amount,points|integer|min:1',
            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone|email',
            'customer.phone' => 'required_without_all:customer.id,customer.email',
            'customer.country_code' => 'required_with:customer.phone|size:2|exists:tenant.countries,code',
            'product_rewards' => 'sometimes|required|array',
            'product_rewards.*.id' => 'required_with:product_rewards|integer|exists:tenant.product_reward,product_reward_pk',
            'product_rewards.*.quantity' => 'required_with:product_rewards|integer|min:1',
            'qr_shop_id' => 'sometimes|required|string',
        ];
    }

    private function getRedeemRules(): array
    {
        return [
            'amount' => 'required_without_all:points,visits,catalog_items|numeric|min:1',
            'points' => 'required_without_all:amount,visits,catalog_items|integer|min:1',
            'visits' => 'required_without_all:amount,points,catalog_items|integer|min:1',
            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone',
            'customer.phone' => 'required_without_all:customer.id,customer.email',
            'customer.pin_code' => 'sometimes|required|digits:4',
            'catalog_items' => 'required_without_all:amount,points,visits|array|size:1',// Only one catalog item at once
            'catalog_items.*.id' => 'required_with:catalog_items|integer|exists:tenant.punch_item,punch_item_pk',
            'catalog_items.*.quantity' => 'sometimes|required_with:catalog_items|integer|min:1|max:1',
        ];
    }

    private function getReturnRules(): array
    {
        return [
            'amount' => 'required_without_all:points|numeric|min:1',
            'points' => 'required_without_all:amount|integer|min:1',
            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone',
            'customer.phone' => 'required_without_all:customer.id,customer.email',
            'customer.pin_code' => 'sometimes|required|digits:4',
        ];
    }

    private function getAssignCouponRules(): array
    {
        return [
            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone',
            'customer.phone' => 'required_without_all:customer.id,customer.email',
            'customer.pin_code' => 'sometimes|required|digits:4',
            'offer' => 'required|array',
            'offer.id' => 'required_with:offer|integer|exists:tenant.offer,offer_pk',
        ];
    }

    private function getClaimCouponRules(): array
    {
        return array_merge($this->getAssignCouponRules(), [
            'coupon' => 'required|array',
            'coupon.id' => 'required_with:offer|integer|exists:tenant.user_offer_story,user_offer_story_pk',
            'offer' => 'required|array',
            'offer.id' => 'required_with:offer|integer|exists:tenant.offer,offer_pk',
        ]);
    }

    private function getPurchaseGiftCardPrepaidRules(): array
    {
        return [
            'amount' => 'required|numeric|min:0.01',
            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone|email',
            'customer.phone' => 'required_without_all:customer.id,customer.email|numeric',
            'customer.country_code' => 'required_with:customer.phone|size:2|exists:tenant.countries,code',
        ];
    }

    private function getGiftCardPaymentRules(): array
    {
        return [
            'amount' => 'required|numeric|min:0.01',
            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone|email',
            'customer.phone' => 'required_without_all:customer.id,customer.email|numeric',
            'customer.country_code' => 'required_with:customer.phone|size:2|exists:tenant.countries,code',
        ];
    }

    private function getReturnStoreCreditRules(): array
    {
        return [
            'amount' => 'required|numeric|min:0.01',
            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone|email',
            'customer.phone' => 'required_without_all:customer.id,customer.email|numeric',
            'customer.country_code' => 'required_with:customer.phone|size:2|exists:tenant.countries,code',
        ];
    }

    private function getStoreCreditRules(): array
    {
        return [
            'amount' => 'required|numeric|min:1|max:999999999',
            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone|email',
            'customer.phone' => 'required_without_all:customer.id,customer.email|numeric',
            'customer.country_code' => 'required_with:customer.phone|size:2|exists:tenant.countries,code',
        ];
    }

    private function getAdjustBalanceRules(): array
    {
        return [
            'points' => 'required|integer',
            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone|email',
            'customer.phone' => 'required_without_all:customer.id,customer.email|numeric',
            'customer.country_code' => 'required_with:customer.phone|size:2|exists:tenant.countries,code',
        ];
    }

    /**
     * Authorize get coupon. User cannot get the coupon again if already assigned
     *
     * @return bool
     * @throws AuthorizationException
     */
    private function authorizeGetCoupon(): bool
    {
        if (isset($this->customer['id'])) {
            $user = Customer::findByUIdForResponse($this->customer['id']);
        } else {
            throw new AuthorizationException('Customer not found');
        }

        // Check if the offer type belongs to the IDs (8,9,10)
        $offer = Offer::findOrFail($this->offer['id']);
        if (!in_array($offer->offer_type_fk, [8, 9, 10], true)) {
            throw new AuthorizationException(
                'This type of offer is not a coupon. Only offer type: 8,9,10 can be tagged'
            );
        }

        // Offer belongs to business
        if ($offer->business_fk != $this->getBusiness()->business_pk) {
            return false;
        }

        // Check if the offer is in pending state to claim/redeem and if the coupon_perm_available value to let the customer issue another coupon
        $userOffer = UserOffer::where('user_fk', $user->id)
            ->where('offer_fk', $this->offer['id'])
            ->with([
                'userOfferStories' => function ($query) {
                    $query->with('transaction')->orderByDesc('user_offer_story_pk')->take(1);
                }
            ])
            ->first();

        if ($userOffer) {
            $userOfferStory = $userOffer->userOfferStories->first();
            $couponTrxSubTypeId = $userOfferStory->transaction->transaction_sub_type_fk ?? null;

            if ($couponTrxSubTypeId == TRX_SUBTYPE_COUPON_GET_COUPON) {
                throw new AuthorizationException('This coupon has already been assigned.');
            } elseif ($couponTrxSubTypeId == TRX_SUBTYPE_COUPON_CLAIMED && $offer->coupon_perm_available == 0) {
                throw new AuthorizationException('This coupon can be used only once.');
            }
        }

        // Check if the convertible coupon option is enabled
        if ($offer->coupon_convertible === 1) {
            throw new AuthorizationException('This offer is for convertible coupon.');
        }

        return true;
    }

    /**
     * Authorize claim coupon. User cannot get the coupon again if not assigned
     *
     * @return bool
     * @throws AuthorizationException
     */
    private function authorizeClaimCoupon(): bool
    {
        if (!isset($this->customer['id'])) {
            return false;
        }

        if(!isset($this->offer['id'])){
            return true;
        }

        $offer = Offer::findOrFail($this->offer['id']);

        $user = Customer::findByUIdForResponse($this->customer['id']);

        $userOfferStory = UserOfferStory::with('userOffer', 'transaction')
            ->findOrFail($this->coupon['id']);

        // Coupon belongs to user attached to the transaction
        if ($userOfferStory->userOffer->user_fk != $user->id) {
            throw new AuthorizationException('This user is unauthorized to redeem this coupon.');
        }

        // Offer belongs to coupon
        if ($userOfferStory->userOffer->offer_fk != $this->offer['id']) {
            throw new AuthorizationException('This coupon has a different offer');
        }

        if ($userOfferStory->coupon_redeemed == 1) {
            throw new AuthorizationException('This coupon has already been redeemed.');
        }

        $userOffer = UserOffer::with('userOfferStories.transaction')
            ->where('user_fk', $user->id)
            ->where('offer_fk', $offer->offer_pk)
            ->whereHas('userOfferStories.transaction', function ($query) {
                $query->whereIn('transaction_sub_type_fk', [74, 75, 91, 92]);
            })
            ->first();

        if (!$userOffer) {
            throw new AuthorizationException('The coupon must be claimed before redeeming.');
        }

        if (!in_array($userOfferStory->transaction->transaction_sub_type_fk, [74, 91], true)) {
            throw new AuthorizationException('The coupon must be claimed before redeeming.');
        }

//        $claimed = 0;
//        $redeemed = 0;
//        foreach ($userOffer->userOfferStories as $userOfferStory) {
//            if (in_array($userOfferStory->transaction->transaction_sub_type_fk, [74, 91], true)) {
//                $claimed++;
//            } elseif (in_array($userOfferStory->transaction->transaction_sub_type_fk, [75, 92], true)) {
//                $redeemed++;
//            }
//        }
//
//        if ($claimed === 0) {
//            throw new AuthorizationException('The coupon must be claimed before redeeming.');
//        }
//
//        if ($redeemed > 0) {
//            throw new AuthorizationException('This coupon has already been redeemed.');
//        }

        // User Has at least one transaction type 74 for offer
//        $userOffer = UserOffer::where('user_fk', $user->id)
//            ->where('offer_fk', $this->offer['id'])
//            ->whereHas('userOfferStories.transaction', function ($query) {
//                $query->where('transaction_sub_type_fk', 74);
//            })
//            ->first();


        // Offer belongs to business
        if ($offer->business_fk != $this->getBusiness()->business_pk) {
            return false;
        }

        return true;
    }

    /**
     * @return bool
     * @throws AuthorizationException
     */
    private function authorizeRewardStoreCredit(): bool
    {
        $businessRules = BusinessRules::forBusinessWithTaxes($this->getBusiness()->business_pk);
        if ($businessRules->reward_store_credit_flag === 0) {
            throw new AuthorizationException('Reward Store Credit not allowed');
        }
        return true;
    }

    /**
     * @return bool
     * @throws AuthorizationException
     */
    private function authorizeClaimSpinToWin(): bool
    {
        if (!isset($this->customer['id']) || !isset($this->user_prize_win['id'])) {
            return false;
        }

        $userPrizeWin = UserPrizeWin::where('user_prize_win_pk', $this->user_prize_win['id'])->first();

        $user = Customer::findByUIdForResponse($this->customer['id']);

        if ($user->id != $userPrizeWin->user_fk) {
            throw new AuthorizationException('This user is unauthorized to redeem this prize.');
        }

        return true;
    }

    /**
     * @return bool
     * @throws AuthorizationException
     */
    private function authorizeEmployee(): bool
    {
        $this->getBusiness()->business_pk;

        $employeeIds = Employee::getEmployeesForPagination($this->getBusiness()->business_pk, true)
            ->pluck('id')->toArray();

        $employee = Employee::findUid($this->employee['id'], ['id']);

        if (!in_array($employee->id, $employeeIds)) {
            throw new AuthorizationException('This employee is not authorized to perform this action.');
        }
        return true;
    }

    /**
     * Configure the validator instance.
     *
     * @param Validator $validator
     */
    public function withValidator(Validator $validator)
    {
        $validator->after(function ($validator) {
            if ($this->intent === 'qr_code') {
                $this->validateQrCode($validator);
            }
            if ($this->intent === 'redeem_ecom_coupon' || $this->intent === 'claim_ecom_coupon' || $this->intent === 'cancel_ecom_coupon') {
                $this->validatePosBranch($validator);
            }

            // Validate QR Shop ID
            if (isset($this->qr_shop_id)) {
                $this->validateQrShopId($validator);
            }
        });
    }

    /**
     * Validate QR Shop ID for Reward intent
     * @param $validator
     * @return void
     */
    private function validateQrShopId($validator): void
    {
        $appBusiness = $this->getBusiness();

        // Check the transaction intent
        if ($this->intent !== 'reward') {
            $validator->errors()->add('qr_shop_id', 'The QR shop ID is applicable only for reward transactions.');
            return;
        }

        // Check if the qr shop ID and branch ID exist together
        if (isset($this->branch['id']) && isset($this->qr_shop_id)) {
            $validator->errors()->add(
                'qr_shop_id',
                'The QR shop ID cannot be provided if the branch ID is present; only one of them should be passed.'
            );
            return;
        }

        // Check if the shop ID exists for the business
        $posBranch = $this->getBusinessBranchIdFromPosShopId(
            $this->qr_shop_id,
            $appBusiness->business_pk,
            POS_SYSTEM_CUSTOM_QR_CODE,
            true
        );
        if (!$posBranch) {
            $validator->errors()->add('qr_shop_id', 'The QR shop id is invalid.');
        }
    }

    /**
     * @param Validator $validator
     */
    private function validateQrCode(Validator $validator)
    {
        $appBusiness = $this->getBusiness();

        $menuEnabled = IntegrationMenuTrait::findActiveForBusinessAndSystem(
            $appBusiness->business_pk,
            POS_SYSTEM_CUSTOM_QR_CODE
        );

        $traceId = EncryptorService::TRACE_ID . 'F-BTC-VALQR-' . $appBusiness->business_pk;

        Log::info(__METHOD__, [
            'traceId' => $traceId,
            'business_pk' => $appBusiness->business_pk,
            'pos_system_fk' => POS_SYSTEM_CUSTOM_QR_CODE,
            'menuEnabled' => (bool)$menuEnabled,
        ]);

        if (!$menuEnabled) {
            $validator->errors()
                ->add('qr_code_info', 'Scanning receipts is not available at the moment. Please try again later.');
            return;
        }

        try {
            $service = new EncryptorService($appBusiness);
            $qrCodeInfo = $service->decrypt($this->qr_code_info); // encrypted info
        } catch (\Exception $e) {
            $validator->errors()->add('qr_code_info', $e->getMessage());
            Log::info(__METHOD__, [
                'traceId' => $traceId,
                'business_pk' => $appBusiness->business_pk,
                'message' => $e->getMessage(),
            ]);
            return;
        }

        // $qrCodeInfo contains normalized data
        $data = [
            'amount' => $qrCodeInfo->amount,
            'timestamp' => $qrCodeInfo->timestamp,
            'location_id' => $qrCodeInfo->locationId,
            'invoice_id' => $qrCodeInfo->invoiceId,
            'sale' => $qrCodeInfo->sale
        ];

        $rules = [
            'amount' => 'required_without:sale|nullable|numeric|min:0.01',
            'timestamp' => 'required|date|after_or_equal:' . $service->getExpiresAt(),
            'location_id' => 'required|string',
            'invoice_id' => 'required_without:sale|string',
        ];

        //Merge qr code sale rules
        if (!empty($data['sale'])) {
            $qrCodeSaleRules = $this->getQrCodeSaleRules();
            $rules = array_merge($rules, $qrCodeSaleRules);
        }

        // Create a new validator and pass customer info from QR code and the rules
        $qrcodeValidator = \Illuminate\Support\Facades\Validator::make($data, $rules, [
            'timestamp.after_or_equal' => 'The QR Code is expired',
        ]);

        // Merge all the errors from customer info validator with the main validator
        if ($qrcodeValidator->fails()) {
            $validator->errors()->merge($qrcodeValidator->errors());

            Log::info(__METHOD__, [
                'traceId' => $traceId,
                'business_pk' => $appBusiness->business_pk,
                'message' => 'QR Code validation failed',
                'errors' => $qrcodeValidator->errors(),
            ]);
        }

        if (!empty($qrCodeInfo->sale)) {
            $this->qrSale = $qrCodeInfo->sale;
        }
    }


    private function getRedeemEcomCouponRules(): array
    {
        return [
            'branch' => 'required|array',
            'branch.id' => 'required|string|exists:tenant.business_branch,branch_uid',

            'amount' => 'required_without:catalog_item|numeric|min:0',
            'subtotal' => 'required_with:amount|numeric|min:0',

            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone',
            'customer.phone' => 'required_without_all:customer.id,customer.email',
            'customer.pin_code' => 'sometimes|required|digits:4',

            'catalog_item' => 'required_without:amount,subtotal|array',// Only one catalog item at once
            'catalog_item.id' => 'required_with:catalog_item|integer|exists:tenant.punch_item,punch_item_pk',
        ];
    }

    private function getClaimEcomCouponRules(): array
    {
        return [
            'branch' => 'required|array',
            'branch.id' => 'required|string|exists:tenant.business_branch,branch_uid',

            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone',
            'customer.phone' => 'required_without_all:customer.id,customer.email',
            'customer.pin_code' => 'sometimes|required|digits:4',

            'offer' => 'required|array',
            'offer.id' => 'required_with:offer|integer|exists:tenant.offer,offer_pk',

            'coupon' => 'required|array',
            'coupon.id' => 'required_with:coupon|integer|exists:tenant.user_offer_story,user_offer_story_pk',
        ];
    }

    private function getCancelEcomCouponRules()
    {
        return [
            'branch' => 'required|array',
            'branch.id' => 'required|string|exists:tenant.business_branch,branch_uid',

            'customer' => 'required|array',
            'customer.id' => 'required_without_all:customer.email,customer.phone|exists:tenant.user,user_uid',
            'customer.email' => 'required_without_all:customer.id,customer.phone',
            'customer.phone' => 'required_without_all:customer.id,customer.email',
            'customer.pin_code' => 'sometimes|required|digits:4',

            'code' => 'required|string|exists:mysql.coupons,code'
        ];
    }

    private function getClaimSpinToWinPrizeRules()
    {
        $appBusiness = $this->getBusiness();

        return [
            'customer' => 'required_if:intent,spin_to_win|array',
            'customer.id' => [
                'required_with:customer',
                function ($attribute, $value, $fail) use ($appBusiness) {
                    $exists = User::where('business_fk', $appBusiness->business_pk)
                        ->exists();
                    if (!$exists) {
                        $fail('The ' . $attribute . ' field is invalid.');
                    }
                },
            ],
            'user_prize_win' => 'required_if:intent,spin_to_win|array',
            'user_prize_win.id' => [
                'required_if:intent,spin_to_win',
                'integer',
                function ($attribute, $value, $fail) use ($appBusiness) {
                    $exists = DB::table('user_prize_win as upw')
                        ->leftJoin('spin_win as sw', function ($join) use ($appBusiness) {
                            $join->on('sw.spin_win_pk', '=', 'upw.spin_win_fk')
                                ->where('sw.business_fk', $appBusiness->business_pk);
                        })
                        ->leftJoin('user as u', function ($join) use ($appBusiness) {
                            $join->on('u.id', '=', 'upw.user_fk')
                                ->where('u.business_fk', $appBusiness->business_pk);
                        })
                        ->where('upw.user_prize_win_pk', $value)
                        ->whereNull('upw.transaction_fk')
                        ->where('upw.enabled', 1)
                        ->where('u.enabled', 1)
                        ->where('sw.business_fk', $appBusiness->business_pk)
                        ->where('u.business_fk', $appBusiness->business_pk)
                        ->exists();
                    if (!($exists)) {
                        $fail('The ' . $attribute . ' field is invalid.');
                    }
                },
            ],
        ];
    }

    /**
     * @param Validator $validator
     */
    private function validatePosBranch(Validator $validator)
    {
        $branchId = $this->branch['id'];
        $businessBranch = BusinessBranch::findUid($branchId, ['business_branch_pk']);

        $posBranch = PosBranch::findByAttributes([
            'business_branch_fk' => $businessBranch->business_branch_pk,
            'pos_system_fk' => POS_SYSTEM_SHOPIFY,
            'enabled' => 1
        ]);

        if ($posBranch->isEmpty()) {
            $validator->errors()->add('branch.id', 'Ecom integration is not connected to this branch.');
        }
    }

    /**
     * Remove the user (customer/employee) checking inf the intent is QR code
     * This to make sure that the rules are not added when the call is for the QR login/signup
     * @param $rules
     * @return void
     */
    private function excludeUserValidationRules(&$rules): void
    {
        unset($rules['customer']);
        unset($rules['customer.id']);
        unset($rules['customer.email']);
        unset($rules['customer.phone']);
        unset($rules['customer.country_code']);
        unset($rules['employee']);
        unset($rules['employee.id']);
        unset($rules['employee.pin_code']);
    }

    /**
     * @return array
     */
    private function getQrCodeSaleRules()
    {
        return [
            'sale' => 'sometimes|array',
            'sale.branch_link_id' => 'sometimes|required|integer',
            'sale.sale_external_id' => 'sometimes|required|string|min:1|max:36',
            'sale.sale_number' => 'string|min:1|max:50',
            'sale.customer_external_id' => 'required|string|min:1|max:36',
            'sale.sale_status' => 'sometimes|required|string|in:new,processing,on-hold,completed,cancelled',
            'sale.payment_status' => 'sometimes|required|string|in:paid',
            'sale.fulfillment_status' => 'string|min:1|in:unfulfilled,fulfilled,partial,restocked',
            'sale.sale_created_at' => 'sometimes|required|Date',
            'sale.sale_total' => 'sometimes|required|numeric|min:0|max:99999999.99',
            'sale.sale_subtotal' => 'sometimes|required|numeric|min:0|max:99999999.99',
            'sale.sale_total_tax' => 'sometimes|required|numeric|min:0|max:99999999.99',
            'sale.sale_shipping' => 'numeric|min:0|max:99999999.99',
            'sale.sale_shipping_tax' => 'numeric|min:0|max:99999999.99',
            'sale.sale_discounts' => 'sometimes|required|numeric|min:0|max:99999999.99',
            'sale.sale_discounts_tax' => 'sometimes|required|numeric|min:0|max:99999999.99',
            'sale.sale_currency' => 'sometimes|required|string|min:3|max:3|exists:currency,iso_code',
            'sale.redemption_transactions' => 'sometimes|array',
            'sale.redemption_transactions.*' => [
                'required_with:redemption_transactions|integer|min:1',
                //                    Rule::exists('transaction', 'transaction_pk')->where(function ($query) {
//                        $posUser = $this->getPosUser();
//
//                        return $query->where('user_fk', $posUser->user_fk ?? null)
//                            ->whereIn('transaction_sub_type_fk', [
//                                TRX_SUBTYPE_REDEMPTION_PTS,
//                                TRX_SUBTYPE_COUPON_CLAIMED,
//                                TRX_SUBTYPE_COUPON_CONVERTIBLE_CLAIMED
//                            ])
//                            ->where('entity_fk', $this->posBranch->business_branch_fk);
//                    }),
            ],
            'sale.customer' => 'sometimes',
            'sale.customer.first_name' => 'string|min:1',
            'sale.customer.last_name' => 'string|min:1',
            'sale.customer.birth_date' => 'date|min:1',
            'sale.customer.email' => 'email|max:100',
            'sale.customer.phone' => 'regex:/^\+?[1-9]\d{1,14}$/', //E.164 formatted phone numbers
            'sale.customer.language' => 'in:en,fr,es,pt,nl,ar,ku',
            'sale.sale_lines' => 'sometimes|required|array',
            'sale.sale_lines.*.line_external_id' => 'required|string|min:1|max:36',
            'sale.sale_lines.*.quantity' => 'required|integer|min:0',
            'sale.sale_lines.*.line_total' => 'required|numeric|min:0|max:99999999.99',
            'sale.sale_lines.*.line_subtotal' => 'required|numeric|min:0|max:99999999.99',
            'sale.sale_lines.*.line_total_tax' => 'required|numeric|min:0|max:99999999.99',
            'sale.sale_lines.*.line_discounts' => 'required|numeric|min:0|max:99999999.99',
            'sale.sale_lines.*.line_discounts_tax' => 'required|numeric|min:0|max:99999999.99',
            'sale.sale_lines.*.line_item' => 'required',
            'sale.sale_lines.*.line_item.item_external_id' => 'required|string|min:1|max:36',
            'sale.sale_lines.*.line_item.variant_external_id' => 'string|min:1|max:36',
            'sale.sale_lines.*.line_item.description' => 'string|min:1|max:10000',
            'sale.sale_lines.*.line_item.name' => 'required|string|min:1|max:255',
            'sale.sale_lines.*.line_item.sku' => 'string|min:1|max:255',
            'sale.sale_lines.*.line_item.price' => 'required|numeric|min:0|max:99999999.99',
            'sale.sale_lines.*.line_item.categories' => 'present|array',
            'sale.sale_lines.*.line_item.categories.*.category_external_id' => 'required_with:sale_lines.*.line_item.categories.*|string|min:1|max:36',
            'sale.sale_lines.*.line_item.categories.*.name' => 'required_with:sale_lines.*.line_item.categories.*|string|min:1|max:255'
        ];
    }

    /**
     * @return array
     */
    public function messages(): array
    {
        $lang = App::getLocale();

        return [
            'created_at.before_or_equal' =>  __('api.INVALID_TRANSACTION_DATE_PAST_OR_TODAY', [], $lang)
        ];
    }
}
