<?php

namespace App\Http\Controllers\Api\V1\Exposed;

use App\Http\Controllers\Api\V1\ApiController;
use App\Http\Resources\CrmFieldCollection;
use App\Traits\CrmFieldsTrait;

class CrmFieldController extends ApiController
{
    use CrmFieldsTrait;

    /**
     * List all CRM fields for a business
     * <AUTHOR>
     * @since 14/06/2023
     * @return CrmFieldCollection
     */
    public function index(): CrmFieldCollection
    {
        $appBusiness = $this->getBusinessForAppKey();

        $collection = $this->getCrmFieldsForBusiness($appBusiness, null, false, true);

        return new CrmFieldCollection($collection, true);
    }
}