<?php
namespace KangarooRewards\Import\Common\Helpers;

class ErrorMessageHelper
{
    /**
     * @param string $errorType
     * @return string
     */
    public static function getErrorMessage(string $errorType): string
    {
        return match ($errorType) {
            'warning_empty_field' => 'Field is empty',
            'invalid_email' => 'Invalid email address',
            'invalid_phone' => 'Invalid phone number',
            'invalid_date_format' => 'Invalid date format',
            'invalid_special_characters' => 'The field contains disallowed special characters',
            'invalid_tags' => 'Invalid tags list',
            'invalid_consent' => 'Invalid consent value',
            'existing_customer' => 'Existing customer',
            'duplicate_email' => 'Duplicate email address',
            'duplicate_phone' => 'Duplicate phone number',
            'email_required_without_phone' => 'Email address is required without phone number',
            'phone_required_without_email' => 'Phone number is required without email address',
            'duplicate_header_columns' => 'The header record contains duplicate column names',
            'invalid_language' => 'Invalid language',
            'invalid_points_balance' => 'Invalid points balance',
            'export_errors_started' => 'The error file is being prepared and will be sent to the admin email address shortly.',
            'phone_already_exists' => 'The phone number is already associated with a different customer',
            'email_already_exists' => 'The email address is already associated with a different customer',
            default => 'Unknown error',
        };
    }
}