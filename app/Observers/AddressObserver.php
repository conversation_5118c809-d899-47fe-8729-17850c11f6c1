<?php

namespace App\Observers;

use App\Models\V1\Address;
use App\Traits\HasFeatureFlag;
use App\User;
use Illuminate\Support\Facades\Log;
use KangarooRewards\Workflows\Dto\WorkflowEventData;
use KangarooRewards\Workflows\Helpers\TriggerEventEnum;
use KangarooRewards\Workflows\Jobs\WorkflowEventHandlerJob;

class AddressObserver
{
    /**
     * Handle events after all transactions are committed.
     *
     * @var bool
     */
    public $afterCommit = true;

    /**
     * Handle the Address "created" event.
     *
     * @param Address $address
     * @return void
     */
    public function created(Address $address)
    {
        $this->dispatchWorkflowCompletedProfileEvent($address);
    }

    /**
     * Handle the Address "updated" event.
     *
     * @param Address $address
     * @return void
     */
    public function updated(Address $address)
    {
        $this->dispatchWorkflowCompletedProfileEvent($address);
    }

    private function dispatchWorkflowCompletedProfileEvent(Address $address):void
    {
        if ($address->entity_type_fk == 4 && HasFeatureFlag::feature('WORKFLOW')) {
            $user = User::find($address->entity_fk);
            if ($user) {
                $eventData = WorkflowEventData::fromRequestData([
                    'main_trigger_id' => TriggerEventEnum::USER_PROFILE_COMPLETE->value,
                    'event_uuid' => WorkflowEventData::orderedUuidHex(),
                    'business_fk' => $user->business_fk,
                    'user_fk' => $address->entity_fk,
                    'entity_name' => 'User',
                    'entity_id' => $address->entity_fk,
                ]);

                dispatch(new WorkflowEventHandlerJob($eventData))->delay(10);
            }
        }
    }
}
