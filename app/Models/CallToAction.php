<?php

namespace App\Models;

use App\Models\V1\TransactionSubType;
use Illuminate\Database\Eloquent\Model;

class CallToAction extends Model
{
    protected $table = 'call_to_action';
    protected $primaryKey = 'call_to_action_pk';
    protected $connection = 'tenant';

    const CREATED_AT = 'utc_created';
    const UPDATED_AT = 'utc_created';

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        //
    ];

    public function transactionSubType(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(TransactionSubType::class, 'transaction_sub_type_fk', 'transaction_sub_type_pk');
    }
}
