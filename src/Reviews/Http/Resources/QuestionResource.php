<?php
namespace KangarooRewards\Reviews\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class QuestionResource extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        $include = explode(',', $request->include);

        $includeReplies = in_array('question_replies', $include, true);

        $data = [
            'id' => $this->question_pk,
            'created_at' => (new Carbon($this->created_at))->toIso8601String(),
            'email' => $this->email,
            'first_name' => $this->first_name,
            'body' => $this->body,
            'product_id' => $this->external_product_id,
            'type' => $this->type,
            'source' => $this->source,
            'question_replies' => $this->when($includeReplies, (new QuestionReplyCollection(collect($this->replies ?? [])->map(function ($item) {
                return (object)$item;
            })))->response()->getData()->data)
        ];

        return [
            $this::$wrap => $data
        ];
    }

}