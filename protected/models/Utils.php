<?php /** @noinspection Annotator */

use KangarooRewards\OAuth2\Client\Provider\Kangaroo;

/**
 * This is the Utils class that holds common functions that are not related to a specific model
 *
 *
 */
class Utils
{
    const OFFER = 1;
    const PUNCH_ITEM = 2;
    const PRODUCT = 3;
    const RESELLER_KANGAROO = 'KANGAROO_REWARDS';
    const RESELLER_WL1 = 'WL1_LOYALTEE_CLUB';

    /**
     * Sanitize HTML input to prevent XSS attacks.
     * NOTE: Sanitization breaks the HTML if it contains invalid nesting.
     *
     * Removes: iframe, object, embed, script, http://
     *
     * @param string $userInputHtml
     * @return string Sanitized HTML
     */
    public static function sanitizeHtml($userInputHtml):string
    {
        $p = new CHtmlPurifier();
        // More permissive tag and attribute whitelist
        $p->options = [
            'URI.AllowedSchemes' => ['https' => true, '' => true,],
            'URI.MakeAbsolute' => false, // Do not convert to absolute
            'URI.Base' => null, // Avoid prefixing with any base URI
            'URI.Disable' => false, // Do not disable URI processing
            'CSS.AllowedProperties' => [
                'color', 'background-color', 'font', 'font-size', 'font-weight', 'font-style', 'text-decoration',
                'padding', 'margin', 'border', 'border-collapse', 'width', 'height', 'max-width', 'min-width',
                'max-height', 'min-height', 'text-align',
            ],
            'Attr.AllowedFrameTargets' => ['_blank'],
            'HTML.SafeIframe' => false,
            'HTML.SafeObject' => false,
            'HTML.SafeEmbed' => false,
            'HTML.Allowed' => 'p,br,hr,b,strong,i,em,u,a[href|title|target],ul,ol,li,dl,dt,dd,span[style],div[style],img[src|alt|width|height|style],table[border|cellpadding|cellspacing|align|bgcolor|width|class|style],tr[bgcolor|class|style],td[align|colspan|rowspan|bgcolor|width|height|valign|class|style],th,thead,tbody,tfoot,h1,h2,h3,h4,h5,h6,blockquote,pre,caption,address',
        ];

        // not supported: article,aside,nav,section,details,summary,figure,figcaption,mark,time
        return $p->purify($userInputHtml);
    }

    /**
     * Check for HTML violations.
     * @param string $html
     * @return array
     */
    public static function getHtmlViolations(string $html): array
    {
        if (empty($html)) {
            return [];
        }

        $issues = [];
        // Store original libxml error handling state and enable user error handling
        $originalInternalErrors = libxml_use_internal_errors(true);

        $dom = new DOMDocument();
        // Attempt to load HTML.
        // Prepending XML encoding declaration helps loadHTML interpret fragments correctly.
        // LIBXML_HTML_NOIMPLIED prevents adding <html><body> tags to fragments.
        // LIBXML_HTML_NODEFDTD prevents adding a default doctype.
        // Use @ to suppress PHP warnings on parsing errors; libxml errors are handled internally.
        @$dom->loadHTML('<?xml encoding="UTF-8">' . $html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        // Check for disallowed tags
        foreach (['script', 'iframe', 'object', 'embed'] as $tag) {
            if ($dom->getElementsByTagName($tag)->length > 0) {
                $issues[] = "Disallowed tag <$tag> found.";
            }
        }

        $sqlKeywordsPattern = '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bDELETE\b|\bUPDATE\b|\bDROP\b)/i';
        $sqlIssueInAttributes = false;
        $sqlIssueInTextContent = false;

        // Iterate over all elements to check attributes
        foreach ($dom->getElementsByTagName('*') as $el) {
            if ($el->hasAttributes()) {
                foreach ($el->attributes as $attr) {
                    $name = strtolower($attr->name);
                    $value = $attr->value;

                    // Check for event handlers (e.g., onclick)
                    if (preg_match('/^on[a-z]+/', $name)) {
                        $issues[] = "Event handler '$name' found.";
                    }

                    // Check for javascript: URI in href or src
                    if (in_array($name, ['href', 'src']) && preg_match('/^\s*javascript:/i', $value)) {
                        $issues[] = "JavaScript URI in '$name' attribute.";
                    }

                    // Check for CSS expression()
                    /*if ($name === 'style' && stripos($value, 'expression(') !== false) {
                        $issues[] = "Expression() found in inline style.";
                    }*/

                    // Check for potentially dangerous CSS in style attributes
                    if ($name === 'style' && preg_match('/expression\(|@import|url\(data:|behavior:|javascript:/i', $value)) {
                        $issues[] = "Potentially dangerous CSS detected in inline style.";
                    }

                    // Check for SQL keywords in attribute values
                    if (!$sqlIssueInAttributes && preg_match($sqlKeywordsPattern, $value)) {
                        $sqlIssueInAttributes = true;
                    }
                }
            }
        }

        // Check for SQL keywords in text content of the parsed DOM
        $xpath = new DOMXPath($dom);
        $textNodes = $xpath->query('//text()'); // Get all text nodes
        foreach ($textNodes as $textNode) {
            if (preg_match($sqlKeywordsPattern, $textNode->nodeValue)) {
                $sqlIssueInTextContent = true;
                break; // Found it, no need to check further text nodes
            }
        }

        if ($sqlIssueInAttributes || $sqlIssueInTextContent) {
            $issues[] = "SQL keywords detected in content.";
        }

        // Clear any libxml errors that might have been generated during parsing
        libxml_clear_errors();
        // Restore original libxml error handling state
        libxml_use_internal_errors($originalInternalErrors);

        // Return unique violation messages
        return array_unique($issues);
    }

    /**
     * Fix common invalid nesting issues in HTML.
     * @param string $html
     * @return string
     */
    public static function fixInvalidNesting(string $html): string
    {
        // Replace common invalid block-in-inline nesting
        $html = preg_replace('/<p[^>]*>\s*(<(div|table|section|article|aside|nav|header|footer)[^>]*>)/i', '$1', $html);
        $html = preg_replace('/(<\/(div|table|section|article|aside|nav|header|footer)>)\s*<\/p>/i', '$1', $html);
        return $html;
    }

    /**
     * Parses a user agent string to extract device (OS) and app (browser) name.
     * @param string $userAgent
     * @return array ['device' => ..., 'app' => ...]
     */
    public static function parseUserAgent(string $userAgent): array
    {
        $userAgent = str_replace('like Mac OS X', '', $userAgent);
        // Device/OS extraction
        $osList = [
            '/windows nt 10/i'      => 'Windows 10',
            '/windows nt 6.3/i'     => 'Windows 8.1',
            '/windows nt 6.2/i'     => 'Windows 8',
            '/windows nt 6.1/i'     => 'Windows 7',
            '/windows nt 6.0/i'     => 'Windows Vista',
            '/windows nt 5.1/i'     => 'Windows XP',
            '/macintosh|mac os x/i' => 'Mac OS X',
            '/android/i'            => 'Android',
            '/iphone/i'             => 'iPhone',
            '/ipad/i'               => 'iPad',
            '/linux/i'              => 'Linux',
        ];
        $device = 'Unknown';
        foreach ($osList as $regex => $value) {
            if (preg_match($regex, $userAgent)) {
                $device = $value;
                break;
            }
        }

        // App/Browser extraction
        $browserList = [
            '/brave\/([0-9\.]+)/i'     => 'Brave', // Brave is based on Chrome, must be checked before Chrome
            '/vivaldi\/([0-9\.]+)/i'   => 'Vivaldi', // Vivaldi is based on Chrome, must be checked before Chrome
            '/edge\/([0-9\.]+)/i'      => 'Edge',
            '/opera\/([0-9\.]+)/i'     => 'Opera',
            '/opr\/([0-9\.]+)/i'       => 'Opera',
            '/chrome\/([0-9\.]+)/i'    => 'Chrome',
            '/firefox\/([0-9\.]+)/i'   => 'Firefox',
            '/safari\/([0-9\.]+)/i'    => 'Safari',
            '/msie ([0-9\.]+)/i'       => 'Internet Explorer',
            '/trident\/.*rv:([0-9\.]+)/i' => 'Internet Explorer',
        ];
        $app = 'Unknown';
        foreach ($browserList as $regex => $value) {
            if (preg_match($regex, $userAgent)) {
                $app = $value;
                break;
            }
        }

        return [
            'device' => $device,
            'app' => $app,
        ];
    }

    /**
     * This function is used to log the database transaction ID and thread ID in a log file
     * It must be called from inside a DB transaction start and end block
     *
     * <AUTHOR>
     * @param array $context
     * @return void
     */
    public static function logDbTransactionId(array $context = [])
    {
        //return; // Use only for debugging
        try {
            $dbTrx = Yii::app()->db->createCommand("
                SELECT tx.trx_id, tx.trx_mysql_thread_id
                FROM information_schema.INNODB_TRX AS tx 
                WHERE tx.trx_mysql_thread_id = CONNECTION_ID()
            ")->queryRow();

            if (empty($dbTrx)) {
                return;
            }
            $dbTrxId = $dbTrx['trx_id'] ?? null;
            $dbTrxThreadId = $dbTrx['trx_mysql_thread_id'] ?? null;

            $calledFrom = $context['method'] ?? '';

            unset($context['method']);

            Utils::logMessage(json_encode(array_merge([
                'INNODB_TRX_ID' => $dbTrxId,
                'INNODB_TRX_THREAD_ID' => $dbTrxThreadId,
            ], $context)), $calledFrom);
        } catch (\Exception $e) {
        }
    }

    public static function getPhotosBucketName(): string
    {
        $reseller = self::getResellerEnvId();

        if ($reseller === self::RESELLER_KANGAROO) {
            $bucketName = 'kng-pic';
            if (\Yii::app()->params['envParam'] === 'STG') {
                $bucketName = 'kng-stg-pic';
            }

            return $bucketName;
        }

        if ($reseller === self::RESELLER_WL1) {
            $bucketName = 'kng-wl1-pic';
            if (\Yii::app()->params['envParam'] === 'STG') {
                $bucketName = 'kng-wl1-stg-pic';
            }

            return $bucketName;
        }

        throw new \RuntimeException('Undefined Reseller Environment Identifier');
    }

    public static function getResellerEnvId(): string
    {
        if (!isset(\Yii::app()->params['envReseller'])) {
            return self::RESELLER_KANGAROO;
        }

        return \Yii::app()->params['envReseller'];
    }

    /**
     * Check if an IP address exists in the blacklisted_ips table.
     *
     * @param string $ip The IP address to check.
     * @return bool Whether the IP address exists in the table.
     * @throws CException
     */
    public static function isIpBlacklisted(string $ip): bool
    {
        $count = (int) Yii::app()->db->createCommand()
            ->select('COUNT(*)')
            ->from('blacklisted_ips')
            ->where('ip = :ip', array(':ip' => $ip))
            ->queryScalar();

        return $count > 0;
    }

    /**
     * Gets the appropriate cookie domain based on server URL and environment
     * 
     * @return string Cookie domain to use
     */
    public static function getCookieDomain(): string 
    {
        // Use empty domain for local development
        if (Yii::app()->params['envParam'] === 'LOCAL') {
            return '';
        }

        $urlParts = parse_url(Yii::app()->params['serverUrl']);
        $host = $urlParts['host'];

        // Find last two segments of domain (e.g. example.com)
        $lastDot = strrpos($host, '.');
        if ($lastDot === false) {
            return $host;
        }

        $secondLastDot = strrpos(substr($host, 0, $lastDot), '.');
        if ($secondLastDot === false) {
            return $host;
        }

        return substr($host, $secondLastDot);
    }

    public static function getG2ReviewUrl($userId): string
    {
        $email = User::model()->findByPk($userId)->email;
        $email = urlencode($email);

        $token = 'bfef55db9ff6ff8c65c43b55ed4566b3b447ebf61f4bb3c0b26e70a642c5463a';
        $prodId = '605e6a9b-ac3a-4083-a549-ee5825251b86';
        $apiUrl = "https://www.g2.com/partnerships/KangarooRewards/tokens?api_token=$token&product_id=$prodId";

        $r = Utils::httpRequestV2([
            'apiUrl' => $apiUrl,
            'reqType' => 1,
            'headers' => []
        ], '', PlatformSolution::PLATFORM_NAME_WEBSITE);

        $response = json_decode($r, true);
        $state = $response['state'];

        return "https://www.g2.com/partnerships/KangarooRewards/users/login.embed?state={$state}&email={$email}";
    }

    public static function logFailedLoginAttempt($credential, int $platformId)
    {
        Yii::log(json_encode([
            'message' => 'FAILED Login attempt',
            'username' => $credential,
            'ip' => Yii::app()->request->userHostAddress,
            'session_id' => session_id(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
        ]), CLogger::LEVEL_INFO, __METHOD__);

        if (\Yii::app()->params['envParam'] !== 'PROD') {
            return;
        }

        $kngIps = ['**************', '***********', '**************', '**************', '**************'];

        if (in_array(Yii::app()->request->userHostAddress, $kngIps, false)) {
            return;
        }

        try {
            $bigQuery = new \Google\Cloud\BigQuery\BigQueryClient([
                'projectId' => \Yii::app()->params['GCP_PROJECT_ID'],
                'keyFilePath' => \Yii::app()->basePath . '/config/google_service_account_credentials.json',
            ]);

            $dataset = $bigQuery->dataset('mc_kng_audit');
            $table = $dataset->table('user_failed_logins');

            $data = [
                'data' => [
                    'created_at' => date('Y-m-d H:i:s'),
                    'username' => $credential,
                    'ip' => Yii::app()->request->userHostAddress,
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                    'platform_id' => $platformId,
                ]
            ];
            $table->insertRows([$data]);
        } catch (\Exception $e) {
            //
        }
    }

    public static function qrCodeHelper($userId, $userEntities, $post): string
    {
        if (Yii::app()->session->get('loginas_user_id') != null) {
            $userId = Yii::app()->session->get('loginas_user_id');
        }
        $branch = BusinessBranch::model()->findByPk($userEntities['mainBranchId']);
        $business = Business::model()->findByPk($userEntities['businessId']);

        try {
            $oauthProviderToken = OauthProviderToken::getAccessTokenForUser($userId);

            $api = new KangarooApiClient($business, $branch, $oauthProviderToken->access_token, null);

            $params = array_intersect_key($post, array_flip(['intent', 'alg', 'base_64', 'qr_code_info']));

            return $api->qrCodeHelper($params);
        } catch (\League\OAuth2\Client\Provider\Exception\IdentityProviderException $e) {
            Utils::logMessage($e . ' ' . json_encode([
                'user_id' => $userId,
                'business_pk' => $userEntities['businessId'],
            ]), __METHOD__, CLogger::LEVEL_WARNING);
            return json_encode(['status' => 'NOT_OK', 'message' => Yii::t('backend', 'BACKEND_WSSECURITY_AUTHORIZATION_ERROR')]);
        } catch (\GuzzleHttp\Exception\GuzzleException $e) {
            Utils::logMessage($e . ' ' . json_encode([
                'user_id' => $userId,
                'business_pk' => $userEntities['businessId'],
            ]), __METHOD__, CLogger::LEVEL_WARNING);
            if ($e->hasResponse()) {
                $errors = json_decode((string)$e->getResponse()->getBody(), true);
                if (isset($errors['error']['message'])) {
                    return json_encode(['status' => 'NOT_OK', 'message' => $errors['error']['message']]);
                }
                foreach ($errors as $error) {
                    return json_encode(['status' => 'NOT_OK', 'message' => $error[0]]);
                }
            }
            return json_encode(['status' => 'NOT_OK', 'message' => $e->getMessage()]);
        } catch (\Exception $e) {
            Utils::logMessage($e . ' ' . json_encode([
                'user_id' => $userId,
                'business_pk' => $userEntities['businessId'],
            ]), __METHOD__, CLogger::LEVEL_WARNING);
            return json_encode(['status' => 'NOT_OK', 'message' => Yii::t('backend', 'UNKNOWN_ERROR')]);
        }
    }

    public static function sendDoubleOptInSms($userModel, $business)
    {
        try {
            // Initialize Kangaroo API provider
            $kangarooApiProvider = new Kangaroo([
                'clientId' => Yii::app()->params['kangarooApiClientId'],
                'clientSecret' => Yii::app()->params['kangarooApiClientSecret'],
                'urlAccessToken' => Yii::app()->params['kangarooApiUri'] . '/oauth/token',
                'redirectUri' => null,
            ]);

            // Retrieve Access Token using client_credentials grant
            $token = $kangarooApiProvider->getAccessToken('client_credentials', ['scope' => 'admin',]);

            $api = new KangarooApiClient($business, $business->mainBranch, $token->getToken(), $userModel);

            $response = $api->internalSendUserMessage([
                'type' => 'opt_in',
                'business_id' => $business->business_pk,
            ]);

            Utils::logMessage($response . ' ' . json_encode([
                'user_id' => $userModel->id,
                'business_pk' => $business->business_pk,
            ]), __METHOD__);
        } catch (\Exception $e) {
            Utils::logMessage($e . ' ' . json_encode([
                'user_id' => $userModel->id,
                'business_pk' => $business->business_pk,
            ]), __METHOD__, CLogger::LEVEL_WARNING);
        }
    }

    public static function captureRequest()
    {
        try {
            $headers = '';
            foreach (self::getallheaders() as $header => $value) {
                $headers .= $header . ': ' . $value . PHP_EOL;
            }
            $cookies = '';
            foreach ($_COOKIE as $cookie => $value) {
                $cookies .= $cookie . ': ' . $value . PHP_EOL;
            }
            $command = Yii::app()->db_audit->createCommand();
            $command->insert('temp_raw_requests', [
                'ip_address' => Yii::app()->request->userHostAddress,
                'endpoint'=> $_SERVER['REQUEST_URI'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'query_string' => $_SERVER['QUERY_STRING'] ?? null,
                'headers' => $headers,
                'body' => file_get_contents('php://input'),
                'cookies' => $cookies,
            ]);
        } catch (Exception $e) {

        }
    }

    /**
     * Performs a deep verification including disposable email, domain and MX records existence
     * @param $email
     * @return bool
     */
    public static function emailDeepVerification($email): bool
    {
        $checker = new \Aman\EmailVerifier\EmailChecker();
        $checker->setFromEmail($email);
        $isDisposable = $checker->checkDisposableEmail($email, true);
        $isValidDomain = true; // $checker->checkDomain($email);// not working as expected for gmail accounts
        $mxRecord = $checker->checkMxAndDnsRecord($email);
        $isValidMxRecord = isset($mxRecord[0]) && $mxRecord[0] === 'valid';

        if ($isValidDomain && $isValidMxRecord && !$isDisposable) {
            return true;
        }

        return false;
    }

    public static function saveIdempotencyKey(string $key, array $data = [])
    {
        Yii::app()->db->createCommand()->insert('idempotency_keys', [
            'id' => $key,
            'data' => json_encode($data),
        ]);
    }

    public static function transformPtsExpiration($items): array
    {
        $newArray = [];
        foreach ($items as $item) {
            $newArray[$item['_user_id']] = $item['expired_points'];
        }
        return $newArray;
    }

    public static function getExchangeRate(string $code)
    {
        $cacheKey = 'exchange_rate';
        $data = Yii::app()->cache->get($cacheKey);

        if ($data !== false) {
            $r = (array) json_decode($data, true);
            return $r['conversion_rates'][$code] ?? 0;
        }

        // alternative https://exchangeratesapi.io/
        $key ='************************';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://v6.exchangerate-api.com/v6/{$key}/latest/USD");
        curl_setopt($ch, CURLOPT_HEADER, 0); // No header in the result
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Return, do not echo result
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);

        // Fetch and return content
        $data = curl_exec($ch);

        if (curl_errno($ch)) {
            Yii::log('Failed CURL REQUEST error=' . curl_error($ch), CLogger::LEVEL_ERROR,  __METHOD__);
            return 0;
        }

        curl_close($ch);

        $r = (array) json_decode($data, true);

        if (isset($r['result']) && $r['result'] === 'success') {
            Yii::app()->cache->set($cacheKey, $data, 24*60*60);
        } else {
            Yii::log($data, CLogger::LEVEL_ERROR,  __METHOD__);
        }

        return $r['conversion_rates'][$code] ?? 0;
    }

    public static function array_columns(array $arr, array $keysSelect): array
    {
        $keys = array_flip($keysSelect);
        $filteredArray = array_map(function($a) use($keys){
            if (is_object($a)) {
                $b = [];
                foreach ($keys as $key => $val) {
                    if (in_array($key, ['user_uid'], true)) {
                        $b[$key] = bin2hex($a->{$key}); // convert from bin to hex
                    } else {
                        $b[$key] = $a->{$key};
                    }
                }
                return $b;
            }
            return array_intersect_key($a,$keys);
        }, $arr);

        return $filteredArray;
    }

    /**
     * Returns DSN attribute from current DB Connection String
     *
     * Used in : Everywhere
     *
     * @version V4.8.6
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function getDsnAttribute($name)
    {
        if (preg_match('/' . $name . '=([^;]*)/', Yii::app()->db->connectionString, $match)) {
            return $match[1];
        } else {
            return null;
        }
    }

    /**
     * Returns statically defined languages to avoid DB Calls
     * Used on the landing page or public pages
     *
     * @return array|array[]
     */
    public static function getStaticLanguages(): array
    {
        return [
            [
                'language_pk' => 1,
                'abreviation' => 'en',
                'name' => 'English',
                'default' => 1,
            ],[
                'language_pk' => 2,
                'abreviation' => 'fr',
                'name' => 'Français',
                'default' => 0,
            ],[
                'language_pk' => 3,
                'abreviation' => 'es',
                'name' => 'Español',
                'default' => 0,
            ],
        ];
    }

    /**
     * Returns true if the url is main domain (www.kangaroorewards.com or kangaroorewards.com)
     *
     * Used in : Website()
     *
     * @version August 2017 release V4.8.3
     * @since 4.8.3
     * <AUTHOR>
     * @access public
     * @return boolean
     */
    public static function isRootDomain()
    {
        return true;
        // $configHost = strtolower((string)parse_url(Yii::app()->params['serverUrl'], PHP_URL_HOST));

        // return in_array(strtolower((string)$_SERVER['HTTP_HOST']), [$configHost, 'www.' . $configHost]);
    }

    // TEST
    public static function bitwiseId()
    {
        // (shard ID << 46) | (type ID << 36) | (local ID<<0)
        $shardID1 =  3429; $typeID1 =  1; $localID1 =  7075733;
        $id = ($shardID1 << 46) | ($typeID1 << 36) | ($localID1 << 0);
        echo "Encoded: ID={$id} shardID={$shardID1} typeID={$typeID1} localID={$localID1}" . '<BR>';

        //decrypt
        // Shard ID = (241294492511762325 >> 46) & 0xFFFF = 3429
        // Type ID  = (241294492511762325 >> 36) & 0x3FF = 1
        // Local ID = (241294492511762325 >>  0) & 0xFFFFFFFFF = 7075733
        // $id = 241294492511762325;
        $shardID = ($id >> 46) & 0xFFFF;
        $typeID = ($id >> 36) & 0x3FF;
        $localID = ($id >> 0) & 0xFFFFFFFFF;

        echo "Decoded: ID={$id} shardID={$shardID} typeID={$typeID} localID={$localID}" . '<BR>';
        die;
    }

    /**
     * @return bool
     */
    public static function isApi(): bool
    {
//        if (Yii::app()->params['envParam'] === 'PROD') {
//            return false;
//        }
        $headers = self::getallheaders();

        return isset($headers['Authorization']);
    }

    public static function getallheaders() {
        if (!function_exists('getallheaders')) {
            function getallheaders() {
                $headers = [];
                foreach ($_SERVER as $name => $value) {
                    if (substr((string)$name, 0, 5) == 'HTTP_') {
                        $headers[str_replace(' ', '-', ucwords((string)strtolower((string)str_replace('_', ' ', substr((string)$name, 5)))))] = $value;
                    }
                }
                return $headers;
            }
        }

        return getallheaders();
    }

    public static function timeElapsedString($datetime, $full = false)
    {
        $now = new DateTime();
        $ago = new DateTime($datetime);
        $diff = $now->diff($ago);

        // Calculate weeks from days
        $weeks = floor($diff->d / 7);
        $days = $diff->d % 7;

        // Define the time units and their singular names
        $units = [
            'y' => 'year',
            'm' => 'month',
            'w' => 'week',
            'd' => 'day',
            'h' => 'hour',
            'i' => 'minute',
            's' => 'second',
        ];

        // Assign the calculated values to the corresponding keys
        $values = [
            'y' => $diff->y,
            'm' => $diff->m,
            'w' => $weeks,
            'd' => $days,
            'h' => $diff->h,
            'i' => $diff->i,
            's' => $diff->s,
        ];

        $elapsed = [];

        foreach ($values as $key => $value) {
            if ($value) {
                $unit = $units[$key];
                $elapsed[] = $value . ' ' . $unit . ($value > 1 ? 's' : '');
            }
        }

        if (!$full) {
            $elapsed = array_slice($elapsed, 0, 1);
        }

        return $elapsed ? implode(', ', $elapsed) . ' ago' : 'just now';
    }

    /*public static function timeElapsedString($datetime, $full = false)
    {
        $now = new DateTime;
        $ago = new DateTime($datetime);
        $diff = $now->diff($ago);

        $diff->w = floor($diff->d / 7);
        $diff->d -= $diff->w * 7;

        $string = array(
            'y' => 'year',
            'm' => 'month',
            'w' => 'week',
            'd' => 'day',
            'h' => 'hour',
            'i' => 'minute',
            's' => 'second',
        );
        foreach ($string as $k => &$v) {
            if ($diff->$k) {
                $v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? 's' : '');
            } else {
                unset($string[$k]);
            }
        }

        if (!$full) $string = array_slice($string, 0, 1);
        return $string ? implode(', ', $string) . ' ago' : 'just now';
    }*/

    /**
     * Convert a value to studly caps case.
     */
    public static function strStudlyCase($value)
    {
        $value = ucwords((string)str_replace(['-', '_'], ' ', $value));
        return str_replace(' ', '', $value);
    }

    /**
     * Convert a value to camel case.
     */
    public static function strCamelCase($value)
    {
        return lcfirst(self::strStudlyCase($value));
    }

    /**
     * Convert a value to camel case.
     */
    public static function strCamelCapsCase($value)
    {
        return ucfirst((string)self::strStudlyCase($value??''));
    }

    public static function getPusherChannel(BusinessBranch $branch, $user): string
    {
        if(!$user) {
            return '';
        }
        $branchUId = bin2hex($branch->branch_uid);
        $userUId = bin2hex($user->user_uid);

        return 'private-BB-' . $branchUId . '-Emp-' . $userUId;
    }

    public static function sendMarketingDnsUpdateEmail(BusinessRules $rules, $post, $businessId)
    {
        if(!$rules->allow_custom_sender_email || !isset($post['BusinessRules']['sender_from_email'])) {
            return;
        }

        if(!$post['BusinessRules']['sender_from_email']) {
            return;
        }

        if($rules->sender_from_email !== $post['BusinessRules']['sender_from_email']) {
            $email = $post['BusinessRules']['sender_from_email'];
            $dnsHost = $post['custom_email_dns_server'] ?? null;

            $content = '<p>Email: '.$email.'</p>';
            $content .= '<p>Dns Host: ' . $dnsHost.'</p>';
            $content .= '<p>Business ID: ' . $businessId.'</p>';

            MessagesQueue::create([
                'send_to' => Yii::app()->params['technicalSupport'],
                'cell_email' => MESSAGE_SEND_BY_EMAIL,
                'params' => [
                    'emailView' => 'text',
                    'layout' => 'bilingual',
                    'emailSubject' => 'Marketing Custom Sender Email',
                    'emailData' => ['content' => $content],
                    'viewPath' => null,
                    'fromExtra' => Yii::app()->params['appShortName'],
                ],
            ]);

            $customSender  = new CustomEmailSender((string) $email);
            $customSender->authenticateDomain();
        }
    }

    public static function sendUpdateEmailProviderNotification(BusinessRules $rules, $email, $oldEmail, $dnsHost, $businessId, $comment)
    {
        if (!$rules->allow_custom_sender_email || !isset($email)) {
            return;
        }

        if (empty($email)) {
            return;
        }

        if ($email !== $oldEmail) {
            $content = '<p>Email: ' . $email . '</p>';
            $content .= '<p>Dns Host: ' . (($dnsHost == '') ? "I'm not sure" : $dnsHost) . '</p>';
            $content .= '<p>Business ID: ' . $businessId . '</p>';

            MessagesQueue::create([
                'send_to' => Yii::app()->params['technicalSupport'],
                'cell_email' => MESSAGE_SEND_BY_EMAIL,
                'params' => [
                    'emailView' => 'text',
                    'layout' => 'bilingual',
                    'emailSubject' => $comment,
                    'emailData' => ['content' => $content],
                    'viewPath' => null,
                    'fromExtra' => Yii::app()->params['appShortName'],
                ],
            ]);
        }
    }

    /**
     * Sends user info to Integrations API for Pusher
     *
     * @param $data
     * @return array
     */
    public static function sendSelfSignUpEvent($data): array
    {
//        Array
//        (
//            [user] => Array()
//            [business_branch_fk] => 11
//            [employee_fk] => 11
//            [usercaslvalue] => 1|0
//        )

        $employee = User::model()->findByPk($data['employee_fk']);
        $branch = BusinessBranch::model()->findByPk($data['business_branch_fk']);

        $branchUId = bin2hex($branch->branch_uid);
        $employeeUId = bin2hex($employee->user_uid);
        $channelName = 'private-BB-' . $branchUId . '-Emp-' . $employeeUId;

        $userInfo = [
            'phone' => $data['user']['client_phonenumber'],
            'email' => $data['user']['client_email'],
            'first_name' => $data['user']['first'],
            'last_name' => $data['user']['last'],
            'gender' => $data['user']['gender'],
            'language' => $data['language'],
            'birthdate' => $data['user']['client_birthdate'],
            'notes' => $data['user']['notes'] ?? null,
            'country_phone_fk' => $data['user']['country_phone_fk'],
            'country_phone_code' => $data['user']['country_phone_code'],
            'consent_email' => $data['usercaslvalue'],
            'consent_phone' => $data['usercaslvalue'],
            'platform_solution_fk' => $data['platform_fk'],
        ];

        $postData = json_encode([
            'userId' => $employeeUId,
            'bbId' => $branchUId,
            'data' => $userInfo,
        ]);

        Yii::log(json_encode([
            'IP' => Yii::app()->request->userHostAddress,
            'business_pk' => $branch->business_fk,
            'business_branch_pk' => $branch->business_branch_pk,
            'branch_uid' => $branchUId,
            'employee_id' => $employee->id,
            'employee_uid' => $employeeUId,
            'channelName' => $channelName,
            'pusher' => 'pusher:broadcast',
            'userInfo' => $userInfo,
            'message' => 'Self Sign Up Event Broadcast',
        ]), CLogger::LEVEL_INFO, __METHOD__);

        $r = Utils::httpRequestV2([
            'apiUrl' => Yii::app()->params['integration_api_url'] . '/selfSignUpEvent',
            'reqType' => 1,
            'headers' => [
                'Content-Type: application/json',
                'Content-Length: ' . strlen((string)$postData)
            ]
        ],$postData, PlatformSolution::PLATFORM_NAME_WEBSITE);

        return (array) json_decode($r, true);
    }

    /**
     * Not used
     */
    public static function generateMerchantJWT($userId)
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $key = Yii::app()->params['jwtKey'];

        $userEntities = UserEntityPermission::getUserEntitiesByRole($userId, __METHOD__, $platformName);

        // Token Expiration Date - 1 day
        $tokenExpiryDate = new \DateTime('+1 day');

        // $jwt = JWT::encode($userEntities, $key);
        $jwt = \Firebase\JWT\JWT::encode([
            'uid' => $userId, // User ID - Business Admin, business Clerk, Branch Admin
            'bid' => $userEntities['businessId'], // Business ID - used to identify the Tenant Business
            'exp' => $tokenExpiryDate->getTimestamp(), // (Expiration Time) Claim
        ], $key, 'HS256');

        return $jwt;
    }

    public static function sendPushFireBaseNotification($to = '', $data = array())
    { //THIS IS THE UPDATED VERSION!!!!
        $apiKey = 'AIzaSyAD3keivGLdK6UdUWxisYj9EJzpvb1388E'; //this is api legacy key

        $fields = array('to' => $to, 'notification' => $data);

        $headers = array( 'Authorization: key=' .$apiKey, 'Content-Type: application/json');

        $url = 'https://fcm.googleapis.com/fcm/send';

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));
        $result = curl_exec($ch);
        if ($result === false) {
            Yii::log(curl_error($ch), CLogger::LEVEL_ERROR, 'sendPushFireBaseNotification');
            throw new Exception("Curl failed:" . curl_error($ch), 1);
        }
        curl_close($ch);
        return $result;
    }

    public static function sendPushFirebase(){
        $to = "endAAkvOxsw:APA91bHGvcrRQ-XdY1nsTlybBZFIRasnm_C7hbJihYT3mYZthXTspf5IMK1lbjhSt736t9MvoWPNMO0h3jyo0yxt8lm9Yq6HP8EZx7cJVe6_YPhSjkhKXG2l5JBpRK0Bg-q9viGlQCtR";

        $data = array(
            'body' => 'new message that is cool', //this is the body message
            'title' => 'a cool Title' //this is the title of the message
        );

        $result = Utils::sendPushFireBaseNotification($to, $data);

        print_r($result); //print this when you need to

    }

    /**
     * Get set up the phone based on Country
     *
     * Used in : Merchant App
     *
     * @version V4.17
     * <AUTHOR> David
     * @access public
     * @return string
     */
    public static function checkPhoneCountry($countryIso, $phone) {
        if (strtolower((string)$countryIso??'') == 'lb' && strlen((string)trim((string)$phone??'')) == 7 && strpos((string)$phone??'', "@") === FALSE) {
            $phone = '0' . trim((string)$phone??'');
        }
        return $phone;
    }

    /**
     * Find out if a feature is enabled or not
     *
     * @version V5
     * <AUTHOR> Dear
     * @access public
     * @return bool
     */
    public static function feature($flag)
    {
        if(strtoupper((string)$flag) === 'UBP') {
            return true;
        }

        $cachedFeatureFlag = Yii::app()->request->getFeatureCache($flag);
        if (is_bool($cachedFeatureFlag)) {
            return $cachedFeatureFlag;
        }
        $result = Yii::app()->db->createCommand()
            ->select("ff.enabled")
            ->from('feature_flags ff')
            ->where('ff.name = :flag', array(':flag' => $flag))
            ->queryRow();
        Yii::app()->request->setFeatureCache($flag, !!$result['enabled']);
        return $result['enabled'] == 1;
    }

    /**
     * Find out if a feature is enabled or not for a business
     * The flag is stored in the business rules table
     *
     * @param string $flag
     * @param string|int $businessId
     * @return bool
     * @throws CException
     * @version 5.17.0
     * <AUTHOR>
     * @access public
     */
    public static function featureForBusiness(string $flag, $businessId): bool
    {
        $flagKey = $flag . '_' . $businessId;
        $cachedFeatureFlag = Yii::app()->request->getFeatureCache($flagKey);
        if (is_bool($cachedFeatureFlag)) {
            Yii::log(json_encode([
                'featureKey' => $flagKey,
                'fromCache' => true,
            ]), CLogger::LEVEL_INFO, __METHOD__);
            return $cachedFeatureFlag;
        }

        Yii::log(json_encode([
            'featureKey' => $flagKey,
            'fromCache' => false,
        ]), CLogger::LEVEL_INFO, __METHOD__);

        $mapToField = [
            'FEATURE_AUDIT_LOG' => 'audit_log_flag',
        ];

        // Get the field name for the flag
        $field = $mapToField[$flag] ?? null;

        if (is_null($field)) {
            return false;
        }

        $result = Yii::app()->db->createCommand()
            ->select("{$field}")
            ->from('business_rules')
            ->where('business_fk = :id', [':id' => $businessId])
            ->queryRow();
        Yii::app()->request->setFeatureCache($flagKey, !!$result[$field]);
        return (int) $result[$field] === 1;
    }

    /**
     * Find out if a feature is enabled or not for a business
     * The feature
     * @param string $flag
     * @param string|int $businessId
     * @return bool
     * @throws CException
     * @version 5.17.0
     * <AUTHOR>
     * @access public
     */
    public static function featureMenuForBusiness(string $flag, $businessId): bool
    {
        $flagKey = $flag . '_' . $businessId;
        $cachedFeatureFlag = Yii::app()->request->getFeatureCache($flagKey);
        if (is_bool($cachedFeatureFlag)) {
            Yii::log(json_encode([
                'featureKey' => $flagKey,
                'fromCache' => true,
            ]), CLogger::LEVEL_INFO, __METHOD__);
            return $cachedFeatureFlag;
        }

        Yii::log(json_encode([
            'featureKey' => $flagKey,
            'fromCache' => false,
        ]), CLogger::LEVEL_INFO, __METHOD__);

        $mapToId = [
            FEATURE_MENU_DASHBOARD => 1,
            FEATURE_MENU_MARKETING => 2,
            FEATURE_MENU_RULES => 3,
            FEATURE_MENU_REDEMPTION => 4, // Redemption catalog
            FEATURE_MENU_PRODUCT_REWARD => 5, // A la carte
            FEATURE_MENU_OFFERS => 6,
            FEATURE_MENU_GIFT_CARDS => 7,
            FEATURE_MENU_SLIDESHOW => 8,
            FEATURE_MENU_BRANCHES => 9,
            FEATURE_MENU_SOCIAL_MEDIA => 10,
            FEATURE_MENU_PRODUCTS => 11,
            FEATURE_MENU_PAYMENTS => 12,
            FEATURE_MENU_INVITE_CONTACTS => 13,
            FEATURE_MENU_INTEGRATIONS => 22,
            FEATURE_MENU_BUSINESS_PROFILE => 23,
            FEATURE_MENU_BUSINESS_DRAW => 25,
            FEATURE_MENU_REFERRALS => 27,
            FEATURE_MENU_FREQUENT_BUYER => 29,
            FEATURE_MENU_CUSTOMERS => 30,
            FEATURE_MENU_IMPORT_TRANSACTIONS => 32,
            FEATURE_MENU_SURVEY => 33,
            FEATURE_MENU_BANNERS => 34,
            FEATURE_MENU_REVIEWS => 35,
            FEATURE_MENU_ROLES => 36,
            FEATURE_MENU_AUDIT_LOGS => 37,
        ];

        // Get the field name for the flag
        $mainMenuFk = $mapToId[$flag] ?? null;

        if (is_null($mainMenuFk)) {
            return false;
        }

        $result = Yii::app()->db->createCommand()
            ->select('bm.enabled')
            ->from('business_menu bm')
            ->where('bm.business_fk = :businessId AND bm.main_menu_fk = :mainMenuFk',
                [':businessId'=> $businessId, ':mainMenuFk'=> $mainMenuFk])
            ->queryRow();

        Yii::app()->request->setFeatureCache($flagKey, !!$result['enabled']);
        return (int) $result['enabled'] === 1;
    }


    /**
     * Get the Start & Last Date. Allow manage Forward & Backward dates
     *
     * Used in : WebSite (Business Tiers)
     *
     * @param $year
     * @param $month
     * @param $nbAddRemove
     * @param $isFuturePeriod
     * @return array
     * @throws Exception
     * @version V4.8.0
     * <AUTHOR> David
     * @access public
     */
    public static function getStartFinalDateBusTiers($year, $month, $nbAddRemove, $isFuturePeriod)
    {
        if ($isFuturePeriod) {
            $sDate = $year.'-'.$month.'-01';
            $fDate = ($year+$nbAddRemove).'-'.$month.'-01';
            // $lastDate = date('Y-m-t 23:59:59', strtotime((string)$fDate));
            // $startDate = date('Y-m-d 00:00:00', strtotime((string)$sDate));
        } else {
            $sDate = ($year-$nbAddRemove).'-'.$month.'-01';
            $fDate = $year.'-'.$month.'-01';
            // $startDate = date('Y-m-d 00:00:00', strtotime((string)$sDate));
            // $lastDate = date('Y-m-t 23:59:59', strtotime((string)$fDate));
        }

        $startDate = new \DateTime($sDate);
        $startDate->setTime(0, 0, 0);

        $lastDate = new \DateTime($fDate);
        $lastDate->sub(new \DateInterval('P0Y1M0DT0H0M0S')); //Substract 1 month
        $lastDate->setTime(23, 59, 59);

        return [
            'startDate' => $startDate->format('Y-m-d 00:00:00'),
            'lastDate' => $lastDate->format('Y-m-t 23:59:59')
        ];
    }

    /**
     * Sending an Email/SMS when redeeming a Third Party Reward (Partner reward)
     *
     * Used in : Website()
     *
     * @version June 2017 release V4.8.0
     * @since 4.8.0
     * <AUTHOR>
     * @access public
     * @return void
     */
    public static function sendRedeemPartnerRewardNotification($item, $userModel, $branchId, $lang)
    {
        $branch = BusinessBranch::model()->findByPk($branchId);
        $business = Business::model()->findByPk($branch->business_fk);
        $emailSubject = Yii::t('backend', 'BACKEND_REDEEM_TPR_SUBJECT', [], null, $lang);

        $emailAttributes = [
            'send_to' => $userModel->email,
            'cell_email' => MESSAGE_SEND_BY_EMAIL,
            'user_fk' => $userModel->id,
            'business_fk' => $business->business_pk,
            'params' => [
                'emailView' => 'redeemPartnerReward',
                'layout' => 'bilingual',
                'emailSubject' => $emailSubject,
                'emailData' => [
                    'item' => $item,
                    'isAdmin' => false,
                    'username' => $userModel->getFullName(),
                    'branchName' => $branch->name,
                    'contactName' => $branch->contact_name,
                    'contactPhone' => $branch->phone ? Utils::getFormattedPhoneNumber($branch->phone, $branch->country_phone_fk) : null,
                    'businessLogo' => $branch->logo_image_medium,
                ],
                'viewPath' => 'application.views.mail.' . $lang,
                'fromExtra' => $branch->name,
                'withdraw_credit_flag' => true
            ],
        ];

        if ($userModel->email) {
            // Utils::sendEmailWithTemplate(
            //     'redeemPartnerReward', 'bilingual', $emailSubject, $userModel->email, [
            //             'item' => $item,
            //             'isAdmin' => false,
            //             'username' => $userModel->getFullName(),
            //             'branchName' => stripslashes((string)$branch->name),
            //             'contactName' => $branch->contact_name,
            //             'contactPhone' => $branch->phone ? Utils::getFormattedPhoneNumber($branch->phone, $branch->country_phone_fk) : null,
            //             'businessLogo' => $branch->logo_image_medium,
            //         ], 'application.views.mail.' . $lang, null, $branch->name
            // ); return;

            // For User
            MessagesQueue::create($emailAttributes);
        } elseif ($userModel->phone) {
            $smsBody = Yii::t('backend', 'BACKEND_REDEEM_TPR_SMS', [
                '{reward}' => $item['punch_item_title'], '{points}' => $item['punch_value'], '{business_name}' => $branch->name,
            ], null, $lang);

            // Utils::validateAndSendSMS($smsBody, $userModel->phone, $userModel->country_phone_fk, $platformName = '');

            MessagesQueue::create([
                'send_to' => $userModel->phone,
                'cell_email' => MESSAGE_SEND_BY_SMS,
                'country_phone_fk' => $userModel->country_phone_fk,
                'user_fk' => $userModel->id,
                'business_fk' => $business->business_pk,
                'params' => ['message_body' => $smsBody, 'withdraw_credit_flag' => true],
            ]);
        }

        $userEntity = UserEntityPermission::model()->findByAttributes([
            'entity_fk' => $business->business_pk,
            'entity_type_fk' => $business->entity_type_fk,
        ]);

        if ($userEntity) {
            $userAdmin = User::model()->findByPk($userEntity->user_fk);

            // For Admin
            $emailAttributes['send_to'] = $userAdmin->email;
            $emailAttributes['params']['emailData']['isAdmin'] = true;
            MessagesQueue::create($emailAttributes);
        }
    }

    /**
     * Returns the trace when an exceptions is thrown
     *
     * Used in : Website()
     *
     * @version January 2017 release V4.6.0
     * @since 4.6.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function getTraces()
    {
        // $traces=debug_backtrace();
        // $msg = '';
        // foreach($traces as $trace) {
        //     if(isset($trace['file'],$trace['line']) && strpos((string)$trace['file'],YII_PATH)!==0) {
        //         $msg.="\nin ".$trace['file'].' ('.$trace['line'].')';
        //     }
        // }

        $trace=debug_backtrace();
        // skip the first 3 stacks as they do not tell the error position
        if(count($trace)>3) {
            $trace=array_slice($trace,3);
        }

        $traceString='';

        foreach($trace as $i=>$t) {
            if(!isset($t['file']))
                $trace[$i]['file']='unknown';

            if(!isset($t['line']))
                $trace[$i]['line']=0;

            if(!isset($t['function']))
                $trace[$i]['function']='unknown';

            $traceString.="#$i {$trace[$i]['file']}({$trace[$i]['line']}): ";
            if(isset($t['object']) && is_object($t['object']))
                $traceString.=get_class($t['object']).'->';
            $traceString.="{$trace[$i]['function']}()\n";

            unset($trace[$i]['object']);
        }

        return $traceString;
    }

    /**
     * Log a message to console or log file
     *
     * Used in : Website()
     *
     * @version January 2017 release V4.6.0
     * @since 4.6.0
     * <AUTHOR>
     * @access public
     * @param string $message
     * @param string $category
     * @param string $level
     * @return void
     */
    public static function logMessage($message, $category = '', $level = CLogger::LEVEL_INFO)
    {
        if (PHP_SAPI === 'cli') {
            echo '['.date(DATE_ATOM).'] '. $category .' ' . $message . PHP_EOL;
        } else {
            Yii::log($message, $level, $category);
        }
    }


    /**
     * TIMEZONE - Globalization implementation
     * Collecting some statistics
     *
     * Used in : Website(SystemAdmin)
     *
     * @version May - June 2015 release V3.3.0
     * @since 2.1.1
     * @access public
     * @param $timeZoneFk
     * @param bool $refreshCache
     * @return array
     * @throws CException
     */
    public static function getSystemStat($timeZoneFk, $refreshCache = false)
    {
        $stat = array();

        // $userTimeZone = TimezoneMysql::model()->findByPk($timeZoneFk)->name;

        $cacheKey = 'sys_main_stat';
        $expires = 24 * 61 * 60; //15 min

        if ($refreshCache) {
            $results = false;
        } else {
            //get from cache
            $results = Yii::app()->cache->get($cacheKey);
        }

        if ($results === false) {

            // Yii::log('Cache expired', CLogger::LEVEL_INFO, __METHOD__);
            //-------------------------- BUSINESS -----------------------------------
            //All businesses
            //$b = Business::model()->findAll();
            $stat['total_businesses'] = 0;//count($b);

            //Enabled businesses
            //$b = Business::model()->findAllByAttributes(array('enabled' => 1));
            $stat['enabled_businesses'] = 0; //count($b);

            //Stores visits
            $b = 0;
//            Yii::app()->db->createCommand()
//                ->select(array('sum(visits_counter)'))
//                ->from('visits_tracking')
//                ->where('(called_from="STORE_VIEW" OR called_from="STORE_LIST")')
//                ->queryScalar();
            $stat['stores_total_visits'] = $b;

            //Stores visits Today
            $b = 0;
//                Yii::app()->db->createCommand()
//                ->select(array('sum(visits_counter)'))
//                ->from('visits_tracking vt')
//                ->where('MONTH(CONVERT_TZ(vt.utc_created, @@global.time_zone, :userTz)) = MONTH(CONVERT_TZ(NOW(), @@global.time_zone, :userTz)) AND YEAR(CONVERT_TZ(vt.utc_created, @@global.time_zone, :userTz)) = YEAR(CONVERT_TZ(NOW(), @@global.time_zone, :userTz)) AND (vt.called_from="STORE_VIEW" OR vt.called_from="STORE_LIST")', [':userTz' => $userTimeZone])
//                ->queryScalar();
            $stat['stores_month_visits'] = $b;

            //Stores visits last week
            $b = 0;
//            Yii::app()->db->createCommand()
//                ->select(array('sum(visits_counter)'))
//                ->from('visits_tracking vt')
//                ->where('YEAR(CONVERT_TZ(vt.utc_created, @@global.time_zone, :userTz)) = YEAR(CONVERT_TZ(CURRENT_DATE - INTERVAL 1 MONTH, @@global.time_zone, :userTz)) AND MONTH(CONVERT_TZ(vt.utc_created, @@global.time_zone, :userTz)) = MONTH(CONVERT_TZ(CURRENT_DATE - INTERVAL 1 MONTH, @@global.time_zone, :userTz)) AND (vt.called_from="STORE_VIEW" OR vt.called_from="STORE_LIST")', [':userTz' => $userTimeZone])
//                ->queryScalar();
            $stat['stores_last_month_visits'] = $b;

            //-------------------------- BUSINESS -----------------------------------

            //-------------------------- USERS -----------------------------------
            //All users
//            $u = Yii::app()->db->createCommand()
//                ->select(array('count(1)'))
//                ->from('user u')
//                ->join('user_permission up', 'u.id=up.userid')
//                ->where("up.itemname='user'")
//                ->queryScalar();
            $stat['total_users'] = 0; //$u;

            //Enabled users
//            $u = Yii::app()->db->createCommand()
//                ->select(array('count(1)'))
//                ->from('user u')
//                ->join('user_permission up', 'u.id=up.userid')
//                ->where("up.itemname='user' AND u.enabled=1")
//                ->queryScalar();
            $stat['total_users_enabled'] = 0; //$u;

            //users with email
//            $u = Yii::app()->db->createCommand()
//                ->select(array('count(1)'))
//                ->from('user u')
//                ->join('user_permission up', 'u.id=up.userid')
//                ->where("(email <>'' AND email IS NOT NULL) AND up.itemname='user' ")
//                ->queryScalar();
            $stat['total_users_email'] = 0 ;//$u;

            //users with phones
//            $u = Yii::app()->db->createCommand()
//                ->select(array('count(1)'))
//                ->from('user u')
//                ->join('user_permission up', 'u.id=up.userid')
//                ->where("phone REGEXP '^-?[0-9]+$' AND up.itemname='user'")
//                ->queryScalar();
            $stat['total_users_phone'] = 0;// $u;

            //Users signed up today
//            $u = Yii::app()->db->createCommand()
//                ->select(array('count(1)'))
//                ->from('user u')
//                ->join('user_permission up', 'u.id=up.userid')
//                ->where('DATE(CONVERT_TZ(u.utc_created, @@global.time_zone, :userTz)) = DATE(CONVERT_TZ(NOW(), @@global.time_zone, :userTz)) AND up.itemname="user"', [':userTz' => $userTimeZone])
//                ->queryScalar();
            $stat['users_today'] = 0; // $u;

            //Users signed up current week
//            $u = Yii::app()->db->createCommand()
//                ->select(array('count(1)'))
//                ->from('user u')
//                ->join('user_permission up', 'u.id=up.userid')
//                ->where('YEARWEEK(CONVERT_TZ(u.utc_created, @@global.time_zone, :userTz),7) = YEARWEEK(CONVERT_TZ(NOW(), @@global.time_zone, :userTz),7) AND up.itemname="user"', [':userTz' => $userTimeZone])
//                ->queryScalar();
            $stat['users_current_week'] = 0; //$u;

            //Users signed up last week
//            $u = Yii::app()->db->createCommand()
//                ->select(array('count(1)'))
//                ->from('user u')
//                ->join('user_permission up', 'u.id=up.userid')
//                ->where('CONVERT_TZ(u.utc_created, @@global.time_zone, :userTz) >= DATE_SUB(DATE(CONVERT_TZ(NOW(), @@global.time_zone, :userTz)), INTERVAL WEEKDAY(CONVERT_TZ(NOW(), @@global.time_zone, :userTz))+7 DAY) AND CONVERT_TZ(u.utc_created, @@global.time_zone, :userTz) <  DATE_SUB(DATE(CONVERT_TZ(NOW(), @@global.time_zone, :userTz)), INTERVAL WEEKDAY(CONVERT_TZ(NOW(), @@global.time_zone, :userTz)) DAY) AND up.itemname="user"', [':userTz' => $userTimeZone])
//                ->queryScalar();

            $stat['users_last_week'] = 0; //$u;
            //-------------------------- USERS -----------------------------------

            //add in cache for 10 min
            Yii::app()->cache->set($cacheKey, $stat, $expires);

            $results = $stat;
        } else {
            // Yii::log('From Cache', CLogger::LEVEL_INFO, __METHOD__);
        }

        return $results;
    }

    /**
     * TIMEZONE - Globalization implementation
     * Collecting some statistics
     *
     * Used in : Website(SystemAdmin)
     *
     * @version May - June 2015 release V3.3.0
     * @since 2.1.1
     * @access public
     * @return array
     */
    public static function systemStatsCharts($userTimeZone, $refreshCache = false)
    {
        $cacheKey = 'sys_main_charts';
        $expires = 24 * 61 * 60; //15 min

        if ($refreshCache) {
            $results = false;
        } else {
            //get from cache
            $results = Yii::app()->cache->get($cacheKey);
        }

        if ($results === false) {
            // Yii::log('Cache expired', CLogger::LEVEL_INFO, __METHOD__);

            $users = [];// User::chartDataTotalMonthlyUsers();
            $followers =[]; // Followers::systemChartDataBusinessFollowers();
            $transactionsCurrentMonth =[];// Business::chartDataBusinessTransactioinsCurrentMonth($userTimeZone);
            $transactionsLastMonth = []; //Business::chartDataBusinessTransactioinsLastMonth($userTimeZone);
            $transactionsToday = []; //Business::chartDataBusinessTransactioinsToday($userTimeZone);

            $trxType = [2, 5, 10, 11, 7, 8, 9, 12];
            $trxSybType = [2, 4, 15, 16, 19, 33, 37, 38, 41, 42, 17, 32, 24, 25, 31, 30, 55, 109]; // not include 5, 18 - redeem
            $totalPtsRewarded = []; //Transaction::chartDataTotalPtsPunchesByBranch($trxType, $trxSybType);

            $trxType = [5, 7];
            $trxSybType = [5, 18, 43]; // include 5, 18 - redeem
            $totalPtsRedemed = []; //Transaction::chartDataTotalPtsPunchesByBranch($trxType, $trxSybType);

            //users with email
//            $u = Yii::app()->db->createCommand()
//                ->select(array('count(1)'))
//                ->from('user u')
//                ->join('user_permission up', 'u.id=up.userid')
//                ->where("(email <>'' OR email IS NOT NULL) AND (phone='' OR phone IS NULL) AND up.itemname='user'")
//                ->queryScalar();
            $emailPhoneUsers['total_users_email'] = 0; //$u;

            //users with phones
//            $u = Yii::app()->db->createCommand()
//                ->select(array('count(1)'))
//                ->from('user u')
//                ->join('user_permission up', 'u.id=up.userid')
//                ->where("(phone REGEXP '^-?[0-9]+$') AND (email='' OR email IS NULL) AND up.itemname='user' ")
//                ->queryScalar();
            $emailPhoneUsers['total_users_phone'] = 0; //$u;

            //users with phones and emails
//            $u = Yii::app()->db->createCommand()
//                ->select(array('count(1)'))
//                ->from('user u')
//                ->join('user_permission up', 'u.id=up.userid')
//                ->where("(phone REGEXP '^-?[0-9]+$') AND (email<>'' AND email IS NOT NULL) AND up.itemname='user' ")
//                ->queryScalar();
            $emailPhoneUsers['total_users_phone_email'] = 0; //$u;

            //users with phones and emails
//            $usersByPlatform = Yii::app()->db->createCommand()
//                ->select(array(
//                    'ps.name',
//                    'count(1) as total',
//                ))
//                ->from('user u')
//                ->join('user_permission up', 'u.id=up.userid')
//                ->join('platform_solution ps', 'u.platform_solution_fk=ps.platform_solution_pk AND up.itemname="user"')
//                ->group("platform_solution_fk")
//                ->queryAll();

            //total users
//            $u = Yii::app()->db->createCommand()
//                ->select(array('count(1)'))
//                ->from('user u')
//                ->join('user_permission up', 'u.id=up.userid')
//                ->where("up.itemname='user'")
//                ->queryScalar();
            $totalUsers =0 ;// $u;

            $results = [
                'users' => $users,
                'followers' => $followers,
                'transactions' => $transactionsLastMonth,
                'transactionsNow' => $transactionsCurrentMonth,
                'emailPhoneUsers' => $emailPhoneUsers,
                'usersByPlatform' => $usersByPlatform = 0,
                'totalUsers' => $totalUsers,
                'totalPtsRewarded' => $totalPtsRewarded,
                'totalPtsRedemed' => $totalPtsRedemed,
                'transactionsToday' => $transactionsToday,
            ];

            //add in cache for 60 min
            Yii::app()->cache->set($cacheKey, $results, $expires);
        } else {
            // Yii::log('From Cache', CLogger::LEVEL_INFO, __METHOD__);
        }

        return $results;
    }

    /**
     * Get the label for pts or punches
     *
     * Used in : Merchant App
     *
     * @version V4.7.0
     * <AUTHOR> David
     * @access public
     * @return string
     */
    public static function getPtsPunchsLabel(...$params) {
        $entityType = $params[0];
        $value = $params[1];

        if ($entityType == 1 || $entityType == 2) {
            if ($value == 1)
                return 'Pt';  //Yii::t('frontend', 'POINT');
            else
                return 'Pts'; //Yii::t('frontend', 'POINTS');
        } else {
            if ($value == 1)
                return Yii::t('frontend', 'PUNCHE');
            else
                return Yii::t('frontend', 'PUNCHES');
        }
    }

    /**
     * @param array $pointsLabel
     * @param mixed $value
     * @return string
     */
    public static function getPtsLabelForValue(array $pointsLabel, $value)
    {
        if ($value == 1)
            return $pointsLabel['singular_short'];
        else
            return $pointsLabel['plural_short'];
    }

    /**
     * Get a birthdate with format month day
     *
     * Used in : Website() Import contacts
     *
     * @version V4.7.0
     * <AUTHOR> David
     * @access public
     * @return string
     */
    public static function getBirthDateFormatMonthDay($birthdate) {
        $birthDateFormat = '';
        if ($birthdate) {
            if ($birthdate != '0000-00-00')
                $birthDateFormat = date_format(new DateTime($birthdate), 'F d');
        }

        return $birthDateFormat;
    }

    /**
     * Get a birthdate changing the current year for 1800
     *
     * Used in : Website() Import contacts
     *
     * @version V4.7.0
     * <AUTHOR> David
     * @access public
     * @return string
     */
    public static function getLangAbreviation($langId) {
        if ($langId == '1')
            return 'en';
        else if ($langId == '2')
            return 'fr';
        else if ($langId == '3')
            return 'es';
        else if ($langId == '4')
            return 'pt';
        else
            return 'en';
    }

    /**
     * Get a birthdate changing the current year for 1800
     *
     * Used in : Website() Import contacts
     *
     * @version V4.7.0
     * <AUTHOR> David
     * @access public
     * @return string
     */
    public static function getBirthDate($birthdate) {
        if ($birthdate) {
            if (strpos((string)$birthdate??'', "-00"))
                return null;
            else {
                $formatBirthDate = Utils::getDateFormatFromString($birthdate);
                $year = date("Y");
                if (strpos((string)$formatBirthDate, $year) >= 0) {
                    $newBirthDate = str_replace($year,"1800",$formatBirthDate);
                    return $newBirthDate;
                } else
                    return $formatBirthDate;
            }
        } else
            return null;
    }

    /**
     * Get a date format from string
     *
     * Used in : Website()
     *
     * @version V4.7.0
     * <AUTHOR> David
     * @access public
     * @return string
     */
    public static function getDateFormatFromString($date){
        if ($date) {
            try {
                $date = new DateTime($date);
                return $date->format('Y-m-d');
            } catch (Exception $e) {
                return null;
            }
        } else
            return null;
    }

    /**
     * Make Links in a text clickable
     *
     * Used in : Website()
     *
     * @version January 2017 release V4.6.0
     * @since 4.6.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function makeLinksClickable($text){
        return preg_replace('!(((f|ht)tp(s)?://)[-a-zA-Zа-яА-Я()0-9@:%_+.~#?&;//=]+)!i', '<a href="$1" class="normal-blue-link" target="_blank" rel="noreferrer">$1</a>', $text);
    }

    /**
     * Add some fields for Gift card purchases on the tablet
     *
     * Used in : Website()
     *
     * @version January 2016 release V3.5.0
     * @since 3.5.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function prepareGiftCardsPurchasedTablet($arr)
    {
        foreach ($arr as $key => $value) {
            $arr[$key]['offer_image'] = 1;
            $arr[$key]['offer_image1_large'] = '/images/new-gift-card-blueborder.png';
            $arr[$key]['offer_title'] = Yii::t('backend', 'BACKEND_DASHBOARD_PURCHASE_IN_STORE');
            $arr[$key]['offer_description'] = Yii::t('backend', 'BACKEND_DASHBOARD_PURCHASE_IN_STORE');
        }

        return $arr;
    }

    /**
     * Merge and sort two arrays for Gift Cards
     *
     * Used in : Website()
     *
     * @version January 2016 release V3.5.0
     * @since 3.5.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function mergeAndSortPurchasedGiftCards($arr1, $arr2)
    {
        //merge paypal purchase and tablet purchase
        $merged = array_merge($arr1, $arr2);

        // Obtain a list of columns
        foreach ($merged as $key => $row) {
            $total[$key] = $row['total'];
            $unique[$key] = $row['unique_customers'];
        }

        // Sort the data with volume descending, edition ascending
        // Add $data as the last parameter, to sort by the common key
        array_multisort($total, SORT_DESC, $unique, SORT_ASC, $merged);

        return $merged;
    }

    /**
     * Helper function to merge array recursively
     *
     * Used in : Website(Business Admin)
     *
     * @version December 2016 release V4.3.0
     * @since 4.3.0
     * <AUTHOR>
     * @access public
     * @param mixed $array1
     * @param mixed $array2
     * @return mixed
     */
    public static function array_merge_recursive_distinct(array $array1, array $array2)
    {
        $merged = $array1;

        foreach ($array2 as $key => $value) {
            if (is_array($value) && isset($merged[$key]) && is_array($merged[$key])) {
                $merged[$key] = self::array_merge_recursive_distinct($merged[$key], $value);
            } else {
                $merged[$key] = $value;
            }
        }

        return $merged;
    }

    /**
     * Leg message from worker
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.1.0
     * @since 4.1.0
     * <AUTHOR>
     * @access public
     * @return void
     */
    public static function workerLog($message)
    {
        if (php_sapi_name() == "cli") {
            print_r('['.date(DATE_ATOM).'] '. $message); echo PHP_EOL;
        }
    }

    /**
     * Returns a GUIDv4 string
     *
     * Uses the best cryptographically secure method
     * for all supported pltforms with fallback to an older,
     * less secure version.
     *
     * @version October 2016 release V4.2.0
     * @since 4.2.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function guidv4()
    {
        if (function_exists('com_create_guid') === true) {
            return trim((string)com_create_guid(), '{}');
        }

        $data = openssl_random_pseudo_bytes(16);
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40); // set version to 0100
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80); // set bits 6-7 to 10
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }

    /**
     * Getting the current status from UserStatusManager
     * If new business admin logs in first time Redirect to create password page if the password is correct
     *
     * Used on : Merchant->Login (Merchant App/Web App)
     *
     * @version V4.2.0
     * @param $userId
     * @param $inputPassword
     * @param $timezoneId
     * @param $platformSolutionFk
     * @return bool
     * @throws CDbException
     */

    public static function checkCurrentUserStatus($userId, $inputPassword, $timezoneId, $platformSolutionFk)
    {
        if (!$userId) {
            return false;
        }

        //loading the user record
        $userModel = User::model()->findByPk($userId);
        //getting the current status
        $userCurrentStatus = UserStatusManager::getCurrentUserStatus($userId);

        if ($userCurrentStatus == 15 && $userModel->enabled == 0 && User::passwordVerify($inputPassword, $userModel->password)) //Business Signup and not Active
        {
            $randomToken = User::generateRandomString(60);

            $userModel->utc_link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
            $userModel->passwordreset = $randomToken;
            $userModel->update('passwordreset, link_expires');

            return true;

        } elseif ($userCurrentStatus == 27 && $userModel->enabled == 0 && User::passwordVerify($inputPassword, $userModel->password)) //Business Signup and not Active
        {
            $userModel->enabled = 1;
            $userModel->update('enabled');

            $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 28, '', '', 1, $timezoneId); //ACTIVE_BUSINESS_SELFSIGNUP_TEMP_PSW

            return true;
        }
    }

    /**
     * Send email with template
     * IMPORTANT: This function has to be used for ALL platforms
     * Adding attachment (3.4.0)
     *
     * Used in : Website - SysAdmin(Business Contacts list: cronJob) / Merchant Admin (employee creation)
     * @version Mar - Apr 2015 release V3.2.5
     * <AUTHOR>
     * @param string $emailView - view path
     * @param string $layout - layout path
     * @param string $emailSubject
     * @param string $sendTo
     * @param array $data  - array of variables used in email template
     * @param string $viewPath - application.views.mail.en
     * @param string $attachment  - file path to attach
     * @param $businessId
     * @param $userId
     * @param bool $withDrawCredit
     * @param bool $dbTransactionRequired
     * @param null|MessagesQueue $messageQueue
     * @param string $fromExtra  - From string that will be shown in the email
     * @return array
     */
    public static function sendEmailWithCustomTemplate($emailBody, $emailSubject, $sendTo, $fromExtra = null, $businessId  = null, $userId = null, $withDrawCredit = false)
    {
        $transaction = Yii::app()->db->getCurrentTransaction();
        $requireTransaction = false;
        if ($transaction === null && $withDrawCredit && isset($businessId) && ($businessId != KNG_BUSINESS_ID)) {
            $transaction = Yii::app()->db->beginTransaction();
            $requireTransaction = true;
        }

        try {
            if(FakeContactRule::isFakeEmail($sendTo)) {
                Utils::logMessage('Fake email address: ' . $sendTo, __METHOD__, CLogger::LEVEL_INFO);
                throw new Exception('Fake email address');
            }

            if ($withDrawCredit && isset($businessId) && ($businessId != KNG_BUSINESS_ID)) {
                $transactionalMessage = new TransactionalMessage('email', $businessId, $withDrawCredit);
                if (!$transactionalMessage->isValid()) {
                    if ($requireTransaction) {
                        $transaction->rollback();
                    }

                    return [
                        'status' => 'NOT_OK',
                        'message_status' => 'failed',
                        'error' => Yii::t('backend', 'BACKEND_MKTCAMPAIGN_NOT_ENOUGH_CREDIT_TITLE'),
                        'message' => Yii::t('frontend', 'ERROR_SENDING_EMAIL') . Yii::t('backend', 'BACKEND_MKTCAMPAIGN_NOT_ENOUGH_CREDIT_TITLE'),
                        'errors' => Yii::t('backend', 'BACKEND_MKTCAMPAIGN_NOT_ENOUGH_CREDIT_TITLE'),
                    ];
                }

                $messageQueue = MessagesQueue::createWithStatusSent([
                    'send_to' => $sendTo,
                    'cell_email' => MESSAGE_SEND_BY_EMAIL,
                    'business_fk' => $businessId,
                    'user_fk' => $userId,
                    'params' => [
                        'emailBody' => $emailBody,
                        'emailSubject' => $emailSubject,
                        'businessId' => $businessId,
                        'withdraw_credit_flag' => $withDrawCredit,
                    ],
                ]);
                Utils::logMessage('New message_queue: ' . $messageQueue->message_queue_pk, __METHOD__, CLogger::LEVEL_INFO);

                $transactionalMessage->createTransactionalMessage($messageQueue->message_queue_pk);
            }

            $mail = new YiiMailer();
            $mail->IsSMTP(); // Set mailer to use SMTP
            $mail->clearLayout();
            $mail->setBody($emailBody);
            $mail->setReplyTo(Yii::app()->params['emailout']);
            $emailConfig = Utils::getTransactionalEmailConfig($businessId ?? null);
            if ($fromExtra == null) {
                $fromExtra = $emailConfig->from_name;
            }
            $mail->setFrom($emailConfig->from_email, $fromExtra);
            $mail->setTo($sendTo);
            $mail->setSubject($emailSubject);

            if ($mail->send()) {
                $result = ['status' => 'OK', 'message_status' => 'sent', 'message' => 'Email Sent'];
            } else {
                Yii::log('Email not sent reason: ' . $mail->getError() . ' sendTo: ' . json_encode($sendTo), CLogger::LEVEL_ERROR, __METHOD__);
                $result = ['status' => 'NOT_OK', 'message_status' => 'failed', 'error' => $mail->getError(), 'message' => Yii::t('frontend', 'ERROR_SENDING_EMAIL') . $mail->getError(), 'errors' => $mail->getError()];
            }
            if ($requireTransaction) {
                $transaction->commit();
            }

        } catch (\Exception $e) {
            if ($requireTransaction) {
                $transaction->rollback();
            }
            Yii::log('Exception reason: ' . $e->getTraceAsString() . ' sendTo: ' . json_encode($sendTo), CLogger::LEVEL_ERROR, __METHOD__);
            $result = [
                'status' => 'NOT_OK',
                'message_status' => 'failed',
                'message' => $e->getMessage(),
                'error' => $e->getMessage(),
                'errors' => $e->getMessage(),
            ];
        }
        return $result;
    }

    /**
     * Getting latitude and longitude by address from google maps
     *
     * Used on : Merchant Signup (Merchant App)
     *
     * <AUTHOR> David
     * @version V4.2.0
     * @since 4.2.0
     * @access private
     * @return array
     */

    public static function getLatLongByAddress($AddressInfo)
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $stateName = '';
        $countryName = '';
        $cityName = '';
        $zipcode = '';

        if ($AddressInfo['country'] != '') {
            $countryName = $AddressInfo['country'];
        }

        if ($AddressInfo['province'] != '') {
            $stateName = $AddressInfo['province'];
        }

        if ($AddressInfo['city'] != '') {
            $cityName = $AddressInfo['city'];
        }

        if ($AddressInfo['zipcode'] != '') {
            $zipcode = ', ' . $AddressInfo['zipcode'];
        }

        $address = $AddressInfo['address'] . ', ' . $cityName . ', ' . $stateName . ', ' . $countryName . $zipcode;

        //Yii::log('Getting lat/long from address: '.$address, CLogger::LEVEL_INFO, $platformName.'_'.__METHOD__);
        //$prepAddr = str_replace(' ','+',$address);
        return Utils::getLongLatFromAddress($address, $platformName);
    }

    /**
     * Converting hex color to RGB
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.1.0
     * @since 4.1.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function hex2rgb($colour, $opacity = 0.6)
    {
        if (!$colour) {
            return false;
        }

        if ($colour[0] == '#') {
            $colour = substr((string)$colour, 1);
        }
        if (strlen((string)$colour) == 6) {
            list($r, $g, $b) = array($colour[0] . $colour[1], $colour[2] . $colour[3], $colour[4] . $colour[5]);
        } elseif (strlen((string)$colour) == 3) {
            list($r, $g, $b) = array($colour[0] . $colour[0], $colour[1] . $colour[1], $colour[2] . $colour[2]);
        } else {
            return false;
        }

        $r = hexdec($r);
        $g = hexdec($g);
        $b = hexdec($b);
        $rgb = [$r, $g, $b];

        return 'rgba(' . implode(",", $rgb) . ',' . $opacity . ')';
    }

    /**
     * Run manually from a web page commands
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.1.0
     * @since 4.1.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function runImportProductsCommand($businessId)
    {
        $commandPath = Yii::app()->getBasePath() . DIRECTORY_SEPARATOR . 'commands';
        $runner = new CConsoleCommandRunner();
        $runner->addCommands($commandPath);
        $commandPath = Yii::getFrameworkPath() . DIRECTORY_SEPARATOR . 'cli' . DIRECTORY_SEPARATOR . 'commands';
        $runner->addCommands($commandPath);
        $args = array('yiic', 'processpossale', $businessId, '--interactive=0');
        ob_start();
        $runner->run($args);
        echo htmlentities(ob_get_clean(), null, Yii::app()->charset);
    }

    /**
     * Crate folder for images
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.1.0
     * @since 4.1.0
     * <AUTHOR>
     * @access public
     * @return void
     */
    public static function createFolder($path, $platformName)
    {
        $user = Yii::app()->params['envParam'] == 'PROD' ? 'nginx' : 'apache';
        if (!is_dir($path . '/')) {
            // changed to is_dir to keep syntax within context
            if (!mkdir($path . '/', 0777, true)) {
                Yii::log("Failed to create directory: $path", CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
                throw new Exception("Failed to create directory: $path", 1);
            }

            $filemode = 0750;//Everything for owner, read and execute for owner's group
            // $filemode = 0755;//Everything for owner, read and execute for others

            chown($path, $user);
            chgrp($path, $user);
            chmod($path, $filemode);

            // $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($path));
            // foreach($iterator as $item) {
            //     chmod($item, $filemode);
            // }
        }
    }

    /**
     * Get days of week list
     *
     * Used in : Website()
     *
     * @version June 2016 release V4.1.0
     * @since 4.1.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function getDaysOfWeek()
    {
        return [
            Yii::t('backend', 'SUNDAY'),
            Yii::t('backend', 'MONDAY'),
            Yii::t('backend', 'TUESDAY'),
            Yii::t('backend', 'WEDNESDAY'),
            Yii::t('backend', 'THURSDAY'),
            Yii::t('backend', 'FRIDAY'),
            Yii::t('backend', 'SATURDAY'),
        ];
    }

    /**
     * Getting Excel column letter from number
     *
     * Used in : Website()
     *
     * @version March 2016 release V4.1.0
     * @since 4.1.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function getExcelColumnFromNumber($num)
    {
        $numeric = ($num - 1) % 26;
        $letter = chr(65 + $numeric);
        $num2 = intval(($num - 1) / 26);
        if ($num2 > 0) {
            return self::getNameFromNumber($num2) . $letter;
        } else {
            return $letter;
        }
    }

    public static function getNameFromNumber($num)
    {
        $columnName = '';

        while ($num > 0) {
            $remainder = ($num - 1) % 26;
            $columnName = chr(65 + $remainder) . $columnName;
            $num = intval(($num - $remainder) / 26);
        }

        return $columnName;
    }
    /**
     * Getting exported transactions filename
     *
     * Used in : Website()
     *
     * @version March 2016 release V4.0.0
     * @since 4.0.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function getExportTrxFilename($firstDate, $lastDate)
    {
        $fileName = 'Transactions.xlsx';

        $d1 = new DateTime($firstDate);
        $d2 = new DateTime($lastDate);
        $interval = $d1->diff($d2);
        $days = (int) ($interval->days);

        if ($days == 0) {
            $fileName = 'Transactions-' . $d1->format('Y-m-d') . '.xlsx';
        } else {
            $fileName = 'Transactions-' . $d1->format('Y-m-d') . '-' . $d2->format('Y-m-d') . '.xlsx';
        }

        return $fileName;
    }

    /**
     * Getting original image
     *
     * Used in : Website()
     *
     * @version March 2016 release V4.0.0
     * @since 4.0.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function getOriginalImg($img)
    {
        if (!$img) {
            return null;
        }

        $imgInfo = pathinfo($img);

        if (strpos((string)$imgInfo['filename'], 'orig_') === false) {
            $prefix = 'orig_';
        } else {
            return $img;
        }

        if (strpos((string)$img, 'nocover')) {
            return null;
        }

        // list($origW, $origH) = getimagesize(realpath(ltrim((string)$originalImg, '/')));

        $img = $imgInfo['dirname'] . '/' . $prefix . $imgInfo['filename'] . '.' . $imgInfo['extension'];
        return '/' . ltrim((string)$img, '/');
    }

    /**
     * Cropping branch cover photo
     *
     * Used in : Website()
     *
     * @version March 2016 release V4.0.0
     * @since 4.0.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function cropImage($branch, $total, $i, $recrop = false)
    {
        $busBranchId = $branch->business_branch_pk;

        if ($busBranchId <= 1) {
            return;
        }

        try {
            if (!$branch->cover_image) {
                echo $i . '/' . $total . ' busBranchId=' . $busBranchId . ' status: no cover';
                echo PHP_EOL;
                return;
            }

            $imgInfo = pathinfo($branch->cover_image);
            $newName = time() . uniqid('_', true) . '.' . $imgInfo['extension'];

            $targetFolder = 'photos/business/' . $busBranchId . '/cover';
            $newfile = 'photos/business/' . $busBranchId . '/cover/' . $newName;

            if (strpos((string)$imgInfo['filename'], 'orig_') === false) {
                $prefix = 'orig_';
            } else {
                $prefix = '';
            }

            $originalImg = $imgInfo['dirname'] . '/' . $prefix . $imgInfo['filename'] . '.' . $imgInfo['extension'];

            if (strpos((string)$originalImg, 'nocover')) {
                echo $i . '/' . $total . ' busBranchId=' . $busBranchId . ' status: default cover';
                echo PHP_EOL;
                return;
            }

            $branch->cover_image = $originalImg;

            copy(ltrim((string)$originalImg, '/'), $newfile);

            list($origW, $origH) = getimagesize(realpath(ltrim((string)$originalImg, '/')));

            $maxWidth = 1280;
            $maxHeight = 1056;
            $minWidth = 1280;
            $minHeight = 1056;
            $subfolder = 'cover';
            $targ_w = 1280;
            $targ_h = 1056; //large
            $maxLWidth = 1280;
            $maxLHeight = 1056; //large
            $maxMWidth = 640;
            $maxMHeight = 528; //medium
            $maxTWidth = 320;
            $maxTHeight = 264; //small

            $arr = array(
                'uploaddir' => $targetFolder,
                'tempdir' => $targetFolder,
                'new_uploadfile' => $newfile,
                'temp_uploadfile' => $newfile,
                'thumb' => false,
                'width' => $maxWidth,
                'height' => $maxHeight,
                'x' => 0,
                'y' => 0,
            );

            $thumb = Utils::saveResizeImageAspect($arr);

            $imagePath = $newfile;
            $imagine = new Imagine\Imagick\Imagine();
            $orig = $imagine->open(realpath($imagePath));
            $profile = Imagine\Image\Profile::fromPath('images/config/sRGB_IEC61966-2-1_no_black_scaling.icc');

            $largeFolder = 'photos/business/' . $busBranchId . '/' . $subfolder . '/large';
            $mediumFolder = 'photos/business/' . $busBranchId . '/' . $subfolder . '/medium';
            $thumbnailFolder = 'photos/business/' . $busBranchId . '/' . $subfolder . '/thumbnail';

            list($width, $height) = getimagesize($newfile);

            //centering the box
            $x = ($width - $maxLWidth) / 2;
            $x = ($x < 0) ? 0 : $x;
            $y = ($height - $maxLHeight) / 2;
            $y = ($y < 0) ? 0 : $y;

            $xm = ($width - $maxMWidth) / 2;
            $xm = ($xm < 0) ? 0 : $xm;
            $ym = ($height - $maxMHeight) / 2;
            $ym = ($ym < 0) ? 0 : $ym;

            $xt = ($width - $maxTWidth) / 2;
            $xt = ($xt < 0) ? 0 : $xt;
            $yt = ($height - $maxTHeight) / 2;
            $yt = ($yt < 0) ? 0 : $yt;

            if ($recrop) {
                $x = 0;
                $xm = 0;
                $xt = 0;
                $y = 0;
                $ym = 0;
                $yt = 0;
            }

            $boxL = new Imagine\Image\Box($maxLWidth, $maxLHeight);
            $boxM = new Imagine\Image\Box($maxMWidth, $maxMHeight);
            $boxT = new Imagine\Image\Box($maxTWidth, $maxTHeight);
            $pointL = new Imagine\Image\Point($x, $y);
            $pointM = new Imagine\Image\Point($xm, $ym);
            $pointT = new Imagine\Image\Point($xt, $yt);

            $parts = explode('/', $imagePath);
            $lastPart = end($parts);
            $image_large = $largeFolder . '/' . $lastPart;
            $image_medium = $mediumFolder . '/' . $lastPart;
            $image_thumbnail = $thumbnailFolder . '/' . $lastPart;

            $largeFullPath = YiiBase::getPathOfAlias('webroot') . '/' . $image_large;
            $medFullPath = YiiBase::getPathOfAlias('webroot') . '/' . $image_medium;
            $thumbFullPath = YiiBase::getPathOfAlias('webroot') . '/' . $image_thumbnail;

            $image = $orig;

            $image->crop($pointL, $boxL)->profile($profile)->save($largeFullPath);
            copy(ltrim((string)$largeFullPath, '/'), $image_medium);
            copy(ltrim((string)$largeFullPath, '/'), $image_thumbnail);
            // $image->crop($pointM, $boxM)->profile($profile)->save($medFullPath);
            // $image->crop($pointT, $boxT)->profile($profile)->save($thumbFullPath);

            Offer::resizeImageWithSizeLimit($image_large, $maxLWidth, $maxLHeight, $kB = 500);
            Offer::resizeImageWithSizeLimit($image_medium, $maxMWidth, $maxMHeight, $kB = 100);
            Offer::resizeImageWithSizeLimit($image_thumbnail, $maxTWidth, $maxTHeight, $kB = 50);

            $branch->cover_image = '/' . ltrim((string)$originalImg, '/');
            $branch->cover_image_large = '/' . ltrim((string)$image_large, '/');
            $branch->cover_image_medium = '/' . ltrim((string)$image_medium, '/');
            $branch->cover_image_thumbnail = '/' . ltrim((string)$image_thumbnail, '/');
            $branch->save(false);

            $image->__destruct();

            $memUsage = Utils::convertMemUsage(memory_get_usage(true));
            $virtMem = Utils::getVirtualMemoryTaken();
            echo $i . '/' . $total . ' busBranchId=' . $busBranchId . ' status: processed' . ' usage=' . $memUsage . ' virtMem=' . $virtMem;
            echo PHP_EOL;

            //recrop only once
            if ($recrop) {
                return;
            }
        } catch (Exception $e) {
            echo $i . '/' . $total . ' busBranchId=' . $busBranchId . ' status: recrop';
            echo PHP_EOL;
            Yii::log('busBranchId=' . $busBranchId . ' error: ' . $e->getMessage(), CLogger::LEVEL_WARNING, 'WEBSITE' . '_' . __FUNCTION__);
            Utils::cropImage($branch, $total, $i, $recrop = true);
        }
    }

    /**
     * Convert Memory usage in MB, KB etc
     *
     * Used in : Website()
     *
     * @version March 2016 release V4.0.0
     * @since 4.0.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function convertMemUsage($size)
    {
        $unit = array('b', 'kb', 'mb', 'gb', 'tb', 'pb');
        return @round($size / 1024 ** ($i = floor(log($size, 1024))), 2) . ' ' . $unit[$i];
    }

    /**
     * Get virtual memory uage. Works only on Linux
     *
     * Used in : Website()
     *
     * @version March 2016 release V4.0.0
     * @since 4.0.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function getVirtualMemoryTaken()
    {
        if (strtoupper((string)substr((string)PHP_OS, 0, 3)) === 'WIN') {
            return 0;
        } else {
            $pid = getmypid();
            $a = `ps -p $pid v | awk 'END{print $9}'`;
            return $a * 1;
        }
    }

    /**
     * Normalizing url
     *
     * Used in : Website()
     *
     * @version March 2016 release V4.0.0
     * @since 4.0.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function normalizeUrl($url, $scheme = 'https://')
    {
        $url = strtolower((string)trim((string)$url));

        if ($ret = parse_url($url)) {
            if (!isset($ret["scheme"])) {
                $url = $scheme . $url;
            }
        }

        return $url;
    }

    /**
     * Getting offer expires wording: expired, expires in
     *
     * Used in : Website()
     *
     * @version January 2016 release V3.5.0
     * @since 3.5.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function getOfferExpiredWord($date)
    {
        $date1 = new DateTime($date ?? '');
        $date2 = new DateTime("now");
        $interval = $date1->diff($date2);
        $days = (int)($interval->days);

        if ($date1 < $date2) {
            return Yii::t('frontend', 'EXPIRED');
        } elseif ($days <= 20 && $days > 1) {
            return Yii::t('frontend', 'FRONTEND_OFFERS_EXPIRES_IN_PLURAL', array('{days}' => $days));
        } elseif ($days == 1) {
            return Yii::t('frontend', 'FRONTEND_OFFERS_EXPIRES_IN_SINGULAR', array('{days}' => $days));
        } elseif ($days == 0) {
            return Yii::t('frontend', 'FRONTEND_OFFERS_EXPIRES_TODAY');
        } else {
            return '';
        }
    }

    /**
     * Getting up or down word depending of the two number
     *
     * Used in : Website(Weekly Report)
     *
     * @version January 2016 release V3.5.0
     * @since 3.5.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function getUpDownWord($a, $b)
    {
        if ($a > $b) {
            return 'up';
        } elseif ($a < $b) {
            return 'down';
        } else {
            return '';
        }
    }

    /**
     * Calculating percentage change
     *
     * Used in : Website(CronJob)
     *
     * @version January 2016 release V3.5.0
     * @since 3.5.0
     * <AUTHOR>
     * @access public
     * @return float
     */
    public static function percentageChange($first, $last)
    {
        if ($first == 0 && $last == 0) {
            return 0;
        } elseif ($first == 0) {
            return 100;
        } else {
            return ($last - $first) / $first * 100;
        }
        //(31 - 27)/27*100;//14.81
    }

    /**
     * Getting SMS status for a specific message by it ID - Twilio
     *
     * Used on : Website / Smartphone / Merchant Tablet app
     * @version January 2016 release V3.5.0
     * @since 3.5.0
     * <AUTHOR>
     * @param string $sid - Message unique id
     * @param string $platformName
     * @param mixed $smsVendorId
     * @access public
     * @return array
     */
    public static function getSMSStatus(string $sid, string $platformName, $smsVendorId = null): array
    {
        $AccountSid = Yii::app()->params['TwillioAccountSid'];
        $AuthToken = Yii::app()->params['TwillioAuthToken'];

        // Twilio SubAccount
        if ($smsVendorId) {
            $smsVendor = SmsVendor::model()->findByPk($smsVendorId);
            if ($smsVendor && $smsVendor->client_id && $smsVendor->client_secret) {
                $AccountSid = $smsVendor->client_id;
                $AuthToken = $smsVendor->client_secret;
            }
        }

        try
        {
            $client = new Twilio\Rest\Client($AccountSid, $AuthToken);
            $message = $client->messages($sid)->fetch();

            return [
                'status' => 'OK', 'message_status' => $message->status, 'error' => $message->errorMessage,
                'error_message' => $message->errorMessage, 'error_code' => $message->errorCode,
            ];

        } catch (\Twilio\Exceptions\RestException $e) {
            Yii::log('Services_Twilio_RestException sendTo=' . $sid . ' error=' . $e, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            return ['status' => 'NOT_OK', 'message_status' => 'failed', 'error' => (array) $e,
                'error_message' => $e->getMessage(), 'error_code' => $e->getCode(),
            ];
        } catch (Exception $e) {
            Yii::log('Exception sendTo=' . $sid . ' error=' . $e, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            return  ['status' => 'NOT_OK', 'message_status' => 'failed', 'error' => (array) $e,
                'error_message' => $e->getMessage(), 'error_code' => $e->getCode(),
            ];
        }
    }

    /**
     * Getting recipient name or first name of username
     *
     * Used in : Website()
     *
     * @version January 2016 release V3.5.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function getRecipientName($isNewUser, $recipientName, $userModel)
    {
        if (!$isNewUser) {
            if ($recipientName != '' && $recipientName != null) //check for first name
            {
                return ucfirst((string)$recipientName);
            } elseif ($userModel->first != '' && $userModel->first != null) {
                return ucfirst((string)$userModel->first);
            }

        } else {
            return $recipientName; //returns without any changes empty or what has been entered in input
        }
    }

    /**
     * User signup for Transfer Points: sending verification email or SMS
     * Include shortUrlKg as a main function for shortening, timezone
     * Gift points email template changes (3.4.1)
     * Schedule gift card (3.5.0)
     *
     * Used in Website(Signup), Smartphone (iphone/android), Merchant Tablet app
     * @version January 2016 release V3.5.0
     * @since   3.3.1
     * <AUTHOR>
     * @param   object $giftCardQueue
     * @param   object $userModel
     * @param   array $phoneEmail
     * @param   boolean $isNewUser
     * @param   string $platformName
     * @access  public
     * @return  array
     */
    public static function sendTransferPointsEmailSMS(GiftCardQueue $giftCardQueue, $userModel, $phoneEmail, $isNewUser, $platformName)
    {
        Yii::log('ARGS=' . CJSON::encode(func_get_args()), CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

        $giftCardImage = null;

        $mainBranch = BusinessBranch::model()->findByPk($giftCardQueue->business_branch_fk);
        $businessId = $mainBranch->business_fk;
        $businessName = stripslashes((string)$mainBranch->name??'');
        $currency = Currency::getCurrencySymbolByBranchId($mainBranch['business_branch_pk'], $platformName);
        $currencySymbol = $currency['currencySymbol'];
        $recipientName = $giftCardQueue->recipient_name;
        $senderName = $giftCardQueue->sender_name;
        $strLang = Language::model()->findByPk($giftCardQueue->recipient_lang_fk)->abreviation;
        $notifReqFk = $giftCardQueue->notification_requester_fk;
        $offerId = $giftCardQueue->offer_fk;
        $timeZoneId = $giftCardQueue->timezone_mysql_fk;
        $notificationPk = GiftCardQueue::getGiftCardNotificationIdReceiver($giftCardQueue->gift_card_queue_pk);
        $denominator = Currency::getCurrencyDenominator($businessId);
        $businessModel = Business::model()->findByPk($businessId);

        if ($notifReqFk == NOTIF_REQ_GIFTCARD_AMOUNT_TRANSFER_RECEIVER_TITLE) {
            $amount = $giftCardQueue->amount;
        /*} else if ($notifReqFk == NOTIF_REQ_TRANSFER_POINTS_RECEIVER_TITLE) {
            $amount = $giftCardQueue->gift_card_value;*/
        } else {
            $amount = round($giftCardQueue->gift_card_value / $denominator, 2);
        }

        if (in_array($giftCardQueue->email_sms_status_fk, [6, 7])) {
            throw new Exception("The Gift Card has been accepted or recalled", 1);
        }

        $country = Countries::model()->findByPk($userModel->country_phone_fk);

        $randomToken = User::model()->generateRandomString(60);

        ActivationToken::create([
            'id' => $randomToken,
            'user_fk' => $userModel->id,
            'utc_created' => date("Y-m-d H:i:s"),
            'email' => $phoneEmail['isEmail'] ? $userModel->email : null,
            'phone' => $phoneEmail['isPhone'] ? $userModel->phone : null,
            'intent' => 'gift_card_signup_welcome',
            'utc_link_expires' => date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES)),
            'country_code' => $country->code ?? null,
        ]);

        $uri = BusinessProfile::getWebAppUri($businessId);
        if (!$uri) {
            $uri = rtrim((string)Yii::app()->params['membersUrl'], '/');
        }

        $uri .= '/public/acceptGiftCard';

        $emailLink = $uri . '?' . http_build_query([
                'u' => bin2hex($userModel->user_uid),
                'n' => $notificationPk,
            ]);

        $phoneLink = $emailLink;

        if ($isNewUser) {
            $randomPin = Utils::generatePin();

            //saving token and expiry date in user model
//            $userModel->passwordreset = $randomToken;
            $userModel->enabled = 1;
            $userModel->password = '';
            $userModel->pin_permission = User::cryptPassword($randomPin);
            $userModel->pin_enabled = 1;
            $userModel->utc_link_expires = date("Y-m-d H:i:s", strtotime((string)ACTIVATION_LINK_EXPIRES));
            $userModel->timezone_mysql_fk = $giftCardQueue->timezone_mysql_fk;
            $userModel->save();

            UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 35, '', '', $userModel->platform_solution_fk, $giftCardQueue->timezone_mysql_fk); //PIN_CODE_AUTO_GENERATED

//            $emailLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 0, 'e' => 1, 'g' => 1, 'o' => $offerId, 'b' => $businessId, 'l' => $strLang, 'a' => $amount, 'n' => $notificationPk]); //c-cell phone , e- email, g - from gift card purchase
//            $phoneLink = Yii::app()->createAbsoluteUrl('site/verifyAccount', ['q' => $randomToken, 'p' => $randomPin, 'c' => 1, 'e' => 0, 'g' => 1, 'o' => $offerId, 'b' => $businessId, 'l' => $strLang, 'a' => $amount, 'n' => $notificationPk]); //c-cell phone , e- email, g - from gift card purchase

            //Yii::log('New User', CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);
        } else {
            //Yii::log('User exists', CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);
//            $emailLink = Yii::app()->createAbsoluteUrl('site/acceptGiftCard', ['u' => $userModel->id, 'c' => 0, 'e' => 1, 'g' => 1, 'o' => $offerId, 'b' => $businessId, 'l' => $strLang, 'a' => $amount, 'n' => $notificationPk]); //c-cell phone , e- email, g - from gift card purchase
//            $phoneLink = Yii::app()->createAbsoluteUrl('site/acceptGiftCard', ['u' => $userModel->id, 'c' => 1, 'e' => 0, 'g' => 1, 'o' => $offerId, 'b' => $businessId, 'l' => $strLang, 'a' => $amount, 'n' => $notificationPk]); //c-cell phone , e- email, g - from gift card purchase

            $randomPin = '';
        }

        $senderName1 = ($senderName != '') ? $senderName : Yii::t('frontend', 'FRONTEND_A_FRIEND', array(), null, $strLang);

        //getting Recipient Name or first name or username
        $recipientName = Utils::getRecipientName($isNewUser, $recipientName, $userModel);

        if ($giftCardQueue->offer_fk != null) {
            $giftCardOffer = Offer::model()->findByPk($giftCardQueue->offer_fk);
            $giftCardImage = $giftCardOffer['offer_image' . $giftCardOffer['offer_image'] . '_medium'];
        }

        $emailData = [
            'username' => $userModel->email,
            'recipientName' => $recipientName,
            'pincode' => $randomPin,
            'link' => $emailLink,
            'isNewUser' => $isNewUser,
            'notifReqFk' => $notifReqFk,
            'senderName' => $senderName1,
            'transferUnits' => $giftCardQueue->gift_card_value,
            'businessName' => $businessName,
            'amount' => $amount,
            'currencySymbol' => $currencySymbol,
            'businessLogo' => $mainBranch['logo_image_medium'],
            'giftCardImage' => $giftCardImage,
            'whiteLabel' => $businessModel->white_label_flag,
            'businessEmail' => $mainBranch->email,
            'isTransferAmount' => true
        ];

        $transferUnits = $giftCardQueue->gift_card_value;
        $labelPts = ((int) $transferUnits == 1) ? 'pt' : 'pts';
        $transferUnits = $transferUnits . ' ' . $labelPts;
        $emailSubject = '';
        $shortLink = Utils::shortUrlKg($phoneLink, $platformName);

        if ($isNewUser == false && $notifReqFk == 29) {
            //Kangaroo user TRANSFER
            /*$smsBody = Yii::t('frontend', 'FRONTEND_TRANSFER_POINTS_SMS_KNGUSER', ['{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']], null, $strLang);
            $emailSubject = Yii::t('frontend', 'FRONTEND_TRANSFER_POINTS_EMAIL_SUBJECT', array('{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName), null, $strLang);*/
            $smsBody = Yii::t('frontend', 'FRONTEND_TRANSFER_POINTS_SMS_KNGUSER', ['{sender}' => $senderName1, '{points}' => $transferUnits, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']], null, $strLang);
            $emailSubject = Yii::t('frontend', 'FRONTEND_TRANSFER_POINTS_EMAIL_SUBJECT', array('{sender}' => $senderName1, '{points}' => $transferUnits, '{businessName}' => $businessName), null, $strLang);
            $emailData['isTransferAmount'] = false;
        } elseif ($isNewUser == true && $notifReqFk == 29) {
            //New user TRANSFER
            /*$smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_TRANSFER_POINTS_SMS_NEWKNGUSER', ['{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']], null, $strLang);
            $emailSubject = Yii::t('frontend', 'FRONTEND_TRANSFER_POINTS_EMAIL_SUBJECT', array('{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName), null, $strLang);*/
            $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_TRANSFER_POINTS_SMS_NEWKNGUSER', ['{sender}' => $senderName1, '{points}' => $transferUnits, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']], null, $strLang);
            $emailSubject = Yii::t('frontend', 'FRONTEND_TRANSFER_POINTS_EMAIL_SUBJECT', array('{sender}' => $senderName1, '{points}' => $transferUnits, '{businessName}' => $businessName), null, $strLang);
            $emailData['isTransferAmount'] = false;
        } elseif ($isNewUser == false && ($notifReqFk == 30 || $notifReqFk == 43)) {
            //Kangaroo user GIFTCARD
            $smsBody = Yii::t('frontend', 'FRONTEND_GIFTCARD_TRANSFER_PTS_SMS_KNGUSER', ['{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']], null, $strLang);
            $emailSubject = Yii::t('frontend', 'FRONTEND_GIFTCARD_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName), null, $strLang);
        } elseif ($isNewUser == true && ($notifReqFk == 30 || $notifReqFk == 43)) {
            //New user GIFTCARD
            $smsBody = Yii::t('frontend', 'FRONTEND_USERSIGNUP_GIFTCARD_TRANSFER_PTS_SMS_NEWKNGUSER', ['{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName, '{link}' => $shortLink['shortUrl']], null, $strLang);
            $emailSubject = Yii::t('frontend', 'FRONTEND_GIFTCARD_TRANSFER_PTS_EMAIL_SUBJECT', array('{sender}' => $senderName1, '{amount}' => $amount, '{currency}' => $currencySymbol, '{businessName}' => $businessName), null, $strLang);
        }

        // Yii::log('BEFORE inserting in user Status Manager', CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);

        if ($phoneEmail['isPhone']) {
            $country = Countries::model()->findByPk($userModel->country_phone_fk);
            $phoneIsMobile = Utils::phoneNumberType($userModel->phone, $country->code, $platformName);

            if ($phoneIsMobile == 'landline') {
                return ['status' => 'NOT_OK', 'message_status' => 'failed', 'error' => 'Landline', 'reason' => 'phone_landline', 'message' => Yii::t('frontend', 'FRONTEND_PHONE_CANNOT_RECEIVE_TEXT')];
            }

            if ($isNewUser == true) //inserting in user Status Manager
            {
                $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 64, $oldPhoneEmail = '', $userModel->phone, $userModel->platform_solution_fk, $timeZoneId, $userModel->country_phone_fk);
            }
            //PHONE_NOT_VERIFIED

            $result = Utils::validateAndSendSMS($smsBody, $userModel->phone, $userModel->country_phone_fk, $platformName, [], $businessId,
                $userModel->id, null, true);

            //updating the satus and attempts
            GiftCardQueue::updateStatusAndAttempts($giftCardQueue->gift_card_queue_pk, $result, $platformName);

            return $result;
        } elseif ($phoneEmail['isEmail']) {
            if ($isNewUser == true) { //Creating user status for the user
                $userStatus = UserStatusManager::createUserStatusManagerRecordV330($userModel->id, $modifiedBy = null, $status_manager_fk = 66, $oldPhoneEmail = '', $userModel->email, $userModel->platform_solution_fk, $timeZoneId); //EMAIL_NOT_VERIFIED
            }

            Yii::log('sendEmailWithTemplate=' . CJSON::encode($emailData), CLogger::LEVEL_INFO, __FUNCTION__);

            //Sending the email
            $result = Utils::sendEmailWithTemplate('giftcardTemplate', 'bilingual', $emailSubject, $userModel->email, $emailData, $viewPath = 'application.views.mail.' . $strLang, null, $businessName, $businessId,
                $userModel->id, null, true);

            //updating the satus and attempts
            GiftCardQueue::updateStatusAndAttempts($giftCardQueue->gift_card_queue_pk, $result, $platformName);

            return $result;
        } else {
            throw new Exception("Not valid email/phone", 1);
        }
    }

    /**
     * Sending confirmation email for Gift card purchae on the Tablet app
     * Gift card amount (v4.7.0)
     * Used in : Website()
     *
     * @version April 2017 release V4.7.0
     * @version 3.4.1
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function sendConfirmGiftCardPurchaseEmailV470($giftCardQueue, $platformName)
    {
        Yii::log('ARGS=' . CJSON::encode(func_get_args()), CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

        $senderUser = User::model()->findByPk($giftCardQueue->sender_fk);
        $mainBranch = BusinessBranch::model()->findByPk($giftCardQueue->business_branch_fk);
        $businessId = $mainBranch->business_fk;
        $businessName = stripslashes((string)$mainBranch->name??'');
        $businessModel = Business::model()->findByPk($businessId);
        $currency = Currency::getCurrencySymbolByBranchId($giftCardQueue->business_branch_fk, $platformName);
        $currencySymbol = $currency['currencySymbol'];
        $strLang = Language::model()->findByPk($giftCardQueue->recipient_lang_fk)->abreviation;
        $denominator = Currency::getCurrencyDenominator($businessId);
        $amount = $giftCardQueue->amount;
        $senderName = $giftCardQueue->sender_name;
        $emailData = [];
        $giftCardImage = '';

        $senderName2 = '';
        if ($senderName != '') {
            $senderName2 = $senderName;
        } elseif ($senderUser->first != null && $senderUser->first != '') {
            $senderName2 = ucfirst((string)$senderUser->first);
        } else {
            $senderName2 = 'Client';
        }

        $tmzName = TimezoneMysql::model()->findByPk($giftCardQueue->timezone_mysql_fk)->name;
        $purchaseDate = Utils::convertDateTimeToUserTmz($tmzName, date('Y-m-d H:i:s'), 'Y-m-d H:i');

        $userPhone = ($giftCardQueue->receiver_is_cell) ? Utils::getFormattedPhoneNumber($giftCardQueue->email_phone, $giftCardQueue->country_phone_fk) : '';

        if ($giftCardQueue->offer_fk != null) {
            $giftCardOffer = Offer::model()->findByPk($giftCardQueue->offer_fk);
            $giftCardImage = $giftCardOffer['offer_image' . $giftCardOffer['offer_image'] . '_medium'];
        }

        //appending to the original aray
        $emailData['username'] = '';
        $emailData['receiverEmail'] = $giftCardQueue->email_phone;
        $emailData['receiverCell'] = $userPhone;
        $emailData['purchaseDate'] = $purchaseDate;
        $emailData['isNewUser'] = false;
        $emailData['senderName'] = $senderName2;
        $emailData['amount'] = $amount;
        $emailData['businessLogo'] = $mainBranch->logo_image_medium;
        $emailData['giftCardImage'] = $giftCardImage;
        $emailData['transferUnits'] = $giftCardQueue->gift_card_value;
        $emailData['currencySymbol'] = $currencySymbol;
        $emailData['businessName'] = $businessName;
        $emailData['recipientName'] = '';
        $emailData['pincode'] = '';
        $emailData['link'] = '';
        $emailData['whiteLabel'] = $businessModel->white_label_flag;
        $emailData['businessEmail'] = $mainBranch->email;

        $emailSubject = Yii::t('frontend', 'FRONTEND_GIFTCARD_CONFIRMATION_EMAIL_SUBJECT', array('{amount}' => $emailData['amount'], '{currency}' => $emailData['currencySymbol'], '{businessName}' => $emailData['businessName']), null, $strLang);

        if ($businessModel->white_label_flag == 0) {
            $smsBody = Yii::t('frontend', 'FRONTEND_GIFTCARD_CONFIRMATION_SMS', ['{amount}' => $emailData['amount'], '{currency}' => $emailData['currencySymbol'], '{businessName}' => $emailData['businessName']], null, $strLang);
        } else {
            $smsBody = Yii::t('frontend', 'FRONTEND_GIFTCARD_CONFIRMATION_SMS_WLABEL', ['{amount}' => $emailData['amount'], '{currency}' => $emailData['currencySymbol'], '{businessName}' => $emailData['businessName']], null, $strLang);
        }

        if ($senderUser->email != '' && $senderUser->email != null) {
            Utils::sendEmailWithTemplate('giftCardPurchaseConfirm', 'bilingual', $emailSubject, $senderUser->email, $emailData, $viewPath = 'application.views.mail.' . $strLang, null, $emailData['businessName'], $businessId,
                $senderUser->id, null, true);
        } else {
            Utils::validateAndSendSMS($smsBody, $senderUser->phone, $senderUser->country_phone_fk, $platformName, [], $businessId,
                $senderUser->id, null, true);
        }
    }

    /**
     * Sending confirmation email for Gift card purchae on the Tablet app
     * Moved from User model
     * Used in : Website()
     *
     * @version January 2016 release V3.5.0
     * @version 3.4.1
     * <AUTHOR>
     * @access public
     * @return array
     */
    /*public static function sendConfirmGiftCardPurchaseEmail($giftCardQueue, $platformName)
    {
        Yii::log('ARGS=' . CJSON::encode(func_get_args()), CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

        $senderUser = User::model()->findByPk($giftCardQueue->sender_fk);
        $mainBranch = BusinessBranch::model()->findByPk($giftCardQueue->business_branch_fk);
        $businessId = $mainBranch->business_fk;
        $businessName = stripslashes((string)$mainBranch->name);
        $businessModel = Business::model()->findByPk($businessId);
        $currency = Currency::getCurrencySymbolByBranchId($giftCardQueue->business_branch_fk, $platformName);
        $currencySymbol = $currency['currencySymbol'];
        $strLang = Language::model()->findByPk($giftCardQueue->recipient_lang_fk)->abreviation;
        $denominator = Currency::getCurrencyDenominator($businessId);
        $amount = round($giftCardQueue->gift_card_value / $denominator, 2);
        $senderName = $giftCardQueue->sender_name;
        $emailData = [];
        $giftCardImage = '';

        $senderName2 = '';
        if ($senderName != '') {
            $senderName2 = $senderName;
        } elseif ($senderUser->first != null && $senderUser->first != '') {
            $senderName2 = ucfirst((string)$senderUser->first);
        } else {
            $senderName2 = 'Client';
        }

        $tmzName = TimezoneMysql::model()->findByPk($giftCardQueue->timezone_mysql_fk)->name;
        $purchaseDate = Utils::convertDateTimeToUserTmz($tmzName, date('Y-m-d H:i:s'), 'Y-m-d H:i');

        $userPhone = ($giftCardQueue->receiver_is_cell) ? Utils::getFormattedPhoneNumber($giftCardQueue->email_phone, $giftCardQueue->country_phone_fk) : '';

        if ($giftCardQueue->offer_fk != null) {
            $giftCardOffer = Offer::model()->findByPk($giftCardQueue->offer_fk);
            $giftCardImage = $giftCardOffer['offer_image' . $giftCardOffer['offer_image'] . '_medium'];
        }

        //appending to the original aray
        $emailData['username'] = '';
        $emailData['receiverEmail'] = $giftCardQueue->email_phone;
        $emailData['receiverCell'] = $userPhone;
        $emailData['purchaseDate'] = $purchaseDate;
        $emailData['isNewUser'] = false;
        $emailData['senderName'] = $senderName2;
        $emailData['amount'] = $amount;
        $emailData['businessLogo'] = $mainBranch->logo_image_medium;
        $emailData['giftCardImage'] = $giftCardImage;
        $emailData['transferUnits'] = $giftCardQueue->gift_card_value;
        $emailData['currencySymbol'] = $currencySymbol;
        $emailData['businessName'] = $businessName;
        $emailData['recipientName'] = '';
        $emailData['pincode'] = '';
        $emailData['link'] = '';
        $emailData['whiteLabel'] = $businessModel->white_label_flag;

        $emailSubject = Yii::t('frontend', 'FRONTEND_GIFTCARD_CONFIRMATION_EMAIL_SUBJECT', array('{amount}' => $emailData['amount'], '{currency}' => $emailData['currencySymbol'], '{businessName}' => $emailData['businessName']), null, $strLang);

        if ($businessModel->white_label_flag == 0) {
            $smsBody = Yii::t('frontend', 'FRONTEND_GIFTCARD_CONFIRMATION_SMS', ['{amount}' => $emailData['amount'], '{currency}' => $emailData['currencySymbol'], '{businessName}' => $emailData['businessName']], null, $strLang);
        } else {
            $smsBody = Yii::t('frontend', 'FRONTEND_GIFTCARD_CONFIRMATION_SMS_WLABEL', ['{amount}' => $emailData['amount'], '{currency}' => $emailData['currencySymbol'], '{businessName}' => $emailData['businessName']], null, $strLang);
        }

        if ($senderUser->email != '' && $senderUser->email != null) {
            Utils::sendEmailWithTemplate('giftCardPurchaseConfirm', 'bilingual', $emailSubject, $senderUser->email, $emailData, $viewPath = 'application.views.mail.' . $strLang, null, $emailData['businessName'], $businessId);
        } else {
            Utils::validateAndSendSMS($smsBody, $senderUser->phone, $senderUser->country_phone_fk, $platformName, [], $businessId);
        }

    }*/

    /**
     * Making a http request using curl
     *
     * Used in : Website()
     *
     * @param $reqInfo
     * @param $data
     * @param $platformName
     * @param bool $jsonResponse
     * @return mixed
     * <AUTHOR>
     * @access public
     * @version Aug - Sept 2015 release V3.4.1
     * @since 3.4.1
     * @throws Exception
     */
    public static function httpRequest($reqInfo, $data, $platformName, $jsonResponse = true)
    {
        $reqType = (isset($reqInfo['reqType']) && $reqInfo['reqType'] == 0) ? 0 : 1; //1 - post 0 - get
        // Init the CURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, Yii::app()->params['serverUrl'] . $reqInfo['apiUrl']);
        curl_setopt($ch, CURLOPT_HEADER, 0); // No header in the result
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Return, do not echo result
        curl_setopt($ch, CURLOPT_POST, 1); // This is a POST request
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data); // Data to POST
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);

        // Fetch and return content
        $data = curl_exec($ch);

        if (curl_errno($ch)) {
            $curlError = curl_error($ch);
            curl_close($ch);

            if ($jsonResponse) {
                Yii::log('Failed CURL REQUEST error=' . $curlError, CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
                Utils::sendResponse(200, CJSON::encode(['status' => 'NOT_OK', 'message' => Yii::t('backend', 'UNKNOWN_ERROR')]));
            } else {
                Utils::logMessage(json_encode([
                    'data' => $data,
                    'error' => $curlError,
                    'platform' => $platformName,
                ]));
                throw new RuntimeException('CURL Error: ' . $curlError);
            }
        }

        curl_close($ch);

        return $data;
    }

    /**
     * Making a http request using curl
     *
     * Used in : Website()
     *
     * @version Aug - Sept 2015 release V3.4.1
     * @since 3.4.1
     * <AUTHOR>
     * @access public
     * @return string|array|mixed
     */
    public static function httpRequestV2($reqInfo, $data, $platformName)
    {
        $reqType = (isset($reqInfo['reqType']) && $reqInfo['reqType'] == 0) ? 0 : 1; //1 - post 0 - get
        // Init the CURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $reqInfo['apiUrl']); //absolute url
        curl_setopt($ch, CURLOPT_HEADER, 0); // No header in the result
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Return, do not echo result
        curl_setopt($ch, CURLOPT_POST, 1); // This is a POST request
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data); // Data to POST
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);

        if (isset($reqInfo['headers'])) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $reqInfo['headers']);
        }

        // Fetch and return content
        $data = curl_exec($ch);

        if (curl_errno($ch)) {
            Yii::log('Failed CURL REQUEST error=' . curl_error($ch), CLogger::LEVEL_ERROR, $platformName . '_' . __METHOD__);
            Utils::sendResponse(200, CJSON::encode(['status' => 'NOT_OK', 'message' => Yii::t('backend', 'UNKNOWN_ERROR')]));
        }

        curl_close($ch);

        return $data;
    }

    /**
     * Validating date/time value using Yii CTypeValidator class
     *
     * Used in : Website
     *
     * @version Aug - Oct 2015 release V3.4.0
     * <AUTHOR>
     * @access public
     * @param string $type - "time" or "datetime"
     * @param string $format - "dd-MM-yyyy hh:mm", "hh:mm" - http://www.yiiframework.com/doc/api/1.1/CDateTimeParser
     * @param string $value  - value to validate
     * @return boolean - is valid format or not
     */
    public static function dateTimeValidator($type, $format, $value)
    {
        $validator = new CTypeValidator;
        $validator->type = $type;

        if ($type == 'time') {
            $validator->timeFormat = $format;
        } else {
            $validator->datetimeFormat = $format;
        }

        return $validator->validateValue($value);
    }

    /**
     * Normalizing time value
     *
     * Used in : Website()
     *
     * @version Aug - Sept 2015 release V3.4.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function normalizeTime($time)
    {
        $normalizedTime = null;
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        Yii::log('Time value received=' . $time, CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);

        if ($time != null && $time != '') {
            $timeFromArray = explode(':', $time);

            if (isset($timeFromArray[0]) && strlen((string)$timeFromArray[0]) == 1) {
                $timeFromArray[0] = '0' . $timeFromArray[0];
            }

            if (isset($timeFromArray[1]) && strlen((string)$timeFromArray[1]) == 1) {
                $timeFromArray[1] = '0' . $timeFromArray[1];
            }

            $normalizedTime = implode(':', $timeFromArray);
        }
        Yii::log('Time value normalized=' . $normalizedTime, CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);

        return $normalizedTime;
    }

    /**
     * Creating the string with List of Days (Mo, Tu...) from an array [1,2,3...]
     *
     * Used in : Website(BusinessAdmin)
     *
     * @version Aug - Sept 2015 release V3.4.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function buildWeekDays($freq_details)
    {
        $result = '';
        if ($freq_details != '') {
            foreach ($freq_details as $item) {
                switch ($item) {
                    case 1:
                        $result .= Yii::t('backend', 'S_MONDAY') . ',';
                        break;
                    case 2:
                        $result .= Yii::t('backend', 'S_TUESDAY') . ',';
                        break;
                    case 3:
                        $result .= Yii::t('backend', 'S_WEDNESDAY') . ',';
                        break;
                    case 4:
                        $result .= Yii::t('backend', 'S_THURSDAY') . ',';
                        break;
                    case 5:
                        $result .= Yii::t('backend', 'S_FRIDAY') . ',';
                        break;
                    case 6:
                        $result .= Yii::t('backend', 'S_SATURDAY') . ',';
                        break;
                    case 7:
                        $result .= Yii::t('backend', 'S_SUNDAY') . ',';
                        break;
                    default:
                        # code...
                        break;

                } //switch
            } //foreach
            return $result = rtrim((string)$result, ",");
        } //if
    }

    /**
     * Checking with Twilio if a phone number is mobile or landline
     *
     * Used in : Website()
     *
     * @version July - Aug 2015 release V3.3.1
     * <AUTHOR>
     * @access public
     * @return string - landline, mobile, or voip
     */
    public static function phoneNumberType($phoneNumber, $countryCode, $platformName)
    {
        $AccountSid = Yii::app()->params['TwillioAccountSid'];
        $AuthToken = Yii::app()->params['TwillioAuthToken'];
        // Note: No need to implement Twilio SubAccount for lookup
        $client = new Twilio\Rest\Client($AccountSid, $AuthToken);
        $number = $client->lookups->v1->phoneNumbers($phoneNumber)
            ->fetch(["countryCode" => strtoupper((string)$countryCode), "type" => "carrier"]);

        Yii::log('Twilio Lookup ' . $phoneNumber . ' Type: ' . $number->carrier['type'], CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
        //print_r($number);die;
        return $number->carrier['type'];
    }

    /**
     * Prepare image for cropping add white collor if the image is smaller
     * Using Imagine library with Imagick driver
     * Used in : Website(Merchant, BusinessAdmin)
     *
     * @version May - June 2015 release V3.3.0
     * @since 3.3.0
     * <AUTHOR>
     * @param array $arr
     * @access public
     * @return array
     */
    public static function prepareImageToCrop($arr)
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $imagine = new Imagine\Imagick\Imagine();
        $image = $imagine->open(realpath($arr['new_uploadfile']));

        $box = new Imagine\Image\Box($arr['width'], $arr['height']);
        $palette = new Imagine\Image\Palette\RGB();
        $color = $palette->color('#fff', 100);
        $profile = Imagine\Image\Profile::fromPath('images/config/sRGB_IEC61966-2-1_no_black_scaling.icc');

        //resize the uploaded image if it is smaller than the placeholder
        if ($image->getSize()->getWidth() > $arr['width'] || $image->getSize()->getHeight() > $arr['height']) {
            if ($image->getSize()->getWidth() > $arr['width']) {
                $resizeBox = $image->getSize()->widen($arr['width']);
            } elseif ($image->getSize()->getHeight() > $arr['height']) {
                $resizeBox = $image->getSize()->heighten($arr['height']);
            }

            //Yii::log('Resizing1='.$resizeBox, CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);

            $image
                ->resize($resizeBox)
                ->profile($profile)
                ->save(realpath($arr['new_uploadfile']));
        }

        if ($image->getSize()->getWidth() > $arr['width'] || $image->getSize()->getHeight() > $arr['height']) {
            if ($image->getSize()->getWidth() > $arr['width']) {
                $resizeBox = $image->getSize()->widen($arr['width']);
            } elseif ($image->getSize()->getHeight() > $arr['height']) {
                $resizeBox = $image->getSize()->heighten($arr['height']);
            }

            //Yii::log('Resizing2='.$resizeBox, CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);

            $image
                ->resize($resizeBox)
                ->profile($profile)
                ->save(realpath($arr['new_uploadfile']));
        }

        $box_w = $arr['width'];
        $box_h = $arr['height'];

        $hratio = $box_h / $image->getSize()->getHeight();
        $wratio = $box_w / $image->getSize()->getWidth();
        $ratio = min($hratio, $wratio);

        //if the source is smaller than the thumbnail size,
        //don't resize -- add a margin instead
        if ($ratio > 1.0) {
            $ratio = 1.0;
        }

        //compute sizes
        $sy = floor($image->getSize()->getHeight() * $ratio);
        $sx = floor($image->getSize()->getWidth() * $ratio);

        //Compute margins. Using these margins centers the image in the thumbnail.
        $m_y = floor(($box_h - $sy) / 2);
        $m_x = floor(($box_w - $sx) / 2);

        // Yii::log('Box W x H='. $arr['width'] . 'x'. $arr['height'], CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);
        // Yii::log('Img W x H='.$image->getSize()->getWidth().'x'.$image->getSize()->getHeight(), CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);
        // Yii::log('New X='. $m_x. 'New Y='. $m_y, CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);

        $newImage = $imagine->create($box, $color);

        $newImage->paste($image, new Imagine\Image\Point($m_x, $m_y));
        $newImage
            ->profile($profile)
            ->save(realpath($arr['new_uploadfile']));

        return $arr['new_uploadfile'];
    }

    /**
     * Prepare image for cropping, resizing image
     * Using Imagine library with Imagick driver
     * Used in : Website(Merchant, BusinessAdmin)
     *
     * @version May - June 2015 release V3.5.0
     * @since 3.5.0
     * <AUTHOR>
     * @param array $arr
     * @access public
     * @return array
     */
    public static function saveResizeImage($arr)
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $imagine = new Imagine\Imagick\Imagine();
        $image = $imagine->open(realpath($arr['new_uploadfile']));

        $box = new Imagine\Image\Box($arr['width'], $arr['height']);
        $palette = new Imagine\Image\Palette\RGB();
        $color = $palette->color('#fff', 100);
        $profile = Imagine\Image\Profile::fromPath('images/config/sRGB_IEC61966-2-1_no_black_scaling.icc');

        //resize the uploaded image if it is smaller than the placeholder
        if ($image->getSize()->getWidth() > $arr['width'] && $image->getSize()->getHeight() > $arr['height']) {
            $max = max($arr['width'], $arr['height']);

            if ($arr['width'] > $arr['height']) {
                $resizeBox = $image->getSize()->widen($arr['width']);
            } else {
                $resizeBox = $image->getSize()->heighten($arr['height']);
            }

            Yii::log('Resizing1=' . $resizeBox, CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

            $image
                ->resize($resizeBox)
                ->profile($profile)
                ->save(realpath($arr['new_uploadfile']), ['jpeg_quality' => 100]);
        }

        return $arr['new_uploadfile'];
    }

    /**
     * Prepare image for cropping, resizing image
     *
     * @version April 2016 V4.0
     * <AUTHOR>
     * @param array $arr
     * @access public
     * @return array
     */
    public static function saveResizeImageAspect($arr)
    {
        $platformName = PlatformSolution::PLATFORM_NAME_WEBSITE;
        $imagine = new Imagine\Imagick\Imagine();
        $image = $imagine->open(realpath($arr['new_uploadfile']));
        $profile = Imagine\Image\Profile::fromPath('images/config/sRGB_IEC61966-2-1_no_black_scaling.icc');

        //resize the uploaded image if it is smaller than the placeholder
        if ($image->getSize()->getWidth() > $arr['width'] && $image->getSize()->getHeight() > $arr['height']) {

            // $resizeBox = new Imagine\Image\Box($arr['width'], $arr['height']);

            // Yii::log('Resizing1=' . $resizeBox, CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

            // $image
            //     ->resize($resizeBox)
            //     ->profile($profile)
            //     ->save(realpath($arr['new_uploadfile']), ['jpeg_quality' => 100]);
        } else {

            $ratio_orig = $image->getSize()->getWidth() / $image->getSize()->getHeight();

            if ($arr['width'] / $arr['height'] > $ratio_orig) {
                $width = $arr['height'] * $ratio_orig;
                $height = $arr['height'];
            } else {
                $height = $arr['height'];
                $width = $arr['width'] * $ratio_orig;
            }

            $resizeBox = new Imagine\Image\Box($width, $height);

            Yii::log('Resizing2=' . $resizeBox, CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

            $image
                ->resize($resizeBox)
                ->profile($profile)
                ->save(realpath($arr['new_uploadfile']), ['jpeg_quality' => 100]);
        }

        return $arr['new_uploadfile'];
    }

    /**
     * Calculates height and width for the image to fit into a image placeholder used to crop an image.
     * the return value would be an array with keys width and height.
     * image is the path to the picture e.g. photos/business/1.jpg
     *
     * Used in : Website(UserProfile)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function getResizedImageDiminesions($image, $newWidth = 66, $newHeight = 66)
    {
        $dim = getimagesize($image);

        $height = $dim[1];
        $width = $dim[0];

        if ((int) $height < (int) $width) {
            $newHeight = (int) ($newWidth * $height / $width);
        } else {
            $newWidth = (int) ($newHeight * $width / $height);
        }

        return array('height' => $newHeight, 'width' => $newWidth);
    }

    /**
     * TIMEZONE - Globalization implementation
     * Getting phone number formatted for a specific country
     * INTERNATIONAL - +****************
     * NATIONAL (*************
     * E164  +***********
     * RFC3966 is as per INTERNATIONAL format, but with all spaces and other separating symbols
     *
     * Used in : Website(Display phone, change phone)
     * @param string $phone - the phone number in any format with + or without
     * @param int|string $countryFk - Country ID or Country ISO code 2
     * @param string $format - NATIONAL, INTERNATIONAL, E164, RFC3966
     * @return string
     * @throws \libphonenumber\NumberParseException
     * @since 3.1.1
     * @access public
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     */
    public static function getFormattedPhoneNumber($phone, $countryFk, $format = 'NATIONAL')
    {
        if (!$countryFk || !$phone) {
            return $phone;
        }

        $countryCode2 = $countryFk;
        if (is_numeric($countryFk)) {
            $countryCode2 = Countries::model()->findByPk($countryFk)->code;
        }

        $phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
        $phonenumberFormatted = $phoneUtil->parse($phone, $countryCode2);

        if ($format == 'NATIONAL') {
            $format = \libphonenumber\PhoneNumberFormat::NATIONAL;
        } elseif ($format == 'INTERNATIONAL') {
            $format = \libphonenumber\PhoneNumberFormat::INTERNATIONAL;
        } elseif ($format == 'E164') {
            $format = \libphonenumber\PhoneNumberFormat::E164;
        } elseif ($format == 'RFC3966') {
            $format = \libphonenumber\PhoneNumberFormat::RFC3966;
        } else {
            $format = \libphonenumber\PhoneNumberFormat::INTERNATIONAL;
        }

        $phonenumberFormatted = $phoneUtil->format($phonenumberFormatted, $format);

        return $phonenumberFormatted;
    }

    public static function getPhoneCodeFromPhoneNumber($phone)
    {
        try {
            $phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
            $phonenumberFormatted = $phoneUtil->parse($phone);
            return $phonenumberFormatted->getCountryCode();
        } catch (\Exception $e) {
            return null;
        }
    }
    /**
     * For the Merchant app the IP can NEVER BE NULL - It gets the country code from Clerk BusinessBranch
     * Get all the enabled countries with their info to fill the phone drop down
     * and flag the one that should be selected by default
     *
     * Used in : Merchant Android app
     * <AUTHOR> C.
     * @version : May - Jun 2015 release V3.3.0
     * @param string $platformName
     * @return array of countries
     */
    public static function getMerchantEnabledCountriesWithDefaultSelected($platformName, $businessBranchPk, $funcVersionName = 'NOT DEFINED')
    {

        $result = self::getMerchantLocation($platformName, $businessBranchPk, $funcVersionName);

        $countries = Countries::getAllCountries();

        $selected = false;
        $key = null;

        for ($i = 0; $i < count($countries); $i++) {
            if ($countries[$i]['code'] == $result['location']['countryCode']) {
                $countries[$i]['selected'] = true;
                $selected = true;
                $key = $i;
            } else {
                $countries[$i]['selected'] = false;
            }
        }

        if (!$selected) {
            $key = array_search('CA', array_column($countries, 'code'));
            $countries[$key]['selected'] = true;
        }

        return ['countryList' => $countries, 'selectedCountry' => $countries[$key]];
    }

    /**
     * Get all the enabled countries with their info to fill the phone drop down
     * and flag the one that should be selected by default
     *
     * Used in : Merchant Android app
     * <AUTHOR> N.
     * @version : May - Jun 2015 release V3.3.0
     * @param string $platformName
     * @return array of countries
     */
    public static function getEnabledCountriesWithDefaultSelected($platformName, $ip = null, $funcVersionName = 'NOT DEFINED')
    {
        $result = self::getLocation($platformName, $ip, $funcVersionName);
        $countries = Countries::getAllCountries();
        $selected = false;
        $key = null;
        for ($i = 0; $i < count($countries); $i++) {
            if ($countries[$i]['code'] == $result['location']['countryCode']) {
                $countries[$i]['selected'] = true;
                $selected = true;
                $key = $i;
            } else {
                $countries[$i]['selected'] = false;
            }
        }
        if (!$selected) {
            $key = array_search('CA', array_column($countries, 'code'));
            $countries[$key]['selected'] = true;
        }
        return ['countryList' => $countries, 'selectedCountry' => $countries[$key]];
    }

    /**
     * Get all the enabled countries with their info to fill the phone drop down
     * and flag the one that should be selected by default
     *
     * Used in : Merchant Android app
     * <AUTHOR> N.
     * @version : May - Jun 2015 release V3.3.0
     * @param string $platformName
     * @return array of countries
     */
    public static function getAllCountriesWithDefaultSelected($platformName, $ip = null, $funcVersionName = 'NOT DEFINED')
    {
        $result = self::getLocation($platformName, $ip, $funcVersionName);
        $countries = Countries::getAll();
        $selected = false;
        $key = null;
        for ($i = 0; $i < count($countries); $i++) {
            if ($countries[$i]['code'] == $result['location']['countryCode']) {
                $countries[$i]['selected'] = true;
                $selected = true;
                $key = $i;
            } else {
                $countries[$i]['selected'] = false;
            }
        }
        if (!$selected) {
            $key = array_search('CA', array_column($countries, 'code'));
            $countries[$key]['selected'] = true;
        }
        return ['countryList' => $countries, 'selectedCountry' => $countries[$key]];
    }

    /**
     * Update: The new update includes the detection of the country code and selecting the
     * the right twillio number accordingly. Philippe N. Version July August - V3.3.1
     * INTERNATIONAL PhoneNumber - Globalization implementation
     * TIMEZONE - Globalization implementation
     * Validate and Send the SMS
     *
     * Used on : Website / Smartphone / Merchant Tablet app
     * @param string $smsBody
     * @param string $sendTo - the phone number in national format 5141234567
     * @param int|string $countryFk - country used for phone formatting
     * @param string $platformName
     * @param array $options - array('StatusCallback' => 'http://foo.bar/status')
     * @param null|int|string $businessId
     * @param mixed $userId
     * @param null|MessagesQueue $messageQueue
     * @param bool $withDrawCredit
     * @return array
     * @throws Exception
     * @access public
     * @version May 2021 release V5.17.0
     * @since Aug - Dec Release V3.1.0
     * <AUTHOR>
     */
    public static function validateAndSendSMS($smsBody, $sendTo, $countryFk, $platformName, $options = [],
        $businessId = null, $userId = null, $messageQueue = null, $withDrawCredit = false
    )
    {
        Utils::logMessage(json_encode(func_get_args()), __METHOD__);
        $business = Business::model()->findByPk($businessId);

        /*$business = Business::model()->findByPk($businessId);

        if ((int) $business->enabled === 0) {
            Yii::log('validateAndSendSMS Business DISABLED sent To:' . $sendTo .' with content: ' . $smsBody . ' businessId='
                . $businessId . ' employeeId=' . $userId, CLogger::LEVEL_INFO, $platformName . '_' . __METHOD__);

            return ['status' => 'NOT_OK', 'sms_status' => 'failed', 'errorCode' => '', 'error' => '', 'message' => Yii::t('frontend', 'SEND_SMS_INVALID_PHONE_NUMBER')];
        }*/

        if ($countryFk === null || $countryFk === '') {
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            Utils::logMessage('country ID is NULL sendTo=' . $sendTo .  ' businessId=' . $businessId . ' userAgent=' . $userAgent, __METHOD__);
            return ['status' => 'NOT_OK', 'sms_status' => 'failed', 'errorCode' => 21401, 'error' => 'country ID is null', 'message' => Yii::t('frontend', 'SEND_SMS_INVALID_PHONE_NUMBER')];
        }

        $country = Countries::model()->findByPk($countryFk);
        $intlPhone = Utils::getFormattedPhoneNumber($sendTo, $country->code, 'E164');

        if (!$country->allow_sms) {
            Utils::logMessage('country.allow_sms is OFF sendTo=' . $intlPhone .  ' businessId=' . $businessId . ' country_code:'. $country->code, __METHOD__, CLogger::LEVEL_INFO);
            return ['status' => 'OK', 'message_status' => '', 'errorCode' => '', 'error' => '', 'message' => ''];
        }

        if (!Utils::isValidPhone($intlPhone, $country->code, $platformName)) {
            Utils::logMessage('Invalid phone number sendTo=' . $intlPhone .  ' businessId=' . $businessId, __METHOD__, CLogger::LEVEL_WARNING);
            return ['status' => 'NOT_OK', 'message_status' => 'failed', 'errorCode' => 21401, 'error' => 'NVFN', 'message' => Yii::t('frontend', 'SEND_SMS_INVALID_PHONE_NUMBER')];
        }

        if (FakeContactRule::isFakePhone($intlPhone)) {
            Utils::logMessage('Fake phone: ' . $intlPhone, __METHOD__, CLogger::LEVEL_INFO);
            return [
                'status' => 'NOT_OK',
                'message_status' => 'failed',
                'errorCode' => 21401,
                'error' => 'NVFN',
                'message' => Yii::t('backend', 'BACKEND_FAKE_CONTACT'),
                'sms_status' => '',
                'sms_sid' => '',
                'sendTo' => $sendTo,
            ];
        }

        if ($business && $business->industryProhibitedSms()) {
            Utils::logMessage('Industry Prohibited SMS sendTo=' . $intlPhone .  ' businessId=' . $businessId, __METHOD__, CLogger::LEVEL_INFO);
            return ['status' => 'OK', 'message_status' => '', 'errorCode' => '', 'error' => '', 'message' => ''];
        }

        $transaction = Yii::app()->db->getCurrentTransaction();
        $requireTransaction = false;
        if ($transaction === null && $withDrawCredit && isset($businessId) && ($businessId != KNG_BUSINESS_ID)) {
            $transaction = Yii::app()->db->beginTransaction();
            $requireTransaction = true;
        }
        try {
            if ($withDrawCredit && isset($businessId) && ($businessId != KNG_BUSINESS_ID)) {
                $transactionalMessage = new TransactionalMessage('sms', $businessId, $withDrawCredit);
                if (!$transactionalMessage->isValid()) {
                    if ($requireTransaction) {
                        $transaction->rollback();
                    }

                    Utils::logMessage(json_encode(['sendTo' => $sendTo, 'msg' => 'Failed credit']), __METHOD__);

                    return [
                        'status' => 'NOT_OK',
                        'message_status' => 'failed',
                        'error' => Yii::t('backend', 'BACKEND_MKTCAMPAIGN_NOT_ENOUGH_CREDIT_TITLE'),
                        'message' => Yii::t('backend', 'BACKEND_MKTCAMPAIGN_NOT_ENOUGH_CREDIT_TITLE'),
                        'errorCode' => '',
                        'sms_status' => '',
                        'sms_sid' => '',
                        'sendTo' => $sendTo,
                    ];
                }

                if (!$messageQueue) {
                    $messageQueue = MessagesQueue::createWithStatusSent([
                        'send_to' => $sendTo,
                        'cell_email' => MESSAGE_SEND_BY_SMS,
                        'business_fk' => $businessId,
                        'user_fk' => $userId,
                        'country_phone_fk' => $countryFk,
                        'platformName' => $platformName,
                        'params' => [
                            'message_body' => $smsBody,
                            'withdraw_credit_flag' => $withDrawCredit,
                        ],
                    ]);
                    Utils::logMessage('New message_queue: ' . $messageQueue->message_queue_pk . 'phone=' . $sendTo, __METHOD__, CLogger::LEVEL_INFO);
                }

                $transactionalMessage->createTransactionalMessage($messageQueue->message_queue_pk);
            }

            $businessRule = null;
            if(isset($businessId) && $businessId){
                $businessRule = BusinessRules::getBusinessRules($businessId);
            }

            $result = self::validateAndSendSMSV519($smsBody, $sendTo, $intlPhone, $countryFk, $platformName, $options, $businessId, $businessRule);
            if ($requireTransaction) {
                $transaction->commit();
            }
        } catch (\Exception $e) {
            if ($requireTransaction) {
                $transaction->rollback();
            }
            Utils::logMessage('Exception reason: ' . $e->getTraceAsString() . ' sendTo: ' . $sendTo, __METHOD__);

            Yii::log('Exception reason: ' . $e->getTraceAsString() . ' sendTo: ' . json_encode($sendTo), CLogger::LEVEL_ERROR, __METHOD__);
            $result = [
                'status' => 'NOT_OK',
                'message_status' => 'failed',
                'error' => $e->getMessage(),
                'errorCode' => '',
                'sms_status' => '',
                'sms_sid' => '',
                'sendTo' => $sendTo,
                'message' => $e->getMessage()
            ];
        }
        return $result;
    }

    private static function validateAndSendSMSV519($smsBody, $sendTo, $intlPhone, $countryFk, $platformName, $options = [], $businessId = null, $rules = null)
    {
        Utils::logMessage(json_encode(func_get_args()), __METHOD__);

        $trxSmsSenderName = 'Kangaroo';
        if ($rules && $rules->sms_msg_name_trx) {
            $trxSmsSenderName = $rules->sms_msg_name_trx;
        }

        $smsVendor = SmsVendor::getVendorForBusiness((int) $countryFk, $businessId);
        $sms = new SMS($smsVendor, $intlPhone, $smsBody);
        $r = $sms->from('system_phone') //system_phone | commercial_phone
            ->fromName($trxSmsSenderName)
            ->send();

        Utils::logMessage(json_encode(array_merge($r, [
            'sendTo' => $sendTo,
            'smsvendorPk' => $smsVendor->smsvendor_pk,
        ])), __METHOD__);

        return [
            'status' => $r['status'],
            'sms_status' => $r['message_status'],
            'message_status' => $r['message_status'],
            'sms_sid' => $r['sid'],
            'smsvendor_pk' => $smsVendor->smsvendor_pk,
            'sendTo' => $sendTo,
            'errorCode' => null,
            'error' => $r['error'],
            'message' => $r['sid'] ? '' : Yii::t('frontend', 'SEND_SMS_INVALID_PHONE_NUMBER'),
        ];

        /*$trace = debug_backtrace();
        $calledFrom = $trace[1]['function'];

        // Step 1: Include Twillio
        spl_autoload_unregister(array('YiiBase', 'autoload'));
        require_once Yii::app()->params['TwillioIncludePath'];
        //require_once Yii::app()->params['libphonenumber'];
        spl_autoload_register(array('YiiBase', 'autoload'));

        $AccountSid = Yii::app()->params['TwillioAccountSid'];
        $AuthToken = Yii::app()->params['TwillioAuthToken'];

        $client = new Services_Twilio($AccountSid, $AuthToken);

        if ($countryFk == null || $countryFk == '') {
            $userAgent = (isset($_SERVER['HTTP_USER_AGENT'])) ? $_SERVER['HTTP_USER_AGENT'] : '';
            Yii::log('country ID is NULL sendTo=' . $sendTo . ' calledFrom=' . $calledFrom . ' userAgent=' . $userAgent, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            return array('status' => 'NOT_OK', 'sms_status' => 'failed', 'errorCode' => 21401, 'error' => 'country ID is null', 'message' => Yii::t('frontend', 'SEND_SMS_INVALID_PHONE_NUMBER'));
        }

        $from = SmsVendor::getTwillioNumberByCountry($countryFk); //Yii::app()->params['TwillioFrom'];  //for testing "+***********";

        // Val: The business is not active anymore: 20201-05-05 This can be removed
        if ($businessId && $businessId == 1112) {
            $smsVendor = SmsVendor::model()->findByPk(SMS_VENDOR_TWILIO_DOG_PERFECT);
            if ($smsVendor) {
                $from = $smsVendor->system_phone;
            }
        }

        //getting country phone code
        $country = Countries::model()->findByPk($countryFk);
        $phoneCode = $country->phone_code;
        $iso2 = $country->code;

        //INTERNATIONAL format E164
        $intlPhone = Utils::getFormattedPhoneNumber($sendTo, $countryFk, 'E164');

        //Validating Phone number before sending the SMS, return true if valid
        if (Utils::isValidPhone($intlPhone, $iso2, $platformName) == false) {
            Yii::log('Invalid phone number sendTo=' . $intlPhone, CLogger::LEVEL_WARNING, $platformName . '_' . __FUNCTION__);
            return array('status' => 'NOT_OK', 'message_status' => 'failed', 'errorCode' => 21401, 'error' => 'NVFN', 'message' => Yii::t('frontend', 'SEND_SMS_INVALID_PHONE_NUMBER'));
        }

        $smsSid = null;

        try
        {
            $sms = $client->account->messages->sendMessage(
                $from,
                $intlPhone,
                $smsBody,
                $mediaURL = null,
                $options
                // $options = [
                //     'StatusCallback' => Yii::app()->params['serverUrl'] . '/app/twilioCallback',
                // ]
            );

            $smsSid = $sms->sid;
            $message = $client->account->messages->get($sms->sid);
            //Yii::log('Send SMS', CLogger::LEVEL_INFO, 'status='.$message->status.' sid='.$sms->sid);

            if ($message->status == 'failed' || $message->status == 'undelivered') {
                Yii::log('sendTo=' . $intlPhone . ' errorCode=' . $message->error_code . ' errorMessage=' . $message->error_message . 'messageStatus=' . $message->status, CLogger::LEVEL_WARNING, $platformName . '_' . __FUNCTION__);
                //print_r($message); die();
                $result = array('status' => 'NOT_OK', 'sms_sid' => $smsSid, 'message_status' => $message->status, 'errorCode' => $message->error_code, 'phoneFrom' => '******-759-2979', 'error' => 'SMS undelivered: ' . $message->error_message, 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'));
            } elseif ($message->status == 'accepted') {
                $result = array('status' => 'OK', 'sms_sid' => $smsSid, 'message_status' => 'queued', 'errorCode' => null);
            } else {
                // Display a confirmation message on the screen
                $result = array('status' => 'OK', 'sms_sid' => $smsSid, 'message_status' => $message->status, 'errorCode' => null);
            }
        } catch (Services_Twilio_RestException $e) {
            if ($e->getCode() == '20404') {
                //The requested resource not found
                Yii::log('Services_Twilio_RestException sendTo=' . $intlPhone . ' errorCode=' . $e->getCode() . ' errorMessage=' . $e->getMessage(), CLogger::LEVEL_WARNING, $platformName . '_' . __FUNCTION__);
                $result = array('status' => 'OK', 'sms_sid' => $smsSid, 'message_status' => 'queued', 'errorCode' => $e->getCode(), 'error' => (array) $e);
            } else {
                Yii::log('Services_Twilio_RestException sendTo=' . $intlPhone . ' errorCode=' . $e->getCode() . ' errorMessage=' . $e->getMessage(), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
                $result = array('status' => 'NOT_OK', 'sms_sid' => $smsSid, 'message_status' => 'failed', 'errorCode' => $e->getCode(), 'error' => (array) $e, 'phoneFrom' => '******-759-2979', 'message' => Yii::t('frontend', 'UNKNOWN_ERROR'));
            }
        }

        //add to queue
        // $statusCode = EmailSmsStatus::getStatusCode($result['message_status']);
        // $smsSid = (isset($sms->sid)) ? $sms->sid : null ;
        // MessagesQueue::create($sendTo, $cellEmail = 2, ['message_body' => $smsBody], $statusCode, $smsSid);

        return $result;*/
    }

    /**
     * Validate phone number with libphonenumber library
     *
     * Used in : Website(Login)
     *
     * @param string $phone
     * @param string $code2 - country code ISO 2
     * @param string $platformName
     * @return bool
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     */
    public static function isValidPhone($phone, $code2, $platformName)
    {
        if ($phone == '' || $phone == null) {
            return false;
        }

        $phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
        try {
            $trace = debug_backtrace();
            $calledFrom = $trace[1]['function'];

            $code2 = strtoupper((string)$code2);
            $phoneNumberProto = $phoneUtil->parse($phone, $code2);
            $type = $phoneUtil->getNumberType($phoneNumberProto);

            Yii::log('Received phone: ' . $phone . ' code=' . $code2 . ' type=' . $type . ' calledFrom:' . $calledFrom, CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);

            $isValidPhone = $phoneUtil->isValidNumber($phoneNumberProto);

            if (!$isValidPhone) {
                Yii::log('Phone not valid: ' . $phone . ' code=' . $code2 . ' is valid=' . (int) $isValidPhone . ' calledFrom: ' . $calledFrom, CLogger::LEVEL_WARNING, $platformName . '_' . __FUNCTION__);
            }

            return $isValidPhone;

        } catch (\libphonenumber\NumberParseException $e) {
            Yii::log('Phone parse exception: ' . $phone . ' code=' . $code2 . ' Error=' . $e->getMessage(), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
        }

        return false;
    }

    /**
     * Clean a string and keep digits only
     *
     * Used in : Website(Login)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function normalizeDigitsOnly($string)
    {
        return preg_replace('/[^0-9]+/', '', $string ?? '');
    }

    /**
     * For the Merchant app the IP can NEVER BE NULL - It gets the country code from Clerk BusinessBranch
     * Get the location by ip inluding country name, code, region, lat, long, ip
     *
     * Used in : Merchant Tablet app
     * @version May - June 2015 release V3.3.0
     * <AUTHOR> C.
     * @access public
     * @return array
     */
    public static function getMerchantLocation($platformName, $businessBranchPk, $funcVersionName = 'NOT DEFINED')
    {
        $response = [];

        try {

            $branchLocation = BusinessBranch::getBranchAddressDetails($businessBranchPk);

            $response = [];
            $response['countryCode'] = $branchLocation['countryCode'];
            $response['countryCode3'] = $branchLocation['countryCode3'];
            $response['countryPhoneCode'] = $branchLocation['countryPhoneCode'];
            $response['countryName'] = $branchLocation['countryName'];
            $response['regionName'] = $branchLocation['regionName'];
            $response['countryPk'] = $branchLocation['country_pk'];

        } catch (Exception $e) {
            Yii::log('Error getting Address location from busines branch : ' . $businessBranchPk . ' / Error: ' . CJSON::encode($e), CLogger::LEVEL_ERROR, $platformName . '_' . $funcVersionName . '_' . __FUNCTION__);
            return Utils::getLocationExternal($platformName);
        }

        return ['status' => 'OK', 'location' => $response];
    }

    /**
     * Temporal Fix - July 14th - Making sure to the default country phone code is CA instead of USD
     * Get the locatio by ip inluding country name, code, region, lat, long, ip
     *
     * Used in : Website(Login, signup) / Smartphone (ios/android)
     * @version May - June 2015 release V3.3.0
     * <AUTHOR> C.
     * @access public
     * @return array
     */
    public static function getCanadaLocation($platformName, $funcVersionName = 'NOT DEFINED')
    {
        $response = [];

        try {

            $defaultCanada = Countries::model()->findByAttributes(['country_pk' => 2]);

            $response = [];
            $response['countryCode'] = $defaultCanada['code'];
            $response['countryCode3'] = $defaultCanada['code_iso3'];
            $response['countryPhoneCode'] = $defaultCanada['phone_code'];
            $response['countryName'] = $defaultCanada['name'];
            //$response['regionName']         = $defaultCanada['regionName'];

        } catch (Exception $e) {
            Yii::log('Error getting location by IP from GeoIP database. Error: ' . CJSON::encode($e), CLogger::LEVEL_ERROR, $platformName . '_' . $funcVersionName . '_' . __FUNCTION__);
            return Utils::getLocationExternal($platformName);
        }

        return ['status' => 'OK', 'location' => $response];
    }

    /**
     * UPDATE : Patricia C. - Temporal Fix - July 14th - Making sure to the default country phone code is CA instead of USD
     * Get the locatio by ip inluding country name, code, region, lat, long, ip
     *
     * Used in : Website(Login, signup) / Smartphone (ios/android)
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function getLocation($platformName, $ip = null, $funcVersionName = 'NOT DEFINED')
    {
        $response = [];
        try {
            //Yii::log('IP: '.$ip, CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);

            if ($ip == null) {
                Yii::log('Error*** THE IP THAT WAS SENT AS PARAM WAS NULL ***Error', CLogger::LEVEL_WARNING, $platformName . '_' . $funcVersionName . '_' . __METHOD__);
                //return Utils::getLocationExternal($platformName);
                //Patricia C. Temporal Fix - July 14th - Making sure to the default country phone code is CA instead of US
                return Utils::getCanadaLocation($platformName, $funcVersionName);
            }

            $location = Utils::lookupLocation($ip);

            if (!$location || in_array($location->countryCode, ['A1', 'A2', 'EU', 'AP'])) {
                //Details http://dev.maxmind.com/geoip/legacy/mod_geoip2/
                Yii::log('Not valid location by IP from GeoIP database: ' . serialize($location) . ' for IP: ' . $ip, CLogger::LEVEL_ERROR, $platformName . '_' . $funcVersionName . '_' . __METHOD__);
                return Utils::getLocationExternal($platformName);
            }

            $city = Utils::removeAccents($location->city);
            $regionName = Utils::removeAccents($location->regionName);

            $response = [];
            $response['countryCode'] = $location->countryCode;
            $country = Countries::model()->findByAttributes(['code' => $location->countryCode]);
            $response['countryCode3'] = $country->code_iso3 ?? '';
            $response['countryPhoneCode'] = $country->phone_code ?? '';
            $response['countryName'] = $location->countryName;
            $response['regionName'] = $regionName;
            $response['region'] = $location->region;
            $response['postalCode'] = (isset($location->postalCode)) ? $location->postalCode : '';
            $response['lat'] = (isset($location->latitude)) ? $location->latitude : '';
            $response['long'] = (isset($location->longitude)) ? $location->longitude : '';
            $response['city'] = $city;
            $response['ip'] = $ip;

        } catch (Exception $e) {
            Yii::log('Error getting location by IP from GeoIP database. Error: ' . CJSON::encode($e), CLogger::LEVEL_ERROR, $platformName . '_' . $funcVersionName . '_' . __FUNCTION__);
            return Utils::getLocationExternal($platformName);
        }

        return ['status' => 'OK', 'location' => $response];
    }


    public static function lookupLocation(string $ip): object
    {
        $locationInfo = Utils::getGeoInfoFromIpWhoIs($ip);
        if ($locationInfo) {
            return (object)$locationInfo;
        }

        $defaultLocation = [
            'countryCode' => 'CA',
            'countryName' => 'Canada',
            'regionName' => 'Ontario',
            'region' => 'ON',
            'city' => 'Toronto',
            'latitude' => 0,
            'longitude' => 0
        ];
        $reader = new \GeoIp2\Database\Reader(Yii::getPathOfAlias('application.extensions') . '/geoip/GeoLite2-City.mmdb');
        try {
            $location = $reader->city($ip);
        } catch (\GeoIp2\Exception\AddressNotFoundException $e) {
            return (object)$defaultLocation;
        } catch (\MaxMind\Db\Reader\InvalidDatabaseException $e) {
            return (object)$defaultLocation;
        } catch (\Exception $e) {
            return (object)$defaultLocation;
        }

        $cityNames = $location->city->names;
        $regionNames = $location->subdivisions[0]->names ?? [];
        $countyNames = $location->country->names;

        return (object)[
            'city' => $location->city->names['en'] ?? reset($cityNames) ?? '',
            'regionName' => $location->subdivisions[0]->names['en'] ?? reset($regionNames) ?? '',
            'region' => $location->subdivisions[0]->isoCode ?? '',
            'latitude' => $location->location->latitude ?? 0,
            'longitude' => $location->location->longitude ?? 0,
            'countryCode' => $location->country->isoCode ?? "",
            'countryName' => $location->country->names['en'] ?? reset($countyNames) ?? '',
            'postalCode' => $location->postal->code ?? ""
        ];
    }


    public static function getGeoInfoFromIpWhoIs(string $ip): ?array
    {
        $url = "https://ipwho.is/" . $ip;
        if (Yii::app()->params['envParam'] === 'PROD') {
            $ipKey = Yii::app()->params['IP_WHOIS_KEY'];
            $url = "https://ipwhois.pro/$ip?key=$ipKey";
        }

        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HEADER => false,
            CURLOPT_TIMEOUT => 10,
        ]);

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            error_log('cURL error: ' . curl_error($ch));
            curl_close($ch);
            return null;
        }

        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            error_log("HTTP error: $httpCode");
            return null;
        }

        $data = json_decode($response, true);

        if (!is_array($data) || empty($data['success'])) {
            Utils::logMessage($response, __METHOD__);
            return null;
        }

        return [
            'countryCode' => $data['country_code'] ?? null,
            'countryName' => $data['country'] ?? null,
            'regionName' => $data['region'] ?? null,
            'region' => $data['region_code'] ?? null,
            'city' => $data['city'] ?? null,
            'latitude' => $data['latitude'] ?? 0,
            'longitude' => $data['longitude'] ?? 0,
            'postalCode' => $data['postal'] ?? null,
            'timezone' => $data['timezone']['id'] ?? null,
        ];
    }

    /**
     * Getting location details including country name, code, region, lat, long
     * using an external service as a second option: IT GEST THE IP FROM THE SERVER
     *
     * Used in : Website(Login)
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function getLocationExternal($platformName)
    {
        if (is_callable('curl_init')) {
            $c = curl_init();
            curl_setopt($c, CURLOPT_URL, 'http://ip-api.com/php/');
            curl_setopt($c, CURLOPT_HEADER, false);
            curl_setopt($c, CURLOPT_TIMEOUT, 10);
            curl_setopt($c, CURLOPT_RETURNTRANSFER, true);
            $resultArray = unserialize(curl_exec($c));
            curl_close($c);
        } else {
            $resultArray = unserialize(file_get_contents('http://ip-api.com/php/'));
        }

        if ($resultArray['status'] == 'success') {
            $response['countryCode'] = $resultArray['countryCode'];
            $response['countryCode3'] = Countries::model()->findByAttributes(['code' => $resultArray['countryCode']])->code_iso3;
            $response['countryPhoneCode'] = Countries::model()->findByAttributes(['code' => $resultArray['countryCode']])->phone_code;
            $response['countryName'] = $resultArray['country'];
            $response['regionName'] = $resultArray['regionName'];
            $response['region'] = $resultArray['region'];
            $response['postalCode'] = $resultArray['zip'];
            $response['lat'] = $resultArray['lat'];
            $response['long'] = $resultArray['lon'];
            $response['city'] = $resultArray['city'];
            $response['ip'] = $resultArray['query'];
            $response['timezone'] = $resultArray['timezone'];

            Yii::log('Getting location and IP from External resource: ip-api.com' . json_encode($response), CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
            return ['status' => 'OK', 'location' => $response];
        } else {
            Yii::log('Error getting location from External resource: ip-api.com ' . json_encode($resultArray), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            return ['status' => 'NOT_OK', 'errorDetails' => $resultArray];
        }
    }

    /**
     * Updated: 15-06-2015, Valentin - Changed the external service that gets the IP and it will return an array with status
     * Getting IP address from an external service
     *
     * Used on : Website / Merchant Android app
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function getExternalIPV330($platformName)
    {
        $ipaddress = null;
        foreach (array(
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR',
        ) as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    if (filter_var($ip, FILTER_VALIDATE_IP) !== false) {
                        $ipaddress = $ip;
                    }
                }
            }
        }

        if ($ipaddress == '127.0.0.1' || $ipaddress == '::1' || substr((string)$ipaddress??'', 0, 8) == '192.168.') {
            $ip = '************'; //file_get_contents('https://api.ipify.org');
            return ['status' => 'OK', 'ip' => $ip];

        } else {
            return ['status' => 'OK', 'ip' => $ipaddress];
        }
    }

    /**
     * Checks if a string is utf8 or not
     *
     * Used in : Website(Merchant signup - getting city from geoIp)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return boolean
     */
    public static function seemsUtf8($str)
    {
        $length = strlen((string)$str??'');
        for ($i = 0; $i < $length; $i++) {
            $c = ord($str[$i]);
            if ($c < 0x80) {
                $n = 0;
            }
            # 0bbbbbbb
            elseif (($c & 0xE0) == 0xC0) {
                $n = 1;
            }
            # 110bbbbb
            elseif (($c & 0xF0) == 0xE0) {
                $n = 2;
            }
            # 1110bbbb
            elseif (($c & 0xF8) == 0xF0) {
                $n = 3;
            }
            # 11110bbb
            elseif (($c & 0xFC) == 0xF8) {
                $n = 4;
            }
            # 111110bb
            elseif (($c & 0xFE) == 0xFC) {
                $n = 5;
            }
            # 1111110b
            else {
                return false;
            }
            # Does not match any model
            for ($j = 0; $j < $n; $j++) {
                # n bytes matching 10bbbbbb follow ?
                if ((++$i == $length) || ((ord($str[$i]) & 0xC0) != 0x80)) {
                    return false;
                }

            }
        }
        return true;
    }

    /**
     * Converts all accent characters to ASCII characters.
     * If there are no accent characters, then the string given is just returned.
     * Used in : Website(MerchantController)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @param string $string Text that might have accent characters
     * @return string Filtered string with replaced characters.
     */
    public static function removeAccents($string)
    {
        if (!preg_match('/[\x80-\xff]/', $string)) {
            return $string;
        }

        if (Utils::seemsUtf8($string)) {
            $chars = array(
                // Decompositions for Latin-1 Supplement
                chr(195) . chr(128) => 'A', chr(195) . chr(129) => 'A',
                chr(195) . chr(130) => 'A', chr(195) . chr(131) => 'A',
                chr(195) . chr(132) => 'A', chr(195) . chr(133) => 'A',
                chr(195) . chr(135) => 'C', chr(195) . chr(136) => 'E',
                chr(195) . chr(137) => 'E', chr(195) . chr(138) => 'E',
                chr(195) . chr(139) => 'E', chr(195) . chr(140) => 'I',
                chr(195) . chr(141) => 'I', chr(195) . chr(142) => 'I',
                chr(195) . chr(143) => 'I', chr(195) . chr(145) => 'N',
                chr(195) . chr(146) => 'O', chr(195) . chr(147) => 'O',
                chr(195) . chr(148) => 'O', chr(195) . chr(149) => 'O',
                chr(195) . chr(150) => 'O', chr(195) . chr(153) => 'U',
                chr(195) . chr(154) => 'U', chr(195) . chr(155) => 'U',
                chr(195) . chr(156) => 'U', chr(195) . chr(157) => 'Y',
                chr(195) . chr(159) => 's', chr(195) . chr(160) => 'a',
                chr(195) . chr(161) => 'a', chr(195) . chr(162) => 'a',
                chr(195) . chr(163) => 'a', chr(195) . chr(164) => 'a',
                chr(195) . chr(165) => 'a', chr(195) . chr(167) => 'c',
                chr(195) . chr(168) => 'e', chr(195) . chr(169) => 'e',
                chr(195) . chr(170) => 'e', chr(195) . chr(171) => 'e',
                chr(195) . chr(172) => 'i', chr(195) . chr(173) => 'i',
                chr(195) . chr(174) => 'i', chr(195) . chr(175) => 'i',
                chr(195) . chr(177) => 'n', chr(195) . chr(178) => 'o',
                chr(195) . chr(179) => 'o', chr(195) . chr(180) => 'o',
                chr(195) . chr(181) => 'o', chr(195) . chr(182) => 'o',
                chr(195) . chr(182) => 'o', chr(195) . chr(185) => 'u',
                chr(195) . chr(186) => 'u', chr(195) . chr(187) => 'u',
                chr(195) . chr(188) => 'u', chr(195) . chr(189) => 'y',
                chr(195) . chr(191) => 'y',
                // Decompositions for Latin Extended-A
                chr(196) . chr(128) => 'A', chr(196) . chr(129) => 'a',
                chr(196) . chr(130) => 'A', chr(196) . chr(131) => 'a',
                chr(196) . chr(132) => 'A', chr(196) . chr(133) => 'a',
                chr(196) . chr(134) => 'C', chr(196) . chr(135) => 'c',
                chr(196) . chr(136) => 'C', chr(196) . chr(137) => 'c',
                chr(196) . chr(138) => 'C', chr(196) . chr(139) => 'c',
                chr(196) . chr(140) => 'C', chr(196) . chr(141) => 'c',
                chr(196) . chr(142) => 'D', chr(196) . chr(143) => 'd',
                chr(196) . chr(144) => 'D', chr(196) . chr(145) => 'd',
                chr(196) . chr(146) => 'E', chr(196) . chr(147) => 'e',
                chr(196) . chr(148) => 'E', chr(196) . chr(149) => 'e',
                chr(196) . chr(150) => 'E', chr(196) . chr(151) => 'e',
                chr(196) . chr(152) => 'E', chr(196) . chr(153) => 'e',
                chr(196) . chr(154) => 'E', chr(196) . chr(155) => 'e',
                chr(196) . chr(156) => 'G', chr(196) . chr(157) => 'g',
                chr(196) . chr(158) => 'G', chr(196) . chr(159) => 'g',
                chr(196) . chr(160) => 'G', chr(196) . chr(161) => 'g',
                chr(196) . chr(162) => 'G', chr(196) . chr(163) => 'g',
                chr(196) . chr(164) => 'H', chr(196) . chr(165) => 'h',
                chr(196) . chr(166) => 'H', chr(196) . chr(167) => 'h',
                chr(196) . chr(168) => 'I', chr(196) . chr(169) => 'i',
                chr(196) . chr(170) => 'I', chr(196) . chr(171) => 'i',
                chr(196) . chr(172) => 'I', chr(196) . chr(173) => 'i',
                chr(196) . chr(174) => 'I', chr(196) . chr(175) => 'i',
                chr(196) . chr(176) => 'I', chr(196) . chr(177) => 'i',
                chr(196) . chr(178) => 'IJ', chr(196) . chr(179) => 'ij',
                chr(196) . chr(180) => 'J', chr(196) . chr(181) => 'j',
                chr(196) . chr(182) => 'K', chr(196) . chr(183) => 'k',
                chr(196) . chr(184) => 'k', chr(196) . chr(185) => 'L',
                chr(196) . chr(186) => 'l', chr(196) . chr(187) => 'L',
                chr(196) . chr(188) => 'l', chr(196) . chr(189) => 'L',
                chr(196) . chr(190) => 'l', chr(196) . chr(191) => 'L',
                chr(197) . chr(128) => 'l', chr(197) . chr(129) => 'L',
                chr(197) . chr(130) => 'l', chr(197) . chr(131) => 'N',
                chr(197) . chr(132) => 'n', chr(197) . chr(133) => 'N',
                chr(197) . chr(134) => 'n', chr(197) . chr(135) => 'N',
                chr(197) . chr(136) => 'n', chr(197) . chr(137) => 'N',
                chr(197) . chr(138) => 'n', chr(197) . chr(139) => 'N',
                chr(197) . chr(140) => 'O', chr(197) . chr(141) => 'o',
                chr(197) . chr(142) => 'O', chr(197) . chr(143) => 'o',
                chr(197) . chr(144) => 'O', chr(197) . chr(145) => 'o',
                chr(197) . chr(146) => 'OE', chr(197) . chr(147) => 'oe',
                chr(197) . chr(148) => 'R', chr(197) . chr(149) => 'r',
                chr(197) . chr(150) => 'R', chr(197) . chr(151) => 'r',
                chr(197) . chr(152) => 'R', chr(197) . chr(153) => 'r',
                chr(197) . chr(154) => 'S', chr(197) . chr(155) => 's',
                chr(197) . chr(156) => 'S', chr(197) . chr(157) => 's',
                chr(197) . chr(158) => 'S', chr(197) . chr(159) => 's',
                chr(197) . chr(160) => 'S', chr(197) . chr(161) => 's',
                chr(197) . chr(162) => 'T', chr(197) . chr(163) => 't',
                chr(197) . chr(164) => 'T', chr(197) . chr(165) => 't',
                chr(197) . chr(166) => 'T', chr(197) . chr(167) => 't',
                chr(197) . chr(168) => 'U', chr(197) . chr(169) => 'u',
                chr(197) . chr(170) => 'U', chr(197) . chr(171) => 'u',
                chr(197) . chr(172) => 'U', chr(197) . chr(173) => 'u',
                chr(197) . chr(174) => 'U', chr(197) . chr(175) => 'u',
                chr(197) . chr(176) => 'U', chr(197) . chr(177) => 'u',
                chr(197) . chr(178) => 'U', chr(197) . chr(179) => 'u',
                chr(197) . chr(180) => 'W', chr(197) . chr(181) => 'w',
                chr(197) . chr(182) => 'Y', chr(197) . chr(183) => 'y',
                chr(197) . chr(184) => 'Y', chr(197) . chr(185) => 'Z',
                chr(197) . chr(186) => 'z', chr(197) . chr(187) => 'Z',
                chr(197) . chr(188) => 'z', chr(197) . chr(189) => 'Z',
                chr(197) . chr(190) => 'z', chr(197) . chr(191) => 's',
                // Euro Sign
                chr(226) . chr(130) . chr(172) => 'E',
                // GBP (Pound) Sign
                chr(194) . chr(163) => '');

            $string = strtr($string, $chars);
        } else {
            // Assume ISO-8859-1 if not UTF-8
            $chars['in'] = chr(128) . chr(131) . chr(138) . chr(142) . chr(154) . chr(158)
            . chr(159) . chr(162) . chr(165) . chr(181) . chr(192) . chr(193) . chr(194)
            . chr(195) . chr(196) . chr(197) . chr(199) . chr(200) . chr(201) . chr(202)
            . chr(203) . chr(204) . chr(205) . chr(206) . chr(207) . chr(209) . chr(210)
            . chr(211) . chr(212) . chr(213) . chr(214) . chr(216) . chr(217) . chr(218)
            . chr(219) . chr(220) . chr(221) . chr(224) . chr(225) . chr(226) . chr(227)
            . chr(228) . chr(229) . chr(231) . chr(232) . chr(233) . chr(234) . chr(235)
            . chr(236) . chr(237) . chr(238) . chr(239) . chr(241) . chr(242) . chr(243)
            . chr(244) . chr(245) . chr(246) . chr(248) . chr(249) . chr(250) . chr(251)
            . chr(252) . chr(253) . chr(255);

            $chars['out'] = "EfSZszYcYuAAAAAACEEEEIIIINOOOOOOUUUUYaaaaaaceeeeiiiinoooooouuuuyy";

            $string = strtr($string, $chars['in'], $chars['out']);
            $double_chars['in'] = array(chr(140), chr(156), chr(198), chr(208), chr(222), chr(223), chr(230), chr(240), chr(254));
            $double_chars['out'] = array('OE', 'oe', 'AE', 'DH', 'TH', 'ss', 'ae', 'dh', 'th');
            $string = str_replace($double_chars['in'], $double_chars['out'], $string);
        }

        return $string;
    }

    /**
     * Generates random number
     *
     * Used in : Website(MerchantController)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return int
     */
    public static function randomNumber($digits)
    {
        $min = 10 ** ($digits - 1);
        $max = 10 ** $digits - 1;
        return mt_rand($min, $max);
    }

    /**
     * Converts a Timestamp from a time zone to UTC
     *
     * Used in : Website(Business Admin)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @param string $dateTimeToConvert
     * @param int $timezoneId
     * @param string $format
     * @return string
     */
    public static function utcDateTime($dateTimeToConvert, $timezoneId, $format = 'Y-m-d H:i:s')
    {
        $serverTimeZone = date_default_timezone_get();
        $timeZoneName = TimezoneMysql::model()->findByPk($timezoneId)->name;
        $dateTime = new DateTime($dateTimeToConvert, new DateTimeZone($timeZoneName));
        $dateTime->setTimezone(new DateTimeZone($serverTimeZone));

        //print_r($dateTime); die;
        return $dateTime->format($format);
    }

    /**
     * Converts a Timestamp from UTC to the Tmz UTC of the front-end user
     * The Tmz UTC of the user is received as a param
     *
     * Used in : Merchant Android app - iPhone/android App - Website
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @param string $timeZoneName
     * @param string $dateTimeToConvert
     * @param string $format
     * @return string
     */
    public static function convertDateTimeToUserTmz($timeZoneName, $dateTimeToConvert, $format = 'Y-m-d H:i:s')
    {
        // Val: passing null is deprecated in php 8.3, for compatibility with php 7.0 use now
        if (is_null($dateTimeToConvert)) {
            $dateTimeToConvert = 'now';
        }
        $serverTimeZone = date_default_timezone_get();
        $dateTime = new DateTime($dateTimeToConvert ?? 'now', new DateTimeZone($serverTimeZone));
        $dateTime->setTimezone(new DateTimeZone($timeZoneName));
        return $dateTime->format($format);
    }

    public static function isInternalIP($ip): bool
    {
        if (!filter_var($ip, FILTER_VALIDATE_IP)) {
            return false;
        }

        $privateRanges = array(
            '10.0.0.0/8',
            '**********/12',
            '***********/16',
            '***********/16',
            '*********/8'
        );

        foreach ($privateRanges as $range) {
            if (self::ip_in_range($ip, $range)) {
                return true;
            }
        }

        return false;
    }

    private static function ip_in_range($ip, $range)
    {
        list($subnet, $bits) = explode('/', $range??'');
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;
        return ($ip & $mask) == $subnet;
    }

    /**
     * Getting Time Zone PK from the session
     *
     * Used in : Website(Everywhere where is needed timezone)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @return int
     * @throws Exception
     */
    public static function getTimezoneSession()
    {
        if (null == Yii::app()->session->get('timezonePk')) {
            $ip = Yii::app()->request->userHostAddress;
            if (self::isInternalIP(Yii::app()->request->userHostAddress)) {
                $ip = Utils::getExternalIPV330(PlatformSolution::PLATFORM_NAME_WEBSITE);
                $ip = $ip['ip'] ?? '************';
            }

            $date = new DateTime();
            $timestamp = $date->getTimestamp();

            $tz = Utils::getTimeZone($timestamp, null, null, $ip ?? null, PlatformSolution::PLATFORM_NAME_WEBSITE);

            if ($tz['status'] == 'OK') {
                //timeZonePk and timezonePk case sensitive
                Yii::app()->session->add('timezonePk', $tz['timeZonePk']);
                return $tz['timeZonePk'];
            } else {
                return 165;
            }

            //Yii::log('Timezone is not set in session ', CLogger::LEVEL_WARNING, $platformName.'_'.__FUNCTION__);
        } else {
            return Yii::app()->session->get('timezonePk');
        }
    }

    /**
     * Getting Time Zone information by IP or Lat, Long
     * At least lat, long or ip is required
     *
     *
     * Used in : Website(Log in)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access public
     * @param float|null $lat - latitude
     * @param float|null $long - longitude
     * @param string $ip - IP address
     * @param string $platformName
     * @return array
     */
    public static function getTimeZone($timestamp, $lat, $long, $ip, $platformName)
    {
        if ($lat != null && $lat != '' && $long != null && $long != '') {
            $tz = self::getTimeZoneByGoogle($lat, $long, $timestamp, $platformName);
            Yii::log('timestamp=' . $timestamp . ' lat=' . $lat . ' long' . $long . ' ip=' . $ip . ' tz=' . CJSON::encode($tz), CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
            return $tz;
        } elseif ($ip != null && $ip != '') {
            $location = Utils::lookupLocation($ip);
            if ($location) {
                $lat = $location->latitude;
                $long = $location->longitude;
            } else {
                $lat = 45.49;
                $long = -73.55;
                Yii::log('Failed to Geolocate the IP. Using default lat=' . $lat . ' long' . $long . ' for ip=' . $ip, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            }

            $tz = self::getTimeZoneByGoogle($lat, $long, $timestamp, $platformName);
            Yii::log('timestamp=' . $timestamp . ' lat=' . $lat . ' long' . $long . ' ip=' . $ip . ' tz=' . CJSON::encode($tz), CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
            return $tz;
        } else {
            Yii::log('Timestamp and Lat, long or IP is required ip=' . $ip . ' lat=' . $lat . ' long=' . $long . ' timestamp=' . $timestamp, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            return ['status' => 'NOT_OK', 'message' => 'Timestamp and Lat, long or IP is required'];
        }
    }

    /**
     * get the platform solution id and return the platform solution name
     * @version Jan 2016 V3.5.0
     * <AUTHOR> N.
     * @access Public
     * @param string $platformSolution
     * @throws Exception
     */
    public static function getPlatformSolutionName($platformSolution)
    {
        if ($platformSolution != '' && $platformSolution != null) {
            $result = PlatformSolution::model()->findByPk($platformSolution);
            if ($result) {
                return $result->name;
            } else {
                return 'NOT_DEFINED';
            }

        } else {
            throw new Exception('error, please check platform solution value');
        }

    }

    /**
     * <AUTHOR> N.
     * @param string $status (ok, not_ok)
     * @param string $message (returned message)
     * @param int $code
     * @param Array $result
     * @return JSON object
     */
    public static function getJsonResult($status, $message, $code, $result)
    {
        $theResponse = array('status' => $status, 'message' => $message, 'code' => $code, 'results' => $result);
        return CJSON::encode($theResponse);
    }


    /**
     * Returns the clicks for a specific short url
     *
     * Used in : Website()
     *
     * @version June 2018 release V4.15.0
     * @since 4.15.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function getUrlStats($shortUrl)
    {
        $username = Yii::app()->params["shortUrlKgUser"];
        $password = Yii::app()->params["shortUrlKgPass"];
        $apiUrl = Yii::app()->params["shortUrlKgApi"];
        $format = 'json'; // output format: 'json', 'xml' or 'simple'

        // Init the CURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_HEADER, 0); // No header in the result
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Return, do not echo result
        curl_setopt($ch, CURLOPT_POST, 1); // This is a POST request
        curl_setopt($ch, CURLOPT_POSTFIELDS, array( // Data to POST
            'shorturl' => $shortUrl,
            'format' => $format,
            'action' => 'url-stats',
            'username' => $username,
            'password' => $password,
        ));

        // Fetch and return content
        $data = curl_exec($ch);

        if (curl_errno($ch)) {
            Yii::log('Failed to retrieve stats for URL from custom domain CURL error=' . curl_error($ch), CLogger::LEVEL_ERROR, __FUNCTION__);
        }

        curl_close($ch);

        return $result = (array) json_decode($data);
    }

    /**
     * Short url with custom domain
     * First it will short using own solution, in case it failed, will short with Google and if limit exceded or throws an error than short with bitly
     * Update: 2015-05-20 (Valentin), Additional release V3.2.5.1, using shortUrlKg as a main function for shortening
     * Used in : Website(Marketing Campaign) / CRON Job (Sending Business Contacts Invitations) / ALL platforms
     *
     * @version Mar - Apr 2015 release V3.2.5
     * <AUTHOR>
     * @access public
     * @param string $longUrl - the url to short
     * @param string $platformName
     * @param string $title - (optional) page title when redirecting from short url to long url
     * @param string $keyword - (optional) keyword instead of random string
     * @return array
     */
    public static function shortUrlKg($longUrl, $platformName, $title = 'Kangaroo Rewards', $keyword = null)
    {
        // $r = Utils::shortUrl($longUrl, $platformName);
        // if ($r['status'] === 'OK') {
        //     return $r;
        // }
        $username = Yii::app()->params["shortUrlKgUser"];
        $password = Yii::app()->params["shortUrlKgPass"];
        $api_url = Yii::app()->params["shortUrlKgApi"];
        $format = 'json'; // output format: 'json', 'xml' or 'simple'

        // Init the CURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_HEADER, 0); // No header in the result
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Return, do not echo result
        curl_setopt($ch, CURLOPT_POST, 1); // This is a POST request
        curl_setopt($ch, CURLOPT_POSTFIELDS, array( // Data to POST
            'url' => $longUrl,
            'keyword' => $keyword,
            'title' => $title,
            'format' => $format,
            'action' => 'shorturl',
            'username' => $username,
            'password' => $password,
        ));

        // Fetch and return content
        $data = curl_exec($ch);

        if (curl_errno($ch)) {
            Yii::log('Failed to short url with custom domain CURL error=' . curl_error($ch), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
        }

        curl_close($ch);

        $result = (array) json_decode($data);

        //print_r($data); die;
        if (isset($result['status']) && $result['status'] == 'success') {
            return ['status' => 'OK', 'shortUrl' => $result['shorturl']];
        } else {
            Yii::log('Failed to short url with custom domain=' . $data, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            //return ['status'=>'NOT_OK', 'message'=>$result['message'], 'shortUrl'=>$longUrl ];
            //try to short with Google or Bitly
            return Utils::shortUrl($longUrl, $platformName);
        }
    }

    /**
     * Sends the response to the client
     * @version Mar - Apr 2015 release V3.2.5
     * <AUTHOR>
     * @param int $status
     * @param string $body
     * @param string $contentType
     * @access public
     */
    public static function sendResponse($status = 200, $body = '', $contentType = 'application/json; charset=UTF-8')
    {
        $status_header = 'HTTP/1.1 ' . $status . ' ' . self::_getStatusCodeMessage($status);
        // set the status
        header($status_header);
        // set the content type
        header('Content-type: ' . $contentType);
        // header("Access-Control-Allow-Origin: *");

        echo $body;

        Yii::app()->end();
    }

    /**
     * Send email with template
     * IMPORTANT: This function has to be used for ALL platforms
     * Adding attachment (3.4.0)
     *
     * Used in : Website - SysAdmin(Business Contacts list: cronJob) / Merchant Admin (employee creation)
     * @version Mar - Apr 2015 release V3.2.5
     * <AUTHOR>
     * @param string $emailView - view path
     * @param string $layout - layout path
     * @param string $emailSubject
     * @param array|string $sendTo - array of emails
     * @param array $data  - array of variables used in email template
     * @param string $viewPath - application.views.mail.en
     * @param string $attachment  - file path to attach
     * @param string $fromExtra  - From string that will be shown in the email
     * @param string $businessId
     * @param $userId
     * @param bool $withDrawCredit
     * @param null|MessagesQueue $messageQueue
     * @return array
     */
    public static function sendEmailWithTemplate($emailView, $layout, $emailSubject, $sendTo, $data, $viewPath = null, $attachment = null, $fromExtra = null, $businessId = null, $userId = null, $messageQueue = null, $withDrawCredit = false)
    {
        if (is_array($sendTo)) {
            foreach ($sendTo as $sendToEmail) {
                if(FakeContactRule::isFakeEmail($sendToEmail)) {
                    Utils::logMessage('Fake email address: ' . $sendToEmail, __METHOD__, CLogger::LEVEL_INFO);
                    return [
                        'status' => 'NOT_OK',
                        'message_status' => 'failed',
                        'message' => 'Fake email address'
                    ];
                }
            }
        } elseif(FakeContactRule::isFakeEmail($sendTo)) {
            Utils::logMessage('Fake email address: ' . $sendTo, __METHOD__, CLogger::LEVEL_INFO);
            return [
                'status' => 'NOT_OK',
                'message_status' => 'failed',
                'message' => 'Fake email address'
            ];
        }

        if ($businessId) {
            $business = Business::model()->findByPk($businessId);
            if (!$business->enabled) {
                return [
                    'status' => 'NOT_OK',
                    'message_status' => 'failed',
                    'message' => 'Business Disabled'
                ];
            }
        }

        $transaction = Yii::app()->db->getCurrentTransaction();
        $requireTransaction = false;
        if ($transaction === null && $withDrawCredit && isset($businessId) && ($businessId != KNG_BUSINESS_ID)) {
            $transaction = Yii::app()->db->beginTransaction();
            $requireTransaction = true;
        }

        try {
            if ($withDrawCredit && isset($businessId) && ($businessId != KNG_BUSINESS_ID)) {
                $transactionalMessage = new TransactionalMessage('email', $businessId, $withDrawCredit);
                if (!$transactionalMessage->isValid()) {
                    if ($requireTransaction) {
                        $transaction->rollback();
                    }

                    return [
                        'status' => 'NOT_OK', 'message_status' => 'failed', 'error' => Yii::t('backend', 'BACKEND_MKTCAMPAIGN_NOT_ENOUGH_CREDIT_TITLE'),
                        'message' => Yii::t('frontend', 'ERROR_SENDING_EMAIL') . Yii::t('backend', 'BACKEND_MKTCAMPAIGN_NOT_ENOUGH_CREDIT_TITLE'),
                        'errors' => Yii::t('backend', 'BACKEND_MKTCAMPAIGN_NOT_ENOUGH_CREDIT_TITLE'),
                        'send_to' => json_encode($sendTo)
                    ];
                }

                if (!$messageQueue) {
                    $messageQueue = MessagesQueue::createWithStatusSent([
                        'send_to' => $sendTo,
                        'cell_email' => MESSAGE_SEND_BY_EMAIL,
                        'business_fk' => $businessId,
                        'user_fk' => $userId,
                        'params' => [
                            'emailView' => $emailView,
                            'layout' => $layout,
                            'emailSubject' => $emailSubject,
                            'businessId' => $businessId,
                            'emailData' => $data,
                            'withdraw_credit_flag' => $withDrawCredit,
                        ],
                    ]);
                    Utils::logMessage('New message_queue: ' . $messageQueue->message_queue_pk, __METHOD__, CLogger::LEVEL_INFO);
                }

                $transactionalMessage->createTransactionalMessage($messageQueue->message_queue_pk);
            }

            $mail = new YiiMailer();
            $mail->IsSMTP(); // Set mailer to use SMTP

            if ($viewPath != null) {
                $file = str_replace('application', Yii::app()->basePath, str_replace('.', '/', $viewPath)) . '/' . $emailView . '.php';
                if (file_exists($file)) {
                    Yii::log('ViewPath exists sendTo: ' . json_encode($sendTo) . ' viewPath: ' . $viewPath, CLogger::LEVEL_INFO, __METHOD__);
                    $mail->setViewPath($viewPath);
                } else {
                    Yii::log('ViewPath doesn\'t exist. sendTo: ' . json_encode($sendTo) . ' viewPath: ' . $viewPath, CLogger::LEVEL_INFO, __METHOD__);
                    $viewPath = 'application.views.mail.en';
                    $mail->setViewPath($viewPath);
                }
            }

            $mail->setView($emailView);
            $mail->setLayout($layout);
            $mail->setData($data);
            $emailConfig = Utils::getTransactionalEmailConfig($businessId ?? null);
            if ($fromExtra == null) {
                $fromExtra = $emailConfig->from_name;
            }
            $mail->setReplyTo($emailConfig->reply_to);
            $mail->setFrom($emailConfig->from_email, $fromExtra);
            $mail->setTo($sendTo);
            if ($attachment != null) {
                $mail->setAttachment($attachment);
            }

            // if (Yii::app()->params['envParam'] == 'PROD') {
            //     $mail->setBcc(Yii::app()->params['emailin']);
            // }

            $mail->setSubject($emailSubject);

            if ($mail->send()) {
                $result = ['status' => 'OK', 'message_status' => 'sent', 'message' => 'Email Sent', 'send_to' => json_encode($sendTo)];
            } else {
                Yii::log('Email not sent reason: ' . $mail->getError() . ' sendTo: ' . json_encode($sendTo), CLogger::LEVEL_ERROR, __METHOD__);
                $result = [
                    'status' => 'NOT_OK', 'message_status' => 'failed', 'error' => $mail->getError(),
                    'message' => Yii::t('frontend', 'ERROR_SENDING_EMAIL') . $mail->getError(),
                    'errors' => $mail->getError(), 'send_to' => json_encode($sendTo)
                ];
            }
            if ($requireTransaction) {
                $transaction->commit();
            }

        } catch (\Exception $e) {
            if ($requireTransaction) {
                $transaction->rollback();
            }
            Yii::log('Exception reason: ' . $e->getTraceAsString() . ' sendTo: ' . json_encode($sendTo), CLogger::LEVEL_ERROR, __METHOD__);
            $result = [
                'status' => 'NOT_OK',
                'message_status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
        return $result;
    }

    public static function sendEmailViaSendGridApi($emailView, $layout, $emailSubject, $sendTo, $data, $viewPath = null, $attachment = null, $fromExtra = null, $businessId = null, $userId = null): array
    {
        if (!filter_var($sendTo, FILTER_VALIDATE_EMAIL)) {
            return ['status' => 'NOT_OK', 'message_status' => 'failed', 'error' => 'Invalid email address', 'message' => 'Invalid email address'];
        }

        if ($viewPath != null) {
            $file = str_replace('application', Yii::app()->basePath, str_replace('.', '/', $viewPath)) . '/' . $emailView . '.php';
            if (file_exists($file)) {
                Yii::log('ViewPath exists sendTo: ' . json_encode($sendTo) . ' viewPath: ' . $viewPath, CLogger::LEVEL_INFO, __METHOD__);
            } else {
                Yii::log('ViewPath doesn\'t exist. sendTo: ' . json_encode($sendTo) . ' viewPath: ' . $viewPath, CLogger::LEVEL_INFO, __METHOD__);
                $viewPath = 'application.views.mail.en';
            }
        }

        $emailConfig = Utils::getTransactionalEmailConfig($businessId ?? null);
        if (!$fromExtra) {
            $fromExtra = $emailConfig->from_name;
        }

        try {
            $controller = new CController('site');
            if ($viewPath != null) {
                $viewFile = str_replace('application', Yii::app()->basePath, str_replace('.', '/', $viewPath)) .'/' . $emailView . '.php';
            } else {
                $viewFile = Yii::app()->basePath . '/views/mail/' . $emailView . '.php';
            }

            $emailBody = $controller->renderInternal($viewFile, $data, true);

            $receiverName = "";

            $senderEmail = $emailConfig->from_email ?? Yii::app()->params['emailin'];

            $from = new \SendGrid\Mail\From($senderEmail, $fromExtra);
            $to = new \SendGrid\Mail\To($sendTo, $receiverName);
            $replyTo = new \SendGrid\Mail\ReplyTo($emailConfig->reply_to);
            $content = new \SendGrid\Mail\Content("text/html", $emailBody);
            $mail = new \SendGrid\Mail\Mail($from, $to, $emailSubject, null, $content);
            $sg = new \SendGrid(Yii::app()->params['sendGridApiKey']);

            $mail->setReplyTo($replyTo);

            $response = $sg->client->mail()->send()->post($mail);

            if ($response->statusCode() >= 200 && $response->statusCode() < 400) {
                return ['status' => 'OK', 'message_status' => 'sent', 'message' => 'Email Sent', 'send_to' => json_encode($sendTo)];
            }

            return [
                'status' => 'NOT_OK',
                'message_status' => 'failed',
                'message' => '',
                'error' => json_encode($response->headers()),
            ];
        } catch (Exception $e) {
            Utils::logMessage('Error sending email to=' . $sendTo . ' ' . $e, __METHOD__, CLogger::LEVEL_ERROR);
            return ['status' => 'NOT_OK', 'message_status' => 'failed', 'error' => $e->getMessage(), 'message' => $e->getMessage(),];
        }
    }

    /**
     * Generating a random string with a specific length used as a token for activation, redirecting from email
     * Used on : Website (Reset password, sending email, user activation)
     * @todo Consider using random_bytes or openssl_random_pseudo_bytes later
     * <AUTHOR>
     * @version Mar - Apr 2015 release V3.2.5
     * @param int length
     * @access public
     * @return string
     */
    public static function generateRandomString($length)
    {
        //str_shuffle does not generate cryptographically secure values, and should not be used for cryptographic purposes.
        //If you need a cryptographically secure value, consider using random_bytes, openssl_random_pseudo_bytes
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        return substr((string)microtime(true) . str_shuffle($chars), 0, $length);

        // use native random functions
        // if (function_exists('random_bytes')) {
        //     $string = bin2hex(random_bytes($length));// only in php7
        // } elseif (function_exists('openssl_random_pseudo_bytes')) {
        //     $string = bin2hex(openssl_random_pseudo_bytes($length));
        // } else {
        //     $string = str_shuffle($chars);
        // }

        // $string = microtime(true) . $string;

        // Generated bytes will always be longer than requested after
        // conversion to a hex and needs to be trimmed.
        // return substr((string)$string, 0, $length);
    }

    /**
     * Getting the lat and long from address
     * Used in : Website(Stores page, offers nearby, Business sign up)
     * <AUTHOR>
     * @version Jul - Aug 2015 release V3.3.1
     * @since 3.2.5
     * @access public
     * @return JSON/false
     */
    public static function getLongLatFromAddress($dlocation, $platformName)
    {
        // Yii::log('Address=' . $dlocation, CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
        //echo $dlocation
        $address = $dlocation; // Google HQ
        $prepAddr = str_replace(' ', '+', $address);
        try
        {
            $geocode = file_get_contents('http://maps.google.com/maps/api/geocode/json?address=' . $prepAddr . '&sensor=true');
            $output = json_decode($geocode);
            if ($output->status == 'ZERO_RESULTS') {
                Yii::log('Google geolocation from Address response=' . $geocode . ' address sent=' . $dlocation, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
                return false;
            } else {
                return $output;
            }
        } catch (CException $e) {
            Yii::log('Google geolocation from Address response=' . $e->getMessage() . ' address sent=' . $dlocation, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            return false;
        }
    }

    /**
     * Getting IP address
     * Used on : Website(getLatLongFromAddressOrIp)
     * @version Mar - Apr 2015 release V3.2.5
     * <AUTHOR>
     * @access public
     * @return string
     */
    public static function getExternalIP()
    {
        $ipaddress = null;
        foreach (array(
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR',
        ) as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    if (filter_var($ip, FILTER_VALIDATE_IP) !== false) {
                        $ipaddress = $ip;
                    }
                }
            }
        }

        if ($ipaddress == '127.0.0.1' || $ipaddress == '::1' || substr((string)$ipaddress??'', 0, 8) == '192.168.') {
            //return '*************';
            // is a local ip address
            $externalContent = file_get_contents('http://checkip.dyndns.com/');
            preg_match('/Current IP Address: ([\[\]:.[0-9a-fA-F]+)</', $externalContent, $m);
            return $m[1];
        } else {
            return $ipaddress;
        }
    }

    /**
     * Short a long url using Google or Bitly.
     * First it will short with Google if limit exceded or throws an error than short with bitly
     * Update: 2015-05-20 (Valentin), Additional release V3.2.5.1, using shortUrlKg as a main function for shortening
     * Used on : Website(shortUrlKg)
     *
     * @version Mar - Apr 2015 release V3.2.5
     * <AUTHOR>
     * @param string $longUrl
     * @param string $platformName
     * @access public
     * @return array
     */
    public static function shortUrl($longUrl, $platformName)
    {
        $apiLayerResponse = Utils::shortUrlApiLayer($longUrl, $platformName);

        if ($apiLayerResponse['status'] === 'OK') {
            return ['status' => 'OK', 'shortUrl' => $apiLayerResponse['shortUrl'], 'longUrl' => $longUrl];
        }

        $shortIsGdResponse = self::shortenUrlIsGd($longUrl);

        if ($shortIsGdResponse['status'] === 'OK') {
            return ['status' => 'OK', 'shortUrl' => $shortIsGdResponse['shortUrl'], 'longUrl' => $longUrl];
        }

        // $shortIoResponse = Utils::shortUrlShortIo($longUrl);

        // $googleResonse = Utils::shortUrlGoogle($longUrl, $platformName);

        /*if ($googleResonse['status'] == 'OK') {
            return ['status' => 'OK', 'shortUrl' => $googleResonse['shortUrl'], 'longUrl' => $longUrl];
        }*/
        /*if ($shortIoResponse['status'] === 'OK') {
            return ['status' => 'OK', 'shortUrl' => $shortIoResponse['shortUrl'], 'longUrl' => $longUrl];
        }*/

        return ['status' => 'NOT_OK', 'shortUrl' => $longUrl, 'longUrl' => $longUrl];
    }

    public static function shortUrlShortIo($longUrl, $platformName = ''): array
    {
        $client = new \GuzzleHttp\Client();

        $payload = json_encode(['domain' => 'anip.short.gy', 'originalURL' => $longUrl]);

        $response = $client->request('POST', 'https://api.short.io/links', [
            'body' => $payload,
            'headers' => [
                'Authorization' => 'sk_fQzLlj3raAHjg0Xx',
                'accept' => 'application/json',
                'content-type' => 'application/json',
            ],
        ]);

        $r = json_decode($response->getBody(), false);

        if ($r && isset($r->shortURL) && $r->shortURL) {
            return ['status' => 'OK', 'shortUrl' => $r->shortURL, 'longUrl' => $longUrl];
        }
        return ['status' => 'NOT_OK', 'shortUrl' => $longUrl, 'longUrl' => $longUrl];
    }

    public static function shortenUrlIsGd($longUrl)
    {
        $apiUrl = "https://is.gd/create.php?";

        $fullUrl = $apiUrl . http_build_query([
                'format' => 'json',
                'url' => $longUrl, // the URL is automatically URL encoded
            ]);

        $ch = curl_init($fullUrl);

        // Configure cURL options
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Execute the cURL request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($httpCode === 200) {
            $result = json_decode($response, true);
            if (isset($result['shorturl'])) {
                return ['status' => 'OK', 'shortUrl' => $result['shorturl'], 'longUrl' => $longUrl];
            } elseif (isset($result['errorcode'])) {
                Yii::log("Error {$result['errorcode']}: {$result['errormessage']}", CLogger::LEVEL_WARNING, __METHOD__);
                return ['status' => 'NOT_OK', 'shortUrl' => $longUrl, 'longUrl' => $longUrl];
            } else {
                return ['status' => 'NOT_OK', 'shortUrl' => $longUrl, 'longUrl' => $longUrl];
            }
        } else {
            return ['status' => 'NOT_OK', 'shortUrl' => $longUrl, 'longUrl' => $longUrl];
        }
    }

    /**
     * Short url with google
     * Update: 2015-05-20 (Valentin), Additional release V3.2.5.1, using shortUrlKg as a main function for shortening
     * Used on : Website(shortUrl)
     * @version Mar - Apr 2015 V3.2.5
     * <AUTHOR>
     * @param string $longUrl
     * @param string $platformName
     * @return array
     */
    public static function shortUrlGoogle($longUrl, $platformName)
    {
        // Get API key from : http://code.google.com/apis/console/
        $apiKey = Yii::app()->params['googleShortenerKey'];

        $postData = array('longUrl' => $longUrl);
        $jsonData = json_encode($postData);

        $curlObj = curl_init();

        curl_setopt($curlObj, CURLOPT_URL, 'https://www.googleapis.com/urlshortener/v1/url?key=' . $apiKey);
        curl_setopt($curlObj, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curlObj, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curlObj, CURLOPT_HEADER, 0);
        curl_setopt($curlObj, CURLOPT_HTTPHEADER, array('Content-type:application/json'));
        curl_setopt($curlObj, CURLOPT_POST, 1);
        curl_setopt($curlObj, CURLOPT_POSTFIELDS, $jsonData);

        $response = curl_exec($curlObj);

        if (curl_errno($curlObj)) {
            Yii::log('CURL ERROR=' . CJSON::encode(curl_error($curlObj)), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            return ['status' => 'NOT_OK', 'shortUrl' => '', 'message' => 'Url was not shortened'];
        }

        // Change the response json string to object
        $json = json_decode($response);

        curl_close($curlObj);

        if (isset($json->id)) {
            //Yii::log('Google Response='.CJSON::encode($json), CLogger::LEVEL_INFO, $platformName.'_'.__FUNCTION__);
            return ['status' => 'OK', 'shortUrl' => $json->id];
        } else {
            Yii::log('Google Response=' . CJSON::encode($json), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            return ['status' => 'NOT_OK', 'shortUrl' => '', 'message' => 'Errors shortening the url with Google'];
        }
    }

    /**
     * https://apilayer.com/
     *
     * @param $longUrl
     * @param $platformName
     * @return array|string[]
     */
    public static function shortUrlApiLayer($longUrl, $platformName = ''): array
    {
        $apiKey = 'U2MOFVxj4Y7OASJujMhVgbMk4zb6MGQi';
        // $apiKey = 'g12nZ2HojG4cxNr7V0KCowgGMiRnCcNP'; // local

        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => "https://api.apilayer.com/short_url/hash",
            CURLOPT_HTTPHEADER => [
                "Content-Type: application/json",
                "apikey: {$apiKey}"
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode(['long_url' => $longUrl]),
        ]);

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            Yii::log('CURL ERROR=' . CJSON::encode(curl_error($curl)), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            return ['status' => 'NOT_OK', 'shortUrl' => '', 'message' => 'Url was not shortened'];
        }

        $json = json_decode($response);

        curl_close($curl);

        if ($json && isset($json->short_url)) {
            return ['status' => 'OK', 'shortUrl' => $json->short_url];
        }

        return ['status' => 'NOT_OK', 'shortUrl' => '', 'longUrl' => $longUrl];
    }

    /**
     * Generating a qr code image for a user and save the image under photos/business/businessid/qrcode/useremail.png
     * <AUTHOR> N.
     * @used on website when importing users from the system admin
     * @version August - September 2015 - Version 3.3.2
     * @param string|int $qrCodeNumber
     * @param string $qrCodeImageName
     * @param string|int $businessId
     * @param string $todayDate
     */
    public static function generateQrCode($local, $qrCodeNumber, $qrCodeImageName, $businessId, $todayDate = null)
    {
        Yii::import('ext.qrcode.QRCode');
        $code = new QRCode($qrCodeNumber);
        if ($todayDate == null) {
            $todayDate = date("Y-m-d");
        }

        $path = $local . "photos/business/$businessId";
        // to flush the code directly
        //         $code->create();
        if (!is_dir($path)) {
            mkdir($path);
        }

        $path .= "/qrcode";

        if (!is_dir($path)) {
            mkdir($path);
        }

        $path .= "/$todayDate";

        if (!is_dir($path)) {
            mkdir($path);
        }

        // to save the code to file
        $imagePath = "$path/$qrCodeImageName.png";
        $code->create($imagePath);
        Yii::log('Create qrcode image at : ' . $imagePath, CLogger::LEVEL_INFO, __METHOD__);
    }

    /**
     * @param $businessId
     * @param $branchId
     * @return string[]
     * @throws CHttpException
     */
    public static function downloadBranchQrCode($businessId, $branchId): array
    {
        $branchIds = [$branchId];
        if($branchId === 0) {
            $branchIds = BusinessBranch::getAllowedBranchesByBusiness($businessId);
        }

        $qrCodes = [];

        foreach($branchIds as $branchId) {
            $branch = BusinessBranch::model()->findByAttributes([
                'business_fk' => $businessId,
                'business_branch_pk' => $branchId,
            ]);

            if (!$branch) {
                throw new CHttpException(404, Yii::t('backend', 'EX_ACCESS_DENIED'));
            }

            $branchUid = bin2hex($branch->branch_uid);

            $qrCodes[] = (object)[
                'code' => $branchUid,
                'name' => $branch->name
            ];
        }

        $zipFileName = date('Y-m-d') . '.zip';

        $qrCodeGenerator = new QrCodeGenerator($businessId, $qrCodes, 'branches', true);
        $qrCodeGenerator->generateQrCodeImages();
        $path = $qrCodeGenerator->createOrUpdateZipFile($zipFileName);

        return [
            'link' => "/".$path,
        ];
    }

    /**
     * @param Business $business
     * @param int $offerId
     * @return string[]
     * @throws CHttpException
     * @throws Exception
     */
    public static function downloadOfferQrCode(Business $business, int $offerId): array
    {
        $offerIds = [$offerId];
        if ($offerId === 0) {
            // TODO: get all offers (basic, exclude convertible, promos) for business
            throw new RuntimeException('Not implemented');
        }

        $businessId = $business->business_pk;
        $qrCodes = [];

        foreach ($offerIds as $offerId) {
            $offer = Offer::model()->findByPk($offerId);

            if (!$offer) {
                throw new CHttpException(404, Yii::t('backend', 'EX_ACCESS_DENIED'));
            }

            if (!$offer->qrcode) {
                $offer->saveAttributes([
                    'qrcode' => Offer::generateQRCode()
                ]);
                $offer->refresh();
            }

            $fileName = str_replace('/', '-', $business->name) . '-' . $offerId;
            $qrCodes[] = (object)[
                'code' => $offer->qrcode,
                'name' => $fileName
            ];
        }

        $zipFileName = date('Y-m-d') . '.zip';

        $qrCodeGenerator = new QrCodeGenerator($businessId, $qrCodes, 'offer_qrcode', true);
        $qrCodeGenerator->generateQrCodeImages();
        $path = $qrCodeGenerator->createOrUpdateZipFile($zipFileName);

        return [
            'link' => "/" . $path,
        ];
    }

    /**
     * Creates a QR Code for the sign-up widget URL
     * @param Business $business
     * @return string[]
     */
    public static function createSignUpLinkQrCode(Business $business, BusinessRules $businessRules, $reWriteExistingImage = false): array
    {
        $path = '';
        try {
            $branch = $business->mainBranch;
            $entityToken = EntityToken::model()->findByAttributes(['business_branch_fk' => $branch->business_branch_pk, 'enabled' => 1]);

            if (!$entityToken) {
                $entityToken = EntityToken::create($branch->business_branch_pk, 'branch', $branch->branch_timezone_fk);
            }

            //Set the new signup widget URL if rule is enabeld
            if ($businessRules->enable_signup_widget_v2) {
                $signupWidgetUrl = Yii::app()->params['signupWidgetV2Url'];
            } else {
                $signupWidgetUrl = Yii::app()->params['serverUrl'] . '/app/signUpForm';
            }

            $url = $signupWidgetUrl . '?' . http_build_query([
                'token'=> $entityToken->token,
                'showLogo'=> false,
            ]);

            $qrCodeGenerator = new QrCodeGenerator($business->business_pk, [], 'sign_up_qrcode', $reWriteExistingImage);
            $path = '/' . $qrCodeGenerator->generateQrCodeImage((object) [
                'code' => $url,
                'name' => bin2hex($business->business_uid) . strtotime((string)date('Y-m-d H:i:s')),
            ]);
        } catch (\Exception $e) {
            Utils::logMessage($e . ' ' . json_encode(['business_pk' => $business->business_pk,]), __METHOD__, CLogger::LEVEL_WARNING);
        }

        return ['link' => $path,];
    }

    /**
     * get a json object and the language id and returns the right text
     * used on website smartphone when reading multi language
     * <AUTHOR> N.
     * @version Sep 2015 V3.4.0
     * @param string $jsonObject
     * @param int $langId
     * @return string
     */
    public static function getTextByLanguage($jsonObject, $langId)
    {
        if ($jsonObject == null || $jsonObject == '') {
            return '';
        }

        $textArray = CJSON::decode($jsonObject);
        $defaultLanguage = Language::model()->findByAttributes(['default' => 1, 'enabled' => 1])->language_pk;
        if (array_key_exists($langId, $textArray)) {
            return $textArray[$langId] ?? '';
        } elseif ((array_key_exists($defaultLanguage, $textArray))) {
            return $textArray[$defaultLanguage] ?? '';
        } elseif (count($textArray) > 0) {
            return array_values($textArray)[0] ?? '';
        } else {
            return '';
        }

    }

    /**
     * create a json object with the language id and corresponding text
     * used on website smartphone when inserting multi language
     * <AUTHOR> N.
     * @version Sep 2015 V3.4.0
     * @param string $text
     * @param object|null $jsonObject
     * @param integer $langId
     * @return string in right language
     */
    public static function createMultiLanguageJson($text, $langId, $jsonObject = null)
    {
        if ($jsonObject == null) {
            $jsonObject = [$langId => $text];
        } else {
//            $jsonObject = CJSON::decode($jsonObject);
            $jsonObject = json_decode($jsonObject, true, 512, JSON_UNESCAPED_UNICODE);
            $jsonObject[$langId] = $text;
        }
        return json_encode($jsonObject, JSON_UNESCAPED_UNICODE);
//        return CJSON::encode($jsonObject);
    }

    /**
     * create a json object with the key/value for user settings
     * used on website smartphone when inserting user settings
     * <AUTHOR> N.
     * @version June July 2016 V4.1
     * @param unknown $jsonObject
     * @param unknown $key
     * @param string $value
     * @return JSON to be inserted/updated in db
     */
    /*public static function createUpdateJsonSettings($key, $value, $jsonObject = null)
    {
        if ($jsonObject == null) {
            $jsonObject = [$key => $value];
        } else {
            $jsonObject = CJSON::decode($jsonObject);
            $jsonObject[$key] = $value;
        }
        return CJSON::encode($jsonObject);
    }*/

//     /**
    //      * get the value of the user settings by keys
    //      * used on website smartphone when reading user settings
    //      * <AUTHOR> N.
    //      * @version June July 2016 V4.1
    //      * @param unknown $jsonObject
    //      * @param unknown $key
    //      * @return JSON result
    //      */
    //     public static function createUpdateJsonSettings($key, $value, $jsonObject = null)
    //     {
    //         if ($jsonObject == null) {
    //             $jsonObject = [$key => $value];
    //         } else {
    //             $jsonObject = CJSON::decode($jsonObject);
    //             $jsonObject[$key] = $value;
    //         }
    //         return CJSON::encode($jsonObject);
    //     }

    /**
     * create a json object with images small, medium, large, default
     * used on website smartphone when inserting new image
     * <AUTHOR> N.
     * @version Sep 2015 V3.4.0
     * @param array $large
     * @param array $medium
     * @param array $thumb
     * @param string $default
     * @return Json of images
     */
    public static function prepareJsonImagesForInsert($large, $medium, $thumb, $default)
    {
        $jsonObject = ['large' => $large, 'medium' => $medium, 'thumb' => $thumb, 'default' => $default];
        return CJSON::encode($jsonObject);
    }

    /**
     * read the default image from the JSON object of the images stored in the DB
     * <AUTHOR> N.
     * @version Sep 2015 V3.4.0
     * @param string $size large, medium, thumb
     * @param JSON string $jsonObject
     * @return string default image with corresponding size
     */
    public static function getDefaultImageFromJson($size, $jsonObject)
    {
        $default = [
            'medium' => ['1' => '/images/kangaroo_image_default.png'],
            'thumb' => ['1' => '/images/kangaroo_image_default.png'],
            'large' => ['1' => '/images/kangaroo_image_default.png'],
            'default' => '1',
        ];

        $images = empty($jsonObject)? null : CJSON::decode($jsonObject);
        $count = is_array($images) ? count($images[$size]) : 0; // Fix php7.2

        if ($count > 0 && $images[$size][$images['default']] != '') {
            return $images[$size][$images['default']];
        } elseif (count($default[$size]) > 0) {
            return $default[$size][$default['default']];
        } else {
            return '';
        }

    }

    /**
     * read the images from the JSON object of the images stored in the DB
     * <AUTHOR> N.
     * @version Sep 2015 V3.4.0
     * @param string $size large, medium, thumb
     * @param JSON string $jsonObject
     * @return array of images with corresponding size
     */
    public static function getImagesFromJson($size, $jsonObject)
    {
        $images = empty($jsonObject)? null : CJSON::decode($jsonObject);
        $count = is_array($images) ? count($images[$size]) : 0; // Fix php7.2
        if ($count > 0) {
            return $images[$size];
        } else {
            return [];
        }

    }

    /**
     * generate a 4 digit random number
     *
     * Used in website. smartphone
     * <AUTHOR> N.
     * @version March - April 2016 V4.0
     * @return number
     */
    public static function generatePin($digits = 4)
    {
        return abs(random_int(10 ** ($digits - 1), 10 ** $digits - 1));
    }

    /**
     * generate a username 12 digits random number
     *
     * Used in website. smartphone
     * <AUTHOR> N.
     * @version June - July 2016 V4.1
     * @return number
     */
    public static function generateUniqueUsername()
    {
//        $username = str_pad((string)str_replace('.', '', microtime(true)), 14, 0);

        $username = (string) self::generatePin(12);
        $user = User::model()->findByAttributes(['username' => $username]);
        if ($user) {
            Yii::log('username found ' . $username, CLogger::LEVEL_ERROR, __METHOD__);
            return self::generateUniqueUsername();
        }

        return $username;
    }

    /**
     * insert or modify any model
     * @version June July 2016 V4.1
     * <AUTHOR> N.
     * @param object|CActiveRecord $model
     * @return object
     * @throws \Exception
     * @throws CDbException
     */
    public static function saveModel($model)
    {
        if (!$model->save()) {
            throw new \RuntimeException(CJSON::encode($model->getErrors()) .' Model'. get_class($model) . ' attributes=' . json_encode($model->attributes));
        }

        return $model;
    }

    /**
     * sort multi dimensional array
     * @version June July 2016 V4.1
     * <AUTHOR> N.
     * @param model $model
     * @return sorted array
     */
    public static function aasort($array, $key)
    {
        $sorter = array();
        $ret = array();
        reset($array);
        foreach ($array as $ii => $va) {
            $sorter[$ii] = $va[$key];
        }
        asort($sorter);
        foreach ($sorter as $ii => $va) {
            $ret[] = $array[$ii];
        }
        return $ret;
    }
    /**************************************************************************************************/
    /******************************** ALL Private Functions START HERE ********************************/
    /**************************************************************************************************/

    /**
     * Getting Time Zone information using The Google Time Zone API
     * Docs: https://developers.google.com/maps/documentation/timezone/
     *
     * Used in : Website(Log in)
     *
     * @version May - June 2015 release V3.3.0
     * <AUTHOR>
     * @access private
     * @param float $lat - latitude
     * @param float $long - longitude
     * @param int $timestamp
     * @param string $platformName
     * @return array
     */
    private static function getTimeZoneByGoogle($lat, $long, $timestamp, $platformName)
    {
        $ip = Yii::app()->request->userHostAddress;
        $key = Yii::app()->params["googleTimezoneKey"];
        $apiUrl = 'https://maps.googleapis.com/maps/api/timezone/json';
        $location = $lat . ',' . $long;
        $params = '?location=' . $location . '&timestamp=' . $timestamp . '&key=' . $key;

        $cacheKey = 'timezone_' . md5($lat . $long . $ip);
        $cachedTimezone = Yii::app()->cache->get($cacheKey);

        if ($cachedTimezone !== false) {
            //Yii::log('Timezone retrieved from cache for lat=' . $lat . ' long=' . $long . ' timestamp=' . $timestamp, CLogger::LEVEL_INFO, $platformName . '_' . __FUNCTION__);
            return $cachedTimezone;
        }

        if (!$resultJson = file_get_contents($apiUrl . $params)) {
            $error = error_get_last();

            Yii::log('Failed to get Timezone error=' . $error['message'] . ' lat=' . $lat . ' long=' . $long . ' timestamp=' . $timestamp, CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
            return ['status' => 'NOT_OK'];
        } else {
            $result = json_decode($resultJson);

            if (isset($result->status, $result->timeZoneId) && $result->status === 'OK') {
                $tzPk = TimezoneMysql::getTMZPkByTMZName($result->timeZoneId, $platformName);
                $timezoneData = ['status' => 'OK', 'timeZoneId' => $result->timeZoneId, 'timeZonePk' => $tzPk];
                Yii::app()->cache->set($cacheKey, $timezoneData, 24 * 60 * 60); // Cache for 24 hours
                return $timezoneData;
            } else {
                Yii::log('Failed to get Timezone; response=' . $resultJson . ' lat=' . $lat . ' long=' . $long . ' timestamp=' . $timestamp . ' ip=' . $ip . " DEBUG_BACKTRACE: \n" . Utils::getTraces(), CLogger::LEVEL_ERROR, $platformName . '_' . __FUNCTION__);
                $timezoneData = ['status' => 'OK', 'timeZoneId' => 'America/Chicago', 'timeZonePk' => 94];
                Yii::app()->cache->set($cacheKey, $timezoneData, 60 * 60); // Cache for 1 hour
                return $timezoneData;
                // return ['status' => 'NOT_OK'];
            }
        }
    }

    /**
     * Gets the message for a status code
     * @version Mar - Apr 2015 release V3.2.5
     * <AUTHOR>
     * @param mixed $status
     * @access private
     * @return string
     */
    private static function _getStatusCodeMessage($status)
    {
        // these could be stored in a .ini file and loaded
        // via parse_ini_file()... however, this will suffice
        // for an example
        $codes = array(
            100 => 'Continue',
            101 => 'Switching Protocols',
            200 => 'OK',
            201 => 'Created',
            202 => 'Accepted',
            203 => 'Non-Authoritative Information',
            204 => 'No Content',
            205 => 'Reset Content',
            206 => 'Partial Content',
            300 => 'Multiple Choices',
            301 => 'Moved Permanently',
            302 => 'Found',
            303 => 'See Other',
            304 => 'Not Modified',
            305 => 'Use Proxy',
            306 => 'User Authentication Required',
            307 => 'Temporary Redirect',
            400 => 'Bad Request',
            401 => 'Unauthorized',
            402 => 'Payment Required',
            403 => 'Forbidden',
            404 => 'Not Found',
            405 => 'Method Not Allowed',
            406 => 'Not Acceptable',
            407 => 'Proxy Authentication Required',
            408 => 'Request Timeout',
            409 => 'Conflict',
            410 => 'Gone',
            411 => 'Length Required',
            412 => 'Precondition Failed',
            413 => 'Request Entity Too Large',
            414 => 'Request-URI Too Long',
            415 => 'Unsupported Media Type',
            416 => 'Requested Range Not Satisfiable',
            417 => 'Expectation Failed',
            500 => 'Internal Server Error',
            501 => 'Not Implemented',
            502 => 'Bad Gateway',
            503 => 'Service Unavailable',
            504 => 'Gateway Timeout',
            505 => 'HTTP Version Not Supported',
        );

        return (isset($codes[$status])) ? $codes[$status] : '';
    }

    /**************************************************************************************************/
    /******************************** ALL Private Functions END HERE **********************************/
    /**************************************************************************************************/
    public static function googleReCAPTCHAVerification($responseToken, $ip=null)
    {
        try {
            if(isset($responseToken)&&$responseToken!='') {
                $reqInfo['apiUrl'] = 'https://www.google.com/recaptcha/api/siteverify';
                $formData['secret'] = Yii::app()->params['googleReCAPTCHASecret'];
                $formData['response'] = $responseToken;
                if (isset($ip) && $ip != '') {
                    $formData['remoteip'] = $ip;
                } else {
                    $ip = (Yii::app()->request->userHostAddress == '127.0.0.1' || Yii::app()->request->userHostAddress == '::1') ? null : Yii::app()->request->userHostAddress;
                    if ($ip == null) {
                        $ip = self::getExternalIPV330(PlatformSolution::PLATFORM_NAME_WEBSITE);
                        if (isset($ip['ip'])) {
                            $formData['remoteip'] = $ip['ip'];
                        }
                    }
                }
                $response = self::httpRequestV2($reqInfo, $formData, PlatformSolution::PLATFORM_NAME_WEBSITE);
                $result = CJSON::decode($response);
                if (isset($result['success']) && $result['success'] == true) {
                    return true;
                } else {
                    Utils::logMessage($response, __METHOD__);
                }
            }
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }




    /**
     * generateTokenMessage - Used To generate JWT Message in order to validate URL security
     * @params $storeId, $isApiInstallCall
     * <AUTHOR> @version 1.0.0, @since Dec 2019
     */

    public static function generateTokenMessage($storeHash,$isApiInstallCall=false)
    {
        try {

            if (!$isApiInstallCall) {
                $posBranch = PosBranch::model()->findByAttributes(["shop_id" => $storeHash, "enabled" => 1, "pos_system_fk" => POS_SYSTEM_BIGCOMMERCE]);
                $storeInfo = json_decode($posBranch->custom_field_json ?? '[]');
                $timezone = $storeInfo->timezone->name??"UTC";
                if($timezone == "UTC"){
                    $isApiInstallCall=true;
                }
            }else{
                $timezone = "UTC";
            }

            date_default_timezone_set("$timezone");
            $message = array(
                "store_hash" => "$storeHash",
                "exp" => \time()+600,
                "customer" => true,
                "isApiInstallCall" => $isApiInstallCall
            );

            $secret = Yii::app()->params["BigCommerceClientSecret"];
            $message_encoded = \Firebase\JWT\JWT::encode($message, $secret, 'HS512');
//            $messageDecoded = \Firebase\JWT\JWT::decode($message_encoded, $secret, array('HS512'));
            return $message_encoded;


        } catch (Exception $e) {
            Yii::log($e, CLogger::LEVEL_ERROR, __METHOD__);
            return '';
        }
    }

    public static function voucherFlag($businessId)
    {
        $posBranches = PosBranch::model()->findByAttributes(['business_fk' => $businessId, 'pos_system_fk' => POS_SYSTEM_LIGHTSPEED, 'enabled' => 1]);
        $businessRules = BusinessRules::model()->findByAttributes(['business_fk' => $businessId]);
        if ($businessRules->voucher_flag) {
            if (!$businessRules->coupons_convertible_flag && !$posBranches) {
                return true;
            }
        }
        return false;
    }

    /*
     * Whiten the color hex by percentage
     */
    public static function whitenHex($hexInput, $percent) {
        $hexInput = substr((string)$hexInput, 1);
        $split = str_split($hexInput, 2);
        $r = hexdec($split[0]);
        $g = hexdec($split[1]);
        $b = hexdec($split[2]);
        $result = self::whitenRgb($r, $g, $b, $percent);
        return sprintf("#%02x%02x%02x", $result[0], $result[1], $result[2]);
    }

    /*
     * Whiten the color rbg by percentage
     */
    public static function whitenRgb($inputRed, $inputGreen, $inputBlue, $percent) {
        $hslValue = self::rgbToHsl($inputRed, $inputGreen, $inputBlue);
        $hslValue[2] = 0.5 + ($percent / 200); // the percentage of whiteness you want
        $outputRgb = self::hslToRgb($hslValue[0], $hslValue[1], $hslValue[2]);
        return $outputRgb;
    }

    private static function rgbToHsl( $r, $g, $b ) {
        $r /= 255;
        $g /= 255;
        $b /= 255;
        $max = max( $r, $g, $b );
        $min = min( $r, $g, $b );
        $l = ( $max + $min ) / 2;
        $d = $max - $min;
        if( $d == 0 ){
            $h = $s = 0; // achromatic
        } else {
            $s = $d / ( 1 - abs( 2 * $l - 1 ) );
            switch( $max ){
                case $r:
                    $h = 60 * fmod( ( ( $g - $b ) / $d ), 6 );
                    if ($b > $g) {
                        $h += 360;
                    }
                    break;
                case $g:
                    $h = 60 * ( ( $b - $r ) / $d + 2 );
                    break;
                case $b:
                    $h = 60 * ( ( $r - $g ) / $d + 4 );
                    break;
            }
        }
        return array( round( $h, 2 ), round( $s, 2 ), round( $l, 2 ) );
    }

    private static function hslToRgb( $h, $s, $l )
    {
        $c = (1 - abs(2 * $l - 1)) * $s;
        $x = $c * (1 - abs(fmod(($h / 60), 2) - 1));
        $m = $l - ($c / 2);
        if ($h < 60) {
            $r = $c;
            $g = $x;
            $b = 0;
        } else if ($h < 120) {
            $r = $x;
            $g = $c;
            $b = 0;
        } else if ($h < 180) {
            $r = 0;
            $g = $c;
            $b = $x;
        } else if ($h < 240) {
            $r = 0;
            $g = $x;
            $b = $c;
        } else if ($h < 300) {
            $r = $x;
            $g = 0;
            $b = $c;
        } else {
            $r = $c;
            $g = 0;
            $b = $x;
        }
        $r = ($r + $m) * 255;
        $g = ($g + $m) * 255;
        $b = ($b + $m) * 255;
        return array(floor($r), floor($g), floor($b));
    }

    public static function cardNumberDisplay($card)
    {
        return '****' . substr((string)$card??'', -6);
    }

    public static function getTransactionalEmailConfig($businessId, $sendFromType = '')
    {
        $emailProvider = null;
        if(isset($businessId)) {
            $businessRule = BusinessRules::getBusinessRules($businessId);
            if ($businessRule->allow_custom_sender_email) {
                $emailProvider = \application\models\EmailProvider::model()->findByAttributes([
                    'business_fk' => $businessId,
                    'enabled' => 1
                ]);
            }
        }

        $sendFrom = Yii::app()->params['emailin'];
        if (!empty($emailProvider->from_email)) {
            $sendFrom = $emailProvider->from_email;
        }

        $replyTo = Yii::app()->params['emailout'];
        if (!empty($emailProvider->reply_to)) {
            $replyTo = $emailProvider->reply_to;
        }


        if (isset($emailProvider)) {
            if (empty($emailProvider->from_name)) {
                if ($sendFromType == 'mainBranch') {
                    $sendFromName = BusinessBranch::getDefaultBranch($businessId)['name'];
                } else {
                    $sendFromName = Business::model()->findByPk($businessId)->name;
                }
            } else {
                $sendFromName = $emailProvider->from_name;
            }
        } else {
            try {
                if ($sendFromType == 'business') {
                    $sendFromName = Business::model()->findByPk($businessId)->name;
                } elseif ($sendFromType == 'mainBranch') {
                    $sendFromName = BusinessBranch::getDefaultBranch($businessId)['name'];
                } else {
                    $sendFromName = Yii::app()->params['fullnamemailin'];
                }
            } catch (\Exception $e) {
                $sendFromName = Yii::app()->params['fullnamemailin'];
            }
        }

        return (object)[
            'from_email' => $sendFrom,
            'from_name' => $sendFromName,
            'reply_to' => $replyTo
        ];
    }

    /**
     * Check if the business comes from LS ecom app store or Shopify app store
     * and if the business has subscription
     * If no, then the business doesn't have access permission
     * <AUTHOR>
     * @return bool
     */
    public static function checkBusinessAccessSignUpViaIntegration()
    {
        try {
            if (Yii::app()->session->get('business_subscription_required') !== null) {
                return true;
            }
            $userId = Yii::app()->user->id;
            if (Yii::app()->user->checkAccess('business')) {
                $userEntities = UserEntityPermission::getUserEntitiesByRole($userId, __METHOD__, PlatformSolution::PLATFORM_NAME_WEBSITE);
                $businessId = $userEntities['businessId'];
                $businessModel = Business::model()->findByPk($businessId);
                if ($businessModel->status_manager_fk == STATUS_MANAGER_INTEGRATION_SIGNUP_WAIT_FOR_SUBSCRIPTION
                    || $businessModel->status_manager_fk == STATUS_MANAGER_WAIT_FOR_UPDATING_SUBSCRIPTION) {
                    $subscription = Subscription::model()->findAllByAttributes([
                        'business_fk' => $businessId,
                        'enabled' => 1,
                    ], [
                        'order' => 'id DESC',
                        'condition' => 'package_fk IS NOT NULL',
                    ]);
                    if ($subscription) {
                        Yii::app()->session->add('business_subscription_required', false);
                        return true;
                    }
                    $userModel = User::model()->findByPk($userId);
                    $merchantAppLead = MerchantAppLead::model()->findByAttributes(['email' => $userModel->email], ['order' => 'enabled desc']);
                    if ($merchantAppLead && in_array($merchantAppLead->platform_solution_fk, [PLATFORM_POS_SHOPIFY, PLATFORM_POS_LIGHTSPEED_ECOM])) {
                        return false;
                    }
                }
            }
            Yii::app()->session->add('business_subscription_required', false);
            return true;
        } catch (\Exception $e) {
            Yii::log($e->getMessage(), CLogger::LEVEL_ERROR, __METHOD__);
            return false;
        }
    }

    /**
     * Calculate the max redeem amount by balance and ratio
     * @param $ratio
     * @param $ptsBalance
     * @return float|int
     */
    public static function getMaxRedeemAmountByBalance($ratio, $ratioAmount, $ptsBalance)
    {
        //return floor($ptsBalance * 100 / $ratio) / 100;
        if (empty($ratio)) {
            return 0;
        }

        $amount = ($ptsBalance * $ratioAmount) / $ratio;
        return floor($amount * 100) / 100;
    }

    /**
     * Return phone number by extracting the country code
     *
     * <AUTHOR>
     * @since 03/03/2022
     * @param string $phoneNb
     * @retrun string
     */
    public static function extractCountryCode($phoneNb)
    {
        return preg_replace(
            '/\+(?:998|996|995|994|993|992|977|976|975|974|973|972|971|970|968|967|966|965|964|963|962|961|960|886|880|856|855|853|852|850|692|691|690|689|688|687|686|685|683|682|681|680|679|678|677|676|675|674|673|672|670|599|598|597|595|593|592|591|590|509|508|507|506|505|504|503|502|501|500|423|421|420|389|387|386|385|383|382|381|380|379|378|377|376|375|374|373|372|371|370|359|358|357|356|355|354|353|352|351|350|299|298|297|291|290|269|268|267|266|265|264|263|262|261|260|258|257|256|255|254|253|252|251|250|249|248|246|245|244|243|242|241|240|239|238|237|236|235|234|233|232|231|230|229|228|227|226|225|224|223|222|221|220|218|216|213|212|211|98|95|94|93|92|91|90|86|84|82|81|66|65|64|63|62|61|60|58|57|56|55|54|53|52|51|49|48|47|46|45|44\D?1624|44\D?1534|44\D?1481|44|43|41|40|39|36|34|33|32|31|30|27|20|7|1\D?939|1\D?876|1\D?869|1\D?868|1\D?849|1\D?829|1\D?809|1\D?787|1\D?784|1\D?767|1\D?758|1\D?721|1\D?684|1\D?671|1\D?670|1\D?664|1\D?649|1\D?473|1\D?441|1\D?345|1\D?340|1\D?284|1\D?268|1\D?264|1\D?246|1\D?242|1)\D?/'
            , ''
            , $phoneNb
        );
    }

    public static function getBusinessPackagePricingUrl($businessId, $nbp_origin)
    {
        $merchantAppLead = MerchantAppLead::model()->findByAttributes(['business_fk' => $businessId], ['order' => 'enabled desc']);
        $selfSignUpFromShopifyFlag = (isset($merchantAppLead) && ($merchantAppLead->platform_solution_fk == PLATFORM_POS_SHOPIFY)) ? true : false;
        if ($selfSignUpFromShopifyFlag) {
            $posBranch = PosBranch::model()->findByAttributes([
                'pos_system_fk' => POS_SYSTEM_SHOPIFY,
                'enabled' => 1,
                'business_fk' => $businessId
            ]);
            if ($posBranch) {
                return Utils::getShopifyPricingUrl($nbp_origin);
            }
        }
        return null;
    }

    public static function getShopifyPricingUrl($nbp_origin)
    {
        if (isset($nbp_origin) && ($nbp_origin || $nbp_origin == '1')) {
            return rtrim((string)Yii::app()->params['betaUrl'], '/') . '/#/advanced/shopifyPackage';
        } else {
            return rtrim((string)Yii::app()->params['serverUrl'], '/') . '/package/shopify';
        }
    }

    public static function getIntegrationsUrl($nbp_origin, $posSystemId)
    {
        if (isset($nbp_origin) && ($nbp_origin || $nbp_origin == '1')) {
            $url = rtrim((string)Yii::app()->params['betaUrl'], '/');
            switch ($posSystemId) {
                case POS_SYSTEM_SHOPIFY:
                    $url .= '/#/settings/integrations/settings/shopify';
                    break;
                case POS_SYSTEM_LIGHTSPEED_ECOM:
                    $url .= '/#/settings/integrations/settings/lsEcom';
                    break;
                case POS_SYSTEM_BIGCOMMERCE:
                    $url .= '/#/settings/integrations/settings/bigCommerce';
                    break;
                case POS_SYSTEM_WOOCOMMERCE_ECOM:
                    $url .= '/#/settings/integrations/settings/wooCommerce';
                    break;
                case POS_SYSTEM_MAGENTO:
                    $url .= '/#/settings/integrations/settings/magento';
                    break;
                case POS_SYSTEM_VEND:
                    $url .= '/#/settings/integrations/settings/lsSeriesX';
                    break;
                case POS_SYSTEM_LIGHTSPEED:
                    $url .= '/#/settings/integrations/settings/lsRetail';
                    break;
                case POS_SYSTEM_LIGHTSPEED_RESTO:
                    $url .= '/#/settings/integrations/settings/lsRestaurant';
                    break;
                case POS_SYSTEM_HEARTLAND:
                    $url .= '/#/settings/integrations/settings/heartland';
                    break;
                default:
                    $url .= '/#/settings/integrations/dashboard';
            }

            return $url;
        } else {
            if (Yii::app()->user->isGuest) {
                return rtrim((string)Yii::app()->params['serverUrl'], '/') . '/merchant/login?redirect=/businessAdmin/integrations';
            } else {
                return rtrim((string)Yii::app()->params['serverUrl'], '/') . '/businessAdmin/integrations';
            }
        }
    }

    public static function getMerchantLoginUrl($nbp_origin)
    {
        if (isset($nbp_origin) && ($nbp_origin || $nbp_origin == '1')) {
            return rtrim((string)Yii::app()->params['betaUrl'], '/');
        } else {
            return rtrim((string)Yii::app()->params['serverUrl'], '/') . '/merchant/login';
        }
    }

    /**
     * Find out if a feature is enabled for a specific business
     *
     * <AUTHOR>
     * @since 12/07/2022
     * @param string $flag
     * @param integer $businessId
     * @return bool
     */
    public static function featureFlagForBusiness($flag, $businessId)
    {
        $cacheKey = $flag ."_". $businessId;
        $cachedFeatureFlag = Yii::app()->request->getFeatureCache($cacheKey);
        if (is_bool($cachedFeatureFlag)) {
            return $cachedFeatureFlag;
        }
        $result = Yii::app()->db->createCommand()
            ->select("ff.enabled")
            ->from('feature_flags ff')
            ->where('ff.name = :flag AND FIND_IN_SET(:businessId, (ff.businessIds))', array(':flag' => $flag, ':businessId' => $businessId))
            ->queryRow();

        if (!isset($result['enabled'])) {
            return false;
        }
        Yii::app()->request->setFeatureCache($cacheKey, !!$result['enabled']);
        return $result['enabled'] == 1;
    }

    public static function getPosSystemIdByConstantName($posSystem)
    {
        if ($posSystem === 'POS_SYSTEM_LIGHTSPEED_ECOM') {
            return POS_SYSTEM_LIGHTSPEED_ECOM;
        } elseif ($posSystem === 'POS_SYSTEM_MAGENTO') {
            return POS_SYSTEM_MAGENTO;
        } elseif ($posSystem === 'POS_SYSTEM_BIGCOMMERCE') {
            return POS_SYSTEM_BIGCOMMERCE;
        } elseif ($posSystem === 'POS_SYSTEM_SHOPIFY') {
            return POS_SYSTEM_SHOPIFY;
        } elseif ($posSystem === 'POS_SYSTEM_WOOCOMMERCE_ECOM') {
            return POS_SYSTEM_WOOCOMMERCE_ECOM;
        } elseif ($posSystem === 'POS_SYSTEM_ECOMZ') {
            return POS_SYSTEM_ECOMZ;
        } elseif ($posSystem === 'POS_SYSTEM_VEND') {
            return POS_SYSTEM_VEND;
        } elseif ($posSystem === 'POS_SYSTEM_LIGHTSPEED') {
            return POS_SYSTEM_LIGHTSPEED;
        } elseif ($posSystem === 'POS_SYSTEM_LIGHTSPEED_RESTO' || $posSystem === 'POS_SYSTEM_RESTO') {
            return POS_SYSTEM_LIGHTSPEED_RESTO;
        } elseif ($posSystem === 'POS_SYSTEM_HEARTLAND') {
            return POS_SYSTEM_HEARTLAND;
        } elseif ($posSystem === 'POS_SYSTEM_LIGHTSPEED_RESTO_K_SERIES') {
            return POS_SYSTEM_LIGHTSPEED_RESTO_K_SERIES;
        } elseif ($posSystem === 'POS_SYSTEM_ECWID') {
            return POS_SYSTEM_ECWID;
        }

        return constant($posSystem);
    }

    /**
     * Check if business has MFA enabled and if user is verified
     *
     * Used in: BusinessAdminController, RolesController, CustomersController
     * <AUTHOR>
     * @since 15/12/2022
     * @return bool
     * @throws CHttpException
     */
    public static function checkMfaVerification()
    {
        if (!Yii::app()->user->checkAccess('superadmin')) {
            $user = Yii::app()->user;

            $userEntities = UserEntityPermission::getUserEntitiesByRole(
                $user->id, __METHOD__, PlatformSolution::PLATFORM_NAME_WEBSITE
            );

            //Get business profile to check if the MFA is enabled
            $businessProfile = BusinessProfile::model()->findByAttributes([
                'business_fk' => $userEntities['businessId']
            ]);

            // Get business rules
            $businessRules = BusinessRules::getBusinessRules($userEntities['businessId']);

            if (Yii::app()->session->get('force_mfa') && !Yii::app()->session->get('user_verified')) {
                throw new CHttpException(403, Yii::t('backend', 'EX_ACCESS_DENIED'));
            }

            if ($businessRules && $businessRules->enable_mfa_flag == 1 && $businessProfile && $businessProfile->mfa_enabled == 1 && !Yii::app()->session->get('user_verified')) {
                throw new CHttpException(403, Yii::t('backend', 'EX_ACCESS_DENIED'));
            }
        }

        return true;
    }

    /**
     * Check MFA verification for the system admin module.
     * @return void
     * @throws CHttpException
     */
    public static function checkMfaVerificationForSystemAdmin(): void
    {
        $user = Yii::app()->user;

        if (Yii::app()->session->get('force_mfa') && !Yii::app()->session->get('user_verified')) {
            throw new CHttpException(403, Yii::t('backend', 'EX_ACCESS_DENIED'));
        }
    }

    /**
     * Transform the response of qr code items
     * @param $response
     * @return string
     */
    public static function transformQrCodeItemsResponse($response): string
    {
        $items = [];

        // Convert JSON response to an array
        $data = CJSON::decode($response);

        // Transform and response into common response
        if (isset($data['data']) && !empty($data['data'])) {
            $items = array_map(function($item) {
                return array(
                    "id" => (string) $item['id'],
                    "external_id" => (string) $item['external_id'],
                    "title" => $item['title']
                );
            }, $data['data']);
        }

        $response = [
            'status' => 'OK',
            'items' => $items,
            'itemIdField' => 'id',
            'itemNameField' => 'title'
        ];

        return CJSON::encode($response);
    }

    /**
     * Get user amount based on PWP ratio and current active points
     * @param $businessRules
     * @param $userActivePoints
     */
    public static function getUserPointsAmount($businessRules, $userActivePoints)
    {
        if (empty($businessRules->master_redemption_ratio)) {
            return 0;
        }

        $amount = ($userActivePoints * $businessRules->master_redemption_amount) / $businessRules->master_redemption_ratio;
        $amount = floor($amount * 100) / 100;
        $amount = number_format($amount, 2, ".", ",");

        return str_replace(".00", "", $amount);
    }

    /**
     * Check if user is a branch admin
     * @return bool
     */
    public static function isBranchAdmin(): bool
    {
        if (Yii::app()->user->checkAccess('branch') && Yii::app()->user->checkAccess('admin')) {
            return true;
        }

        return false;
    }

    /**
     * Handle showing access denied (JSON response for the API calls - Exception thrown for the web)
     * @return void
     * @throws CHttpException
     */
    public static function handleAccessDenied()
    {
        if (self::isApi()) {
            Utils::sendResponse(403, CJSON::encode(['status' => 'NOT_OK',
                'message' => Yii::t('backend', 'BACKEND_CUSTOM_ROLE_ACCESS_DENIED')
            ]));
        } else {
            throw new CHttpException(403, Yii::t('backend', 'BACKEND_CUSTOM_ROLE_ACCESS_DENIED'));
        }
    }

    /**
     * Send push notifications for business admins
     * @param $businessId
     * @param array $payload
     * @param BusinessBranch|null $branch
     * @return string|void
     */
    public static function sendAdminPushNotification($businessId, array $payload, BusinessBranch $branch = null)
    {
        if (!Utils::feature('SEND_ADMIN_PUSH_NOTIFICATIONS')) {
            return;
        }
        
        try {
            // Get business
            $business = Business::model()->findByAttributes([
                'business_pk' => $businessId,
                'enabled' => 1
            ]);

            if (empty($business)) {
                return;
            }

            // Get default branch
            if (is_null($branch)) {
                $branch = BusinessBranch::model()->findByAttributes([
                    'business_fk' => $businessId,
                    'default_branch' => 1,
                    'enabled' => 1
                ]);
            }

            // Initialize Kangaroo API provider
            $kangarooApiProvider = new Kangaroo([
                'clientId' => Yii::app()->params['kangarooApiClientId'],
                'clientSecret' => Yii::app()->params['kangarooApiClientSecret'],
                'urlAccessToken' => Yii::app()->params['kangarooApiUri'] . '/oauth/token',
                'redirectUri' => null,
            ]);

            // Retrieve Access Token using client_credentials grant
            $token = $kangarooApiProvider->getAccessToken('client_credentials', [
                'scope' => 'admin'
            ]);

            $api = new KangarooApiClient($business, $branch, $token->getToken(), null);

            // Append target & business hexadecimal ID attributes
            $payload ['business_id'] = bin2hex($business->business_uid);
            $payload ['target'] = 'admin';

            return $api->internalSendPushNotification($payload);
        } catch (Exception $e) {
            Yii::log($e . ' ' . json_encode([
                'message' => $e->getMessage(),
                'payload' => $payload,
            ]), CLogger::LEVEL_ERROR, __METHOD__);
        }
    }

    public static function forceWWWInBaseUrl($url)
    {
        if (!empty($url) && strpos((string)$url, 'www.') === false) {
            // Add 'www' to the domain
            $parsedUrl = parse_url($url);
            $url = $parsedUrl['scheme'] . '://www.' . $parsedUrl['host'] . (isset($parsedUrl['path']) ? $parsedUrl['path'] : '') . (isset($parsedUrl['query']) ? '?' . $parsedUrl['query'] : '');
        }

        return $url;
    }

    public static function getServerUrlBasedOnUserLogin():string
    {
        // Get the base server URL from the environment or configuration
        $serverUrl = rtrim(Yii::app()->params['serverUrl'] ?? '', '/');

        if (Yii::app()->params['envParam'] != 'PROD') {
            return $serverUrl;
        }

        $currentHost = $_SERVER['HTTP_HOST'];

        // Parse the serverUrl to extract its host
        $parsedServerUrl = parse_url($serverUrl);
        $serverHost = $parsedServerUrl['host'] ?? '';

        // Check if the current host starts with "www."
        $isWWWCurrentHost = strpos($currentHost, 'www.') === 0;

        // Check if the server host starts with "www."
        $isWWWServerHost = strpos($serverHost, 'www.') === 0;

        // Modify the server URL's host to match the current user's domain style
        if ($isWWWCurrentHost && !$isWWWServerHost) {
            // Add "www." to the server URL's host if the user loaded with www
            $serverHost = 'www.' . $serverHost;
        } elseif (!$isWWWCurrentHost && $isWWWServerHost) {
            // Remove "www." from the server URL's host if the user loaded without www
            $serverHost = preg_replace('/^www\./', '', $serverHost);
        }

        // Reconstruct the server URL with the modified host
        $serverUrl = $parsedServerUrl['scheme'] . '://' . $serverHost;

        // Add the path to the return URL
        return rtrim($serverUrl, '/');
    }
}
