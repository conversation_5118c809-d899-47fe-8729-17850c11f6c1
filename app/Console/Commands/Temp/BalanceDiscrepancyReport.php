<?php

namespace App\Console\Commands\Temp;

use App\Models\V1\Business;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class BalanceDiscrepancyReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'report:balance-discrepancy';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Balance Discrepancy Report';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        DB::table('balance_discrepancy')->truncate();

        $this->info('Table Truncated');

        $businesses = Business::where('enabled', 1)->where('entity_type_fk', 1)->get();

        $bar = $this->output->createProgressBar(count($businesses));

        foreach ($businesses as $business) {
            $this->buildReportForBusiness($business);
            $bar->advance();
        }
        $bar->finish();
    }

    /**
     * @param $business
     */
    private function buildReportForBusiness($business)
    {
        $results = DB::select(DB::raw($this->getQuery($business->business_pk)), [
            'businessId1' => $business->business_pk,
        ]);

//        dd($results);

        foreach ($results as $result) {
            DB::table('balance_discrepancy')->insert((array) $result);
        }
    }

    /**
     * @return string
     */
    private function getQuery($businessId)
    {
        $branchListResult = DB::table('business_branch')
            ->where('business_fk', $businessId)
            ->pluck('business_branch_pk');

        $branchList = implode(',', $branchListResult->toArray());

        return  "
            SELECT 
                business_fk, 
                user_fk, 
                total_transactions,
                earned_points,
                redeemed_points,
                current_points,
                (earned_points - redeemed_points - current_points) AS missing_points
            FROM (
                SELECT
                    bb.business_fk,
                    t.user_fk,
                    bb.name as branch_name,
                    concat(u.first,' ',u.last) as customer_name,
                    u.email,
                    u.phone,
                    count(t.transaction_pk) as total_transactions,
                    SUM(IF(t.transaction_sub_type_fk IN (2,4,15,16,33,37,38,41,42,52,53,54,55,59,67,68,69,70,76,77,80,93,97,100,101,109,117,118),a.units_exchange,0)) as earned_points,
                    SUM(IF(t.transaction_sub_type_fk IN (5,43,44,45,56,58,62,63,64,73,82,91),a.units_exchange,0)) as redeemed_points,
                    uep.current_active_points as current_points,
                    uep.lifetime_balance
                FROM
                    `transaction` as t
                JOIN activity a ON a.transaction_fk = t.transaction_pk
                JOIN transaction_sub_type tst ON tst.transaction_sub_type_pk = t.transaction_sub_type_fk
                JOIN business_branch bb ON bb.business_branch_pk = t.entity_fk
                JOIN user u ON u.id = t.user_fk
                LEFT JOIN user_entity_points as uep ON uep.entity_fk = :businessId1 AND uep.user_fk = t.user_fk
                WHERE
                    t.entity_fk IN ({$branchList})
                AND t.transaction_sub_type_fk IN (2,4,15,16,33,37,38,41,42,52,53,54,55,59,67,68,69,70,76,77,80,93,97,100,101,109,  5,43,44,45,56,58,62,63,64,73,82,91,117,118)
                -- AND DATE(t.utc_created) > '2017-08-01'
                GROUP BY t.user_fk
                HAVING (earned_points - redeemed_points) <> current_points
                ORDER BY transaction_pk
            ) AS t
        ";
    }
}
