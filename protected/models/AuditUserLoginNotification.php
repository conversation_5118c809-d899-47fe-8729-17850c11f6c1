<?php

/**
 * This is the model class for table "user_login_notifications".
 *
 * The following are the available columns in the table 'user_login_notifications':
 * @property string $id
 * @property string|int $user_id
 * @property string $created_at
 * @property string|int $current_login_id
 * @property string|int $previous_login_id
 * @property string $current_ip
 * @property string $previous_ip
 * @property string $current_user_agent
 * @property string $previous_user_agent
 * @property string|int|null $business_fk
 */
class AuditUserLoginNotification extends CActiveRecord
{
    public static $db = null;

    /**
     * Overrides the model getDbConnection to use logging database
     * Returns the database connection used by active record.
     * By default, the "db" application component is used as the database connection.
     * You may override this method if you want to use a different database connection.
     * @return CDbConnection the database connection used by active record.
     * @throws CDbException if "db" application component is not defined
     * @throws CException
     */
    public function getDbConnection()
    {
        if (self::$db !== null) {
            return self::$db;
        } else {
            self::$db = Yii::app()->db_audit;
            if (self::$db instanceof CDbConnection) {
                self::$db->setActive(true);
                return self::$db;
            } else {
                throw new CDbException(Yii::t('yii', 'Active Record requires a "logging" CDbConnection application component.'));
            }
        }
    }

    /**
     * @return string the associated database table name
     */
    public function tableName(): string
    {
        return 'mc_kng_audit.user_login_notifications';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules(): array
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('user_id, current_login_id, current_ip, current_user_agent', 'required'),
            array('user_id', 'length', 'max' => 11),
            array('current_ip', 'length', 'max' => 15),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('id, user_id, created_at, current_login_id, previous_login_id, current_ip, previous_ip, current_user_agent, previous_user_agent, business_fk', 'safe'),
            array('id, user_id, created_at, current_login_id, previous_login_id, current_ip, previous_ip, current_user_agent, previous_user_agent, business_fk', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations(): array
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels(): array
    {
        return array(
            'id' => 'ID',
            'user_id' => 'User',
            'created_at' => 'Created At',
            'current_login_id' => 'Current Login ID',
            'previous_login_id' => 'Previous Login ID',
            'current_ip' => 'Current Ip',
            'previous_ip' => 'Previous Ip',
            'current_user_agent' => 'Current User Agent',
            'previous_user_agent' => 'Previous User Agent',
            'business_fk' => 'Business ID',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical use case:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass a data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id, true);
        $criteria->compare('user_id', $this->user_id, true);
        $criteria->compare('created_at', $this->created_at, true);
        $criteria->compare('current_login_id', $this->current_login_id, true);
        $criteria->compare('previous_login_id', $this->previous_login_id, true);
        $criteria->compare('current_ip', $this->current_ip, true);
        $criteria->compare('previous_ip', $this->previous_ip, true);
        $criteria->compare('current_user_agent', $this->current_user_agent, true);
        $criteria->compare('previous_user_agent', $this->previous_user_agent, true);
        $criteria->compare('business_fk', $this->business_fk, true);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
            'pagination' => array(
                'pageSize' => 100,
            ),
            'sort' => array(
                'defaultOrder' => 'id DESC',
            ),
        ));
    }

    public function review()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id, true);
        $criteria->compare('user_id', $this->user_id, true);
        $criteria->compare('created_at', $this->created_at, true);
        $criteria->compare('current_login_id', $this->current_login_id, true);
        $criteria->compare('previous_login_id', $this->previous_login_id, true);
        $criteria->compare('current_ip', $this->current_ip, true);
        $criteria->compare('previous_ip', $this->previous_ip, true);
        $criteria->compare('current_user_agent', $this->current_user_agent, true);
        $criteria->compare('previous_user_agent', $this->previous_user_agent, true);
        $criteria->compare('business_fk', $this->business_fk, true);

        // if the country is different from the previous login
        // the location info is in the AuditUserLogin -> details -> location -> countryCode
        // Join with AuditUserLogin for current and previous logins
        $criteria->join = "
            LEFT JOIN user_logins AS curr_login ON curr_login.id = t.current_login_id
            LEFT JOIN user_logins AS prev_login ON prev_login.id = t.previous_login_id
        ";
        // Only show when countryCode is different
        $criteria->addCondition("
            JSON_UNQUOTE(JSON_EXTRACT(curr_login.details, '$.location.countryCode')) IS NOT NULL
            AND JSON_UNQUOTE(JSON_EXTRACT(prev_login.details, '$.location.countryCode')) IS NOT NULL
            AND JSON_UNQUOTE(JSON_EXTRACT(curr_login.details, '$.location.countryCode')) !=
                JSON_UNQUOTE(JSON_EXTRACT(prev_login.details, '$.location.countryCode'))
        ");

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
            'pagination' => array(
                'pageSize' => 100,
            ),
            'sort' => array(
                'defaultOrder' => 'id DESC',
            ),
        ));
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return AuditUserLoginNotification the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    /**
     * Handle saving the login notification
     *
     * @param AuditUserLogin $currentLogin
     * @return void
     * <AUTHOR>
     */
    public static function handleSave(AuditUserLogin $currentLogin): void
    {
        // Only the old portal website and new merchant portal for now
        if (!in_array((int)$currentLogin->platform_id, [PLATFORM_WEBSITE, PLATFORM_MERCHANT_PORTAL], true)) {
            return;
        }

        // the model is not saved yet, so we need to set the created_at field
        $currentLogin->created_at = date('Y-m-d H:i:s');

        $previousLogin = self::getPreviousLogin($currentLogin);

        // Check if the previous login is from the same IP address, or the same user agent
        if ($previousLogin && ($previousLogin->ip === $currentLogin->ip && $previousLogin->user_agent === $currentLogin->user_agent)) {
            Yii::log(json_encode([
                'message' => 'The previous login is from the same IP address, or the same user agent',
                'current_login_id' => $currentLogin->getPrimarykey(),
                'previous_login_id' => $previousLogin->getPrimarykey(),
                'current_ip' => $currentLogin->ip,
                'previous_ip' => $previousLogin->ip,
                'current_user_agent' => $currentLogin->user_agent,
                'previous_user_agent' => $previousLogin->user_agent,
            ]), CLogger::LEVEL_INFO, __METHOD__);
            return;
        }

        //Some users are not associated with a business: admin users, support users, etc.
        $userEntities = self::getUserEntitiesByRole($currentLogin->user_id);

        $loginNotification = new AuditUserLoginNotification();
        $loginNotification->user_id = $currentLogin->user_id;
        $loginNotification->created_at = date('Y-m-d H:i:s');
        $loginNotification->current_login_id = $currentLogin->id;
        $loginNotification->previous_login_id = $previousLogin?->id;
        $loginNotification->current_ip = $currentLogin->ip;
        $loginNotification->previous_ip = $previousLogin?->ip;
        $loginNotification->current_user_agent = $currentLogin->user_agent;
        $loginNotification->previous_user_agent = $previousLogin?->user_agent;
        $loginNotification->business_fk = $userEntities['businessId'] ?? null;
        $loginNotification->save(false);

        self::sendNotification($currentLogin, $userEntities);
    }

    /**
     * Send notification to a user
     *
     * @param AuditUserLogin $currentLogin
     * @param array $userEntities
     * @return void
     * <AUTHOR>
     */
    public static function sendNotification(AuditUserLogin $currentLogin, array $userEntities): void
    {
        $user = User::model()->findByPk($currentLogin->user_id);
        if (!$user) {
            return;
        }

        /*if (!self::shouldSendLoginNotification($userEntities)) {
            return;
        }*/

        $emailContent = self::getEmailContent($user, $currentLogin, $userEntities);

        Yii::log(json_encode([
            'message' => 'Sending login notification to user',
            'user' => $user->email,
            'new_ip' => $currentLogin->ip,
            'new_user_agent' => $currentLogin->user_agent,
        ]), CLogger::LEVEL_INFO, __METHOD__);

        MessagesQueue::create([
            'send_to' => $user->email,
            'cell_email' => MESSAGE_SEND_BY_EMAIL,
            'params' => [
                'emailView' => 'text',
                'layout' => 'bilingual',
                'emailSubject' => $emailContent['subject'],
                'emailData' => ['content' => $emailContent['body']],
                'viewPath' => null,
                'fromExtra' => Yii::app()->params['appShortName'],
                'withdraw_credit_flag' => false
            ],
        ]);
    }

    public static function checkLocationAndLockUserAccount(AuditUserLogin $currentLogin): bool
    {
        return false; // Too aggressive
        if (Yii::app()->params['envParam'] !== 'PROD') {
            return false;
        }

        $business = null;
        $userEntity = UserEntityPermission::model()->findByAttributes(['user_fk' => $currentLogin->user_id]);
        if ($userEntity && (int) $userEntity->entity_type_fk === 1) {
            $business = Business::model()->findByPk($userEntity->entity_fk);
        } elseif ($userEntity && (int) $userEntity->entity_type_fk === 2) {
            $branch = BusinessBranch::model()->findByPk($userEntity->entity_fk);
            $business = Business::model()->findByPk($branch->business_fk);
        }

        // Skip Internal Businesses
        if ($business && (int) $business->account_type_id === 5) {
            return false;
        }

        $previousLogin = self::getPreviousLogin($currentLogin);

        if (!$previousLogin) {
            Yii::log(json_encode([
                'message' => 'Previous login not found',
                'current_login_id' => $currentLogin->getPrimarykey(),
            ]), CLogger::LEVEL_INFO, __METHOD__);
            return false;
        }

        $currentDetails = json_decode($currentLogin->details);
        $previousDetails = json_decode($previousLogin->details);

        if (!$currentDetails || !$previousDetails) {
            Yii::log(json_encode([
                'message' => 'Previous login details not found',
                'current_login_id' => $currentLogin->getPrimarykey(),
            ]), CLogger::LEVEL_INFO, __METHOD__);
            return false;
        }

        if (!isset($currentDetails->location) || !isset($previousDetails->location)) {
            Yii::log(json_encode([
                'message' => 'Previous login location not found',
                'current_login_id' => $currentLogin->getPrimarykey(),
            ]), CLogger::LEVEL_INFO, __METHOD__);
            return false;
        }

        $user = User::model()->findByPk($currentLogin->user_id);
        if (!$user) {
            Yii::log(json_encode([
                'message' => 'User not found',
                'user_id' => $currentLogin->user_id,
            ]), CLogger::LEVEL_INFO, __METHOD__);
            return false;
        }

        $countryChanged = $currentDetails->location->countryCode !== $previousDetails->location->countryCode;
        $cityChanged = $currentDetails->location->city !== $previousDetails->location->city;

        if ($countryChanged || $cityChanged) {
            // Too aggressive for now or not innacurate if using VPN
            // lock the user account
            // $user->enabled = 0;
            // $user->save();

            // send notification to the user
            // self::sendNotificationAccountLocked($currentLogin, $user);

            return true;
        }

        Yii::log(json_encode([
            'message' => 'Location not changed',
            'current_login_id' => $currentLogin->getPrimarykey(),
        ]), CLogger::LEVEL_INFO, __METHOD__);

        return false;
    }

    public static function sendNotificationAccountLocked(AuditUserLogin $currentLogin, User $user): void
    {
        $emailContent = self::getEmailContentAccountLocked($user, $currentLogin);

        $supportEmailContent = "<p>User account {$user->email} has been locked due to suspicious login activity.</p>";
        $supportEmailContent .= "User ID: {$user->id}<br>";
        $supportEmailContent .= "IP Address: {$currentLogin->ip}<br>";
        $supportEmailContent .= "<p>Please review and take appropriate action.</p><hr>";
        $supportEmailContent .= $emailContent['body'];

        Yii::log(json_encode([
            'message' => 'Sending account locked notification to user',
            'user' => $user->email,
            'new_ip' => $currentLogin->ip,
            'new_user_agent' => $currentLogin->user_agent,
        ]), CLogger::LEVEL_INFO, __METHOD__);

        MessagesQueue::create([
            'send_to' => $user->email,
            'cell_email' => MESSAGE_SEND_BY_EMAIL,
            'params' => [
                'emailView' => 'text',
                'layout' => 'bilingual',
                'emailSubject' => $emailContent['subject'],
                'emailData' => ['content' => $emailContent['body']],
                'viewPath' => null,
                'fromExtra' => Yii::app()->params['appShortName'],
                'withdraw_credit_flag' => false
            ],
        ]);

        // Send notification to support
        MessagesQueue::create([
            'send_to' => Yii::app()->params['technicalSupport'],
            'cell_email' => MESSAGE_SEND_BY_EMAIL,
            'params' => [
                'emailView' => 'text',
                'layout' => 'bilingual',
                'emailSubject' => "[Alerts] Account locked - Different location",
                'emailData' => ['content' => $supportEmailContent],
                'viewPath' => null,
                'fromExtra' => Yii::app()->params['appShortName'],
                'withdraw_credit_flag' => false
            ],
        ]);
    }

    private static function getEmailContentAccountLocked(User $user, AuditUserLogin $currentLogin): array
    {
        $appShortName = Yii::app()->params['appShortName'];
        $deviceInfo = Utils::parseUserAgent($currentLogin->user_agent);
        $supportEmail = Yii::app()->params['adminEmail'];
        $location = Utils::lookupLocation($currentLogin->ip);
        $locationString = "{$location->city}, {$location->region}, {$location->countryName}";
        if (empty($location->city) || empty($location->region)) {
            $locationString = "{$location->countryName}";
        }
        if (empty($location->countryName)) {
            $locationString = "Unknown";
        }

        $body = "Hi {$user->getFullName()},<br><br>";
        $body .= "<p>We have detected suspicious activity on your {$appShortName} account {$user->email}.</p>";
        $body .= "<p>We have detected that your account has been accessed from a different location than the one you previously logged in from and your account has been locked.</p>";
        $body .= "<br>";
        $body .= "<p>Device: {$deviceInfo['device']}</p>";
        $body .= "<p>App/Browser: {$deviceInfo['app']}</p>";
        $body .= "<p>Location: {$locationString}<br>";
        $body .= "<span style='font-size: 14px;'>Location is approximated based on the accessed IP Address: {$currentLogin->ip}</span></p>";
        $body .= "<br>";
        $body .= "<p>Please contact {$supportEmail} to reactivate your account.</p>";
        $body .= "<br>";
        $body .= "<p>Best regards,<br>{$appShortName}</p>";

        return [
            'subject' => "Your account has been locked",
            'body' => $body,
        ];
    }

    /**
     * Get email content
     *
     * @param User $user
     * @param AuditUserLogin $currentLogin
     * @param array $userEntities
     * @return array
     * <AUTHOR>
     */
    private static function getEmailContent(User $user, AuditUserLogin $currentLogin, array $userEntities): array
    {
        $appShortName = Yii::app()->params['appShortName'];
        $resetPasswordUrl = Yii::app()->params['serverUrl'] . '/merchant/forgotPassword';
        $localDateTime = self::getLocalDateTime($currentLogin->created_at, $userEntities);
        $deviceInfo = Utils::parseUserAgent($currentLogin->user_agent);
        $supportEmail = Yii::app()->params['adminEmail'];
        $location = Utils::lookupLocation($currentLogin->ip);
        $locationString = "{$location->city}, {$location->region}, {$location->countryName}";
        if (empty($location->city) || empty($location->region)) {
            $locationString = "{$location->countryName}";
        }
        if (empty($location->countryName)) {
            $locationString = "Unknown";
        }

        $body = "Hi {$user->getFullName()},<br><br>";
        $body .= "<p>There's been a new sign-in to your {$appShortName} account {$user->email} on {$localDateTime}.</p>";
        $body .= "<br>";
        $body .= "<p>Device: {$deviceInfo['device']}</p>";
        $body .= "<p>App/Browser: {$deviceInfo['app']}</p>";
        $body .= "<p>Location: {$locationString}<br>";
        $body .= "<span style='font-size: 14px;'>Location is approximated based on the accessed IP Address: {$currentLogin->ip}</span></p>";
        $body .= "<br>";
        $body .= "<p>If you did not make this sign-in, please consider resetting your password or contact {$supportEmail} immediately.</p>";
        $body .= "<p><a href='{$resetPasswordUrl}'>No, this wasn't me — secure my account.</a></p>";
        $body .= "<br>";
        $body .= "<p>Best regards,<br>{$appShortName}</p>";

        return [
            'subject' => "New sign-in to your {$appShortName} account",
            'body' => $body,
        ];
    }

    /**
     * Get local date time
     *
     * @param string $createdAt
     * @param array $userEntities
     * @return string
     * <AUTHOR>
     */
    private static function getLocalDateTime(string $createdAt, array $userEntities): string
    {
        $branchTimeZone = 'UTC';
        if (!empty($userEntities)) {
            $defaultBranchId = $userEntities['mainBranchId'];
            $branchTimeZone = BusinessBranch::getTmzNamebyBranchId($defaultBranchId);
        }
        try {
            $dateTime = new DateTime($createdAt, new DateTimeZone('UTC'));
            $dateTime->setTimezone(new DateTimeZone($branchTimeZone));
            return $dateTime->format('l, F d Y g:i:s a T');
        } catch (Exception $e) {
            return $createdAt;
        }
    }

    /**
     * Get user entities by role
     *
     * @param int $userId
     * @return array
     * <AUTHOR>
     */
    private static function getUserEntitiesByRole($userId): array
    {
        $userEntities = UserEntityPermission::model()->findByAttributes(['user_fk' => $userId]);

        if (empty($userEntities)) {
            return [];
        }

        $role = UserEntityPermission::checkPermissions($userId);

        if ($role === 'business_admin_clerk' || $role === 'business_clerk' || $role === 'business_admin') {
            $businessId = $userEntities->entity_fk; //business ID
            $entityTypeFk = $userEntities->entity_type_fk; //1,7,99
            $defaultBranch = BusinessBranch::getDefaultBranch($businessId);
            $branchId = $defaultBranch['business_branch_pk']; // Default Branch Id

            return [
                'role' => $role,
                'businessId' => $businessId,
                'branchId' => $branchId,
                'mainBranchId' => $branchId,
                'entityTypeFk' => $entityTypeFk,
                'employeeId' => $userId
            ];
        } elseif ($role == 'branch_admin') {
            $branchId = $userEntities->entity_fk; // their branch Id
            $entityTypeFk = $userEntities->entity_type_fk; //2,8,100
            $branchModel = BusinessBranch::model()->findByPk($branchId);
            $businessId = $branchModel->business_fk;

            $defaultBranch = BusinessBranch::getDefaultBranch($businessId);
            $mainBranchId = $defaultBranch['business_branch_pk']; // Default Branch Id

            return [
                'role' => $role,
                'businessId' => $businessId,
                'branchId' => $branchId,
                'mainBranchId' => $mainBranchId,
                'entityTypeFk' => $entityTypeFk,
                'employeeId' => $userId
            ];
        }

        return [];
    }

    /**
     * Get previous login
     *
     * @param AuditUserLogin $currentLogin
     * @return ?AuditUserLogin
     * <AUTHOR>
     */
    private static function getPreviousLogin(AuditUserLogin $currentLogin): ?AuditUserLogin
    {
        return AuditUserLogin::model()->findByAttributes([
            'user_id' => $currentLogin->user_id,
        ], [
            'condition' => 'id < ' . $currentLogin->id, // exclude current login
            'order' => 'id DESC',
            'limit' => 1,
        ]);
    }

    /**
     * Should send login notification
     *
     * @param array $userEntities
     * @return bool
     * <AUTHOR>
     */
    /*private static function shouldSendLoginNotification(array $userEntities): bool
    {
        $shouldSendNotification = true;
        if (!empty($userEntities) && isset($userEntities['businessId'])) {
            $businessRules = BusinessRules::model()->findByAttributes([
                'business_fk' => $userEntities['businessId'],
            ]);
            if ($businessRules && (int)$businessRules->notify_new_device_login === 0) {
                $shouldSendNotification = false;
            }
        }
        return $shouldSendNotification;
    }*/
}
