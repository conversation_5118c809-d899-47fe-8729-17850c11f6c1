<?php

namespace App\Http\Controllers;

use App\Models\V1\Business;
use App\Repositories\ClientRepository;
use App\Services\EncryptorService;
use GuzzleHttp\Promise\PromiseInterface;
use Illuminate\Encryption\Encrypter;
use Illuminate\Http\Client\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class TestController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        //$this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     */
    public function qrcode(Request $request): void
    {
        $business = Business::findByPk(15);
        $service = new EncryptorService($business);
        $decrypted = $service->decrypt($request->msg);

        echo 'Amount: ' . $decrypted->amount . '<BR>';
        echo 'POS Location ID: ' . $decrypted->locationId . '<BR>';
        echo 'Timestamp: ' . date('Y-m-d H:i:s', $decrypted->timestamp) . '<BR>';

        //return view('welcome', []);
    }

    public function sse(Request $request)
    {
        return view('testView');
    }

    public function uploadReceipt(Request $request): JsonResponse
    {
        if (!$request->hasFile('receipt')) {
            return response()->json(['error' => 'No file uploaded'], 400);
        }

        // Validate and process the image
        $image = $request->file('receipt');
        $imagePath = $image->getRealPath();
        $structuredResponse = true;

        $apiData = $this->makeApiCall($imagePath, $structuredResponse);

        if (isset($apiData['error'])) {
            return response()->json(['error' => $apiData['error']], 400);
        }

        if (!$structuredResponse) {
            // // Extract the extracted information from the API response
            // $extractedInfo = $apiData['choices'][0]['message']['content'];
            $extractedInfo = 'Here are the extracted details from the receipt:
**Products:**
- T-Shirt XL:  
  - Count: 1  
  - Amount: $14.95  
  - Tax: 20%
  
- Jeans PDK:  
  - Count: 2  
  - Amount: $39.90 each  
  - Tax: 20%

**Subtotal:**
- Gross: $75.80  
- Tax: $18.95  
- Total: $94.75';
            // $parsedInfo = $this->parseExtractedInfo($extractedInfo);
            $parsedInfo = $this->parseReceipt($extractedInfo);
        } else {
            $extractedInfo = $apiData['choices'][0]['message']['content'];
            $parsedInfo = json_decode($extractedInfo, true);
        }

        // print_r($apiData); die;

        return response()->json([
            'rawContent' => $extractedInfo,
            'parsedContent' => $parsedInfo,
        ], 200);
    }

    private function makeApiCall(string $imagePath, bool $structured = false): PromiseInterface | Response | array
    {
        // Send the image to the API for processing
        $imageContent = file_get_contents($imagePath);
        $base64Image = base64_encode($imageContent);

        $messages = [
            [
                'role' => 'system',
                //'content' => 'You are a receipt parser. Extract products, prices, subtotal, discount, and total.'
                'content' => 'You are an expert at structured data extraction. You will be given and image containing a receipt in unstructured text and should convert it into the given structure.'
            ],
            [
                'role' => 'user',
                'content' => [
                    [
                        "type" => "image_url",
                        "image_url" => [
                            "url" => "data:image/jpeg;base64," . $base64Image
                        ]
                    ]
                ]
            ]
        ];

        $payload = [
            'model' => 'gpt-4o-mini',
            'max_completion_tokens' => 8192,
            'temperature' => 0,
            'messages' => $messages
        ];

        if ($structured) {
            // https://platform.openai.com/docs/guides/structured-outputs
            $payload['response_format'] = [
                "type" => "json_schema",
                "json_schema" => [
                    "name" => "receipt_data_extraction",
                    "schema" => [
                        "type" => "object",
                        "properties" => [
                            "shop_name" => ["type" => ["string", "null"]],
                            "products" => [
                                "type" => "array",
                                "items" => ['$ref' => '#/$defs/product'],
                            ],
                            "sub_total" => ["type" => "number"],
                            "total" => ["type" => "number"],
                            "tax" => ["type" => "number"],
                        ],
                        '$defs' => [
                            'product' => [
                                "type" => "object",
                                "properties" => [
                                    "name" => ["type" => "string"],
                                    "quantity" => ["type" => "number"],
                                    "price" => ["type" => "number"],
                                ],
                                "required" => ["name", "quantity", "price"],
                                "additionalProperties" => false
                            ]
                        ],
                        "required" => ["shop_name", "products", "total", "sub_total", "tax"],
                        "additionalProperties" => false
                    ],
                    "strict" => true
                ]
            ];
        }

        return Http::withHeaders([
            'Authorization' => 'Bearer ' . config('app.CHAT_GPT_APP_KEY'),
        ])->post('https://api.openai.com/v1/chat/completions', $payload)->json();
    }

    private function parseReceipt($receipt): array
    {
        $lines = explode("\n", $receipt);
        $result = [
            'products' => [],
            'sub_total' => [
                'gross' => 0,
                'tax' => 0,
                'total' => 0
            ]
        ];

        $currentProduct = null;
        $inSubtotalSection = false;

        foreach ($lines as $line) {
            $line = trim($line);

            if (strpos($line, '**Products:**') !== false) {
                continue;
            }

            if (strpos($line, '**Subtotal:**') !== false) {
                $inSubtotalSection = true;
                continue;
            }

            if (!$inSubtotalSection) {
                if (preg_match('/^- (.+):$/', $line, $matches)) {
                    if ($currentProduct !== null) {
                        $result['products'][] = $currentProduct;
                    }
                    $currentProduct = [
                        'name' => $matches[1],
                        'quantity' => 0,
                        'price' => 0,
                        'tax' => 0
                    ];
                } elseif ($currentProduct !== null) {
                    if (preg_match('/^- Count: (\d+)$/', $line, $matches)) {
                        $currentProduct['quantity'] = intval($matches[1]);
                    } elseif (preg_match('/^- Amount: \$?([\d.]+)( each)?$/', $line, $matches)) {
                        $currentProduct['price'] = floatval($matches[1]);
                        if (isset($matches[2]) && $matches[2] == ' each') {
                            // $currentProduct['price'] *= $currentProduct['quantity'];
                        }
                    } elseif (preg_match('/^- Tax: (\d+)%$/', $line, $matches)) {
                        $currentProduct['tax'] = intval($matches[1]);
                    }
                }
            } else {
                if (preg_match('/^- Gross: \$?([\d.]+)$/', $line, $matches)) {
                    $result['sub_total']['gross'] = floatval($matches[1]);
                } elseif (preg_match('/^- Tax: \$?([\d.]+)$/', $line, $matches)) {
                    $result['sub_total']['tax'] = floatval($matches[1]);
                } elseif (preg_match('/^- Total: \$?([\d.]+)$/', $line, $matches)) {
                    $result['sub_total']['total'] = floatval($matches[1]);
                }
            }
        }

        // Add the last product if it exists
        if ($currentProduct !== null) {
            $result['products'][] = $currentProduct;
        }

        return $result;
    }

    private function parseExtractedInfo(string $text)
    {
        $products = [];
        $subtotal = 0;
        $tax = 0;
        $total = 0;

        // Match product entries with name, count, and amount
        preg_match_all('/-\s+([^\n]+)\s+-\s+Count:\s+(\d+)\s+-\s+Amount:\s+\$(\d+\.\d{2})\s+-\s+Tax:\s+(\d+)%/i', trim($text), $matches, PREG_SET_ORDER);

        // Check if the matches array is not empty
        if (!empty($matches)) {
            print_r($matches); die;
            // Loop through matches and build product array
            foreach ($matches as $match) {
                print_r($matches); die;
                $productName = trim($match[1]);  // Product name
                $quantity = intval($match[2]);   // Quantity
                $price = floatval($match[3]);    // Price per unit
                $taxPercentage = intval($match[4]);  // Tax percentage

                // Calculate the total for each product
                $productTotal = $price * $quantity;
                $taxAmount = $productTotal * ($taxPercentage / 100);

                $products[] = [
                    'name' => $productName,
                    'quantity' => $quantity,
                    'price' => $price,
                    'tax_percentage' => $taxPercentage,
                    'total' => $productTotal,
                    'tax_amount' => $taxAmount,
                ];
            }
        }

        // Extract subtotal, tax, and total using regex
        preg_match('/\- Subtotal \(Gross\):\s*\$(\d+\.\d{2})/i', $text, $subtotalMatch);
        preg_match('/\- Tax:\s*\$(\d+\.\d{2})/i', $text, $taxMatch);
        preg_match('/\- Total \(Sum\):\s*\$(\d+\.\d{2})/i', $text, $totalMatch);

        if (!empty($subtotalMatch)) {
            $subtotal = floatval($subtotalMatch[1]);
        }

        if (!empty($taxMatch)) {
            $tax = floatval($taxMatch[1]);
        }

        if (!empty($totalMatch)) {
            $total = floatval($totalMatch[1]);
        }

        return [
            'products' => $products,
            'sub_total' => $subtotal,
            'tax' => $tax,
            'total' => $total,
        ];
    }

    private function sampleResponse()
    {
        return [
            'products' => [
                ['name' => 'Product 1', 'price' => 5.99],
                ['name' => 'Product 2', 'price' => 10.49]
            ],
            'sub_total' => 16.48,
            'discount' => 1.00,
            'total' => 15.48
        ];
    }
}
