<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\V1\CreateInsightEvent;
use App\Jobs\ProcessInsightEvent;
use Illuminate\Http\Request;

/**
 * Business Settings
 *
 * @Resource("InsightEvent", uri="/insights/events")
 */
class InsightEventController extends ApiController
{
    /**
     * This API endpoint lets you push a collection of events like clicks, views etc.
     * or only one event
     *
     * POST /insights/events
     *
     * @Post("/insights/events")
     * @Versions({"v1"})
     *
     * @param CreateInsightEvent $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(CreateInsightEvent $request)
    {
        $business = $this->getBusinessForAppKey();

        $data = $request->all();

        $userAgent = isset($data['user_agent']) ? $data['user_agent'] : $request->header('User-Agent');
        $data['ip'] = isset($data['ip']) ? $data['ip'] : $request->ip();
        $data['user_agent'] = $userAgent;

        $job = new ProcessInsightEvent($data);

        dispatch($job);

        return $this->requestAccepted('Accepted');
    }
}
