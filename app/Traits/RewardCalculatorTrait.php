<?php

namespace App\Traits;

use App\Helpers\V1\Utils;
use App\Models\V1\Offer;
use App\Models\V1\ProductReward;
use App\Models\V1\Purchase;
use App\Models\V1\UserProductReward;
use Illuminate\Support\Facades\Log;
use KangarooRewards\Common\Traits\BusinessTier;

trait RewardCalculatorTrait
{
    public function calculateBasicReward(): array
    {
        $rewardType = $this->getTrxSubType();

        switch ($rewardType) {
            case 'reward_amount':
                $rewardResult = $this->rewardBasedOnAmount();
                break;
            case 'reward_points':
                //Award any number of points to a customer.
                $rewardResult = $this->rewardBasedOnPoints();
                break;
            case 'reward_visit':
                $rewardResult = $this->rewardBasedOnVisit();
                break;
            default:
                throw new \RuntimeException("The reward type undefined", 1);
        }
        return $rewardResult;
    }

    public function calculateProductRewards()
    {
        $points = 0;
        $appliedProducts = [];
        if (isset($this->data['product_rewards'])) {
            //A la Carte Rewards - apply points

            $productIds = array_column($this->data['product_rewards'], 'id');

            //get products from database
            //filter product rewards that belong to the business
            $productRewards = ProductReward::getAllowedForBusiness($this->business->business_pk, $productIds);

            foreach ($productRewards as $key => $productReward) {
                foreach ($this->data['product_rewards'] as $originalReward) {
                    //only the products that are in database and request input
                    if ($productReward->product_reward_pk === $originalReward['id']) {
                        //calculate the points for A la Carte Rewards
                        $points += ($productReward->point_value * $originalReward['quantity']);
                        $appliedProducts[] = $productReward;
                    }
                }
            }
        }

        return ['points' => $points, 'product_rewards' => $appliedProducts];
    }

    protected function calculateOfferReward($rewardResult): array
    {
        //Only for POINTS business apply offer if the customer is eligible
        if ($this->business->entity_type_fk !== BUSINESS_POINTS) {
            return ['points' => 0, 'offer' => null];
        }

        $purchaseAmount = $rewardResult['amount'] ?? 0; // set to 0, so it will not bypass the check-in condition

        $applyingOffers = Offer::getApplyingOffersForPurchase(
            $this->branch,
            $this->customer->id,
            $purchaseAmount
        );

        $offer = $applyingOffers->first();

        if (!$offer) {
            return ['points' => 0, 'offer' => null];
        }

        $claimOfferPts = Offer::userClaimOfferCalcPoints([
            'applyOffer' => $offer, //get the first offer
            'rewardResult' => $rewardResult,
            'userId' => $this->customer->id,
        ]);

        Log::info(__METHOD__, [
            'user_id' => $this->customer->id,
            'rewardResult' => $rewardResult,
            'claimOfferPts' => $claimOfferPts,
            'offer' => (array) $offer,
        ]);

        return ['points' => $claimOfferPts, 'offer' => $offer];
    }

    /**
     * @return string
     */
    protected function getTrxSubType()
    {
        if (isset($this->data['amount']) && ($this->data['amount'] > 0 || ($this->data['amount'] == 0 && isset($this->data['product_rewards'])) )) {
            return 'reward_amount'; //$amount
        } elseif (isset($this->data['points']) && $this->data['points'] > 0) {
            return 'reward_points'; //Award points
        } elseif (isset($this->data['visits']) && $this->data['visits'] > 0) {
            return 'reward_visit'; //CLAIM
        }
    }

    /**
     * Calculates the number of awarded points based on the amount
     *
     * @throws \LogicException
     *
     * @return mixed
     */
    private function rewardBasedOnAmount()
    {
        $amountOriginal = $this->data['amount']; //The amount from input
        $businessRules = $this->businessRules;

        //Check for the Taxes and Pre-Tax flag
        //Amount Does NOT include TAX = 1 and Amount includes TAX = 0
        if ($businessRules->pre_tax === 0) {
            $amountSpent = ($amountOriginal - ($amountOriginal * $businessRules->tax_rate));
        } else {
            $amountSpent = $amountOriginal;
        }

        $calcRewardPts = $this->calcRewardPointsForUser($amountSpent);

        \Log::info(__METHOD__, [
            'userId' => $this->customer->id,
            'amountSpent' => $amountSpent,
            'amountOriginal' => $amountOriginal,
            'businessId' => $this->business->business_pk,
            'awardedPts' => $calcRewardPts->awardedPts,
            'rewardRatio' => $calcRewardPts->rewardRatio,
            'reward_milestone_amount' => $this->businessRules->reward_milestone_amount,
            'reward_ratio_numerator' => $this->businessRules->reward_ratio_numerator,
            'reward_points_round_up' => $this->businessRules->reward_points_round_up,
            'rewardTierBased' => (array) $calcRewardPts->rewardTierBased,
        ]);

        return [
            'amount' => $amountSpent,
            'amount_orig' => $amountOriginal,
            'punch_value' => $calcRewardPts->awardedPts,
        ];
    }

    /**
     * Returns the number of awarded points
     *
     *
     * @return mixed
     */
    private function rewardBasedOnPoints()
    {
        if (is_int($this->data['points'])) {
            $points = $this->data['points'];
        } else {
            $points = (int) $this->data['points'];
        }

        \Log::info(__METHOD__, [
            'userId' => $this->customer->id,
            'pointsOriginal' => $this->data['points'],
            'awardedPts' => $points,
            'businessId' => $this->business->business_pk,
        ]);

        return [
            'amount' => null,
            'amount_orig' => null,
            'punch_value' => $points,
        ];
    }

    /**
     * Calculates the number of awarded points based on the number of visits
     *
     * @throws \LogicException
     *
     * @return mixed
     */
    private function rewardBasedOnVisit()
    {
        $visits = (int) ($this->data['visits'] ?? 0); //CLAIM value: 1 - 1 punch, 2 and more - multi punch(aggregation)
        $businessRules = $this->businessRules;

        // $secondsSinceLastClaim = Purchase::getLastUserClaimReward($this->customer->id, $this->business->business_pk);
        // dd($secondsSinceLastClaim);
        // Business set up a Frequency in the Business Rule
        if ($businessRules->punch_frequency > 0) {
            $secondsSinceLastClaim = Purchase::getLastUserClaimReward($this->customer->id, $this->business->business_pk);

            // Calculate if there is User can Punch again according to Frequency
            $claimFrequency = $businessRules->punch_frequency * 60; //punch_frequency is in minutes -convert to seconds

            //if($totalseconds > 0 && $totalseconds < $busFreq){
            if ($secondsSinceLastClaim > 0 && $secondsSinceLastClaim < $claimFrequency) {
                $strDetails = 'User $userId: ' . $this->customer->id . '. The total of seconds between last Punch Points Reward Trx and the currect datetime IS (' . $secondsSinceLastClaim . ' seg) and business frequency (' . $claimFrequency . ' seconds)';
                Log::warning(__METHOD__, ['message' => 'MERCHANTAPP_PUNCHPOINTSREWARD_FRECUENCY_ERROR_MESSAGE >> $clerkId: ' . $this->employee->id, 'details' => $strDetails]);
                throw new \LogicException(trans('api.MERCHANTAPP_PUNCHPOINTSREWARD_FRECUENCY_ERROR_MESSAGE'));
            }
        }

//        $rewardRatio = $this->getRewardRatio($businessRules->reward_ratio_numerator, $businessRules->reward_ratio_denominator);

        if ($businessRules->exact_amount_flag == 1 && $businessRules->punch_action_flag == 1) {
//            $visitEarnedPoints = $visits * floor($businessRules->avg_amount_spent * $rewardRatio);
            $amountSpent = $businessRules->avg_amount_spent;
        } elseif ($businessRules->exact_amount_flag == 0 && $businessRules->sys_avg_amount_spent > 0) {
//            $visitEarnedPoints = $visits * floor($businessRules->sys_avg_amount_spent * $rewardRatio);
            $amountSpent = $businessRules->sys_avg_amount_spent;
        } else {
            Log::warning(__METHOD__, ['message' => trans('api.MERCHANTAPP_PUNCHPOINTSREWARD_FRECUENCY_ERROR_MESSAGE') . ' >> $clerkId: ' . $this->employee->id]);
            throw new \LogicException(trans('api.MERCHANTAPP_PUNCHPOINTSREWARD_EXACT_AMOUNT_CLAIM_ERROR_MESSAGE'));
        }

        $calcRewardPts = $this->calcRewardPointsForUser($amountSpent);

        $visitEarnedPoints = $visits * $calcRewardPts->awardedPts;

        \Log::info(__METHOD__, [
            'userId' => $this->customer->id,
            'amountSpent' => $amountSpent,
            'amountOriginal' => $amountSpent,
            'businessId' => $this->business->business_pk,
            'visits' => $visits,
            'visitEarnedPoints' => $visitEarnedPoints,
            'awardedPts' => $calcRewardPts->awardedPts,
            'rewardRatio' => $calcRewardPts->rewardRatio,
            'reward_milestone_amount' => $this->businessRules->reward_milestone_amount,
            'reward_ratio_numerator' => $this->businessRules->reward_ratio_numerator,
            'reward_points_round_up' => $this->businessRules->reward_points_round_up,
            'exact_amount_flag' => $this->businessRules->exact_amount_flag,
            'avg_amount_spent' => $this->businessRules->avg_amount_spent,
            'sys_avg_amount_spent' => $this->businessRules->sys_avg_amount_spent,
            'rewardTierBased' => (array) $calcRewardPts->rewardTierBased,
        ]);

        return [
            'amount' => $visits * $amountSpent,
            'amount_orig' => $visits * $amountSpent,
            'punch_value' => $visitEarnedPoints,
        ];
    }

    /**
     * @see https://floating-point-gui.de/languages/php/
     * @see https://medium.com/@rtheunissen/accurate-numbers-in-php-b6954f6cd577
     * @see http://php-decimal.io/#introduction
     *
     * @param $amountSpent
     * @return object
     */
    private function calcRewardPointsForUser($amountSpent)
    {
        $rewardTierBased = $this->getRewardTierBased();

        if ($rewardTierBased->amount > 0 && $rewardTierBased->points > 0) {
            $rewardRatioRaw = ($amountSpent / $rewardTierBased->amount);
            $rewardRatio = floor($rewardRatioRaw);

            $awardedPts = $rewardRatio * $rewardTierBased->points;
        } elseif ($this->businessRules->reward_milestone_amount === 1 && Utils::isInteger($amountSpent)) {
            // Amount is integer -> DO NOT divide
            $rewardRatioRaw = $amountSpent;
            $rewardRatio = $rewardRatioRaw;
            $awardedPts = $rewardRatio * $this->businessRules->reward_ratio_numerator;
        } else {
            $rewardRatioRaw = ($amountSpent / $this->businessRules->reward_milestone_amount);
            $rewardRatio = floor($rewardRatioRaw);
            $awardedPts = $rewardRatio * $this->businessRules->reward_ratio_numerator;
        }

        \Log::info(__METHOD__, [
            'userId' => $this->customer->id,
            'amountSpent' => $amountSpent,
            'businessId' => $this->business->business_pk,
            'awardedPts' => $awardedPts,
            'rewardRatio' => $rewardRatio,
            'reward_milestone_amount' => $this->businessRules->reward_milestone_amount,
            'reward_ratio_numerator' => $this->businessRules->reward_ratio_numerator,
            'reward_points_round_up' => $this->businessRules->reward_points_round_up,
            'rewardTierBased' => (array) $rewardTierBased,
            'round_awardedPts' => round($awardedPts),
            'floor_awardedPts' => floor($awardedPts),
            'raw_ratio' => $rewardRatioRaw,
            'fmt_floor_ratio' => number_format($rewardRatio, 10),
            'fmt_raw_ratio' => number_format($rewardRatioRaw, 10),
            'intdiv_ratio' => intdiv($amountSpent, $this->businessRules->reward_milestone_amount),
            'bcmath_ratio' => bcdiv($amountSpent, $this->businessRules->reward_milestone_amount),
        ]);

        if ($this->businessRules->reward_points_round_up === 1) {
            $awardedPts = (int) round($awardedPts);
        } else {
            $awardedPts = (int) floor($awardedPts);
        }

        return (object) [
            'awardedPts' => $awardedPts,
            'rewardRatio' => $rewardRatio,
            'rewardTierBased' => $rewardTierBased,
        ];
    }

    /**
     * Returns the amount and points should be awarded to user based on tier
     *
     * <AUTHOR>
     * @return object
     */
    private function getRewardTierBased()
    {
        $rewardTierAmount = 0;
        $rewardTierPts = 0;
        $customerTier = 0;
        if ($this->businessRules->apply_pts_tiers_flag === 1) {
            $customerTiers = BusinessTier::getUserTier($this->businessRules->business_fk, $this->customer->id);

            if ($customerTiers) {
                $customerTier = (int) $customerTiers->business_tiers_pk;
                $rewardTiers = json_decode($this->businessRules->reward_tiers, true);

                $rewardTierAmount = 0;
                $rewardTierPts = 0;
                foreach ($rewardTiers as $rewardTier) {
                    if ((int) $rewardTier['tier_pk'] === $customerTier) {
                        $rewardTierAmount = (float) $rewardTier['tier_amount'];
                        $rewardTierPts = (float) $rewardTier['tier_points'];
                    }
                }
            }
        }

        return (object) [
            'amount' => $rewardTierAmount,
            'points' => $rewardTierPts,
            'userTierId' => $customerTier,
        ];
    }
}