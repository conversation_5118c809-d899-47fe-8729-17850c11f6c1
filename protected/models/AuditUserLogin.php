<?php

/**
 * This is the model class for table "user_logins".
 *
 * The followings are the available columns in table 'user_logins':
 * @property string $id
 * @property string $user_id
 * @property string $db_name
 * @property string $session_id
 * @property string $ip
 * @property string $created_at
 * @property string $user_agent
 * @property int|mixed|null $platform_id
 * @property string|mixed|null $details
 */
class AuditUserLogin extends CActiveRecord
{
    public static $db = null;
    /**
     * Overides the model getDbConnection to use logging database
     * Returns the database connection used by active record.
     * By default, the "db" application component is used as the database connection.
     * You may override this method if you want to use a different database connection.
     * @return CDbConnection the database connection used by active record.
     * @throws CDbException if "db" application component is not defined
     * @throws CException
     */
    public function getDbConnection()
    {
        if (self::$db !== null) {
            return self::$db;
        } else {
            self::$db = Yii::app()->db_audit;
            if (self::$db instanceof CDbConnection) {
                self::$db->setActive(true);
                return self::$db;
            } else {
                throw new CDbException(Yii::t('yii','Active Record requires a "logging" CDbConnection application component.'));
            }
        }
    }
    
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'mc_kng_audit.user_logins';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('user_id, db_name, platform_id', 'required'),
            array('user_id', 'length', 'max'=>11),
            array('db_name, session_id', 'length', 'max'=>100),
            array('ip', 'length', 'max'=>15),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('id, user_id, db_name, session_id, ip, created_at, platform_id, user_agent', 'safe'),
            array('id, user_id, db_name, session_id, ip, created_at, platform_id, user_agent', 'safe', 'on'=>'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'id' => 'ID',
            'user_id' => 'User',
            'db_name' => 'Db Name',
            'session_id' => 'Session',
            'ip' => 'Ip',
            'created_at' => 'Created At',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('id',$this->id,true);
        $criteria->compare('user_id',$this->user_id,true);
        $criteria->compare('db_name',$this->db_name,true);
        $criteria->compare('session_id',$this->session_id,true);
        $criteria->compare('ip',$this->ip,true);
        $criteria->compare('platform_id',$this->platform_id, false);
        $criteria->compare('created_at',$this->created_at,true);
        $criteria->compare('user_agent',$this->user_agent,true);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
            'pagination' => array(
                'pageSize' => 100,
            ),
            'sort' => array(
                'defaultOrder' => 'id DESC',
            ),
        ));
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return UserLogin the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }

    public function afterSave()
    {
        parent::afterSave();

        AuditUserLoginNotification::handleSave($this);
    }

    public static function forChart($period = 'day' )
    {
        $timezone = SERVER_TIME_ZONE;

        $fields = [
            'count(*) as total_requests',
            'CONVERT_TZ(created_at, @@global.time_zone, :timezone) as created_at'
        ];

        if ($period == 'hour') {
            $between = 'created_at BETWEEN NOW() - INTERVAL 1 HOUR AND NOW()';
            $groupCond = 'YEAR(created_at), MONTH(created_at), DAY(created_at), HOUR(created_at), MINUTE(created_at)';
        } elseif ($period == 'day') {
            $between = 'created_at BETWEEN NOW() - INTERVAL 24 HOUR AND NOW()';
            $groupCond = 'YEAR(created_at), MONTH(created_at), DAY(created_at), HOUR(created_at), MINUTE(created_at)';
        } elseif ($period == 'week') {
            $between = 'created_at BETWEEN NOW() - INTERVAL 7 DAY AND NOW()';
            $groupCond = 'YEAR(created_at), MONTH(created_at), DAY(created_at), HOUR(created_at)';
        } elseif ($period == 'month') {
            $between = 'created_at BETWEEN NOW() - INTERVAL 30 DAY AND NOW()';
            $groupCond = 'YEAR(created_at), MONTH(created_at), DAY(created_at), HOUR(created_at)';
        }

        $timeframe = Yii::app()->db_audit->createCommand()
            ->select($fields)
            ->from('mc_kng_audit.user_logins')
            ->where($between)
            ->group($groupCond)
            ->bindParam(':timezone', $timezone)
            ->queryAll();

        return [
            'timeframe' => $timeframe,
        ];
    }
}
