# Account Locking Mechanism

## Overview

This document explains the account locking mechanism implemented in our application to protect against brute force attacks while maintaining a good user experience.

## Features

The account locking system includes the following security features:

1. **Failed Login Attempt Tracking**: The system tracks the number of consecutive failed login attempts per username.
2. **Progressive Delay**: After multiple failed attempts, the system enforces increasing delays between login attempts.
3. **Temporary Account Lockout**: Accounts are temporarily locked after 5 consecutive failed login attempts.
4. **Escalating Lockout Duration**: If an account is repeatedly locked, the lockout duration increases.

## Implementation Details

### Storage Mechanism

The account lockout data is stored in the application's cache system rather than in the user database table. This approach offers:

- No database schema modifications required
- Fast access/update of lockout information
- Automatic cleanup of old lockout data
- Reduced database writes

We use Yi<PERSON>'s cache component to store this information in a structured format:
