<?php

namespace App\Helpers\Predictions;

class PredictionHelper
{
    /**
     * How long it typically takes for a customer to return and purchase
     * We are going to segment by maximum days between purchases and count number of customers in each segment
     * Then, we will find the median of the last 5 records and that could be the max days between visits, but
     * additionally we are going to check if it's less than 20 we are going to return the next segment
     * @param array $segments - contains sorted array of segments with number of users in descending order by segment
     * @return int
     */
    public static function getMaxReturnDaysFromResult(array $segments): int
    {
        /* Example: $segments = [
            ['segment' => 330, 'num_users' => 1],
            ['segment' => 300, 'num_users' => 3],
            ['segment' => 270, 'num_users' => 2],
            ['segment' => 240, 'num_users' => 4],
            ['segment' => 210, 'num_users' => 47],
            ['segment' => 180, 'num_users' => 128],
        ];*/

        if (count($segments) === 0) {
            return 365;
        }

        // Find the median of the first 5 records for num_users
        $segmentsTail = array_slice($segments, 0, 5);
        $numUsers = array_column($segmentsTail, 'num_users');
        $median = self::calculateMedianForNumUsers($numUsers);

        return self::getSegmentDays($median, $segmentsTail);
    }

    private static function calculateMedianForNumUsers(array $numbers): int
    {
        if (empty($numbers)) {
            throw new \LogicException('The array is empty');
        }

        // Sort the array
        sort($numbers);
        $count = count($numbers);

        // Calculate median
        if ($count % 2 === 0) {
            // If even, return the average of the two middle numbers
            $middle1 = $numbers[$count / 2 - 1];
            $middle2 = $numbers[$count / 2];
            return (int) (($middle1 + $middle2) / 2);
        } else {
            // If odd, return the middle number
            return (int) $numbers[floor($count / 2)];
        }
    }

    private static function getSegmentDays(int $median, array $segmentsTail): int
    {
        /* Example: $segmentsTail = [
            ['segment' => 330, 'num_users' => 1],
            ['segment' => 300, 'num_users' => 3],
            ['segment' => 270, 'num_users' => 2],
            ['segment' => 240, 'num_users' => 4],
            ['segment' => 210, 'num_users' => 47],
        ];*/

        if (empty($segmentsTail)) {
            throw new \LogicException('The array is empty');
        }

        $segmentDays = null;

        /*foreach ($segmentsTail as $segment) {
            $numUsers = (int) $segment['num_users'];

            if ($numUsers === $median && $numUsers > 20) {
                $segmentDays = (int) $segment['segment'];
                break;
            }

            if ($numUsers > 20 && $segmentDays === null) {
                $segmentDays = (int) $segment['segment'];
                break;
            }
        }*/

        if ($median <= 20) {
            // Find the first segment with num_users > 20
            foreach ($segmentsTail as $segment) {
                $numUsers = (int) $segment['num_users'];
                if ($numUsers > 20) {
                    $segmentDays = (int) $segment['segment'];
                    break;
                }
            }
        } else {
            // Find the segment that matches the median
            foreach ($segmentsTail as $segment) {
                $numUsers = (int) $segment['num_users'];
                if ($numUsers === $median) {
                    $segmentDays = (int) $segment['segment'];
                    break;
                }
            }
        }

        // return the last segment from the array
        if ($segmentDays === null) {
            $segmentDays = (int) end($segmentsTail)['segment'];
        }

        return $segmentDays;
    }
}
