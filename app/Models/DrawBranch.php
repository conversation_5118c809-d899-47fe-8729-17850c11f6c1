<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\DrawBranch
 *
 * @property int $draw_branches_pk
 * @property int $draw_fk
 * @property int $business_branch_fk
 * @property int $enabled
 * @property \Illuminate\Support\Carbon|null $utc_created Globalition - UTC (0)
 * @property int $timezone_mysql_fk
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Draw> $draws
 * @property-read int|null $draws_count
 * @method static \Illuminate\Database\Eloquent\Builder|DrawBranch newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DrawBranch newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DrawBranch query()
 * @mixin \Eloquent
 */
class DrawBranch extends Model
{
    use HasFactory;

    protected $table = 'draw_branches';
    protected $primaryKey = 'draw_branches_pk';
    protected $connection = 'tenant';

    const CREATED_AT = 'utc_created';
    const UPDATED_AT = null;

    protected $dates = ['utc_created'];

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [

    ];

    public function draws(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Draw::class, 'draw_fk', 'draw_pk');
    }
}
