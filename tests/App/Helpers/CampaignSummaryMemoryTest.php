<?php

namespace Tests\App\Helpers;

use App\Helpers\Campaign\CampaignSummaryMemory;
use App\Helpers\Dto\CampaignData;
use App\Models\V1\Business;
use App\Models\V1\Follower;
use App\Models\V1\UserBusinessNotification;
use App\User;
use KangarooRewards\Common\Traits\BusinessTrait;
use Tests\TestCase;

/**
 * php vendor/bin/phpunit tests/App/Helpers/CampaignSummaryMemoryTest.php
 * php vendor/bin/phpunit --filter=testTargetedReduced tests/App/Helpers/CampaignSummaryMemoryTest.php
 */
class CampaignSummaryMemoryTest extends TestCase
{
    use BusinessTrait;

    private const BUSINESS_ID = 15;
    private Business $business;

    public function setUp(): void
    {
        parent::setUp();
        $this->business = Business::findByPk(self::BUSINESS_ID);
        $this->summaryUid = $this->faker->uuid();
        $this->data = $this->createMock(CampaignData::class);
    }

    public function testTargetedReduced(): void
    {
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        foreach ($targetedReduced as $key => $value) {
            $this->assertContains($value, $targeted);
        }
    }

    public function testEmailExistUsers(): void
    {
        $userHasEmail = $this->createUserHasEmail();
        $userHasPhone = $this->createUserHasPhone();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getEmailExistUsers($targetedReduced);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
        $this->assertContains($userHasEmail->id, $userIds);
        $this->assertNotContains($userHasPhone->id, $userIds);
    }

    /**
     * @dataProvider inputPhoneExistUsers
     */
    public function testPhoneExistUsers($conditions): void
    {
        $userHasEmail = $this->createUserHasEmail();
        $userHasPhone = $this->createUserHasPhone();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getPhoneExistUsers($targetedReduced, $conditions);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
        $this->assertContains($userHasPhone->id, $userIds);
        $this->assertNotContains($userHasEmail->id, $userIds);
    }

    public function testUserIdsNotAllowEmail(): void
    {
        $user = $this->createUserNotAllowEmail();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getUserIdsNotAllowEmail($targetedReduced);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
        $this->assertContains($user->id, $userIds);
    }

    public function testFakeEmailUsers(): void
    {
        $user = $this->createUserFakeEmail();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getFakeEmailUsers($targetedReduced);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
        $this->assertContains($user->id, $userIds);
    }

    public function testUserIdsEmailBlacklisted(): void
    {
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getUserIdsEmailBlacklisted($targetedReduced);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
    }

    public function testUserIdsNotAllowSms(): void
    {
        $user = $this->createUserNotAllowSms();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getUserIdsNotAllowSms($targetedReduced);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
        $this->assertContains($user->id, $userIds);
    }

    /**
     * @dataProvider inputPhoneExistUsers
     */
    public function testUserIdsAllowSmsPending($conditions): void
    {
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getUserIdsAllowSmsPending($targetedReduced, $conditions, true);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
    }

    /**
     * @dataProvider inputPhoneExistUsers
     */
    public function testUserIdsSingleOptInSMS($conditions): void
    {
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getUserIdsSingleOptInSMS($targetedReduced, $conditions, true);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
    }

    public function testLandlineUserIds(): void
    {
        $user = $this->createUserLandline();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getLandlineUserIds($targetedReduced, $conditions = []);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }

        $this->assertContains($user->id, $userIds);
    }

    public function testInvalidPhoneUserIds(): void
    {
        $user = $this->createUserInvalidPhone();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getInvalidPhoneUserIds($targetedReduced, $conditions = []);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }

        $this->assertContains($user->id, $userIds);
    }

    public function testFakePhoneUsers(): void
    {
        $user = $this->createUserFakePhone();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getFakePhoneUsers($targetedReduced, $conditions = []);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
        $this->assertContains($user->id, $userIds);
    }

    public function testUserIdsPhoneBlacklisted(): void
    {
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getUserIdsPhoneBlacklisted($targetedReduced, $conditions = []);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
    }

    public function testCampaignUserListExternalPush(): void
    {
        $user = $this->createUserExternalPush();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->campaignUserListExternalPush($targetedReduced, $conditions = []);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
        $this->assertContains($user->id, $userIds);
    }

    public function testUserIdsNotAllowPush(): void
    {
        $user = $this->createUserNotAllowPush();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getUserIdsNotAllowPush($targetedReduced, $conditions = []);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
        $this->assertContains($user->id, $userIds);
    }

    public function testUserIdsHasPushIos(): void
    {
        $user = $this->createUserHasPushIos();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getUserIdsHasPushIos($targetedReduced, $conditions = []);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
        $this->assertContains($user->id, $userIds);
    }

    public function testUserIdsHasPushAndroid(): void
    {
        $user = $this->createUserHasPushAndroid();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getUserIdsHasPushAndroid($targetedReduced, $conditions = []);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
        $this->assertContains($user->id, $userIds);
    }

    public function testUserIdsAllowPushIos(): void
    {
        $user = $this->createUserHasPushIos();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getUserIdsAllowPushIos($targetedReduced, $conditions = []);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
        $this->assertContains($user->id, $userIds);
    }

    public function testUserIdsAllowPushAndroid(): void
    {
        $user = $this->createUserHasPushAndroid();
        $targeted = self::getAllUserIds($this->business->business_pk);
        $service = new CampaignSummaryMemory($this->business, $this->data, $this->summaryUid);

        $targetedReduced = $service->targetedReduced($targeted);
        $userIds = $service->getUserIdsAllowPushAndroid($targetedReduced, $conditions = []);

        $this->assertNotEmpty($targetedReduced);
        $this->assertIsArray($targetedReduced);
        $this->assertNotEmpty($userIds);
        $this->assertIsArray($userIds);
        foreach ($userIds as $userId) {
            $this->assertContains($userId, $targetedReduced);
        }
        $this->assertContains($user->id, $userIds);
    }

    private function createUserHasEmail(): User
    {
        $user = User::create([
            'enabled' => 1,
            'email' => time() . $this->faker->email(),
            'business_fk' => self::BUSINESS_ID,
        ]);

        // Not necessarily consent ON
        UserBusinessNotification::create([
            'allow_sms' => 0,
            'allow_email' => 0,
            'allow_push' => 0,
            'user_fk' => $user->id,
            'business_fk' => self::BUSINESS_ID,
            'timezone_mysql_fk' => 211,
        ]);

        Follower::create([
            'user_fk' => $user->id,
            'entity_fk' => self::BUSINESS_ID,
            'entity_type_fk' => 1,
            'timezone_mysql_fk' => 211,
        ]);

        return $user;
    }

    private function createUserHasPhone(): User
    {
        $user = User::create([
            'enabled' => 1,
            'phone' => $this->faker->phoneNumber(),
            'country_phone_fk' => 1,
            'business_fk' => self::BUSINESS_ID,
        ]);

        // Not necessarily consent ON
        UserBusinessNotification::create([
            'allow_sms' => 0,
            'allow_email' => 0,
            'allow_push' => 0,
            'user_fk' => $user->id,
            'business_fk' => self::BUSINESS_ID,
            'timezone_mysql_fk' => 211,
        ]);

        Follower::create([
            'user_fk' => $user->id,
            'entity_fk' => self::BUSINESS_ID,
            'entity_type_fk' => 1,
            'timezone_mysql_fk' => 211,
        ]);

        return $user;
    }

    private function createUserHasPushIos(): User
    {
        $user = User::create([
            'enabled' => 1,
            'email' => time() . $this->faker->email(),
            'device_token' => $this->faker->uuid(),
            'gcm_device_token' => null,
            'business_fk' => self::BUSINESS_ID,
        ]);

        $this->createFollowerAndNotification($user);

        return $user;
    }

    private function createUserHasPushAndroid(): User
    {
        $user = User::create([
            'enabled' => 1,
            'email' => time() . $this->faker->email(),
            'device_token' => null,
            'gcm_device_token' => $this->faker->uuid(),
            'business_fk' => self::BUSINESS_ID,
        ]);

        $this->createFollowerAndNotification($user);

        return $user;
    }

    private function createUserNotAllowPush(): User
    {
        $user = User::create([
            'enabled' => 1,
            'email' => time() . $this->faker->email(),
            'device_token' => $this->faker->uuid(),
            'gcm_device_token' => $this->faker->uuid(),
            'business_fk' => self::BUSINESS_ID,
        ]);

        UserBusinessNotification::create([
            'allow_sms' => 0,
            'allow_email' => 0,
            'allow_push' => 0,
            'user_fk' => $user->id,
            'business_fk' => self::BUSINESS_ID,
            'timezone_mysql_fk' => 211,
        ]);

        Follower::create([
            'user_fk' => $user->id,
            'entity_fk' => self::BUSINESS_ID,
            'entity_type_fk' => 1,
            'timezone_mysql_fk' => 211,
        ]);

        return $user;
    }

    private function createUserExternalPush(): User
    {
        $user = User::create([
            'enabled' => 1,
            'email' => time() . $this->faker->email(),//user must have email
            'business_fk' => self::BUSINESS_ID,
        ]);

        $this->createFollowerAndNotification($user);

        return $user;
    }

    private function createUserFakeEmail(): User
    {
        // given there is a rule starts with ttt
        $user = User::create([
            'enabled' => 1,
            'email' => 'ttt' . $this->faker->email(),
            'business_fk' => self::BUSINESS_ID,
        ]);

        $this->createFollowerAndNotification($user);

        return $user;
    }

    private function createUserNotAllowSms(): User
    {
        $user = User::create([
            'enabled' => 1,
            'email' => null,
            'phone' => $this->faker->phoneNumber(),
            'country_phone_fk' => 1,
            'business_fk' => self::BUSINESS_ID,
        ]);

        UserBusinessNotification::create([
            'allow_sms' => 0,
            'user_fk' => $user->id,
            'business_fk' => self::BUSINESS_ID,
            'timezone_mysql_fk' => 211,
        ]);

        Follower::create([
            'user_fk' => $user->id,
            'entity_fk' => self::BUSINESS_ID,
            'entity_type_fk' => 1,
            'timezone_mysql_fk' => 211,
        ]);

        return $user;
    }

    private function createUserNotAllowEmail(): User
    {
        $user = User::create([
            'enabled' => 1,
            'email' => time() . $this->faker->email(),
            'business_fk' => self::BUSINESS_ID,
        ]);

        UserBusinessNotification::create([
            'allow_sms' => 0,
            'allow_email' => 0,
            'user_fk' => $user->id,
            'business_fk' => self::BUSINESS_ID,
            'timezone_mysql_fk' => 211,
        ]);

        Follower::create([
            'user_fk' => $user->id,
            'entity_fk' => self::BUSINESS_ID,
            'entity_type_fk' => 1,
            'timezone_mysql_fk' => 211,
        ]);

        return $user;
    }

    private function createUserFakePhone(): User
    {
        // given there is a rule starts with 555
        $user = User::create([
            'enabled' => 1,
            'email' => null,
            'phone' => '555' . random_int(1000000, 9999999),
            'country_phone_fk' => 1,
            'business_fk' => self::BUSINESS_ID,
        ]);

        $this->createFollowerAndNotification($user);

        return $user;
    }

    private function createUserLandline(): User
    {
        $user = User::create([
            'enabled' => 1,
            'email' => null,
            'phone' => $this->faker->phoneNumber(),
            'phone_number_type_fk' => 2,
            'business_fk' => self::BUSINESS_ID,
        ]);

        $this->createFollowerAndNotification($user);

        return $user;
    }

    private function createUserInvalidPhone(): User
    {
        $user = User::create([
            'enabled' => 1,
            'email' => null,
            'phone' => $this->faker->phoneNumber(),
            'phone_number_type_fk' => 4,
            'business_fk' => self::BUSINESS_ID,
        ]);

        $this->createFollowerAndNotification($user);

        return $user;
    }

    private function createFollowerAndNotification($user): void
    {
        UserBusinessNotification::create([
            'allow_sms' => 1,
            'user_fk' => $user->id,
            'business_fk' => self::BUSINESS_ID,
            'timezone_mysql_fk' => 211,
        ]);

        Follower::create([
            'user_fk' => $user->id,
            'entity_fk' => self::BUSINESS_ID,
            'entity_type_fk' => 1,
            'timezone_mysql_fk' => 211,
        ]);
    }

    private function inputPhoneExistUsers(): array
    {
        return [
            [
                [
                    'ephemeral_entity_campaign_users.has_email' => 0,
                ]
            ],
            [
                [
                    'ephemeral_entity_campaign_users.has_email' => 1,
                ]
            ],
        ];
    }
}
