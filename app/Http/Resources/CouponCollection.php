<?php

namespace App\Http\Resources;

use App\Models\V1\Currency;

class CouponCollection extends ResourceCollection
{
    /**
     * Transform the resource into a JSON array.
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            'data' => $this->collection->map(function($item) use ($request) {
                return (new CouponResource($item))->response()->getData()->data;
            }),
        ];
    }
}
