<?php

namespace App\Services;

use App\Jobs\MailMessage;
use App\Mail\Email;
use App\Models\V1\Business;
use App\Models\V1\Campaign;
use App\Models\V1\User;
use App\User as AppUser;
use App\Models\User as AppModelUser;
use App\Models\v1\Customer;
use DateTime;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\Log;
use LogicException;

class CampaignApprovalService
{
    private User|AppUser|AppModelUser|Customer $user;

    private Campaign $campaign;

    public function __construct(User|AppUser|AppModelUser|Customer $user, Campaign $campaign)
    {
        $this->user = $user; // Employee who created the campaign or System Admin user who approved it
        $this->campaign = $campaign;
    }

    public function getCampaignApprovalJwt(): string
    {
        return JWT::encode([
            'iss' => config('app.url'),
            'sub' => (int) $this->user->id,
            'iat' => (new DateTime())->getTimestamp(),
            'exp' => (new DateTime('+1 hour'))->getTimestamp(),
            'email' => $this->user->email,
            'campaignId' => (int) $this->campaign->entity_campaign_pk,
        ], config('app.jwt_key'), 'HS256');
    }

    /**
     * @param $jwt
     * @return array
     */
    public static function decodeRequest($jwt): array
    {
        try {
            $jwtData = JWT::decode($jwt, new Key(config('app.jwt_key'), 'HS256'));
        } catch (\Exception $e) {
            throw new LogicException('The request is missing a required parameter, includes an invalid parameter value, includes a parameter more than once, or is otherwise malformed. Hint: JWT', 400);
        }

        if (empty($jwtData->campaignId) || empty($jwtData->email) || empty($jwtData->sub)) {
            throw new LogicException('The request is missing a required parameter, includes an invalid parameter value, includes a parameter more than once, or is otherwise malformed. Hint: JWT payload', 400);
        }

        $user = User::findOrFail((int) $jwtData->sub);

        $campaign = Campaign::findOrFail((int) $jwtData->campaignId);

        $business = Business::findByPk($campaign->business_fk);

        return [
            'jwtData' => $jwtData,
            'campaign' => $campaign,
            'business' => $business,
            'user' => $user, // logged in user
        ];
    }

    public function approveCampaign(): void
    {
        Log::info(__METHOD__, [
            'user_id' => $this->user->id,
            'user_email' => $this->user->email,
            'business_fk' => $this->campaign->business_fk,
            'entity_campaign_pk' => $this->campaign->entity_campaign_pk,
            'message' => 'Campaign Approved',
        ]);

        $this->campaign->update([
            'enabled' => 1,
            'status' => 'QUEUE_COMPLETED',
        ]);
    }

    public function sendApprovalNotification(): void
    {
        $business = Business::findByPk($this->campaign->business_fk);

        $emailContent = $this->getEmailContent($business);

        $contacts = [
            config('app.ADMIN_EMAIL'),
            config('app.MAIL_TECH_SUPPORT'),
        ];

        if (config('app.env') === 'production' && config('app.MAIL_BCC_EMAIL')) {
            $contacts[] = config('app.MAIL_BCC_EMAIL'); // Fady
        }

        foreach ($contacts as $email) {
            $this->sendApprovalEmailAsync($email, $emailContent);
        }
    }

    private function sendApprovalEmailAsync(string $emailAddress, string $emailContent): void
    {
        $subject = 'Campaign Approval Request';

        $systemEmail = new Email();
        $systemEmail->subject($subject)->html($emailContent);

        dispatch((new MailMessage())->with($systemEmail)->to($emailAddress));
    }

    private function getEmailContent(Business $business): string
    {
        $jwt = $this->getCampaignApprovalJwt();

        $url = config('app.server_url') . '/site/campaignApproval?t=' . $jwt;

        $approvalLink = '<a href="' . $url . '" style="font-size: large;">Approve</a>';

        $emailContent = '<p> Business Name: ' . $business->name . '</p>';
        $emailContent .= '<p> Campaign Name: ' . $this->campaign->campaign_name . '</p>';
        $emailContent .= '<p> Campaign Subject: ' . $this->campaign->email_subject . '</p>';
        $emailContent .= '<p> SMS Content: ' . $this->campaign->sms_body . '</p>';
        $emailContent .= '<p> <b>Note: </b> The request will expire in 1 hour </p>';
        $emailContent .= '<p> ' . $approvalLink . '</p>';

        return $emailContent;
    }
}
