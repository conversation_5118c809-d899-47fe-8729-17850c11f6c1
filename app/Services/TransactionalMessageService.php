<?php

namespace App\Services;

use App\Helpers\V1\Utils;
use App\Jobs\TransactionalMailMessage;
use App\Jobs\TransactionalTextMessage;
use App\Mail\Email;
use App\Models\V1\Country;
use App\Models\V1\EmailProvider;
use App\Models\V1\UserProfile;
use DateTime;
use DateTimeZone;
use Illuminate\Support\Facades\App;
use libphonenumber\NumberParseException;
use App\Models\User as ModelUser;
use App\User;
use KangarooRewards\Common\Traits\BusinessBranchTrait;

class TransactionalMessageService
{
    use BusinessBranchTrait;

    private $business;
    /** @var User | ModelUser */
    private $user;

    public function __construct($business, $user)
    {
        $this->business = $business;
        $this->user = $user;
    }

    /**
     * Dispatches a job that sends a double opt-in message
     * Prerequisites:
     *  - A dedicated phone number or 10DLC registration is required
     *  - Webhook URL configured for Messaging Service (Twilio) - manually configured
     *  - business_fk set for SmsVendor record
     * @return void
     * @throws NumberParseException
     */
    public function sendDoubleOptInSms(): void
    {
        if ($this->isDoubleOptInSmsSent()) {
            return;
        }

        if (!$this->user->phone) {
            return;
        }

        $smsBody = __('api.DOUBLE_OPT_IN_TEXT_MESSAGE', [
            '{business_name}' => $this->business->name,
        ], App::getLocale());

        $country = Country::findOrFail($this->user->country_phone_fk);

        $intlPhone = Utils::getFormattedPhoneNumber($this->user->phone, $country->code, 'E164');

        dispatch((new TransactionalTextMessage($this->user->id, $this->business->business_pk, null))
            ->content($smsBody)
            ->to($intlPhone));

        $this->updateOptInSmsSent();
    }

    public function sendAccountLockNotification(string $lockoutUntilUtc): void
    {
        $mainBranchId = $this->getDefaultBranchId($this->business->business_pk);
        $mainBranchTmz = $this->getBranchTimezoneName($mainBranchId);
        $lockoutUntil = new DateTime($lockoutUntilUtc, new DateTimeZone('UTC'));
        $lockoutUntil->setTimezone(new DateTimeZone($mainBranchTmz));

        $emailProvider = EmailProvider::where('business_fk', $this->business->business_pk)->first();

        $mailMessageJob = new TransactionalMailMessage(
            $this->user->id,
            $this->business->business_pk,
            $emailProvider,
            false,
            true,
            true
        );

        $accountLockEmail = new Email($emailProvider);
        $accountLockEmail->subject(__('api.ACCOUNT_LOCKED_EMAIL_SUBJECT'))
            ->view('emails.admin.accountLockAttempts.en')
            ->with([
                'userName' => $this->user->getFullName(),
                'lockoutUntil' => $lockoutUntil,
            ]);

        $mailMessageJob
            ->setProvider($emailProvider)
            ->with($accountLockEmail)
            ->to($this->user->email);

        dispatch($mailMessageJob);
    }

    private function isDoubleOptInSmsSent(): bool
    {
        $userProfile = UserProfile::where('user_fk', $this->user->id)->first();

        return $userProfile && (int) $userProfile->opt_in_sms_sent === 1;
    }

    private function updateOptInSmsSent(): void
    {
        $userProfile = UserProfile::where('user_fk', $this->user->id)->first();

        if (!$userProfile) {
            $userProfile = new UserProfile();
            $userProfile->user_fk = $this->user->id;
        }

        $userProfile->opt_in_sms_sent = 1;
        $userProfile->save();
    }
}
