<?php

namespace KangarooRewards\Common\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int id
 * @property string phone_number
 * @property string phone_number_sid
 * @property string created_at
 * @property string updated_at
 * @property int business_fk
 * @property int a2p_10dlc_profile_id
 */
class A2p10dlcPhoneNumber extends Model
{
    protected $table = 'a2p_10dlc_phone_numbers';
    protected $primaryKey = 'id';
    protected $connection = 'tenant';

    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';

    protected $guarded = [
        //
    ];

    public function business()
    {
        return $this->belongsTo(Business::class, 'business_fk', 'business_pk');
    }

    public function a2p10dlcProfile()
    {
        return $this->belongsTo(A2p10dlcProfile::class, 'a2p_10dlc_profile_id', 'id');
    }
}
