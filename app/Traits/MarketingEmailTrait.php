<?php

namespace App\Traits;

trait MarketingEmailTrait
{
    public static function getVariables()
    {
        $variables = CustomMessagesTrait::getCustomVariables();
        return [
            'subject' => [
            ],
            'general' => [
                CUSTOMIZED_MESSAGES_KEY_USER_FIRST => $variables[CUSTOMIZED_MESSAGES_KEY_USER_FIRST],
                CUSTOMIZED_MESSAGES_KEY_USER_LAST => $variables[CUSTOMIZED_MESSAGES_KEY_USER_LAST],
                CUSTOMIZED_MESSAGES_KEY_USER_FULL_NAME => $variables[CUSTOMIZED_MESSAGES_KEY_USER_FULL_NAME],
                CUSTOMIZED_MESSAGES_KEY_USER_EMAIL => $variables[CUSTOMIZED_MESSAGES_KEY_USER_EMAIL],
            ],
            'default' => [
            ],
            'custom' => [
            ],
            'required' => [
            ]
        ];
    }
}