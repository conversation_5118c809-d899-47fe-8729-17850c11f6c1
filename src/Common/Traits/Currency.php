<?php

namespace KangarooRewards\Common\Traits;

use Illuminate\Support\Facades\DB;

trait Currency
{
    /**
     * Get currency for branch
     *
     * @return array
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @version 1.0
     */
    public static function forBranch($branchId)
    {
        return \DB::table('address as a')
            ->select(['cu.name', 'cu.symbol', 'cu.iso_code', 'cu.denominator'])
            ->join('cities as c', 'c.city_pk', '=', 'a.city_fk')
            ->join('regions as r', 'r.region_pk', '=', 'c.region_fk')
            ->join('countries as co', 'co.country_pk', '=', 'r.country_fk')
            ->join('currency as cu', 'cu.currency_pk', '=', 'co.currency_fk')
            ->where('a.entity_fk', $branchId)
            ->whereIn('a.entity_type_fk', [2, 8, 100])
            ->first();
    }

    /**
     * Transform Currency Item
     *
     * @return array
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @version 1.0
     */
    public static function transformItem($currency)
    {
        return [
            'name' => $currency->name,
            'symbol' => $currency->symbol,
            'iso_code' => $currency->iso_code,
        ];
    }


}