<?php

namespace App\Services;

use App\Jobs\TransactionalMailMessage;
use App\Mail\Email;
use App\Models\TimezoneMysql;
use App\Models\UserActionLog;
use App\Models\V1\BusinessBranch;
use App\Models\V1\UserEntityPermission;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;

abstract class FraudulentActivityService
{
    const FRAUD_TYPE_SYSTEM = 1;
    const FRAUD_TYPE_UBER = 2;
    const FRAUD_TYPE_AMAZON = 3;

    protected $business;
    protected $customer;
    private $shouldBlockAction = false;
    private $defaultBranch;
    protected $businessTimezoneName;

    public function __construct($business, $customer)
    {
        $this->business = $business;
        $this->customer = $customer;
    }

    abstract protected function getFraudRules();

    abstract protected function validate($fraudRule);

    abstract protected function prepareUserActionData();

    abstract protected function getNotificationEmailTemplateName();

    abstract protected function prepareNotificationEmailData();

    protected function getDefaultBranch()
    {
        if (!$this->defaultBranch) {
            $this->defaultBranch = BusinessBranch::where([
                'business_fk' => $this->business->business_pk,
                'enabled' => 1,
                'default_branch' => 1
            ])->firstOrFail();
        }

        return $this->defaultBranch;
    }

    protected function getBusinessTimezoneName()
    {
        if (!$this->businessTimezoneName) {
            $timezoneId = $this->getDefaultBranch()->branch_timezone_fk;
            $this->businessTimezoneName = TimezoneMysql::find($timezoneId)->name;
        }

        return $this->businessTimezoneName;
    }

    protected function getConditions($fraudRule)
    {
        $parsedConditions = [];
        if ($fraudRule->fraud_block_action_fk == 1) {
            $parsedConditions['amount'] = $fraudRule->conditions['amount'] ?? 0;
        } elseif ($fraudRule->fraud_block_action_fk == 2) {
            $parsedConditions['count'] = $fraudRule->conditions['count'] ?? 0;
        }

        return $parsedConditions;
    }

    protected function getBlockStartDate($fraudRule)
    {
        $currentTime = Carbon::now();
        $currentTime->subMinutes($fraudRule->block_duration);
        return $currentTime->toDateTimeString();
    }


    public function process()
    {
        $fraudRules = $this->getFraudRules();
        foreach ($fraudRules as $fraudRule) {
            if (!$this->validate($fraudRule)) {
                info(__METHOD__, ['business' => $this->business->business_pk, 'fr' => $fraudRule->fraud_rule_pk]);
                $this->saveUserAction($fraudRule);
                if ((bool)$fraudRule->block_action) {
                    $this->shouldBlockAction = true; // the action should be blocked without further progress.
                    break;
                }
            }
        }
    }

    public function shouldBlockAction()
    {
        return $this->shouldBlockAction;
    }

    private function saveUserAction($fraudRule)
    {
        $userActionLog = UserActionLog::select('data')
            ->where('fraud_rule_fk', $fraudRule->fraud_rule_pk)
            ->where('business_fk', $this->business->business_pk)
            ->whereRaw("created_at >  DATE_SUB(NOW(), INTERVAL {$fraudRule->block_duration} MINUTE)")
            ->orderBy('id', 'DESC')
            ->first();

        $attempts = 0;
        if (isset($userActionLog, $userActionLog->data['attempts'])) {
            $attempts = $userActionLog->data['attempts'];
        }

        $attempts++;

        $data = $this->prepareUserActionData();
        $sent = $this->sendNotification($fraudRule, $attempts);
        UserActionLog::create([
            'user_fk' => $this->customer->id,
            'business_fk' => $this->business->business_pk,
            'platform_solution_fk' => config('app.platform_id', 10),
            'employee_user_fk' => null,
            'data' => array_merge([
                'max_attempts' => $fraudRule->max_attempts,
                'notification_interval' => $fraudRule->notification_interval,
                'attempts' => $attempts,
                'sent' => $sent
            ], $data),
            'fraud_rule_fk' => $fraudRule->fraud_rule_pk
        ]);
    }

    private function sendNotification($fraudRule, $attempts)
    {
        if ($fraudRule->max_attempts != 0 && $fraudRule->max_attempts < $attempts) {
            return false;
        }

        if ($attempts != 1 && ($attempts % $fraudRule->notification_interval === 1)) {
            return false;
        }

        if (empty($fraudRule->email)) {
            return false;
        }

        try {
            $emailTemplateName = $this->getNotificationEmailTemplateName();
            $emailParams = $this->prepareNotificationEmailData();
            $local = 'en';
//            if (!View::exists('emails.admin.'.$emailTemplateName . '.' . $local)) {

            $defaultBranch = $this->getDefaultBranch();

            $subject = __('api.ADMIN_FRAUDULENT_ACTIVITY_EMAIL_SUBJECT');

            $emailModel = new Email();
            $emailModel
                ->subject($subject)
                ->view('emails.admin.' . $emailTemplateName . '.' . $local)
                ->with(array_merge(
                    $emailParams,
                    [
                            'businessName' => $defaultBranch->name
                    ]
                ));

            info(__METHOD__ . 'dispatch admin email', [
                'business' => $this->business->business_pk,
                'email' => $fraudRule->email,
                'template' => $emailTemplateName
            ]);

            dispatch((new TransactionalMailMessage(
                null,
                $this->business->business_pk,
                null,
                false,
                false,
                true
            ))->with($emailModel)->to($fraudRule->email));

//            dispatch((new TransactionalMailMessage(
//                null,
//                KNG_BUSINESS_ID,
//            ))->with($emailModel)->to(config('app.ADMIN_EMAIL')));
            return true;
        } catch (\Exception $e) {
            Log::error(__METHOD__, ['businessId' => $this->business->business_pk, 'template' => $emailTemplateName, 'e' => $e]);
        }
        return false;
    }
}
