<?php

namespace App\Http\Controllers\Api\V1\Users;

use App\Http\Resources\BannerCollection;
use App\Models\V1\Language;
use App\Traits\BannerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\App;
use App\Http\Controllers\Api\V1\ApiController;
use Illuminate\Http\Request;

/**
 * Banner
 *
 * @Resource("Banners", uri="/users/{id}/banners")
 */
class BannerController extends ApiController
{
    use BannerTrait;

    /**
     * Banners list
     *
     * GET /users/{id}/banners
     *
     * @Get("/users/{id}/banners{?page}")
     * @Versions({"v1"})
     * @Transaction({
     *      @Response(200, body={"data":{"profile":{"user_id":12345678,"username":"719397910218","email":"<EMAIL>","first_name":"<PERSON>","last_name":"<PERSON><PERSON>"},"balance":{"active_points":600,"active_punches":0}}})
     *      @Response(404, body={"data":null,"code":404,"error":"Not found","message":"Customer 12345118 could not be found."})
     * })
     * @Parameters({
     *      @Parameter("page", type="integer", required=false description="You can optionally request that the response include the customer's balance. Example: include[]=balance")
     * })
     *
     * @param Request $request
     * @param string $userUId
     *
     * @return JsonResponse
     */
    public function index(Request $request, $userUId)
    {
        $appUser = $request->user();
        $appBusiness = $this->getBusinessForAppKey();

        $this->sanitizeInput();

        $this->validate($request, [
            'per_page' => 'sometimes|integer|min:1|max:' . config('app.pagination_limit'),
//            'archived' => 'sometimes|boolean',
        ]);

        $conditions = [];
        $locale = App::getLocale();
        if (isset($locale)) {
            $langId = Language::getLanguageIdByAbbreviation($locale);
            $conditions[] = ['banners.language_fk', '=', $langId];
        }

        $include = $request->include ? array_map('trim', explode(',', $request->include)) : [];
        $archived = in_array('archived', $include, true) ? null : false;
//
//        if (isset($request->archived)) {
//            $archived = (boolean)$request->archived;
//        }

        $banners = $this->BannersForBusiness(
            $appBusiness,
            $conditions,
            [$appUser->id],
            $archived
        )->simplePaginate((int)$request->per_page);

        return (new BannerCollection($banners))->response();
    }
}
