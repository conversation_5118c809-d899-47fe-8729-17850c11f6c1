<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used on API
    |
     */

    'REQUEST_WRONG_ARGS' => 'Bad Request',
    'REQUEST_NOT_FOUND' => 'Not found',

    'API_GIFTCARD_PURCHASE_SMS' => 'You have just purchased a :currency_cymbol:item_price Gift Card @:business_name',
    'ACTIVATION_OF_YOUR_ACCOUNT' => ':business_name: Activation de votre compte',
    'FRONTEND_RESET_PIN_SMS' => ':business_name. Click this link to reset your PIN :short_link',
    'FRONTEND_RESET_PIN_SMS_BROADNET' => ':business_name. Click this link to reset :short_link',
    'FRONTEND_RESET_PIN_SMS_VERIF_CODE' => ':business_name. Verification code :verif_code to reset your PIN',
    'FRONTEND_RESET_PIN_SMS_VERIF_CODE_BROADNET' => ':business_name. Reset with :verif_code',
    'FRONTEND_CHANGE_PHONE_SMS' => ':business_name. Click this verification link to confirm your cell# :short_link',
    'API_TPR_REDEMPTION_SMS' => 'You have just redeemed :reward for :points points @:business_name',
    'FRONTEND_USERSIGNUP_VERIFY_SMS_STORE' => 'Welcome to :business_name Loyalty Program. Your PIN code is :pin_code. Tap the link to verify your account :short_link',
    'FRONTEND_USERSIGNUP_VERIFY_SMS_STORE_WITHOUT_PIN' => 'Welcome to :business_name Loyalty Program. Tap the link to verify your account :short_link',

    'MERCHANTAPP_PUNCHPOINTSREWARD_EXACT_AMOUNT_CLAIM_ERROR_MESSAGE' => 'When Exact Amount of the purchase is selected, the amount is required.',
    'MERCHANTAPP_PUNCHPOINTSREWARD_FRECUENCY_ERROR_MESSAGE' => 'Ask the cashier when you can claim next!',
    'MERCHANTAPP_CLERKPINCODE_INCORRECT_PIN' => 'Invalid Employee ID (PIN Code)',
    'MERCHANTAPP_CUSTOMER_INCORRECT_PIN' => 'Invalid Customer PIN Code',
    'MERCHANTAPP_POINTREDEEM_MERCHANT_NOT_ALLOW' => 'This Merchant is not allow to Redeem on behalf of the Consumer any more',
    
    'CUSTOMER_NO_ELIGIBLE_TO_REDEEM_THIS' => 'This customer is not eligible to redeem this',

    'TRX_SUBTYPE_COUPON_CLAIMED' => 'Coupon réclamé',
    'TRX_SUBTYPE_OFFER_CREATED_BASIC' => 'Offre créée',
    'TRX_SUBTYPE_TAGGED_BASIC' => 'Offre taguée',
    'TRX_SUBTYPE_UNTAGGED_BASIC' => 'Offre détaguée',
    'TRX_SUBTYPE_FACEBOOK_SHARED_BASIC' => 'Offre partagée sur FB',
    'TRX_SUBTYPE_TWITTER_SHARED_BASIC' => 'Offre partagée sur TW',
    'TRX_SUBTYPE_TARGETED_BASIC' => 'Offre créée - Ciblée',
    'TRX_SUBTYPE_REFER_BUSINESS' => 'Entreprise référée',
    'TRX_SUBTYPE_EXTERNAL_TX' => 'Transaction externe',
    'TRX_SUBTYPE_NEW_BUSINESS' => 'Nouvelle entreprise créée',
    'TRX_SUBTYPE_CHECKIN' => 'Enregistrement',
    'TRX_SUBTYPE_GIFTCARD_POINTS_CREATED' => 'Carte cadeau créée - Points',
    'TRX_SUBTYPE_FACEBOOK_SHARED_GIFTCARD' => 'Carte cadeau partagée sur FB',
    'TRX_SUBTYPE_TWITTER_SHARED_GIFTCARD' => 'Carte cadeau partagée sur TW',
    'TRX_SUBTYPE_TARGETED_GIFTCARD' => 'Carte cadeau créée - Ciblée',
    'TRX_SUBTYPE_GIFTCARD_POINTS_PURCHASED' => 'Carte cadeau achetée - Points',
    'TRX_SUBTYPE_GIFTCARD_POINTS_PAYPAL_REFUND' => 'Carte cadeau remboursée - PayPal',
    'TRX_SUBTYPE_GIFTCARD_POINTS_PURCHASED_MERCHANT' => 'Carte cadeau achetée - En magasin',
    'TRX_SUBTYPE_TAGGED_POINTS' => 'Offre taguée',
    'TRX_SUBTYPE_CLAIMED_POINTS' => 'Offre réclamée',
    'TRX_SUBTYPE_SHARED' => 'Offre partagée',
    'TRX_SUBTYPE_ACCEPTED' => 'Offre acceptée',
    'TRX_SUBTYPE_UNTAGGED_POINTS' => 'Offre détaguée',
    'TRX_SUBTYPE_FACEBOOK_SHARED_POINTS' => 'Offre partagée sur FB',
    'TRX_SUBTYPE_TWITTER_SHARED_POINTS' => 'Offre partagée sur TW',
    'TRX_SUBTYPE_OFFER_CREATED_POINTS' => 'Offre créée',
    'TRX_SUBTYPE_CLERK_CANCELATION' => 'Offre annulée - Employé',
    'TRX_SUBTYPE_TARGETED_POINTS' => 'Offre créée - Ciblée',
    'TRX_SUBTYPE_OFFER_CREATED_PUNCH' => 'Offre créée',
    'TRX_SUBTYPE_TAGGED_PUNCH' => 'Offre taguée',
    'TRX_SUBTYPE_UNTAGGED_PUNCH' => 'Offre détaguée',
    'TRX_SUBTYPE_CLAIMED_PUNCH' => 'Offre réclamée',
    'TRX_SUBTYPE_FACEBOOK_SHARED_PUNCH' => 'Offre partagée sur FB',
    'TRX_SUBTYPE_TWITTER_SHARED_PUNCH' => 'Offre partagée sur TW',
    'TRX_SUBTYPE_TARGETED_PUNCH' => 'Offre créée - Ciblée',
    'TRX_SUBTYPE_REWARD_POINTS' => 'Récompense',
    'TRX_SUBTYPE_REDEMPTION' => 'Échange',
    'TRX_SUBTYPE_CALLBACK' => 'Rappel',
    'TRX_SUBTYPE_DONATION' => 'Don',
    'TRX_SUBTYPE_SIGNUP_REWARD_POINTS' => 'Bonus d’inscription',
    'TRX_SUBTYPE_PRODUCT_REDEMPTION_POINTS' => 'Échange - Catalogue',
    'TRX_SUBTYPE_CLERK_TRX_CANCELATION' => 'Transaction annulée - Employé',
    'TRX_SUBTYPE_CLERK_REDEMPTION' => 'Échange au nom du client - Personnalisé',
    'TRX_SUBTYPE_CLERK_PRODUCT_REDEMPTION_POINTS' => 'Échange au nom du client - Catalogue',
    'TRX_SUBTYPE_POINTS_REFUND' => 'Remboursement - Points',
    'TRX_SUBTYPE_CLAIMED_POINTS_REWARD' => 'Réclamation automatique - Points',
    'TRX_SUBTYPE_AGGREGATED_CLAIMED_POINTS_REWARD' => 'Réclamation automatique multiple - Points',
    'TRX_SUBTYPE_TRANSACTION_SUBTYPE_NAME_FOLLOW_BUSINESS' => 'Bonus Suivre-Mon-Magasin',
    'TRX_SUBTYPE_TRANSFER_POINTS_SENDER_RECALL' => 'Rappel de transfert de points',
    'TRX_SUBTYPE_PRODUCT_CREATED' => 'Produit créé',
    'TRX_SUBTYPE_REWARD_PUNCH' => 'Récompense',
    'TRX_SUBTYPE_PRODUCT_REDEMPTION_PUNCH' => 'Échange',
    'TRX_SUBTYPE_SIGNUP_REWARD_PUNCH' => 'Bonus d’inscription',
    'TRX_SUBTYPE_MULTI_PUNCHES' => 'Récompense Multi-Punch',
    'TRX_SUBTYPE_CLERK_PRODUCT_REDEMPTION_PUNCH' => 'Échange au nom du client',
    'TRX_SUBTYPE_POINTS_REWARD_CREATED' => 'Offre d’échange créée',
    'TRX_SUBTYPE_FACEBOOK_SHARED_RED_CATALOG_POINTS' => 'Offre partagée sur FB',
    'TRX_SUBTYPE_TWITTER_SHARED_RED_CATALOG_POINTS' => 'Offre partagée sur TW',
    'TRX_SUBTYPE_TARGETED_RED_CATALOG_POINTS' => 'Offre créée - Ciblée',
    'TRX_SUBTYPE_PUNCH_REWARD_CREATED' => 'Offre d’échange créée',
    'TRX_SUBTYPE_FACEBOOK_SHARED_RED_CATALOG_PUNCH' => 'Offre partagée sur FB',
    'TRX_SUBTYPE_TWITTER_SHARED_RED_CATALOG_PUNCH' => 'Offre partagée sur TW',
    'TRX_SUBTYPE_TARGETED_RED_CATALOG_PUNCH' => 'Offre créée - Ciblée',
    'TRX_SUBTYPE_TRANSFER_POINTS_SENDER' => 'Transfert de points - Expéditeur',
    'TRX_SUBTYPE_TRANSFER_POINTS_RECEIVER' => 'Transfert de points - Destinataire',
    'TRX_SUBTYPE_TRANSFER_PUNCHES_SENDER' => 'Transfert de punchs - Expéditeur',
    'TRX_SUBTYPE_TRANSFER_PUNCHES_RECEIVER' => 'Transfert de punchs - Destinataire',
    'TRX_SUBTYPE_TROK_ACCEPTED' => 'Trok accepté',
    'TRX_SUBTYPE_BUNDLE_ACCEPTED' => 'Pack accepté',
    'TRX_SUBTYPE_DELETED_TROK' => 'Trok supprimé',
    'TRX_SUBTYPE_TROK_CREATED' => 'Trok créé',
    'TRX_SUBTYPE_TROK_UPDATED' => 'Trok mis à jour',
    'TRX_SUBTYPE_FOLLOW_BUSINESS' => 'Bonus Suivre-Mon-Magasin',
    'TRX_SUBTYPE_ADJUST_BALANCE_POINTS_INCREASE' => 'Ajustement augmentation points',
    'TRX_SUBTYPE_ADJUST_BALANCE_PUNCH_INCREASE' => 'Ajustement augmentation punchs',
    'TRX_SUBTYPE_ADJUST_BALANCE_POINTS_DECREASE' => 'Ajustement diminution points',
    'TRX_SUBTYPE_ADJUST_BALANCE_PUNCH_DECREASE' => 'Ajustement diminution punchs',
    'TRX_SUBTYPE_GIFTCARD_AMOUNT_PURCHASED' => 'Carte cadeau achetée - Montant',
    'TRX_SUBTYPE_GIFTCARD_AMOUNT_PURCHASED_MERCHANT' => 'Carte cadeau achetée - En magasin',
    'TRX_SUBTYPE_TRANSFER_AMOUNT_SENDER' => 'Transfert de montant - Expéditeur',
    'TRX_SUBTYPE_TRANSFER_AMOUNT_RECEIVER' => 'Transfert de montant - Destinataire',
    'TRX_SUBTYPE_GIFTCARD_AMOUNT_PAYMENT' => 'Paiement par carte cadeau',
    'TRX_SUBTYPE_COUPON_CONVERTIBLE_CREATED' => 'Coupon créé',
    'TRX_SUBTYPE_COUPON_GET_COUPON' => 'Coupon tagué',
    'TRX_SUBTYPE_COUPON_CONVERTIBLE_CREATED' => 'Coupon convertible créé',
    'TRX_SUBTYPE_COUPON_CONVERTIBLE_CLAIMED' => 'Coupon convertible échangé',
    'TRX_SUBTYPE_GIFTCARD_AMOUNT_STORE_CREDIT' => 'Remise en argent',

    //** START - Notifications - THE VAR NAME CAN NOT CHANGE (THESE ARE USED IN SOME TABLES)
    'NOTIF_USER_TAGGED_OFFER' => 'Tagged an Offer from :{business_name} ',
    'NOTIF_FRIEND_REQUEST' => 'sent you a friend request.',
    'NOTIF_FRIEND_ACCEPTED' => ':{username} accepted your friend request.',
    'NOTIF_REFER_BUSINESS' => 'has referred :{business_name}  to you.',
    'NOTIF_IS_FOLLOWING' => 'for referring :{business_name} to :{username}.',
    'NOTIF_TITLE_YOU_EARNED' => 'You earned :{units_exchange} Points',
    'NOTIF_TITLE_TROK_ACCEPTED_POINT' => 'Exchange Accepted!',
    'NOTIF_TEXT_TROK_ACCEPTED_POINT' => 'You just got :{units_exchange} Pt from :{business_one} in exchange for your  :{units_exchange} Pt from  :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_POINTS' => 'You just got :{units_exchange} pts from :{business_one} in exchange for your  :{units_exchange} pts from  :{business_two}',
    'NOTIF_TITLE_YOU_EARNED_O_POINTS' => 'Thank you',
    'NOTIF_TITLE_YOU_EARNED_POINT' => 'You earned :{units_exchange} point',
    'NOTIF_OFFER_CREATED' => ':{offer_title} from :{business_name}',
    'NOTIF_PUNCH_CREATED' => ':{offer_title} from :{business_name}',
    'NOTIF_TITLE_NEW_OFFER' => 'New Offer', //changed
    'NOTIF_TITLE_NEW_PUNCH' => 'New Reward', //changed
    'NOTIF_TITLE_FRIEND_ACCEPTED' => 'Friend Request Accepted',
    'NOTIF_TITLE_STORE_REFERAL_ACCEPTED' => 'Store Referral Accepted',
    'NOTIF_TEXT_THANKS_FOR_REFERRING' => 'Thank you for referring :{business_name} to :{username}',
    'NOTIF_TEXT_YOU_EARNED_X_PT' => 'You earned :{units_exchange} point for referring :{business_name} to :{username}',
    'NOTIF_TEXT_YOU_EARNED_X_PTS' => 'You earned :{units_exchange} points for referring :{business_name} to :{username}',
    'NOTIF_TITLE_OFFER_TAGGED' => 'Offer Tagged',
    'NOTIF_USER_TAGGED_OFFER' => ':{username} Tagged an Offer from :{business_name}',
    'NOTIF_TEXT_TROK_ACCEPTED_POINT_YOU' => 'You just accepted :{username_created}\'s Exchange and got :{units_exchange} Pt from :{business_one} in exchange for your  :{units_exchange} Pt from  :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_POINTS_YOU' => 'You just accepted :{username_created}\'s Exchange and got :{units_exchange} pts from :{business_one} in exchange for your  :{units_exchange} pts from  :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_POINT_USER' => ':{username_accepted} just accepted your Exchange; you got :{units_exchange} Pt from :{business_one} in exchange for your :{units_exchange} Pt from :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_POINTS_USER' => ':{username_accepted} just accepted your Exchange; you got :{units_exchange} pts from :{business_one} in exchange for your :{units_exchange} pts from :{business_two}',
    'NOTIF_TITLE_PUNCH_SHARED' => 'Reward shared successfully',
    'NOTIF_PUNCH_SHARED_FACEBOOK' => 'You earned :{units_exchange} Punches for sharing :{offer_title} on Facebook',
    'NOTIF_PUNCH_SHARED_TWITTER' => 'You earned :{units_exchange} Punches for sharing :{offer_title} on Twitter',
    'NOTIF_PUNCH_SHARED_TWITTER1' => 'You earned :{units_exchange} Punch for sharing :{offer_title} on Twitter',
    'NOTIF_PUNCH_SHARED_FACEBOOK1' => 'You earned :{units_exchange} Punch for sharing :{offer_title} on Facebook',
    'NOTIF_TITLE_WELCOME_POINT_PUNCH' => 'Welcome to :{business_name}\'s loyalty program',
    'NOTIF_TEXT_WELCOME_PUNCH_SINGULAR' => 'As a thank you for your loyalty, :{business_name} is offering you :{units_exchange} Punch',
    'NOTIF_TEXT_WELCOME_PUNCH_PLURAL' => 'As a thank you for your loyalty, :{business_name} is offering you :{units_exchange} Punches',
    'NOTIF_TEXT_WELCOME_POINT_SINGULAR' => 'As a thank you for your loyalty, :{business_name} is offering you :{units_exchange} Point',
    'NOTIF_TEXT_WELCOME_POINT_PLURAL' => 'As a thank you for your loyalty, :{business_name} is offering you :{units_exchange} Points',
    'NOTIF_TITLE_OFFER_SHARED' => 'Offer shared successfully',
    'NOTIF_OFFER_SHARED_FACEBOOK_PLURAL' => 'You earned :{units_exchange} Points for sharing :{offer_title} on Facebook',
    'NOTIF_OFFER_SHARED_TWITTER_PLURAL' => 'You earned :{units_exchange} Points for sharing :{offer_title} on Twitter',
    'NOTIF_OFFER_SHARED_FACEBOOK_SINGULAR' => 'You earned :{units_exchange} Point for sharing :{offer_title} on Facebook',
    'NOTIF_OFFER_SHARED_TWITTER_SINGULAR' => 'You earned :{units_exchange} Point for sharing :{offer_title} on Twitter',
    'NOTIF_TEXT_TROK_ACCEPTED_PUNCH_YOU' => 'You just accepted :{username_created}\'s Exchange and got :{units_exchange} Punch from :{business_one} in exchange for your  :{units_exchange} Punch from  :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_PUNCHES_YOU' => 'You just accepted :{username_created}\'s Exchange and got :{units_exchange} Punches from :{business_one} in exchange for your  :{units_exchange} Punches from  :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_PUNCH_USER' => ':{username_accepted} just accepted your Exchange; you got :{units_exchange} Punch from :{business_one} in exchange for your :{units_exchange} Punch from :{business_two}',
    'NOTIF_TEXT_TROK_ACCEPTED_PUNCHES_USER' => ':{username_accepted} just accepted your Exchange; you got :{units_exchange} Punches from :{business_one} in exchange for your :{units_exchange} Punches from :{business_two}',
    'NOTIF_TEXT_YOU_EARNED_X_PUNCH' => 'You earned :{units_exchange} punch for referring :{business_name} to :{username}',
    'NOTIF_TEXT_YOU_EARNED_X_PUNCHES' => 'You earned :{units_exchange} punches for referring :{business_name} to :{username}',
    'NOTIF_PROMOTION_SHARED_FACEBOOK_PLURAL' => 'You earned :{units_exchange} Punches for sharing :{offer_title} on Facebook',
    'NOTIF_PROMOTION_SHARED_TWITTER_PLURAL' => 'You earned :{units_exchange} Punches for sharing :{offer_title} on Twitter',
    'NOTIF_PROMOTION_SHARED_FACEBOOK_SINGULAR' => 'You earned :{units_exchange} Punch for sharing :{offer_title} on Facebook',
    'NOTIF_PROMOTION_SHARED_TWITTER_SINGULAR' => 'You earned :{units_exchange} Punch for sharing :{offer_title} on Twitter',
    'FRONTEND_NOTIF_POINTREWARD_CREATED' => ':{offer_title} from :{business_name}',
    'FRONTEND_NOTIF_POINTREWARD_SHARED_FB_PLURAL' => 'You earned :{units_exchange} Points for sharing :{offer_title} on Facebook',
    'FRONTEND_NOTIF_POINTREWARD_SHARED_TW_PLURAL' => 'You earned :{units_exchange} Points for sharing :{offer_title} on Twitter',
    'FRONTEND_NOTIF_POINTREWARD_SHARED_FB_SINGULAR' => 'You earned :{units_exchange} Point for sharing :{offer_title} on Facebook',
    'FRONTEND_NOTIF_POINTREWARD_SHARED_TW_SINGULAR' => 'You earned :{units_exchange} Point for sharing :{offer_title} on Twitter',
    'FRONTEND_NOTIF_PUNCHREWARD_SHARED_FB_PLURAL' => 'You earned :{units_exchange} Punches for sharing :{offer_title} on Facebook',
    'FRONTEND_NOTIF_PUNCHREWARD_SHARED_TW_PLURAL' => 'You earned :{units_exchange} Punches for sharing :{offer_title} on Twitter',
    'FRONTEND_NOTIF_PUNCHREWARD_SHARED_FB_SINGULAR' => 'You earned :{units_exchange} Punch for sharing :{offer_title} on Facebook',
    'FRONTEND_NOTIF_PUNCHREWARD_SHARED_TW_SINGULAR' => 'You earned :{units_exchange} Punch for sharing :{offer_title} on Twitter',

    //V325
    'NOTIF_REQ_NEW_OFFER_TITLE' => 'New Offer',
    'NOTIF_REQ_NEW_POINT_REDEMPTION_TITLE' => 'New Product to Redeem',
    'NOTIF_REQ_NEW_PUNCH_REDEMPTION_TITLE' => 'New Product to Redeem',
    'NOTIF_REQ_NEW_MKTCAMPAIGN_OFFER_TITLE' => 'A special Offer only for you',
    'NOTIF_REQ_NEW_MKTCAMPAIGN_POINT_REDEMPTION_TITLE' => 'New point product to redeem, exclusive for you',
    'NOTIF_REQ_NEW_MKTCAMPAIGN_PUNCH_REDEMPTION_TITLE' => 'New punch product to redeem, exclusive for you',
    'NOTIF_TEXT_OFFER_CREATED' => ':{offer_title} from :{business_name}',
    'NOTIF_TEXT_POINT_REDEMPTION_CREATED' => ':{offer_title} from :{business_name}',
    'NOTIF_TEXT_PUNCH_REDEMPTION_CREATED' => ':{offer_title} from :{business_name}',
    'NOTIF_TEXT_MKTCAMPAIGN_GENERIC' => ':{offer_title} from :{business_name}',
    'FRONTEND_NOTIF_REWARD_SHARED_FB_ZERO' => 'Thank you for sharing :{offer_title} on Facebook',
    'FRONTEND_NOTIF_REWARD_SHARED_TW_ZERO' => 'Thank you for sharing :{offer_title} on Twitter',
    'NOTIF_REQ_NEW_GIFTCARD_TITLE' => 'New Offer', //A new Gift Card was just created - TITLE
    'NOTIF_TEXT_NEW_GIFTCARD' => ':{offer_title} from :{business_name}', //A new Gift Card was just created - CONTENT

    'NOTIF_REQ_GIFTCARD_PURCHASE_TITLE' => 'Gift Card Purchase was successfully completed', //A Gift Card was just purchased by consumer - TITLE
    'NOTIF_TEXT_GIFTCARD_PURCHASE' => 'You bought :{currency}:{units_exchange} gift card from :{business_name}', //A new Gift Card was just purchased by consumer - CONTENT

    'NOTIF_REQ_POINT_GIFTCARD_SHARED_FACEBOOK_TITLE' => 'Gift Card Shared in Facebook', //A Gift Card was just shared - TITLE
    'FRONTEND_NOTIF_POINT_GIFTCARD_SHARED_FB_SINGULAR' => 'You earned 1 Point for sharing :{offer_title} on Facebook', //A Gift Card was just shared - CONTENT
    'FRONTEND_NOTIF_POINT_GIFTCARD_SHARED_FB_PLURAL' => 'You earned :{units_exchange} Points for sharing :{offer_title} on Facebook', //A Gift Card was just shared - CONTENT
    'FRONTEND_NOTIF_POINT_GIFTCARD_SHARED_FB_ZERO' => 'Thank you for sharing :{offer_title} on Facebook',

    'NOTIF_REQ_POINT_GIFTCARD_SHARED_TWITTER_TITLE' => 'Gift Card Shared in Twitter', //A Gift Card was just shared - TITLE
    'FRONTEND_NOTIF_POINT_GIFTCARD_SHARED_TW_SINGULAR' => 'You earned 1 Point for sharing :{offer_title} on Twitter', //A Gift Card was just shared - CONTENT
    'FRONTEND_NOTIF_POINT_GIFTCARD_SHARED_TW_PLURAL' => 'You earned :{units_exchange} Points for sharing :{offer_title} on Twitter', //A Gift Card was just shared - CONTENT
    'FRONTEND_NOTIF_POINT_GIFTCARD_SHARED_TW_ZERO' => 'Thank you for sharing :{offer_title} on Twitter',

    'NOTIF_REQ_TRANSFER_POINTS_RECEIVER_TITLE' => 'You received a Gift Card', //Receiver notif: received points that were transferred to him - TITLE
    'FRONTEND_NOTIF_TRANSFER_POINTS_RECEIVER' => ':{sender_username} gifted you :{currency}:{units_exchange} from :{business_name}', //Receiver notif: received points that were transferred to him - CONTENT
    'NOTIF_REQ_GIFTCARD_TRANSFER_POINTS_RECEIVER_TITLE' => 'You received a gift card',
    'FRONTEND_NOTIF_GIFTCARD_TRANSFER_POINTS_RECEIVER' => ':{sender_username} gifted :{currency}:{amount} from :{business_name}',
    'ACTIVITY_TRANSFER_POINTS_BOTH' => 'Gifted points from :{business} to :{receiver_username}',

    'NOTIF_REQ_POINT_PURCHASE_REWARD_TITLE' => 'Earned Points',
    'FRONTEND_NOTIF_POINT_PURCHASE_REWARD' => 'You Purchased and earned :{units_exchange} Points at :{business_name}',
    'NOTIF_REQ_PUNCH_PURCHASE_REWARD_TITLE' => 'Earned Punches',
    'FRONTEND_NOTIF_PUNCH_PURCHASE_REWARD' => 'You Purchased and earned :{units_exchange} Punches :{business_name}',
    'NOTIF_REQ_POINT_PURCHASE_REDEMPTION_TITLE' => 'You Redeemed Points',
    'FRONTEND_NOTIF_POINT_PURCHASE_REDEMPTION' => 'You have Redeemed :{units_exchange} Points at :{business_name}',
    'NOTIF_REQ_PUNCH_PURCHASE_REDEMPTION_TITLE' => 'You Redeemed Punches',
    'FRONTEND_NOTIF_PUNCH_PURCHASE_REDEMPTION' => 'You have Redeemed :{units_exchange} Punches at :{business_name}',
    'NOTIF_TEXT_NEW_PRODUCT' => ':{offer_title} from :{business_name}',
    'NOTIF_REQ_NEW_PRODUCT_TITLE' => 'New Product',
    'NOTIF_REQ_ACCEPT_GIFTCARD_TITLE' => 'Gift Card Accepted!',

    'NOTIF_REQ_REFERRAL_REFEREE_REWARD' => 'Referral Bonus',
    'NOTIF_REQ_REFERRAL_REFERER_REWARD' => 'Referral Bonus',
    'NOTIF_TEXT_REFERRER_EARNED' => ':{business_name} offered you :{units_exchange} for referring :{sender}',
    'NOTIF_TEXT_REFERREE_EARNED' => ':{business_name} offered you :{units_exchange} to thank you for your visit',
    'NOTIF_TEXT_REFERER_EARNED_OFFER' => ':{business_name} offered you :{offer_title} for referring :{sender}',
    'NOTIF_TEXT_REFEREE_EARNED_OFFER' => ':{business_name} offered you :{offer_title} to thank you for your visit',
    'NOTIF_TEXT_COUPON_CLAIM' => 'You have redeemed :{offer} from :{business_name} :{date}',
    'NOTIF_REQ_COUPON_CLAIM' => 'Coupon Redeemed',
    'NOTIF_REQ_GIFTCARD_AMOUNT_PURCHASE_TITLE' => 'Gift Card Purchase was successfully completed',
    'NOTIF_REQ_GIFTCARD_AMOUNT_TRANSFER_RECEIVER_TITLE' => 'You received a gift card',
    'FRONTEND_PAYPAL_PAYMENT_SUCCESS4_AMOUNT' => 'Your new :{business_name} balance is now :{currency}:{amount}',
    'FRONTEND_NOTIF_GIFTCARD_PAYMENT' => 'You have Paid :{currency}:{amount} at :{business_name}',
    'NOTIF_REQ_GIFTCARD_AMOUNT_PAYMENT_TITLE' => 'You Paid with Gift Card',
    'NOTIF_REQ_WELCOME_POINTS_TITLE' => 'Signup Bonus',
    'NOTIF_REQ_WELCOME_PUNCH_TITLE' => 'Signup Bonus',
    //** END - Notifications - THE VAR NAME CAN NOT CHANGE (THESE ARE USED IN SOME TABLES)

    'FRONTEND_SEND_GIFT_CARD_TO_FRIEND' => 'You sent a gift card of value :{value} to :{receiver}. Recall?',
    'FRONTEND_RECEIVE_GIFT_CARD_FROM_FRIEND' => 'You received a gift card of value :{value} from :{sender}. Accept?',
    'FRONTEND_GIFT_CARD_CLAIM_BACK_SUCCESS' => 'Gift Card Recalled!',
    'FRONTEND_GIFT_CARD_CLAIM_GIFT_SUCCESS' => 'Gift Card Accepted!',
    'FRONTEND_GIFTCARD_MESSAGE_ACCEPT_BTN' => 'Accept',
    'FRONTEND_GIFTCARD_MESSAGE_RECALL_BTN' => 'Recall',
    'FRONTEND_GIFTCARD_SENDER_EMPTY' => 'Please enter sender name',
    'FRONTEND_GIFTCARD_RECIPIENT_EMPTY' => 'Please enter recipient name',
    'FRONTEND_GIFTCARD_RECIPIENT_ACCEPT_GIFT_SENDER_MESSAGE' => ':{receiver_username} has accepted your :{businessName} Gift Card of :{amount}:{currency}',

    'FRONTEND_TRANSFER_NOT_ENOUGH_BALANCE' => 'You do not have enough :{pts_punches} from :{business_name}',
    'FRONTEND_TRANSFER_PTS_SMS' => 'A friend has gifted you a :{currency}:{amount} value of :{business_name} Points',
    'FRONTEND_TRANSFER_PTS_SMS_NEW_USER' => 'A friend has gifted you a :{currency}:{amount} value of :{business_name} Points; Tap here to claim :{link}',
    'FRONTEND_TRANSFER_PTS_EMAIL_SUBJECT'=>  ':{sender} offered you a Gift Card of :{currency}:{amount} from «:{business_name}»',
    'FRONTEND_TRANSFER_NOT_ALLOW_TO_YOURSELF' => 'Sounds thoughtful, but you cannot gift yourself',
    'NOTIF_REQ_POINT_OFFER_SHARED_FACEBOOK' => 'Offer shared on Facebook',
    'NOTIF_REQ_BASIC_OFFER_SHARED_FACEBOOK' => 'Offer shared on Facebook',
    'NOTIF_REQ_POINT_REWARD_SHARED_FACEBOOK' => 'Reward shared on Facebook',
    'PT'=>'pt',
    'PTS' => 'pts',
    'POINT' => 'Point',
    'POINTS' => 'Points',
    'PUNCH'=>'Punch',
    'PUNCHES' => 'punches',

    'BACKEND_GENERIC_DAY' => 'jour',
    'BACKEND_GENERIC_DAYS' => 'jours',
    'BACKEND_REFERRAL_REFEREE_SMS_EARN_EXPIRE' => 'Salut, tu dois visiter :{business_name}. Certain tu vas aimer ca. Tu gagneras :{points}; Valide pour :{valid} :{referrer}',
    'BACKEND_REFERRAL_REFEREE_SMS_EARN' => 'Salut, tu dois visiter :{business_name}. Certain tu vas aimer ca. En plus tu gagneras :{points}; :{referrer}',
    'BACKEND_REFERRAL_REFEREE_SMS_ZERO' => ' Salut, tu dois visiter :{business_name}. Certain tu vas aimer ca; :{referrer}',

    'FRONTEND_USER_REFERER_EARN_SUBJECT' => 'Vous avez gagné :{pts} d\'un ami référé - :{business_name}',
    'FRONTEND_USER_REFERER_EARN_SMS' => 'Votre solde @:{business_name} est: :{balance}. Vous avez gagné :{pts} d\'un ami référé.',

    'FRONTEND_GENERIC_MAIL_DOWNLOADKNGAPP'=> 'Téléchargez l\'application Kangaroo Rewards sur votre téléphone mobile, c\'est Gratuit!',
    'FRONTEND_GENERIC_MAIL_SIGNATURE'=> 'Merci,<br>L\'équipe Kangaroo!',
    'FRONTEND_GENERIC_MAIL_ASK_FOR_SUPPORT_1'=> 'Si vous rencontrez des problèmes, s\'il vous plaît laissez nous le plaisir de vous assister ',
    'FRONTEND_GENERIC_MAIL_ASK_FOR_SUPPORT_2'=> '',
    'FRONTEND_THANKS'=>'Merci',

    'TRX_SUBTYPE_INITIAL_POINTS_IMPORT' => 'Initial points import',
    'TRX_SUBTYPE_INITIAL_PUNCHES_IMPORT' => 'Initial punches import',
    'TRX_SUBTYPE_CONVERTIBLE_PRODUCT_REDEMPTION_POINTS' => 'Points Redemption converted to Gift Card',
    'TRX_SUBTYPE_CONVERTIBLE_GIFTCARD_AMOUNT_PURCHASED' => 'Converted to Gift Card Amount',
    'TRX_SUBTYPE_SPIN_DRAW_FREE_POINTS' => 'Spin Draw Free Points',
    'TRX_SUBTYPE_SPIN_DRAW_FREE_PRODUCT' => 'Spin Draw Free Product',
    'TRX_SUBTYPE_SPIN_DRAW_PORCENTAGE_OFF' => 'Spin Draw Percentage Off',
    'TRX_SUBTYPE_SPIN_DRAW_OFFER' => 'Spin Draw Offer',
    'TRX_SUBTYPE_BONUS_POINTS_ASSIGNED' => 'Bonus points assigned',
    'TRX_SUBTYPE_SURVEY_COMPLETED' => 'Survey Completed',

    // refund the redeemed points
    'NOTIF_REQ_REFUND_REDEEMED_POINTS_TITLE' => 'Refund Points',
    'FRONTEND_ACTIVITY_REDEEM_POINT_REFUND' => 'Refund :{units_exchange} Points from expired coupon',

    //refund
    'NOTIF_REQ_POINT_PURCHASE_REFUND_TITLE' => 'Refund Points',
    'FRONTEND_ACTIVITY_POINT_REFUND' => 'Refund :{units_exchange} Points',

    'FRONTEND_NOTIF_TITLE_YOU_EARNED' => 'You earned :{units_exchange} Points',
    'FRONTEND_ACTIVITY_POINT_REDEEM' => 'You redeemed :{units_exchange} Points',
    'NOTIF_REQ_PRODUCT_REVIEW_TITLE' => 'Product review bonus',
    'FRONTEND_NOTIF_PRODUCT_REVIEW_REWARD_POINTS' => 'You earned :{units_exchange} Points for review a product on the website.',

    'FRONTEND_BUS_CAT_RESTAURANT' => 'Cafés & Restaurants',
    'FRONTEND_BUS_CAT_SERVICES' => 'Services',
    'FRONTEND_BUS_CAT_BAR' => 'Bars',
    'FRONTEND_BUS_CAT_CONVEN_STORE' => 'Épiceries',
    'FRONTEND_BUS_CAT_DEPT_STORE' => 'Boutiques',
    'FRONTEND_BUS_CAT_LIFESTYLE' => 'Beauté et mode de vie',
    'FRONTEND_BUS_CAT_ELECTRO' => 'Électroniques',
    'FRONTEND_BUS_CAT_ENTERT' => 'Divertissement',
    'FRONTEND_BUS_CAT_OTHER' => 'Autre',
    'FRONTEND_BUS_CAT_FINANCE' => 'Finance',
    'FRONTEND_BUS_CAT_E_WALLET' => 'Portefeuille électronique or E-Wallet',
    'FRONTEND_BUS_CAT_E_COM' => 'E-Com',
    'FRONTEND_BUS_CAT_TRANSPORT' => 'Transport',
    'FRONTEND_BUS_CAT_TRAVEL' => 'Voyage',
    'FRONTEND_BUS_CAT_SHOPPING' => 'Achats',
    'FRONTEND_BUS_CAT_CHARITY' => 'Caritative',

    'FRONTEND_BUS_CAT_PRACTITIONER' => 'Médecin généraliste',
    'FRONTEND_BUS_CAT_HEALTH' => 'Santé',
    'FRONTEND_BUS_CAT_HOSPITALS' => 'Hôpitaux, centres médicaux, laboratoires médicaux',
    'FRONTEND_BUS_CAT_MEDICAL' => 'Équipement médical',
    'FRONTEND_BUS_CAT_OBSTETRICIAN' => 'Gynécologue obstétricien',
    'FRONTEND_BUS_CAT_OPTICAL' => 'Optique',
    'FRONTEND_BUS_CAT_PHARMA' => 'Pharmacies',
    'FRONTEND_BUS_CAT_PHYSIOTHERAPIST' => 'Physiothérapeute',
    'FRONTEND_BUS_CAT_SURGEON' => 'Chirurgien et spécialistes',
    'FRONTEND_BUS_CAT_AUTOMOTIVE' => 'Automobile, équipement et transport',
    'FRONTEND_BUS_CAT_BROKERS' => 'Coutier en douane',
    'FRONTEND_BUS_CAT_EDUCATIONAL' => 'Éducatif',
    'FRONTEND_BUS_CAT_FASHION' => 'Mode et commerce de détail',
    'FRONTEND_BUS_CAT_FINANCIAL' => 'Services Financiers',
    'FRONTEND_BUS_CAT_MARKETING' => 'Publicité Marketing',
    'FRONTEND_BUS_CAT_CORPORATE' => 'Services Corporatifs',

    'BACKEND_OFFERS' => 'des offres',
    'BACKEND_OFFER' => 'une offre',
    'BACKEND_AND' => 'et ',

    'FRONTEND_USERSIGNUP_WELCOME' => 'Bienvenue au programme de fidélisation :business_name. Votre code NIP est :pin_code.',
    'FRONTEND_USERSIGNUP_WELCOME_BROADNET' => 'Bienvenue au programme de fidélisation :business_name: :pin_code.',
    'FRONTEND_USERSIGNUP_WELCOME_WITHOUT_PIN' => 'Bienvenue au programme de fidélisation :business_name.',

    'FRONTEND_USER_PIN' => 'Votre code NIP est :{pin_code}',
    'FRONTEND_USER_PIN_BROADNET' => 'Utilisez :{pin_code} pour vous connecter',

    'REPORTS_FIELD_ID' => 'ID',
    'REPORTS_FIELD_FIRST_NAME' => 'Prenom',
    'REPORTS_FIELD_LAST_NAME' => 'Nom de famille',
    'REPORTS_FIELD_EMAIL' => 'e-mail',
    'REPORTS_FIELD_PHONE' => 'Numéro de téléphone',
    
    // Loyalty Generated Revenue
    'REPORTS_FIELD_DATE' => 'Date',
    'REPORTS_FIELD_TOTAL_AMOUNT' => 'Total Amount',
    'REPORTS_FIELD_CUSTOMERS_COUNT' => 'Customers Count',
    'REPORTS_FIELD_TRANSACTIONS_COUNT' => 'Transactions Count',
    'REPORTS_FIELD_CAMPAIGNS_AMOUNT' => 'Campaigns Amount',

    // Average Customer Spend
    'REPORTS_FIELD_AVG_AMOUNT' => 'Average Amount',
    'REPORTS_FIELD_VISITS' => 'Visites',
    'REPORTS_FIELD_POINTS_EARNED' => 'Points gagnés',
    'REPORTS_FIELD_POINTS_REDEEMED' => 'Points échangés',
    

    // Customers Unsubscribed
    'REPORTS_FIELD_EMAIL_ACTION' => 'Email Action',
    'REPORTS_FIELD_SMS_ACTION' => 'SMS Action',
    'REPORTS_FIELD_PUSH_ACTION' => 'Push Action',
    'REPORTS_FIELD_UPDATED_AT' => 'Updated At',
    'REPORTS_FIELD_CAMPAIGN_NAME' => 'Campaign Name',
    
    // Campaign Engagement
    'REPORTS_FIELD_REDEEMED_AT' => 'Redeemed At',
    'REPORTS_FIELD_TARGETED_AT' => 'Targeted At',
    'REPORTS_FIELD_TRX_NUM' => 'Transactions #',
    'REPORTS_FIELD_OFFER_REDEMPTION_TYPE' => 'Type',
    'REPORTS_FIELD_OFFER_REDEMPTION_NAME' => 'Reward Name',

    'BACKEND_DRAW_RESULTS_ADMIN_EMAIL_SUBJECT' => 'Résultats du tirage - VOUS AVEZ GAGNÉ :{prize}!',
    'BACKEND_DRAW_WINNER_SMS_CONTENT' => ':{business_name} - Résultats du tirage au sort – VOUS AVEZ GAGNÉ :{prize}!  Venez en magasin pour réclamer votre prix avant le :{expiry_date}!',
    'FRONTEND_GENERIC_MAIL_SIGNATURE2'=> 'Cordialement,<br>L\'équipe Kangaroo',
    'CHARGE_FAILED_EMAIL_SUBJECT' => 'Attention requise - paiement non réussi',
    'AMAZON_GIFT_CARD_REDEMPTION_EMAIL_SUBJECT' => 'Utiliser votre code de coupon Amazon!',
    'AMAZON_GIFT_CARD_REDEMPTION_SMS' => 'Nous sommes ravis de vous informer que vous avez réussi à utiliser un passionnant code de coupon Amazon :code. :businessName.',
    'ADMIN_INSUFFICIENT_BUSINESS_WALLET_AMOUNT_EMAIL_SUBJECT' => 'Attention requise - Les bons ne sont pas disponibles',
    'ADMIN_FRAUDULENT_ACTIVITY_EMAIL_SUBJECT' => 'Avis important : Blocage temporaire des bons de remboursement',
    
    /**
     * 
     * CUSTOMIZABLE MESSAGES DEFAULT VARIABLES
     * 
     */
    /**
    * WELCOME SMS
    */
    'CUSTOMIZED_MESSAGE_WELCOME_SMS_PIN_CODE' => '. Votre code PIN est {{$pinCode}}.',
    'CUSTOMIZED_MESSAGE_WELCOME_SMS_PIN_CODE_BROADNET' => ': {{$pinCode}}.',
    'CUSTOMIZED_MESSAGE_WELCOME_SMS_WITHOUT_PIN_CODE' => '.',

    'ACCOUNT_LOCKED_EMAIL_SUBJECT' => 'Votre compte a été temporairement verrouillé',
];
