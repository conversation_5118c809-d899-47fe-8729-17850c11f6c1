<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ResqueQueue extends Model
{
    /**
     * The name of the "created at" column.
     *
     * @var string
     */
    const CREATED_AT = 'created_at';

    /**
     * The name of the "updated at" column.
     *
     * @var string
     */
    const UPDATED_AT = null;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'tenant';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'resque_queue';

    /**
     * The primary key associated with the model.
     *
     * @var int
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [
    ];
    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
    ];

    protected $casts = [
        'args' => 'array',
    ];

    public static function resqueTaskExists($hash)
    {
        return ResqueQueue::where('hash', $hash)
            ->where('active', 1)
            ->orderBy('id', 'DESC')
            ->first();
    }

    public static function createResqueTask($args): ResqueQueue
    {
        $queue = new ResqueQueue();
        $queue->hash = $args['hash'];
        $queue->task = $args['task'];
        $queue->name = 'Worker_KangarooApiTask';
        $queue->queue = 'kangaroo_api_task';
        $queue->args = $args['args'] ?? null;
        $queue->job_id = null;
        $queue->save();

        return $queue;
    }

    public static function taskSuccess($id)
    {
        $model = ResqueQueue::find($id);

        if (!$model) {
            return;
        }

        $model->finished_at = date('Y-m-d H:i:s');
        $model->active = 0;
        $model->save();

        info(__METHOD__, ['resque_queue_id' => $id, 'args' => $model->args, 'msg' => 'Job finished successfully']);
    }

    public static function taskFail($id, $exception = null)
    {
        $model = ResqueQueue::find($id);

        if (!$model) {
            return;
        }
        $model->active = 0;
        $model->error_log = $exception;
        $model->save();
    }
}
