<?php

namespace App\Models\V1;

use App\Http\Resources\ProductResource;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Helpers\V1\Utils;

class Product extends Model
{
    protected $table = 'products';
    protected $primaryKey = 'product_pk';
    protected $connection = 'tenant';

    /**
     * The name of the "created at" column.
     *
     * @var string
     */
    const CREATED_AT = 'utc_created';

    /**
     * The name of the "updated at" column.
     *
     * @var string
     */
    const UPDATED_AT = 'utc_updated';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'promote_flag',
        'product_name',
        'product_sku',
        'product_actual_price',
        'product_real_price',
        'pos_item_id',
        'pos_product_id',
        'pos_account_id',
        'type',
        'timezone_mysql_fk',
        'pos_system_fk',
        'enabled'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [];

    public function productBranch()
    {
        return $this->hasMany(ProductBranch::class, 'product_fk', 'product_pk');
    }

    public static function forBusinessPaginated($businessId)
    {
        $branches = BusinessBranch::forBusiness($businessId);
        $branchIds = $branches->map(function ($branch) {
            return $branch->business_branch_pk;
        });

        $query = Product::join('product_branches as pb', 'products.product_pk', '=', 'pb.product_fk')
            ->whereIn('business_branch_fk', $branchIds)
            ->where('products.enabled', '=', 1)
            ->where('products.promote_flag', '=', 1)
            ->groupBy('products.product_pk')
            ->with(['productBranch' => function ($query) {
                $query->where('enabled', 1)
                    ->with('branch');
            }]);
        // ->with(['productReward' => function ($query) {
        //     $query->where('enabled', 1);
        // }]);

        return $query;
    }

    public static function forBusiness($businessId, $allProducts = false, $sortConditions = [])
    {
        $branches = BusinessBranch::forBusiness($businessId);
        $branchIds = $branches->map(function ($branch) {
            return $branch->business_branch_pk;
        });

        $query = Product::select("products.*")
            ->join('product_branches as pb', 'products.product_pk', '=', 'pb.product_fk')
            ->whereIn('pb.business_branch_fk', $branchIds)
            ->where('products.enabled', '=', 1)
            ->groupBy('products.product_pk')
            ->with(['productBranch' => function ($query) use ($branchIds) {
                $query->where('enabled', 1)
                    ->whereIn('business_branch_fk', $branchIds)
                    ->with('branch');
            }]);
        // ->with(['productReward' => function ($query) {
        //     $query->where('enabled', 1);
        // }]);

        if (!$allProducts) {
            $query->where('products.promote_flag', '=', 1);
        }

        if (count($sortConditions) > 0) {
            foreach ($sortConditions as $key => $value) {
                switch ($key) {
                    case 'name':
                        $query->orderBy('products.product_name', $value);
                        break;
                    case 'price':
                        $query->orderBy('products.product_real_price', $value);
                        break;
                    case 'published':
                        $query->orderBy('products.promote_flag', $value);
                        break;
                    default:
                        $query->orderBy('products.product_pk', $value);
                        break;
                }
            }
        }

        return $query;
    }

    /**
     * read the default image from the JSON object of the images stored in the DB
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @param string $size large, medium, thumb
     * @param JSON string $jsonObject
     * @return string default image with corresponding size
     */
    public static function getDefaultImageFromJson($size, $jsonObject)
    {
        $default = [
            'medium' => ['1' => '/images/kangaroo_image_default.png'],
            'thumb' => ['1' => '/images/kangaroo_image_default.png'],
            'large' => ['1' => '/images/kangaroo_image_default.png'],
            'default' => '1',
        ];

        $images = json_decode($jsonObject, true);
        if (isset($images[$size]) && count($images[$size]) > 0 && $images[$size][$images['default']] != '') {
            return '/' . ltrim($images[$size][$images['default']], '/');
        } elseif (isset($default[$size]) && count($default[$size]) > 0) {
            return $default[$size][$default['default']];
        } else {
            return '';
        }
    }

    /**
     * get a json object and the language id and returns the right text
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @param string $jsonObject
     * @param int $langId
     * @return text in right language
     */
    public static function getTextByLanguage($jsonObject, $langId)
    {
        if ($jsonObject == null || $jsonObject == '') {
            return '';
        }

        $textArray = json_decode($jsonObject, true);
        if ($textArray === null) {
            //TODO log error for json decode with slashes
            return '';
        }
        $defaultLanguage = 1;
        if (array_key_exists($langId, $textArray)) {
            $text = $textArray[$langId];
        } elseif ((array_key_exists($defaultLanguage, $textArray))) {
            $text = $textArray[$defaultLanguage];
        } elseif (count($textArray) > 0) {
            $text = array_values($textArray)[0];
        } else {
            $text = '';
        }

        return stripslashes($text);
    }

    public static function transformCollection($products, $langId)
    {
        $_products = [];
        foreach ($products as $product) {
            $_products[] = self::transformItem($product, $langId);
        }

        return $_products;
    }

    /**
     * Transform Product Item
     *
     * @version 1.0
     * @since 1.0
     * <AUTHOR>
     * @access public
     * @return array
     */
    public static function transformItem($product, $langId)
    {
        $res = (new ProductResource($product))->response()->getData()->data;

        return $res;
        //        $images = [
        //            'default' => true,
        //            'path' => config('app.server_url') . Product::getDefaultImageFromJson('large', $product->product_image),
        //            'large' => config('app.server_url') . Product::getDefaultImageFromJson('large', $product->product_image),
        //            'medium' => config('app.server_url') . Product::getDefaultImageFromJson('medium', $product->product_image),
        //            'thumbnail' => config('app.server_url') . Product::getDefaultImageFromJson('thumb', $product->product_image),
        //        ];


        //        dd($res);
        //        $res->images = [
        //            $images
        //        ];

        //        return $res;

        //        return [
        //            'id' => $product->product_pk,
        //            'title' => Product::getTextByLanguage($product->product_name, $langId),
        //            'description' => Product::getTextByLanguage($product->product_description, $langId),
        //            'actual_price' => (float) $product->product_actual_price,
        //            'real_price' => (float) $product->product_real_price,
        //            'images' => [
        //                $images
        //            ],
        //            'terms_conditions' => Product::getTextByLanguage($product->product_term_condition, $langId),
        //            'link' => Product::getTextByLanguage($product->product_link, $langId),
        //        ];
    }
}
