<?php

use PHPUnit\Framework\TestCase;

/**
 * cd protected/tests
 * ../vendor/bin/phpunit unit/UtilsTest.php -v --debug
 * ../vendor/bin/phpunit --filter testGetCookieDomain unit/UtilsTest.php
 */
class UtilsTest extends TestCase
{

    /**
     * ../vendor/bin/phpunit --filter testParseUserAgent unit/UtilsTest.php
     * @dataProvider userAgentProvider
     */
    public function testParseUserAgent(string $userAgent, array $expected)
    {
        $result = Utils::parseUserAgent($userAgent);
        $this->assertEquals($expected, $result);
    }

    /**
     * @dataProvider cookieDomainProvider
     */
    public function testGetCookieDomain(string $serverUrl, string $envParam, string $expected)
    {
        // Mock Yii::app()->params
        $params = [
            'serverUrl' => $serverUrl,
            'envParam' => $envParam
        ];
        
        Yii::app()->params = $params;
        
        $result = Utils::getCookieDomain();
        $this->assertEquals($expected, $result);
    }

    public function cookieDomainProvider(): array
    {
        return [
            'local development' => [
                'serverUrl' => 'http://localhost:8080',
                'envParam' => 'LOCAL',
                'expected' => ''
            ],
            'local 127.0.0.1' => [
                'serverUrl' => 'http://127.0.0.1:8084',
                'envParam' => 'LOCAL',
                'expected' => ''
            ],
            'local site' => [
                'serverUrl' => 'http://site:8084',
                'envParam' => 'LOCAL',
                'expected' => ''
            ],
            'local domain.local' => [
                'serverUrl' => 'http://kangaroorewards.local',
                'envParam' => 'LOCAL',
                'expected' => ''
            ],
            'simple domain' => [
                'serverUrl' => 'https://example.com',
                'envParam' => 'PROD',
                'expected' => 'example.com'
            ],
            'www domain' => [
                'serverUrl' => 'https://www.example.com',
                'envParam' => 'PROD',
                'expected' => '.example.com'
            ],
            'subdomain' => [
                'serverUrl' => 'https://app.example.com',
                'envParam' => 'PROD',
                'expected' => '.example.com'
            ],
            'multiple subdomains' => [
                'serverUrl' => 'https://test.app.example.com',
                'envParam' => 'PROD',
                'expected' => '.example.com'
            ],
            'localhost non-local env' => [
                'serverUrl' => 'http://localhost',
                'envParam' => 'PROD',
                'expected' => 'localhost'
            ],
        ];
    }

    /**
     * @dataProvider accessTokensProvider
     */
    public function testAccessToken(string $token, string $expected)
    {
        $token = strip_tags($token);
        $actualToken = preg_replace('/[^a-zA-Z0-9.]/', '', $token);

        $this->assertSame($expected, $actualToken);
    }

    /**
     * @dataProvider channelNamesProvider
     */
    public function testChannelName(string $channelName, string $expected)
    {
        $actualChannelName = htmlspecialchars($channelName, ENT_COMPAT, 'UTF-8');

        $this->assertSame($expected, $actualChannelName);
    }

    /**
     * @dataProvider socketIdsProvider
     */
    public function testPusherSocketId(string $id, string $expected)
    {
        $actualId = htmlspecialchars($id, ENT_COMPAT, 'UTF-8');

        $this->assertSame($expected, $actualId);
    }

    private function socketIdsProvider(): array
    {
        return [
            ['420542.1448950', '420542.1448950'],
            ['123456.789012', '123456.789012'],
            ['987654.321098', '987654.321098'],
            ['111111.111111', '111111.111111'],
            ['0.000000', '0.000000'],
            ['12345', '12345'],
            ['123.45.67', '123.45.67'],
            ['& < > "', '&amp; &lt; &gt; &quot;'],
            ['~!@#$%^&*()_+{}|:<>?', '~!@#$%^&amp;*()_+{}|:&lt;&gt;?'],
            ['<script>alert</script>', '&lt;script&gt;alert&lt;/script&gt;'],
        ];
    }

    private function channelNamesProvider(): array
    {
        return [
            [
                'private-BB-11eac51fe2212d089c90e2865c66d9cc-Emp-11eac51fe244e3b79c90e2865c66d9cc',
                'private-BB-11eac51fe2212d089c90e2865c66d9cc-Emp-11eac51fe244e3b79c90e2865c66d9cc'
            ],
            [
                '<script>private-BB-11eac51fe2212d089-Emp-11eac51fe244e3b79</script>',
                '&lt;script&gt;private-BB-11eac51fe2212d089-Emp-11eac51fe244e3b79&lt;/script&gt;'
            ],
        ];
    }

    private function accessTokensProvider(): array
    {
        return [
            ['**********.4747kK6DryMt32FLeHgZfwSsjh4W9Cx8IUbJ5uNGVoOPmzlRA', '**********.4747kK6DryMt32FLeHgZfwSsjh4W9Cx8IUbJ5uNGVoOPmzlRA'],
            ['**********4747kK6DryMt32FLeHgZfwSsjh4W9Cx8IUbJ5uNGVoOPmzlRA', '**********4747kK6DryMt32FLeHgZfwSsjh4W9Cx8IUbJ5uNGVoOPmzlRA'],
            ['**********4747', '**********4747'],
            ['sjh4W9Cx8IUbJ5uNGVoOPmz', 'sjh4W9Cx8IUbJ5uNGVoOPmz'],
            [' token with spaces ', 'tokenwithspaces'],
            ['token.with.multiple.dots', 'token.with.multiple.dots'],
            ['token!with!special!chars', 'tokenwithspecialchars'],
            ['token@<EMAIL>', 'tokenwithemail.chars'],
            ['token#with#special#chars', 'tokenwithspecialchars'],
            ['', ''], // empty string
            ['   ', ''], // string with only spaces
            ['abc<>def', 'abcdef'], // string with HTML tags
            ['abc&amp;def', 'abcampdef'], // string with HTML entities
            ['abc&def', 'abcdef'], // string with HTML entities
            ['<script>alert</script>', 'alert'], // string with HTML entities
        ];
    }

    private function userAgentProvider(): array
    {
        return [
            [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.86 Safari/537.36',
                ['device' => 'Windows 10', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:40.0) Gecko/20100101 Firefox/40.1',
                ['device' => 'Windows 7', 'app' => 'Firefox']
            ],
            [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.1.15',
                ['device' => 'Mac OS X', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.111 Mobile Safari/537.36',
                ['device' => 'Android', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1',
                ['device' => 'iPhone', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Windows NT 10.0; WOW64; rv:115.0) Gecko/20100101 Firefox/115.0',
                ['device' => 'Windows 10', 'app' => 'Firefox']
            ],
            [
                'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:114.0) Gecko/20100101 Firefox/114.0',
                ['device' => 'Linux', 'app' => 'Firefox']
            ],
            [
                'Mozilla/5.0 (iPad; CPU OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Mobile/15E148 Safari/604.1',
                ['device' => 'iPad', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Linux; Android 12; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.6099.234 Mobile Safari/537.36',
                ['device' => 'Android', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 13_2_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.184 Safari/537.36',
                ['device' => 'Mac OS X', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36',
                ['device' => 'Windows 7', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (Linux; Android 10; SM-A107F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.74 Mobile Safari/537.36',
                ['device' => 'Android', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15',
                ['device' => 'Mac OS X', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (iPhone; CPU iPhone OS 16_1_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1',
                ['device' => 'iPhone', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/123.0.2420.81 Chrome/123.0.6312.86 Safari/537.36',
                ['device' => 'Windows 10', 'app' => 'Edge']
            ],
            [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.5672.127 Safari/537.36',
                ['device' => 'Mac OS X', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (Linux; Android 11; SM-A515F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.6099.216 Mobile Safari/537.36',
                ['device' => 'Android', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (iPad; CPU OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.5 Mobile/15E148 Safari/604.1',
                ['device' => 'iPad', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Windows NT 6.1; WOW64; Trident/7.0; rv:11.0) like Gecko',
                ['device' => 'Windows 7', 'app' => 'Internet Explorer']
            ],
            [
                'Mozilla/5.0 (X11; Fedora; Linux x86_64; rv:112.0) Gecko/20100101 Firefox/112.0',
                ['device' => 'Linux', 'app' => 'Firefox']
            ],
            [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 11_6_4) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.3 Safari/605.1.15',
                ['device' => 'Mac OS X', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Linux; Android 14; Pixel 8) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.6367.60 Mobile Safari/537.36',
                ['device' => 'Android', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (iPhone; CPU iPhone OS 15_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6 Mobile/15E148 Safari/604.1',
                ['device' => 'iPhone', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Linux; Android 9; Redmi Note 8 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.78 Mobile Safari/537.36',
                ['device' => 'Android', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.5414.87 Safari/537.36',
                ['device' => 'Linux', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Safari/605.1.15',
                ['device' => 'Mac OS X', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Linux; Android 10; SM-G960F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.6099.216 Mobile Safari/537.36',
                ['device' => 'Android', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (iPhone; CPU iPhone OS 14_4_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
                ['device' => 'iPhone', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
                ['device' => 'Windows 8.1', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (Linux; Android 11; M2007J20CG) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.149 Mobile Safari/537.36',
                ['device' => 'Android', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (iPad; CPU OS 13_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Mobile/15E148 Safari/604.1',
                ['device' => 'iPad', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Windows NT 10.0; ARM; Surface Pro X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.5615.49 Safari/537.36',
                ['device' => 'Windows 10', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (X11; Linux i686; rv:52.0) Gecko/20100101 Firefox/52.0',
                ['device' => 'Linux', 'app' => 'Firefox']
            ],
            [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/603.3.8 (KHTML, like Gecko) Version/10.1.2 Safari/603.3.8',
                ['device' => 'Mac OS X', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Linux; Android 13; CPH2411) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.94 Mobile Safari/537.36',
                ['device' => 'Android', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (iPhone; CPU iPhone OS 12_5_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1',
                ['device' => 'iPhone', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.82 Safari/537.36',
                ['device' => 'Windows 8', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-J415FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Mobile Safari/537.36',
                ['device' => 'Android', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.6422.5 Safari/537.36',
                ['device' => 'Linux', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_6) AppleWebKit/601.7.7 (KHTML, like Gecko) Version/9.1.2 Safari/601.7.7',
                ['device' => 'Mac OS X', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Vivaldi/6.4.3160.47 Chrome/120.0.6099.129 Safari/537.36',
                ['device' => 'Windows 10', 'app' => 'Vivaldi']
            ],
            [
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.99 Safari/537.36 Vivaldi/2.9.1705.41',
                ['device' => 'Linux', 'app' => 'Vivaldi']
            ],
            [
                'Mozilla/5.0 (X11; Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.5563.147 Safari/537.36',
                ['device' => 'Linux', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (Linux; Android 10; SM-M205FN) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.199 Mobile Safari/537.36',
                ['device' => 'Android', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Brave/************ Chrome/123.0.6312.86 Safari/537.36',
                ['device' => 'Windows 10', 'app' => 'Brave']
            ],
            [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_5) AppleWebKit/600.8.9 (KHTML, like Gecko) Version/8.0.8 Safari/600.8.9',
                ['device' => 'Mac OS X', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (iPhone; CPU iPhone OS 13_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0 Mobile/15E148 Safari/604.1',
                ['device' => 'iPhone', 'app' => 'Safari']
            ],
            [
                'Mozilla/5.0 (Linux; Android 12; SM-A226B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.162 Mobile Safari/537.36',
                ['device' => 'Android', 'app' => 'Chrome']
            ],
            [
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.5790.170 Safari/537.36',
                ['device' => 'Linux', 'app' => 'Chrome']
            ],
            [
                'SomeUnknownUserAgentString/1.0',
                ['device' => 'Unknown', 'app' => 'Unknown']
            ],
        ];
    }
}
