<?php

namespace KangarooRewards\Common\Services\Transactions;

use Illuminate\Validation\ValidationException;
use KangarooRewards\Common\Models\Business;
use KangarooRewards\Common\Models\BusinessRules;
use KangarooRewards\Common\Models\Customer;
use KangarooRewards\Common\Models\UserEntityPoints;
use KangarooRewards\Common\Repositories\UserEntityPointsRepository;

class TransactionPointBalanceAdjustment extends TransactionBaseService
{
    private bool $overrideBalance;
    private ?UserEntityPoints $userEntityPoints;
    private UserEntityPointsRepository $userEntityPointsRepository;
//    private int $originalPointsBalance;
    private bool $isBalanceIncrease = true;

    public function __construct(int $pointBalance, bool $overrideBalance, Customer $customer, Business $business, BusinessRules $businessRules = null)
    {
        $this->userEntityPointsRepository = new UserEntityPointsRepository();
        $this->pointsBalance = $pointBalance;
        $this->overrideBalance = $overrideBalance;
        $this->setUserEntityPoints($customer->id, $business->business_pk);

        parent::__construct($customer, $business, $businessRules);
    }

    /**
     * @return void
     */
    function create(): void
    {
        $this->adjustPointBalance()
            ->createTransaction()
            ->createActivity();
    }

    protected function initialize(): void
    {
        // Set the trx subtype ID as increase type, and then it will be modified in the method below if needed
        $this->trxType = RESET_BALANCE;
        $this->trxSubType = TRX_SUBTYPE_ADJUST_BALANCE_POINTS_INCREASE;
        $this->activityDescription = 'ACTIVITY_ADJUST_BALANCE';

        // Set the specified points to the adjusted balance
        if (empty($this->userEntityPoints)) {
            return;
        }

        // Get the points need to be adjusted based on increment / override option
        if ($this->overrideBalance) {
            if ($this->userEntityPoints->current_active_points > $this->pointsBalance) {
                // Adjust the decrease points balance
                $this->pointsBalance = $this->userEntityPoints->current_active_points - $this->pointsBalance;

                // Set the trx subtype to decrease balance
                $this->trxSubType = TRX_SUBTYPE_ADJUST_BALANCE_POINTS_DECREASE;

                $this->isBalanceIncrease = false;
            } else {
                // Adjust the increase points balance
                $this->pointsBalance -= $this->userEntityPoints->current_active_points;
            }
        }
    }

    /**
     * @throws ValidationException
     */
    protected function validate(): void
    {
        if ((empty($this->userEntityPoints) || !$this->overrideBalance ) && $this->pointsBalance == 0) {
            throw ValidationException::withMessages([
                'points' => "Points balance is Zero"
            ]);
             // No need to adjust the balance as the points is zero and no record for UEP
        }

        if (!empty($this->userEntityPoints) && $this->overrideBalance && $this->userEntityPoints->current_active_points == $this->pointsBalance) {
             // The customer's points balance matches the specified points balance; no update is needed.
            throw ValidationException::withMessages([
                'points' => "Trying to override same balance points"
            ]);
        }
    }

    /**
     * Update customer points balance by either create or update user entity points
     * @return TransactionPointBalanceAdjustment
     */
    protected function adjustPointBalance(): static
    {
        if (empty($this->userEntityPoints)) {
            // Create user entity point object with the specified points balance
            $this->userEntityPointsRepository->createUserEntityPoints(
                $this->customer->id,
                $this->businessId,
                $this->business->entity_type_fk,
                $this->customerBranch->branch_timezone_fk,
                $this->pointsBalance
            );
        } else {
            if ($this->isBalanceIncrease) {
                // Increase balance after calculating the specified points
                $this->userEntityPointsRepository->increasePointsBalance($this->userEntityPoints, $this->pointsBalance);
            } else {
                // Decrease balance after calculating the specified points
                $this->userEntityPointsRepository->decreasePointsBalance($this->userEntityPoints, $this->pointsBalance);
            }
        }

        return $this;
    }

    /**
     * @param $customerId
     * @param $businessId
     * @return void
     */
    protected function setUserEntityPoints($customerId, $businessId): void
    {
        $this->userEntityPoints = $this->userEntityPointsRepository->findUserEntityPoints($customerId, $businessId);
    }
}