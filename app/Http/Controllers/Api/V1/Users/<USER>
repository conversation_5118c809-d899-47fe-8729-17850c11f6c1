<?php

namespace App\Http\Controllers\Api\V1\Users;

use App\Helpers\DataTransferObject\DataTransferObject;
use App\Helpers\Dto\Referral\RewardReferralData;
use App\Helpers\EcomWidgetHelper;
use App\Helpers\RewardReferral;
use App\Http\Requests\V1\CreateUser;
use App\Http\Requests\V1\UserPinResetRequest;
use App\Http\Requests\V1\UserPinReset;
use App\Http\Requests\V1\VerifyUserCredential;
use App\Http\Requests\V1\TriggerGeofenceRequest;
use App\Http\Resources\UserProfileCollection;
use App\Models\ForgottenDataRequest;
use App\Models\V1\BranchReferralRule;
use App\Models\V1\Referral;
use App\Rules\ConglomerateBusinessMemberValidation;
use App\Rules\ExistsMulti;
use App\Services\UserCrmFieldService;
use App\Traits\FollowerTrait;
use App\Services\UserGeofenceService;
use App\Traits\RelationTrait;
use App\Traits\UserTrait;
use App\User;
use Carbon\Carbon;
use App\Helpers\V1\Utils;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\V1\Business;
use App\Models\V1\Customer;
use App\Models\V1\BusinessBranch;
use App\Models\V1\UserStatusManager;
use App\Models\V1\ActivationToken;
use App\Http\Controllers\Api\V1\ApiController;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Validation\Rule;
use App\Traits\HasFeatureFlag;
use Illuminate\Validation\ValidationException;
use Stripe\Exception\RateLimitException;
use Exception;
use App\Exceptions\CustomValidationException;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

/**
 * Resource Owner
 *
 * @Resource("ResourceOwner", uri="/users/me")
 */
class UserController extends ApiController
{
    use UserTrait;

    /**
     * All users
     *
     * GET /users
     *
     * @Get("/users")
     * @Versions({"v1"})
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        return $this->errorNotImplemented();
    }

    /**
     * Create a new Customer
     * Returns JSON representation of a new created customer
     *
     * POST /users
     *
     * @Post("/users")
     * @Versions({"v1"})
     * @Request({email": "<EMAIL>", "first_name":"Sara","last_name":"Silverman"}, headers={"Authorization": "Bearer eyJ0eXAiOiJKV1Q", "Accept": "application/vnd.kangaroorewards.api.v1+json;"})
     * @Response(200, body={"data":[{"id":********,"first_name":"Sara","last_name":"Silverman","email":"<EMAIL>"},{"id":********,"first":"victoria","last":"Leblanc","email":"<EMAIL>"}],"cursor": {"next": 2,"next_uri": "/customers?page=2"}})
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        \Log::info(__METHOD__, [
            'message' => 'Creating user account starting',
            'input' => $request->all(),
        ]);

        try {
            $business = $this->getBusinessForAppKey();

            $data = $request->isJson() ? $request->json()->all() : $request->all();
            $data['coalition'] = $business->coalition;
            $data['business_pk'] = $business->business_pk;
            $data['ip'] = $data['ip'] ?? Utils::getIpFromRequest($request);
            unset($data['password']);
        } catch (\LogicException $e) {
            \Log::error(__METHOD__ . ' ' . $e, ['input' => $request->all()]);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\App\Exceptions\ResourceNotFoundException $e) {
            \Log::error(__METHOD__ . ' ' . $e, ['input' => $request->all()]);
            return $this->errorNotFound('Resource not found', $e->getMessage());
        } catch (\Exception $e) {
            \Log::error(__METHOD__ . ' ' . $e, ['input' => $request->all()]);
            return $this->errorInternalError();
        }

        /*if ($business->business_pk == 125) {
            return $this->errorForbidden('Users cannot be created at this moment');
        }*/

        // Block Temporary Account creation if limit exceeded
        /*$usersCreatedTodayCount = User::whereDate('utc_created', '>=', date('Y-m-d H:i:s', strtotime('-1 days')))
            ->count();
        if ($business->business_pk === KNG_BUSINESS_ID && $usersCreatedTodayCount > 1000) {
            Log::info(__METHOD__, ['message' => 'BLOCKED_USER_CREATION',
                'usersCreatedTodayCount'=> $usersCreatedTodayCount, 'data' => $data,
            ]);
            $this->setStatusCode(406);
            return $this->respondWithError('Not Acceptable', 'Account creation temporarily suspended. Please try again later');
        }*/

        // dd($business->getConnection());
        // dd($business);

        // Validate the phone number
        if (!empty($data['phone']) && !empty($data['country_code'])) {
            if (!Utils::isValidPhone($data['phone'], $data['country_code'])) {
                return $this->errorWrongArgs('Bad Request', 'Invalid phone number');
            }
            $data['phone'] = Utils::normalizeDigitsOnly($data['phone']);
            $data['phone'] = Utils::getFormattedPhoneNumber($data['phone'], $data['country_code'], 'NATIONAL');
        }

        /**
         * Validate input data before inserting
         * for coalition and regular business
         */
        Customer::validateOnCreate($data)->validate();

        $userCredential = Utils::getUserCredentialFromInput($data);

        $createUserProcessFlagKey = $business->business_pk . '_' . $userCredential;
        if (Cache::get($createUserProcessFlagKey)) {
            \Log::info(__METHOD__, [
                'message' => 'Creating user in progress. Duplicated request',
                'cacheKey' => $createUserProcessFlagKey,
                'businessId' => $business->business_pk,
                'input' => $request->all(),
            ]);
            $this->setStatusCode(409);
            return $this->respondWithError('Conflict', 'Account creation already in progress');
        }

        // create the key in cache to prevent creating the same account from multiple requests
        Cache::put($createUserProcessFlagKey, 1, /* seconds */ 120);

        /**
         * Insert records in DB in a Transaction
         */
        \DB::connection('tenant')->beginTransaction();
        try {
            $user = Customer::createForBusiness($data, $business, true);
            \DB::connection('tenant')->commit();
        } catch (\Exception $e) {
            \DB::connection('tenant')->rollBack();
            \Log::error(__METHOD__ . ' ' . $e, ['data' => $data, 'business_name' => $business->name]);
            Cache::forget($createUserProcessFlagKey);
            return $this->errorInternalError();
        }

        \Log::info(__METHOD__, [
            'message' => 'User account created',
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_phone' => $user->phone,
            'data' => $data,
            'businessId' => $business->business_pk,
            'business_name' => $business->name,
        ]);

        $_user = Customer::transformItem($user);

        Cache::forget($createUserProcessFlagKey);

        return $this->respondWithArray(['data' => $_user]);
    }

    /**
     * Update a user account
     * Returns JSON representation of a new created customer
     *
     * POST /users
     *
     * @Post("/users")
     * @Versions({"v1"})
     * @Request({email": "<EMAIL>", "first_name":"Sara","last_name":"Silverman"}, headers={"Authorization": "Bearer eyJ0eXAiOiJKV1Q", "Accept": "application/vnd.kangaroorewards.api.v1+json;"})
     * @Response(200, body={"data":[{"id":********,"first_name":"Sara","last_name":"Silverman","email":"<EMAIL>"},{"id":********,"first":"victoria","last":"Leblanc","email":"<EMAIL>"}],"cursor": {"next": 2,"next_uri": "/customers?page=2"}})
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $userUid)
    {
        try {
            $user = Auth::user();
            $business = $this->getBusinessForAppKey();

            $data = $request->isJson() ? $request->json()->all() : $request->all();
            $data['coalition'] = $business->coalition;
            $data['business_pk'] = $business->business_pk;
            $data['timezone_mysql_fk'] = $business->branch_timezone_fk;
            $data['ip'] = $request->ip();
            unset($data['password']);
        } catch (\LogicException $e) {
            \Log::error(__METHOD__ . ' ' . $e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \Log::error(__METHOD__ . ' ' . $e);
            return $this->errorInternalError();
        }

        // Validate
        Customer::validateOnUpdate($request, $business, $user->id)->validate();
        // $validator = Customer::validateOnUpdate($request, $business, $user->id);

        // if ($validator->fails()) {
        //     return response()->json($validator->errors()->getMessages(), 422);
        // }

        // Validate the phone number
        if (!empty($data['phone'])) {
            $data['phone'] = Utils::normalizeDigitsOnly($data['phone']);

            if (!Utils::isValidPhone($data['phone'], $data['country_code'])) {
                return $this->errorWrongArgs('Bad Request', 'Invalid phone number');
            }
        }

        // \Log::info(__METHOD__ . ' -> Before update', ['user_id' => $user->id, 'data' => $data, 'business_name' => $business->name]);

        /**
         * Insert records in DB in a Transaction
         */
        \DB::connection('tenant')->beginTransaction();
        try {
            $user = Customer::updateProfile($data, $user, $business);
            $user = $user->getProfile();

            \DB::connection('tenant')->commit();
        } catch (\Exception $e) {
            \DB::connection('tenant')->rollBack();
            \Log::error(
                __METHOD__ . ' ' . $e,
                ['user_id' => $user->id, 'data' => $data, 'business_name' => $business->name]
            );
            return $this->errorInternalError();
        }

        // \Log::info(__METHOD__ . ' -> After update', ['user_id' => $user->id, 'data' => $data, 'business_name' => $business->name]);

        $_user = Customer::transformItem($user);

        $userEmails = UserStatusManager::getEmailsForUser($user->id);
        $_userEmails = UserStatusManager::transformCollection($userEmails);

        $userPhones = UserStatusManager::getPhonesForUser($user->id);
        $_userPhones = UserStatusManager::transformCollection($userPhones);

        return $this->respondWithArray([
            'data' => $_user,
            'included' => [
                'user_emails' => $_userEmails,
                'user_phone_numbers' => $_userPhones
            ],
        ]);
    }

    /**
     * Retrieve a Customer
     * Get a JSON representation of a specific customer
     *
     * GET /users/{id}
     *
     * @Get("/users/{id}{?include=balance}")
     * @Versions({"v1"})
     * @Transaction({
     *      @Response(200, body={"data":{"profile":{"user_id":********,"username":"719397910218","email":"<EMAIL>","first_name":"John","last_name":"Doe"},"balance":{"active_points":600,"active_punches":0}}})
     *      @Response(404, body={"data":null,"code":404,"error":"Not found","message":"Customer 12345118 could not be found."})
     * })
     * @Parameters({
     *      @Parameter("include", type="string", required=false description="You can optionally request that the response include the customer's balance. Example: include=balance")
     * })
     *
     * @param \Illuminate\Http\Request $request
     * @param $userUid
     * @return JsonResponse
     */
    public function show(Request $request, $userUid)
    {
        try {
//            info(__METHOD__. ' Reach endpoint', ['user' => $userUid ?? '']);
            /** @var User $user */
            $user = Auth::user(); //userId
//            info(__METHOD__. ' Get user', ['user' => $userUid ?? '', 'userId' => $user->id ?? null]);
            $business = $this->getBusinessForAppKey();
//            info(__METHOD__, ['user' => $userUid ?? '', 'userId' => $user->id ?? null, 'business' => $business->business_pk ?? null]);

            //Use the conglomerate member business
            if ($request->input('member_business_id')) {
                $business = $this->getConglomerateBusinessMember($business);
            }

            $request->business = $business;

            if (!$user) {
                \Log::warning(
                    __METHOD__,
                    ['message' => 'Customer Not found', 'userUid' => $userUid, 'business' => $business->name]
                );
                return $this->errorNotFound("User $userUid Not Found");
            }

            $user = $user->getProfile();

            $data = Customer::transformItem($user);

            $response = Business::includeRelations($request, $data, $business, $user);

            return $this->respondWithArray($response);
        } catch (\LogicException $e) {
            \Log::error(__METHOD__ . ' ' . $e);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \Log::error(__METHOD__ . ' ' . $e);
            throw  $e;
        }
    }

    /**
     * Verify Email of phone number
     *
     * POST /rpc/verify
     *
     * @param VerifyUserCredential $request
     *
     * @return JsonResponse
     */
    public function verify(VerifyUserCredential $request)
    {
        $appBusiness = $this->getBusinessForAppKey();
        $businessId = $appBusiness->business_pk;

        $activationToken = null;

        try {
            $user = Customer::where('passwordreset', $request->token)->first();

            if (!$user) {
                $activationToken = ActivationToken::findOrFail($request->token);

                $user = Customer::findOrFail($activationToken->user_fk);
            }
        } catch (ModelNotFoundException $e) {
            Log::warning(__METHOD__ . ' ' . $e, ['input' => $request->all(), 'appKey' => (array)$request->appKey]);
            return $this->errorNotFound('Not Found', "Invalid Activation token");
        } catch (\App\Exceptions\ResourceNotFoundException $e) {
            Log::warning(__METHOD__ . ' ' . $e, ['input' => $request->all()]);
            return $this->errorNotFound('Resource not found', $e->getMessage());
        }

        Log::info(__METHOD__, [
            'message' => 'Before verifyCredentials',
            'input' => $request->all(),
            'appKey' => (array)$request->appKey,
            'businessId' => $businessId,
        ]);

        return $this->verifyCredentials($request, $user, $activationToken);
    }

    /**
     * Reset user PIN code
     * Sends an email/SMS with token - POST request
     *
     * POST /rpc/pinResetRequest
     *
     * @param UserPinResetRequest $request
     *
     * @return JsonResponse
     */
    public function pinResetRequest(UserPinResetRequest $request)
    {
        try {
            $appBusiness = $this->getBusinessForAppKey();

            $business = $request->getUserBusiness();

            if ($business) {
                $finalBusiness = $business;
            } else {
                $finalBusiness = $appBusiness;
            }

            $businessId = (int) $finalBusiness->business_pk;

            // **************** Restrictions/Rate Limits ************************
            $canResetPinAfterSignUp = $this->canResetPinAfterSignup($request->only(['phone', 'email']), $businessId);
            if (!$canResetPinAfterSignUp) {
                return $this->conflictError();
            }

            $userCredential = Utils::getUserCredentialFromInput($request->only(['phone', 'email']));

            // Exponential Backoff rate limit per credential
            $canMakeRequests = Utils::canMakeRequests('pinResetRequest_' . $userCredential);
            if (!$canMakeRequests) {
                return $this->tooManyRequests();
            }

            // Exponential Backoff rate limit per IP
            $canMakeRequests = Utils::canMakeRequests('pinResetRequest_' . Utils::getIpFromRequest($request));
            if (!$canMakeRequests) {
                return $this->tooManyRequests();
            }
            // **************** Restrictions/Rate Limits ************************

            // TODO : Reset for coalition

            Log::info(__METHOD__, ['message' => 'Reset PIN email or phone', 'input' => $request->all()]);

            if ($request->email) {
                $user = Customer::where('email', $request->email)->where('business_fk', $businessId)->first();
            } else {
                $user = Customer::where('phone', $request->phone)->where('business_fk', $businessId)->first();
            }

            if (!$user) {
                Log::warning(__METHOD__, [
                    'message' => 'User Not Found by email or phone',
                    'input' => $request->all()
                ]);
                return $this->errorNotFound("User Not Found: {$request->email}");
            }

            if ($request->phone && $finalBusiness->industryProhibitedSms()) {
                info(__METHOD__ . 'Industry Prohibited SMS', [
                    'businessId' => $finalBusiness->business_pk,
                    'userId' => $user->id,
                    'phone' => $request->phone,
                ]);
                return $this->conflictError();
            }

            if ($request->mode === 'token') {
                /** @var Customer $user */
                $user->sendResetPinMessage($finalBusiness);
            } else {
                /** @var Customer $user */
                $user->sendResetPinVerificationCode($finalBusiness, $request->only('email', 'phone'));
            }
        } catch (\LogicException $e) {
            \Log::error(__METHOD__ . ' ' . $e, ['input' => $request->all()]);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \Log::error(__METHOD__ . ' ' . $e, ['input' => $request->all()]);
            return $this->errorInternalError();
        }

        return $this->requestAccepted('Accepted', 'You will receive an email with a link to reset your PIN.');
    }

    /**
     * Reset user PIN code
     * Resets PIN code using token- PUT
     *
     * POST /rpc/pinReset
     *
     * @param UserPinReset $request
     *
     * @return JsonResponse
     */
    public function pinReset(UserPinReset $request)
    {
        Log::info(__METHOD__, ['request_all' => $request->all()]);

        try {
            /** @var Customer|null $user */
            $user = self::findUserByTokenOrVerificationCode($request->validated());

            if (!$user) {
                Log::warning(__METHOD__, [
                    'message' => 'Verification Token Not Found',
                    'request->token' => $request->token,
                ]);
                return $this->errorNotFound("Verification Token Not Found: {$request->token}");
            }
            $business = $this->getBusinessForAppKey();

            $pinHash = \Hash::make($request->pin_code);

            $user->update([
                'pin_permission' => $pinHash,
                'passwordreset' => null,
                'verification_code' => null,
            ]);

            if ($request->use_same_pin === 'on') {
                $userProfiles = self::getUserProfiles($user);
                $userIds = $userProfiles->pluck('id')->toArray();
                User::whereIn('id', $userIds)->update(['pin_permission' => $pinHash]);
            }

            // Delete verification code
            if (isset($request->verification_code, $request->email) && $request->verification_code && $request->email) {
                $activationToken = ActivationToken::where('email', $request->email)
                    ->where('verification_code', $request->verification_code)
                    ->first();
                if ($activationToken) {
                    $activationToken->update([
                        'verification_code' => null,
                    ]);
                }
            }

            if (isset($request->verification_code, $request->phone) && $request->verification_code && $request->phone) {
                $activationToken = ActivationToken::where('phone', $request->phone)
                    ->where('verification_code', $request->verification_code)
                    ->first();
                if ($activationToken) {
                    $activationToken->update([
                        'verification_code' => null,
                    ]);
                }
            }
        } catch (ModelNotFoundException $e) {
            \Log::warning(__METHOD__ . ' ' . $e, ['input' => $request->only('token', 'email', 'phone', 'verification_code')]);
            return $this->errorNotFound('Not Found', 'Verification code not found');
        } catch (\LogicException $e) {
            \Log::warning(__METHOD__ . ' ' . $e, ['input' => $request->only('token', 'email', 'phone', 'verification_code')]);
            return $this->errorWrongArgs('Bad Request', $e->getMessage());
        } catch (\Exception $e) {
            \Log::error(__METHOD__ . ' ' . $e, ['input' => $request->only('token', 'email', 'phone', 'verification_code')]);
            return $this->errorInternalError();
        }

        return $this->respondNoContent();
    }

    /**
     * Verify user credentials: email or phone number using a token
     *
     * @param VerifyUserCredential $request
     * @param $user
     * @param $activationToken
     * @return JsonResponse
     */
    protected function verifyCredentials(VerifyUserCredential $request, $user, $activationToken)
    {
//        if ($activationToken) {
//            $linkExpires = new Carbon($activationToken->utc_link_expires);
//        } else {
//            $linkExpires = new Carbon($user->utc_link_expires);
//        }

        // Check expiry relative to created date instead of depending on DB value
        if ($activationToken) {
//            $linkExpires = (new Carbon('-48 hours'))->modify(ACTIVATION_LINK_EXPIRES);
            $linkExpires = (new Carbon($activationToken->utc_created))->modify(ACTIVATION_LINK_EXPIRES);
        } else {
            $linkExpires = (new Carbon($user->utc_created))->modify(ACTIVATION_LINK_EXPIRES);
        }

        //Checking if the link has expired
        if (Carbon::now() > $linkExpires) {
            return $this->errorWrongArgs('Invalid Token Error', 'Verification Token has expired');
        }

        // IMPORTANT: this is  the business where the user signed up
        // might be different from the APP Business (from X-Application-token)
        $business = Business::findByPk($user->business_fk);

        DB::connection('tenant')->beginTransaction();
        try {
            // IMPORTANT: Do not delete the token, the link should be clickable for 48h

            UserStatusManager::verifyCredentials($request->all(), $user, $business);

            // Get user's referral with checking on status & conditions to reward them
            $referral = Referral::forReferred($user->id);
            if ($referral && $referral->current_status == 1) {
                $referralProgram = BranchReferralRule::findByPk($referral->branch_referral_rules_fk);

                // Check the amount & product conditions
                if ($referralProgram && $referralProgram->cond_min_amount == 0 && !$referralProgram->cond_product_rewards) {
                    $referralData = new RewardReferralData([
                        'amount' => 0,
                        'user' => $user,
                        'branch' => $business->defaultBranch(),
                        'business' => $business,
                        'product_rewards' => [],
                    ]);
                    $rewardReferral = new RewardReferral($referralData);
                    $rewardReferral->handle();
                }
            }

            DB::connection('tenant')->commit();
        } catch (\Exception $e) {
            DB::connection('tenant')->rollBack();
            Log::error(__METHOD__ . ' ' . $e, ['business_name' => $business->name]);
            return $this->errorInternalError();
        }

        $userProfiles = self::getUserProfiles($user);

        $_user = Customer::transformItem($user);
        $_user['business'] = [
            'id' => bin2hex($business->business_uid),
            'name' => stripslashes($business->name),
        ];
        $_user['profiles'] = (new UserProfileCollection($userProfiles))
            ->response()->getData()->data;

        return $this->respondWithArray(['data' => $_user]);
    }

    /**
     * Delete account
     *
     * DELETE /users/{id}
     *
     * @Delete("/users/{id}")
     * @Versions({"v1"})
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return JsonResponse
     */
    public function delete(Request $request)
    {
        /** @var User $user */
        $authUser = Auth::user(); //userId
        $authBusiness = $this->getBusinessForAppKey();

        try {
            $endUsers = RelationTrait::getEndUserIds($authUser, $authBusiness);
            $forgottenRequest = ForgottenDataRequest::findAndCreate([
                'email' => $authUser->email,
                'phone' => $authUser->phone,
                'user_fk' => $authUser->id,
                'platform_fk' => $request->platform_id ?? config('app.platform_id')
            ]);

            foreach ($endUsers as $userId) {
                if ($userId == $authUser->id) {
                    continue;
                }

                $user = User::find($userId);
                ForgottenDataRequest::findAndCreate([
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'user_fk' => $user->id,
                    'platform_fk' => $request->platform_id ?? config('app.platform_id'),
                    'original_forgotten_request_id' => $forgottenRequest->id
                ]);
                //cron job will be scheduled during the night to process the rest users.
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, [
                'user_id' => $user->id ?? null,
                'business_pk' => $authBusiness->business_pk ?? null,
                'business_name' => $authBusiness->name ?? null
            ]);
            return $this->errorInternalError();
        }

        return $this->respondNoContent();
    }

    public function widget(Request $request, $userUid)
    {
        try {
            $helper = new EcomWidgetHelper($userUid);
            $data = $helper->load($request);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' ' . $e, ['input' => $request->all(), 'userUid' => $userUid]);
            return $this->errorInternalError();
        }

        return $this->respondWithArray(['data' => $data]);
    }

    /**
     * Create user with passing the CRM fields
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function signUpWithCrmFields(Request $request): JsonResponse
    {
        Log::info(__METHOD__, [
            'message' => 'Creating user account with CRM fields',
            'input' => $request->all(),
        ]);

        $business = $this->getBusinessForAppKey();
        $data = $request->isJson() ? $request->json()->all() : $request->all();
        $data['coalition'] = $business->coalition;
        $data['business_pk'] = $business->business_pk;

        // Initialize user crm service
        $userCrmFieldService = new UserCrmFieldService($data, $business);

        //Validate data
        $userCrmFieldService->validateOnCreate()->validate();

        // Check if user's account is in progress
        if ($userCrmFieldService->preventDuplicateAccountCreation()) {
            $this->setStatusCode(409);
            return $this->respondWithError('Conflict', 'Account creation already in progress');
        }

        DB::connection('tenant')->beginTransaction();
        try {
            $user = $userCrmFieldService->createUser();
            DB::connection('tenant')->commit();

            Log::info(__METHOD__, [
                'message' => 'User account created', 'userId' => $user['id'] ?? null, 'data' => $data, 'businessId' => $business->business_pk
            ]);

            // Clear creation cache
            $userCrmFieldService->clearCreationCacheFlag();

            return $this->respondWithArray(['data' => $user]);
        } catch (\Exception $e) {
            DB::connection('tenant')->rollBack();
            Log::error(__METHOD__ . ' ' . $e, ['data' => $data, 'businessId' => $business->business_pk]);
            $userCrmFieldService->clearCreationCacheFlag();
            return $this->errorInternalError();
        }
    }

    /**
     * Handle geofencing trigger events
     *
     * POST /users/me/geofence-triggers
     *
     * @param TriggerGeofenceRequest $request
     * @param UserGeofenceService $geofenceService
     * @return JsonResponse
     */
    public function triggerGeofence(TriggerGeofenceRequest $request, UserGeofenceService $geofenceService): JsonResponse
    {
        try {
            $user = Auth::user();
            $business = $request->getAppBusiness();

            $response = $geofenceService->process($request->validated(), $user->id, $business->business_pk, $business->time_zone);
            if (!empty($response)) {
                return response()->json($response);
            }

            return $this->respondNoContent();
        } catch (CustomValidationException $e) {
            Log::warning('Validation error triggering geofence: ' . $e->getMessage(), [
                'details' => $e->getDetails(),
                'user_id' => Auth::id(),
                'business_id' => $request->getAppBusiness()->business_pk,
            ]);

            return response()->json(['error' => $e->getMessage(), 'details' => $e->getDetails()], ResponseAlias::HTTP_UNPROCESSABLE_ENTITY);
        } catch (Exception $e) {
            Log::error('Internal error triggering geofence: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'business_id' => $request->getAppBusiness()->business_pk,
                'geofence_id' => $request->input('geofence_id'),
                'trigger_condition' => $request->input('trigger_condition'),
            ]);

            return $this->errorInternalError();
        }
    }
}

