<?php

namespace KangarooRewards\Common\Services\Customer;

use App\Models\V1\UserBusinessProfile;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use KangarooRewards\Common\Models\UserBusinessNotification;
use KangarooRewards\Common\Repositories\TagRepository;
use KangarooRewards\Common\Repositories\UserTagRepository;
use KangarooRewards\Import\Common\Helpers\ErrorMessageHelper;

class CustomerUpdateService extends CustomerService
{
    /**
     * Update customer and their related details
     * @return void
     * @throws ValidationException
     * @throws \Throwable
     */
    public function process(): void
    {
        DB::connection('tenant')->beginTransaction();
        try {
            $this->updateCustomer();
            $this->updateUserBusinessNotification();
            $this->updateUserBusinessProfile();
            $this->updateUserTags();
            $this->adjustPointsBalance();

            DB::connection('tenant')->commit();
        } catch (ValidationException $e) {
            DB::connection('tenant')->rollBack();

            throw $e;
        } catch (Exception $e) {
            DB::connection('tenant')->rollBack();

            Log::error(__METHOD__, [
                'message' => $e->getMessage(), 'business_id' => $this->businessId,
            ]);

            throw $e;
        }
    }

    /**
     * @return void
     * @throws Exception
     */
    protected function updateCustomer(): void
    {
        $attributes = [];

        $firstName = $this->getFieldValue('first_name');
        if (!is_null($firstName)) {
            $attributes ['first'] = $firstName;
        }

        $lastName = $this->getFieldValue('last_name');
        if (!is_null($lastName)) {
            $attributes ['last'] = $lastName;
        }

        $dob = $this->getFieldValue('dob');
        if (!is_null($dob)) {
            $attributes ['birth_date'] = $dob;
        }

        $language = $this->getFieldValue('language');
        if (!is_null($language)) {
            $attributes ['language'] = $language;
        }

        // Validate and set the email if found
        $email = $this->getFieldValue('email');
        if (!empty($email) && $this->customer->email != $email) {
            // Ensure the email is not associated with a customer other than the one identified
            $isEmailUnique = $this->userRepository->isFieldUniqueToCustomer($this->customer->id, 'email', $email, $this->businessId);
            if (!$isEmailUnique) {
                throw ValidationException::withMessages([
                    'email' => ErrorMessageHelper::getErrorMessage('email_already_exists'),
                ]);
            }

            // Add the email value to the customer attributes
            $attributes ['email'] = $email;
        }

        // Validate and set the phone if found
        $phone = $this->getFieldValue('phone');
        if (!empty($phone) && isset($this->data['phone_country_id']) && $this->customer->phone != $phone) {
            // Ensure the phone number is not associated with a customer other than the one identified
            $isPhoneUnique = $this->userRepository->isFieldUniqueToCustomer($this->customer->id, 'phone', $phone, $this->businessId);
            if (!$isPhoneUnique) {
                throw ValidationException::withMessages([
                    'phone' => ErrorMessageHelper::getErrorMessage('phone_already_exists'),
                ]);
            }

            // Add the phone value to the customer attributes
            $attributes ['phone'] = $phone;
            $attributes ['country_phone_fk'] = $this->data['phone_country_id'];
        }

        // Update customer fields
        if (!empty($attributes)) {
            if ($this->customer->update($attributes)) {
                $this->customer->refresh();
            } else {
                throw new Exception("Failed to update customer attributes");
            }
        }
    }

    /**
     * @return void
     */
    protected function updateUserBusinessNotification(): void
    {
        $attributes = [];

        $smsConsent = $this->getFieldValue('sms_consent');
        if (!is_null($smsConsent)) {
            $attributes ['allow_sms'] = $smsConsent;
        }

        $emailConsent = $this->getFieldValue('email_consent');
        if (!is_null($emailConsent)) {
            $attributes ['allow_email'] = $emailConsent;
        }

        $pushConsent = $this->getFieldValue('push_consent');
        if (!is_null($pushConsent)) {
            $attributes ['allow_push'] = $pushConsent;
        }

        if (!empty($attributes)) {
            // Set the cem flag
            $cemFlag = (
                (isset($attributes['allow_sms']) && $attributes['allow_sms'] == 1) ||
                (isset($attributes['allow_email']) && $attributes['allow_email'] == 1)
            ) ? 1 : 0;

            // Assign the time zone attributes for the create process
            $attributes ['timezone_mysql_fk'] = $this->defaultBusinessBranch->branch_timezone_fk;
            $attributes ['cem_flag'] = $cemFlag;

            UserBusinessNotification::updateOrCreate([
                'user_fk' => $this->customer->id,
                'business_fk' => $this->businessId
            ], $attributes);
        }
    }

    /**
     * @return void
     */
    protected function updateUserBusinessProfile(): void
    {
        $attributes = [];

        $customField1 = $this->getFieldValue('custom_field_1');
        if (!is_null($customField1)) {
            $attributes ['custom_field_1'] = $customField1;
        }

        $customField2 = $this->getFieldValue('custom_field_2');
        if (!is_null($customField2)) {
            $attributes ['custom_field_2'] = $customField2;
        }

        $customField3 = $this->getFieldValue('custom_field_3');
        if (!is_null($customField3)) {
            $attributes ['custom_field_3'] = $customField3;
        }

        $customField4 = $this->getFieldValue('custom_field_4');
        if (!is_null($customField4)) {
            $attributes ['custom_field_4'] = $customField4;
        }

        $customField5 = $this->getFieldValue('custom_field_5');
        if (!is_null($customField5)) {
            $attributes ['custom_field_5'] = $customField5;
        }

        if (!empty($attributes)) {
            UserBusinessProfile::updateOrCreate([
                'user_fk' => $this->customer->id,
                'business_fk' => $this->businessId
            ], $attributes);
        }
    }

    /**
     * @return void
     */
    protected function updateUserTags(): void
    {
        if ($this->isFieldSet('tags')) {
            $tags = $this->data['tags'];

            if (!empty($tags)) {
                $tags = $this->transformUserTagsList($tags);

                $userTagRepository = new UserTagRepository();

                if ($this->importOptions['allow_override_customer_tags']) {
                   $this->overrideCustomerTags($tags, $userTagRepository);
                } else {
                    // Assign tags to customer by checking for unassigned tags and adding only those
                    $this->addCustomerTags($tags, $userTagRepository);
                }
            }
        }
    }

    /**
     * Override customer tags
     * @param array $tags
     * @param UserTagRepository $userTagRepository
     * @return void
     */
    private function overrideCustomerTags(array $tags, UserTagRepository $userTagRepository): void
    {
        // Get existing tags for customer
        $customerExistingTagIds = $userTagRepository->getUserTagIds($this->customer->id);

        if (empty($customerExistingTagIds)) {
            // Attach the tags without executing the remove process
            $this->attachUserTags($tags, $userTagRepository);
        } else {
            // Transform the tags list into IDs
            $this->adjustedTagIds = TagRepository::getBusinessTagIds($tags, $this->businessId);

            // Compare the current customer tags with the adjusted ones to know whether we need to override
            if (!empty(array_diff($this->adjustedTagIds, $customerExistingTagIds)) ||
                !empty(array_diff($customerExistingTagIds, $this->adjustedTagIds))) {
                // Replace existing customer tags by removing the current ones and creating new ones
                $userTagRepository->removeUserTags($this->customer->id);

                // Assign the new tags to customer
                $this->attachUserTags($tags, $userTagRepository);
            }
        }
    }

    /**
     * @param array $tags
     * @param UserTagRepository $userTagRepository
     * @return void
     */
    private function addCustomerTags(array $tags, UserTagRepository $userTagRepository): void
    {
        $unassignedTagIds = $userTagRepository->getUnassignedTagIdsForCustomer($this->customer->id, $this->businessId, $tags);

        // Prepare data for batch insert
        $userTagsData = $this->prepareUserTagsAttributes($unassignedTagIds);

        // Insert batch
        $userTagRepository->insertBatchUserTag($userTagsData);
    }

    /**
     * @param $field
     * @return mixed|null
     */
    private function getFieldValue($field): mixed
    {
        if ($this->isFieldSet($field) && $this->data[$field] != '' && !is_null($this->data[$field])) {
            return $this->data[$field];
        }

        return null;
    }

}