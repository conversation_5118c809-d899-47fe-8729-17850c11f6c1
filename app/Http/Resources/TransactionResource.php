<?php

namespace App\Http\Resources;

use App\Models\PointsExpiration;
use App\Models\V1\Business;
use App\Models\V1\TransactionSubType;
use App\Traits\TransactionSubTypeTrait;
use App\Models\V1\BusinessBranch;
use App\Models\V1\Currency;
use App\Traits\BusinessRulesTrait;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Models\V1\Language as LanguageModel;

// use Illuminate\Http\Resources\Json\JsonResource;

class TransactionResource extends JsonResource
{


    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request
     *
     * @return array
     * @throws \Exception
     */
    public function toArray($request)
    {
        //        dd($this->relationLoaded('branch'));
        $appLocale = \App::getLocale() ?? 'en';

        $relations = explode(',', $request->relationships);

        $includeBranch = (in_array('branch', $relations, true) || in_array('branch.category', $relations, true))
            && $this->relationLoaded('branch');

        $include = explode(',', $request->include);

        $includeEcomCoupon = in_array('ecom_coupon', $include, true);

        $includeTransactionDetail = (in_array('transaction_details', $relations, true)) && $this->relationLoaded('transactionDetails');


        // IMPORTANT $this->activity might not exists for all transactions
        $branchModel = BusinessBranch::find($this->entity_fk);
        $ptsLabel = BusinessRulesTrait::getPointsLabelForBusiness(
            $branchModel->business_fk,
            $request->hasHeader('Accept-Language')
        );

        $defaultBranch = BusinessBranch::where([
            'business_fk' => $branchModel->business_fk,
            'enabled' => 1,
            'default_branch' => 1
        ])->firstOrFail();
        $currency = Currency::forBranch($defaultBranch->business_branch_pk);

        $businessRules = BusinessRulesTrait::getRulesForBusiness($branchModel->business_fk);
        $includePointsExpiry = (in_array('points_expiry', $include, true)) && $businessRules->points_expiry_policy;

        $pointsValue = (int) ($this->activity->units_exchange ?? 0);

        $includeDisplayName = in_array('display_name', $include, true);

        $name = trans('api.' . $this->transactionSubType->name, [
            '{points_label}' => $pointsValue > 0 ? $ptsLabel->plural : $ptsLabel->singular,
        ]);

        $amount = (float) ($this->purchase->amount ?? $this->activity->amount ?? 0);

        return [
            $this::$wrap => [
                'id' => $this->transaction_pk,
                'amount' => $amount,
                'points' => $pointsValue,
                'name' => $name,
                'display_name' => $this->when($includeDisplayName, $this->includeDisplayName($appLocale, $name, $ptsLabel, $pointsValue, $amount, $currency)),
                'comments' => $this->comments,
                'transaction_type' => (int) $this->transaction_sub_type_fk,
                'created_at' => (new Carbon($this->utc_created))->toIso8601String(),
                'updated_at' => (new Carbon($this->utc_updated))->toIso8601String(),
                'branch' => $this->when($includeBranch, $this->includeBranch($relations)),
                'transaction_details' => $this->when($includeTransactionDetail, $this->includeTransactionDetails()),
                'points_expiry' => $this->when($includePointsExpiry, $this->includeExpiryPoints($businessRules)),
                'ecom_coupon' => $this->when($includeEcomCoupon, $this->includeEcomCoupon()),
            ],
            //            $this::$wrap => Transaction::transformItem($this, [])
        ];
    }

    private function includeBranch($relations): array
    {
        $includeCategory = in_array('branch.category', $relations, true);

        // IMPORTANT: relationLoaded for nested relations not working
        // $this->relationLoaded('branch.category')
        // https://github.com/laravel/framework/issues/25105

        return [
            'id' => bin2hex($this->branch->branch_uid),
            'name' => $this->branch->name,
            'category' => $this->when($includeCategory, $this->includeBranchCategory()),
        ];
    }

    private function includeBranchCategory()
    {
        if (!isset($this->branch->category)) {
            return null;
        }
        return (new CategoryResource($this->branch->category))->response()->getData()->data;
    }

    private function includeTransactionDetails()
    {
        if (!isset($this->transactionDetails)) {
            return null;
        }
        return (new TransactionDetailsResource($this->transactionDetails))->response()->getData()->data;
    }

    private function includeEcomCoupon()
    {
        if (!isset($this->ecomCoupon)) {
            return null;
        }
        return (new EcomCouponResource($this->ecomCoupon))->response()->getData()->data;
    }

    private function includeExpiryPoints($businessRules)
    {
        $ptsExpirationConfig = $businessRules->getPointsExpirationConfig();

        //Get the rolling period for pts. expiration
        $ptsExpirationPeriodValue = $ptsExpirationConfig['period']['value'];
        $ptsExpirationPeriodFormat = PointsExpiration::convertDesignatorCharacterToString($ptsExpirationConfig['period']['designator']);

        //Get reward transaction sub types that will affect points positively
        $rewardTrxSubTypeIds = TransactionSubTypeTrait::getRewardTrxSubtypeIdsForPoints();

        if (in_array((int) $this->transaction_sub_type_fk, $rewardTrxSubTypeIds)) {
            $ptsExpirationDate = PointsExpiration::getPointsExpirationDateForTransaction($ptsExpirationPeriodValue, $ptsExpirationPeriodFormat, $this->utc_created);
        } else {
            $ptsExpirationDate = null;
        }
        return $ptsExpirationDate;
    }

    private function includeDisplayName($appLocale, $name, $ptsLabel, $pointsValue, $amount, $currency)
    {
        $pointsValue = (int) ($this->activity->units_exchange ?? 0);
        $pointLabel = $pointsValue > 0 ? $ptsLabel->plural : $ptsLabel->singular;

        $languageId = LanguageModel::getLanguageIdByAbbreviation($appLocale);

        $currencySymbol = $currency->symbol ?? "$";

        $concatTitle = '';
        switch ($this->transaction_sub_type_fk) {
            // Offer Trx
            case TRX_SUBTYPE_CLAIMED_POINTS:
            case TRX_SUBTYPE_TARGETED_BASIC:
            case TRX_SUBTYPE_COUPON_GET_COUPON:
            case TRX_SUBTYPE_COUPON_CLAIMED:
            case TRX_SUBTYPE_COUPON_CONVERTIBLE_CREATED:
            case TRX_SUBTYPE_COUPON_CONVERTIBLE_CLAIMED:
            case TRX_SUBTYPE_OFFER_REFUND_POINTS:
            case TRX_SUBTYPE_SPIN_DRAW_OFFER:
                // Rewards Trx
            case TRX_TYPE_POINTS_PURCHASE:
            case TRX_SUBTYPE_PRODUCT_REDEMPTION_POINTS:
            case TRX_SUBTYPE_CLERK_PRODUCT_REDEMPTION_POINTS:
            case TRX_SUBTYPE_CONVERTIBLE_PRODUCT_REDEMPTION_POINTS:
            case TRX_SUBTYPE_FACEBOOK_SHARED_RED_CATALOG_POINTS:
            case TRX_SUBTYPE_REWARD_REDEEM_BUNDLE:
                // Product Rewards / A La Carte
            case TRX_SUBTYPE_REWARD_POINTS:
            case TRX_SUBTYPE_BOOKING_POINTS:
                $titleLangs = json_decode($this->title_languages, true) ?? [];
                if (!empty($titleLangs)) {
                    $title = $titleLangs[$languageId] ?? $titleLangs[1];
                    $concatTitle = ": $title";
                } else {
                    $concatTitle = ": $pointsValue $pointLabel";
                }
                break;
            case TRX_SUBTYPE_TRANSFER_POINTS_SENDER:
            case TRX_SUBTYPE_TRANSFER_POINTS_RECEIVER:
                $concatTitle = ": $pointsValue $pointLabel";
                break;
            case TRX_SUBTYPE_TRANSFER_AMOUNT_SENDER:
            case TRX_SUBTYPE_TRANSFER_AMOUNT_RECEIVER:
            case TRX_SUBTYPE_GIFTCARD_AMOUNT_PAYMENT:
            case TRX_SUBTYPE_TRANSFER_AMOUNT_SENDER_RECALL:
                $concatTitle = ": $currencySymbol$amount";
                break;

            default:
                $concatTitle = '';
                break;
        }

        return "$name$concatTitle";
    }
}
